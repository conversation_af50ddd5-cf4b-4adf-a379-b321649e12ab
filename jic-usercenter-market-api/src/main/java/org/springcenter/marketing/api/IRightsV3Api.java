package org.springcenter.marketing.api;

import com.jnby.common.CommonRequest;
import com.jnby.common.ResponseResult;
import io.swagger.annotations.ApiOperation;
import org.springcenter.marketing.api.checkRightsDto.CheckRightCouponV3Dto;
import org.springcenter.marketing.api.checkRightsDto.CheckRightCouponV3DtoForPre;
import org.springcenter.marketing.api.context.UseRightsContext;
import org.springcenter.marketing.api.dto.*;
import org.springcenter.marketing.api.entity.CheckRightsEntity;
import org.springcenter.marketing.api.fallback.IMarketingApiFallBackFactory;
import org.springcenter.marketing.api.fallback.IRightsV3ApiFallBackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

@Component
@FeignClient(contextId = "rightsV3RemoteApi", value = "jic-usercenter-market-api-center", fallbackFactory = IRightsV3ApiFallBackFactory.class)
public interface IRightsV3Api {



    @PostMapping(value = "/rightsV3/api/openCard")
    @ApiOperation(value = "开卡")
    public ResponseResult openCard(@RequestBody CommonRequest<OpenCardAndRightsReq> request);



    @PostMapping(value = "/rightsV3/api/checkOrUse")
    @ApiOperation(value = "验证或者消耗权益")
    public ResponseResult<Boolean> check(@RequestBody CommonRequest<CheckRightsEntity> request);



    @PostMapping("/rightsV3/api/preCalcExpansionCoupon")
    @ApiOperation(value = "预计算膨胀券")
    public ResponseResult<List<PreCalcResp>> preCalcExpansionCoupon(@RequestBody CommonRequest<List<CheckRightCouponV3DtoForPre>> request);


    @PostMapping("/rightsV3/api/getUserAvailableCardApplicationParty")
    @ApiOperation(value = "获取当前适用方类型用户所有可用卡")
    public ResponseResult<List<BNewUserMemberCardResp>> getUserAvailableCardApplicationParty(@RequestBody CommonRequest<UserAvailableCardApplicationPartyReq> request);


    @PostMapping("/rightsV3/api/getUserAllCardApplicationParty")
    @ApiOperation(value = "获取当前适用方类型用户所有卡")
    public ResponseResult<List<BNewUserMemberCardResp>> getUserAllCardApplicationParty(@RequestBody CommonRequest<UserAvailableCardApplicationPartyReq> request);

    @PostMapping(value = "/rightsV3/api/recover")
    @ApiOperation(value = "回滚权益")
    public ResponseResult<Boolean> recover(@RequestBody CommonRequest<UseRightsContext> request);


    @PostMapping(value = "/rightsV3/api/reflectAppIdToApplicationPart")
    public ResponseResult<Integer> reflectAppIdToApplicationPart(@RequestBody CommonRequest<String> request);


    @PostMapping(value = "/rightsV3/api/loseCard")
    public ResponseResult loseCard(@RequestBody  CommonRequest<OpenCardAndRightsReq> request);



    @PostMapping("/rightsV3/api/loseCardByOutNo")
    @ApiOperation(value = "失效卡 根据outNo")
    public ResponseResult loseCardByOutNo(@RequestBody  CommonRequest<LoseCardByOutNoReq> request);


    @PostMapping(value = "/rightsV3/api/dealTryAfterBuyIsHaveUnFinshBox")
    public ResponseResult dealTryAfterBuyIsHaveUnFinshBox(@RequestBody  CommonRequest<DealTryAfterBuyIsHaveUnFinishBoxReq> request);

    @PostMapping(value = "/rightsV3/api/isHaveAndNoOrderUser")
    public ResponseResult<Boolean> isHaveAndNoOrderUser(@RequestBody  CommonRequest<HaveUnFinishBoxReq> request);

    /**
     * 获取正式卡 或者免费卡 或者单次卡信息
     * @param request
     * @return
     */
    @PostMapping("/rightsV3/api/getSubMemberCardRights")
    @ApiOperation(value = "获取正式卡 免费卡 单次卡信息")
    public ResponseResult<List<SubMemberCardRightsResp>> getSubMemberCardRights(@RequestBody CommonRequest<SubMemberCardRightsReq> request);


    /**
     * 随盒礼
     * @param request
     * @return
     */
    @PostMapping("/rightsV3/api/getUserBoxGiftRights")
    @ApiOperation(value = "根据下标查询随盒礼")
    public ResponseResult<List<BNewUserBoxGiftResp>> getUserBoxGiftRights(@RequestBody CommonRequest<List<UserBoxGiftRightsReq>> request);



    @PostMapping("/rightsV3/api/getUserBoxGiftRightsByConsumeOutNo")
    @ApiOperation(value = "根据consumeOutNo 去查询 随盒礼权益")
    public ResponseResult<List<BNewUserBoxGiftResp>> getUserBoxGiftRightsByConsumeOutNo(@RequestBody CommonRequest<UserRightsByConsumeOutNoReq> request) ;



    @PostMapping("/rightsV3/api/getUserSubCouponRightsByConsumeOutNo")
    @ApiOperation(value = "根据 consumeOutNo 去查询 权益  膨胀券权益")
    public ResponseResult<List<BNewUserRightsCouponResp>> getUserSubCouponRightsByConsumeOutNo(@RequestBody CommonRequest<UserRightsByConsumeOutNoReq> request) ;


    @ResponseBody
    @PostMapping("/rightsV3/api/getUserRightsByOutNo")
    @ApiOperation(value = "根据outNo获取到用户的权益信息")
    public ResponseResult<List<BNewUserRightsResp>> getUserRightsByOutNo(@RequestBody CommonRequest<GetUserRightsByOutNoReq> request);



    @PostMapping("/rightsV3/api/getUserCardBySubId")
    @ApiOperation(value = "根据subid查询用户卡信息")
    public ResponseResult<BNewUserMemberCardResp> getUserCardBySubId(@RequestBody CommonRequest<GetUserCardBySubId> request);



    @ResponseBody
    @PostMapping("/rightsV3/api/getUserSubV3CouponBySubId")
    @ApiOperation(value = "根据subid查询用户膨胀券规则 只针对用户")
    public ResponseResult<List<UserSubV3CouponBySubIdResp>> getUserSubV3CouponBySubId(@RequestBody CommonRequest<GetUserCardBySubId> request);


    @PostMapping("/rightsV3/api/batchUseUserRights")
    @ApiOperation(value = "批量消耗用户次数类权益")
    public ResponseResult<Boolean> batchUseUserRights(@RequestBody CommonRequest<List<BatchUseUserRightsReq>> request);


    @PostMapping("/rightsV3/api/getBoxGiftByBoxSns")
    @ApiOperation(value = "根据boxSn获取收盒礼")
    public ResponseResult<List<BNewUserBoxGiftResp>> getBoxGiftByBoxSns(@RequestBody  @Validated CommonRequest<GetBoxGiftByBoxSn> request);
}
