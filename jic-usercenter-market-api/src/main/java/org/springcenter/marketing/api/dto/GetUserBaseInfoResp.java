package org.springcenter.marketing.api.dto;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class GetUserBaseInfoResp {

    private Long id;

    @ApiModelProperty(value = "集团卡cClientVipId")
    private Long memberId;

    @ApiModelProperty(value = "是否有集团卡")
    private Boolean isHaveMemberCard =false;

    @ApiModelProperty(value = "是否有品牌卡")
    private Boolean isHaveBrandCard =false;

    @ApiModelProperty(value = "集团卡积分")
    private Long memberIntegral;

    @ApiModelProperty(value = "集团卡名称")
    private String memberTypeName;

    @ApiModelProperty(value = "集团卡id")
    private String memberTypeId;

    @ApiModelProperty(value = "集团卡等级   1 2 3")
    private String memberLvl = "1";

    @ApiModelProperty(value = "集团卡号")
    private String memberCode;

    @ApiModelProperty(value = "集团卡折扣")
    private Double memberDiscount;

    @ApiModelProperty(value = "生日")
    private String birthday;

    @ApiModelProperty(value = "是否生日月  true  是  false 否")
    private Boolean isBirthMonth =false;

    @ApiModelProperty(value = "品牌卡号")
    private String brandCardNo;

    @ApiModelProperty(value = "品牌卡折扣")
    private Double brandDiscount;

    @ApiModelProperty(value = "品牌卡等级  1 2 3 ")
    private String brandCardLel = "1";

    @ApiModelProperty(value = "品牌卡名称")
    private String brandTypeName;

    @ApiModelProperty(value = "品牌卡type id")
    private String brandTypeId;

    @ApiModelProperty(value = "品牌weid")
    private String brandId;

    @ApiModelProperty(value = "用户昵称")
    private String nickName;

    @ApiModelProperty(value = "less亲密值")
    private String lessIntimacyValue = "0";

    @ApiModelProperty(value = "是否展示less亲密值  1 展示  0 不展示")
    private Integer isShowLessIntimacy = 0 ;

    @ApiModelProperty(value = "用户消费金额")
    private Double memberConsume = 0.0;

    @ApiModelProperty(value = "单笔消费规则  如果为 99999999  那么则是最高等级了")
    private Long oneConsumeRule;

    @ApiModelProperty(value = "银卡升级金卡需要的  累计消费")
    private Double consumeSilverToGoldRule;

    @ApiModelProperty(value = "金卡升白金卡需要的  累计消费")
    private Double consumeGoldToPlatinumRule;

    @ApiModelProperty(value = "当前品牌卡累计消费多少可以升到下一级    如果为 99999999  那么则是最高等级了")
    private Long consumeUp;

    @ApiModelProperty(value = "有多少权益")
    private Integer haveRightsNum = 0;

    @ApiModelProperty(value = "176 直营  非176 经销")
    private Integer suitType;

    @ApiModelProperty(value = "门店id")
    private String storeId;

    @ApiModelProperty(value = "品牌卡开卡日期")
    private String brandCreateDate;


    @ApiModelProperty(value = "共享会员卡等级ID")
    private Long vipShareTypeId;

    @ApiModelProperty(value = "共享会员卡等级名称")
    private String vipShareTypeName;

    @ApiModelProperty(value = "共享会员卡折扣")
    private Double vipShareTypeDiscount;

    @ApiModelProperty(value = "会员卡状态：Y正常，N冻结")
    private String status;

    @ApiModelProperty(value = "共享会员卡等级")
    private Long vipShareTypeLevelId = 1L;

    @ApiModelProperty(value = "贵宾邀请制 10-贵宾")
    private String cardStatusExp;

    @ApiModelProperty(value = "是否注销：Y-是；N-否")
    private String isZhux;

}
