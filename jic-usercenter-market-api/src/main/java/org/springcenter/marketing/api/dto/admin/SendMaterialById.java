package org.springcenter.marketing.api.dto.admin;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SendMaterialById {

    @ApiModelProperty(value = "用户奖励主键")
    private Integer id;


    @ApiModelProperty(value = "物流单号")
    private String expressNo;

    @ApiModelProperty(value = "省份")
    private String provinces;

    @ApiModelProperty(value = "城市")
    private String city;

    @ApiModelProperty(value = "区")
    private String districts;

    @ApiModelProperty(value = "详细地址")
    private String address;

    @ApiModelProperty(value = "发送实物奖励手机号")
    private String sendMaterialPhone;

    @ApiModelProperty(value = "真实姓名")
    private String realName;

}
