package org.springcenter.marketing.api.dto.admin;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

@Data
public class CreateOrUpdateQuesTemplateReq {

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;

    /**
     * 场景id
     */
    @NotBlank(message = "场景id必填")
    @ApiModelProperty(value = "场景id")
    private String quesSceneId;

    /**
     * 页面标题
     */
    @NotBlank(message = "页面标题必填")
    @ApiModelProperty(value = "页面标题")
    private String pageTitle;

    /**
     * 备注
     */
    @ApiModelProperty(value = "remark")
    private String remark;

    /**
     * 场景数据 （举例 券类的 优惠券awardId）
     */
    @ApiModelProperty(value = "场景数据 （举例 券类的 优惠券awardId）")
    private String sceneParams;

    /**
     * 形式  0 页面  1 组件
     */
    @NotNull(message = "形式必填")
    @ApiModelProperty(value = "形式  0 页面  1 组件")
    private Integer type;

    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    private String title;

    /**
     * 副标题
     */
    @ApiModelProperty(value = "副标题")
    private String subTitle;

    /**
     * 组件页面  0 生日权益领取页  1 积分兑换成功页
     */
    @ApiModelProperty(value = "组件页面  0 生日权益领取页  1 积分兑换成功页")
    private Integer assemblyPage;

    /**
     * 配置信息  频率设置和奖励设置  json
     */
    @ApiModelProperty(value = "配置信息  频率设置和奖励设置")
    private String assemblyJson;

    /**
     * 0 不自动  1自动
     */
    @ApiModelProperty(value = "0 不自动  1自动")
    private Integer autoEject;

    /**
     * 0 禁用  1启用
     */
    @NotNull(message = "状态必填")
    @ApiModelProperty(value = " 0 禁用  1启用")
    private Integer status;

    /**
     * 跳转类型 0 小程序 1 h5
     */
    @ApiModelProperty(value = " 跳转类型 0 小程序 1 h5")
    private Integer jumpType;

    /**
     * appid   跳转小程序必填
     */
    @ApiModelProperty(value = "appid   跳转小程序必填")
    private String appId;

    /**
     * 小程序路径  跳转小程序必填
     */
    @ApiModelProperty(value = "小程序路径  跳转小程序必填")
    private String pagePath;

    /**
     * 跳转h5路径  跳转h5时候必填
     */
    @ApiModelProperty(value = "跳转h5路径  跳转h5时候必填")
    private String h5Url;

    /**
     * 结束按钮文案
     */
    @ApiModelProperty(value = "结束按钮文案")
    private String endButton;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 0 未删除  1 已删除
     */
    @ApiModelProperty(value = " 0 未删除  1 已删除")
    private Integer isDel;

    @NotEmpty(message = "适用小程序必填")
    @ApiModelProperty(value = "适用小程序等   小程序为 weid           pos+为  1     搭配师后台为  2 ")
    private List<String> suitParams;


    @ApiModelProperty(value = " 创建人")
    private String createBy;

    private String imgUrl;

    @ApiModelProperty(value = " 0  awradId  1 微信券id")
    private Integer sceneParamsType;

    @ApiModelProperty(value = "结束语文案")
    private String overContent;


    @ApiModelProperty(value = "问题和答案")
    private List<ListQuestionTemplateResp.QuestionAndAnswer> questionAndAnswers;

}
