package org.springcenter.marketing.api.dto;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/2/16 16:24
 */
@Data
@ApiModel(value = "用户搜索任务列表信息返回信息")
public class UserTaskListResp {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "icon")
    private String icon;

    @ApiModelProperty(value = "任务名称")
    private String taskName;

    @ApiModelProperty(value = "任务类型0:签到 1:订单评价  2:社区发帖  3:自定义  4:生日 5：公众号 6：导购 7：消费任务 8：童孩 9：尺码 10：性别")
    private Integer taskType;

    @ApiModelProperty(value = "描述")
    private String taskDescription;

    @ApiModelProperty(value = "跳转链接")
    private String taskLink;

    @ApiModelProperty(value = "需完成次数")
    private Integer requiredTimes;

    @ApiModelProperty(value = "已完成次数")
    private Integer finishTimes;

    /* @ApiModelProperty(value = "审核中的次数")
    private Integer underReviewTimes;
    */
    @ApiModelProperty(value = "0初始  2待领取 3已领取/已完成 4任务待领取 5统计中 6已结束")
    private String status;

    @ApiModelProperty(value = "按钮文案")
    private String buttonName;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "积分")
    private Integer gainPoints;

    @ApiModelProperty(value = "券 1代表有券")
    private Integer awardId = 0;

    @ApiModelProperty(value = "奖励领取类型 0:用户手动领取 1：自动发放")
    private Integer drawType;

    @ApiModelProperty(value = "任务编号")
    private String taskNo;


    @ApiModelProperty(value = "任务领取类型 0:不需要领取 1：需要手动领取任务")
    private Integer taskIsDraw;

    private Integer days;

    private Long endTime;

    private Integer statusSort;

}
