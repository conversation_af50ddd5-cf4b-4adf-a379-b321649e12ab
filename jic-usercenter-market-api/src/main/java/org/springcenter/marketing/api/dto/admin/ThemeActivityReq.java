package org.springcenter.marketing.api.dto.admin;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 互动留言板活动信息表
 * @TableName theme_activity
 */
@Data
public class ThemeActivityReq implements Serializable {
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Integer id;

    /**
     * 主题名称
     */
    @ApiModelProperty(value = "主题名称")
    private String themeName;

    /**
     * 主题介绍
     */
    @ApiModelProperty(value = "主题介绍")
    private String themeIntroduce;

    /**
     * 主题图片
     */
    @ApiModelProperty(value = "主题图片")
    private String themeImg;

    /**
     * 主题背景
     */
    @ApiModelProperty(value = "主题背景")
    private String themeBackground;

    /**
     * 参活小程序weid
     */
    @ApiModelProperty(value = "参活小程序weid")
    private String weid;

    /**
     * 活动开始时间
     */
    @ApiModelProperty(value = "活动开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date activityStartTime;

    /**
     * 活动结束时间
     */
    @ApiModelProperty(value = "活动结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date activityEndTime;

    /**
     * 0  开始    1 暂停   （状态在时间后判断， 时间是第一优先级）
     */
    @ApiModelProperty(value = "0  开始    1 暂停   （状态在时间后判断， 时间是第一优先级）")
    private Integer status;

    /**
     * 活动规则
     */
    @ApiModelProperty(value = "活动规则")
    private String activityRule;

    /**
     * 留言排序规则   0  发布时间倒序   1  点赞量从多到少
     */
    @ApiModelProperty(value = "留言排序规则   0  发布时间倒序   1  点赞量从多到少")
    private Integer leaveMessageSort;

    /**
     * 留言评论回复 最少字数
     */
    @ApiModelProperty(value = "留言评论回复 最少字数")
    private Integer leaveMessageMinNum;

    /**
     * 留言评论回复 最大字数
     */
    @ApiModelProperty(value = "留言评论回复 最大字数")
    private Integer leaveMessageMaxNum;

    /**
     * 活动结束后x天，运营发放奖品
     */
    @ApiModelProperty(value = "活动结束后x天，运营发放奖品")
    private Integer activityEndSendAwardDay;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 0  正常   1 删除
     */
    private Integer isDel;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "规则信息 仅修改和新增的    返回的 子数据")
    private List<ThemeActivityDaysRuleDto> list;


    @ApiModelProperty(value = "规则信息 需要删除的")
    private List<Integer> shouldDeleteIds;

    @Data
    public class ThemeActivityDaysRuleDto  {
        /**
         * 主键
         */
        @ApiModelProperty(value = "主键")
        private Integer id;

        /**
         * 活动主键id
         */
        @ApiModelProperty(value = "活动主键id")
        private Integer themeActivityId;

        /**
         * 签到天数
         */
        @ApiModelProperty(value = "签到天数")
        private Integer signInDays;

        /**
//         * 排序  从小到大
         */
        @ApiModelProperty(value = "排序  从小到大 不需要传递")
        private Integer sortNum;

        /**
         * 创建时间
         */
        private Date createTime;

        /**
         * 更新时间
         */
        private Date updateTime;

        /**
         * 0 正常  1 删除
         */
        private Integer isDel;


        /**
         * 奖品id信息
         */
        @ApiModelProperty(value = "逗号分隔的 ids  奖励ids")
        private String AwardConfigIds;

    }



}