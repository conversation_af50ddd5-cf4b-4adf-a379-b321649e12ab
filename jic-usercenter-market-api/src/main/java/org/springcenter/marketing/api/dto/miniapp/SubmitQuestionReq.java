package org.springcenter.marketing.api.dto.miniapp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Date;

@Data
public class SubmitQuestionReq {


    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id  不用传递")
    private String id;

    /**
     * 问卷模版id
     */
    @NotBlank(message = "问卷模版id必传")
    @ApiModelProperty(value = "问卷模版id 必传")
    private String questionTemplateId;

    /**
     * 问卷id
     */
    @NotBlank(message = "问卷id必传")
    @ApiModelProperty(value = "问卷id 必传")
    private String questionId;

    /**
     * 答案id
     */
    @NotBlank(message = "答案id必传")
    @ApiModelProperty(value = "答案id 必传")
    private String answerId;

    /**
     * 用户unionid 或者 员工id
     */
    @NotBlank(message = "用户唯一id必传")
    @ApiModelProperty(value = "用户唯一id 必传")
    private String bizUserId;

    /**
     * 文字类答案
     */
    @ApiModelProperty(value = "n内容  对应文字类答案")
    private String content;

    /**
     * 适用类型  如果是小程序  则里面是 小程序的weid 其他则为 pos+的类型或者 搭配师后台类型
     */
    @ApiModelProperty(value = "适用类型  如果是小程序  则里面是 小程序的weid 其他则为 pos+的类型或者 搭配师后台类型     1  POS+   2 搭配师后台")
    private String suitParam;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 0 未删除  1 已删除
     */
    private Integer isDel;

    @ApiModelProperty(value = "业务id  如果为自由问卷可不填 其他则必填")
    private String bizId;

    @ApiModelProperty(value = "记录信息  如果为自由问卷可不填 其他则必填")
    private String recordMsg;

}
