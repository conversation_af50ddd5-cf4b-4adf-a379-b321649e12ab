package org.springcenter.marketing.api.dto.miniapp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel("草稿箱详情出参")
public class NoteDraftDetailResp implements Serializable {
    
    @ApiModelProperty("草稿ID")
    private String draftId;
    
    @ApiModelProperty("留言内容(全文)")
    private String content;

    @ApiModelProperty("草稿更新时间，格式yyyy.MM.dd HH:mm:ss")
    private String updateTime;
}
