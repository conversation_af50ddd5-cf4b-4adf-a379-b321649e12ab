package org.springcenter.marketing.api.dto.miniapp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("主题勋章列表响应")
public class BadgeThemeDetailResp {

    @ApiModelProperty(value = "主题业务ID")
    private String themeBizId;

    @ApiModelProperty(value = "主题名称")
    private String themeName;

    @ApiModelProperty(value = "主题规则图")
    private String themeRuleImage;

    private String description;
    
    @ApiModelProperty(value = "勋章列表")
    private List<BadgeInfo> badgeList;
    
    @Data
    public static class BadgeInfo {
        @ApiModelProperty(value = "勋章ID")
        private String badgeBizId;
        
        @ApiModelProperty(value = "勋章主图片")
        private String badgeImage;
        
        @ApiModelProperty(value = "勋章名称")
        private String badgeName;
        
        @ApiModelProperty(value = "勋章获取总数")
        private Integer gainCount;
        
        @ApiModelProperty(value = "是否已获得")
        private Boolean gained;

        @ApiModelProperty(value = "小程序配置列表")
        private List<AppConfig> apps;

    }
    /**
     * 小程序应用配置
     */
    @Data
    public static class AppConfig {
        @ApiModelProperty(value = "小程序ID")
        private String weId;

        @ApiModelProperty(value = "购买跳转链接")
        private String buyJumpUrl;
    }
}