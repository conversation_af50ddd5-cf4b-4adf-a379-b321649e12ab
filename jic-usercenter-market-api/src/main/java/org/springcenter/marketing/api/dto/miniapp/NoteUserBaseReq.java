package org.springcenter.marketing.api.dto.miniapp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
@ApiModel("留言用户基础查询入参")
public class NoteUserBaseReq implements Serializable {
    @ApiModelProperty(value = "品牌卡号", required = true)
    @NotBlank(message = "品牌卡号不能为空")
    private String cardNo;
    @ApiModelProperty(value = "unionId", required = true)
    @NotBlank(message = "unionId不能为空")
    private String unionId;
}

