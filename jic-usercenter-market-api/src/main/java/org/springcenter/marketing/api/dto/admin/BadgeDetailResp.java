package org.springcenter.marketing.api.dto.admin;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springcenter.marketing.api.enums.BadgeActivityStatusEnum;

import java.io.Serializable;
import java.util.List;

/**
 * 勋章详情
 *
 * @Author: CodeGenerator
 * @Date: 2025-03-26 09:51:21
 * @Description:
 */
@Data
public class BadgeDetailResp implements Serializable {
    
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "勋章id")
    private Integer id;
    @ApiModelProperty(value = "业务ID")
    private String bizId;
    @ApiModelProperty(value = "勋章名称")
    private String name;
    @ApiModelProperty(value = "勋章图路径")
    private String imageUrl;
    @ApiModelProperty(value = "勋章简介图路径")
    private String introImageUrl;
    @ApiModelProperty(value = "勋章详情图路径")
    private String detailImageUrl;
    @ApiModelProperty(value = "开始时间")
    private String startTime;
    @ApiModelProperty(value = "结束时间")
    private String endTime;
    @ApiModelProperty(value = "商品包ID，为空代表全部商品")
    private String productPackageId;
    @ApiModelProperty(value = "人群包ID")
    private String crowdPackageId;
    @ApiModelProperty(value = "排序值（越大越靠前）")
    private Integer sort;
    @ApiModelProperty(value = "微商城 是否勾选")
    private Boolean orderTypeWsc;
    @ApiModelProperty(value = "线下订单 是否勾选")
    private Boolean orderTypeXxdd;
    @ApiModelProperty(value = "BOX 是否勾选")
    private Boolean orderTypeBox;
    @ApiModelProperty(value = "是否发消息，默认不发")
    private Boolean sendMsg;
    @ApiModelProperty(value = "创建人")
    private String createPerson;
    @ApiModelProperty(value = "创建时间")
    private String createTime;
    /**
     * @see BadgeActivityStatusEnum
     */
    @ApiModelProperty(value = "勋章状态：1=未开始，2=进行中，3=已结束")
    private Integer activityStatus;
    @ApiModelProperty(value = "勋章状态名称")
    private String activityStatusName;
        /**
     * 小程序配置应用列表
     */
    private List<BadgeDetailUpdateReq.AppConfig> apps;
}
