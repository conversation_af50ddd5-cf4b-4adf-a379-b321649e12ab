package org.springcenter.marketing.api.dto.admin;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class UserAwardListResp {

    /**
     * 主键
     */
    private Integer id;

    /**
     * 用户手机号
     */
    @ApiModelProperty(value = "用户手机号")
    private String phone;

    /**
     * 卡号
     */
    @ApiModelProperty(value = "卡号")
    private String cardno;

    /**
     * 最后打卡时间
     */
    @ApiModelProperty(value = "最后打卡时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastSignInTime;

    /**
     * 创建记录时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 0 未删除  1 删除
     */
    @ApiModelProperty(value = "0 未删除  1 删除")
    private Integer isDel;

    /**
     * 签到天数
     */
    @ApiModelProperty(value = "签到天数")
    private Integer signInDays;

    /**
     * 奖励id
     */
    @ApiModelProperty(value = "奖励id")
    private Integer awardConfigId;

    /**
     * 类型  0  内部券   1  微信券  2  积分  3 内部商品  4 外部商品
     */
    @ApiModelProperty(value = "类型  0  内部券   1  微信券  2  积分  3 内部商品  4 外部商品")
    private Integer awardType;

    /**
     * 奖励名称
     */
    @ApiModelProperty(value = "奖励名称")
    private String awardName;

    /**
     * 奖励编码  券则为券awardId  微信为微信券id  积分为积分数量   内部商品为 productCode   外部商品不定
     */
    @ApiModelProperty(value = "奖励编码  券则为券awardId  微信为微信券id  积分为积分数量   内部商品为 productCode   外部商品不定")
    private String awardCode;

    /**
     * 发放个数
     */
    @ApiModelProperty(value = "发放个数")
    private Integer awardNum;

    /**
     * 0  未发放  1 已发放
     */
    @ApiModelProperty(value = "0  未发放  1 已发放")
    private Integer status;

    /**
     * 存储的发放信息 ，  内部券为券号  微信券id  积分数量  快递号
     */
    @ApiModelProperty(value = "存储的发放信息 ，  内部券为券号  微信券id  积分数量  快递号")
    private String commonInfo;

    @ApiModelProperty(value = "省份")
    private String provinces;

    @ApiModelProperty(value = "城市")
    private String city;

    @ApiModelProperty(value = "区")
    private String districts;

    @ApiModelProperty(value = "详细地址")
    private String address;
}
