package org.springcenter.marketing.api.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;


/**
 * <AUTHOR>
 */
@Data
public class SourceTemplateCreateDto {
    @ApiModelProperty(value = "应用id")
    private String appId;

    @ApiModelProperty(value = "应用名字")
    private String appName;

    @ApiModelProperty(value = "位置管理id")
    private String posManagerId;

    @ApiModelProperty(value = "位置管理名字")
    private String posManagerName;

    @ApiModelProperty(value = "位置")
    private Long position;

    @ApiModelProperty(value = "图片")
    private String image;

    @ApiModelProperty(value = "排序")
    private Long sortNo;

    @ApiModelProperty(value = "标题文案")
    private String titleTxt;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "适用人群包id")
    private String adaptPeopleId;


    @ApiModelProperty(value = "适用人群包name")
    private String adaptPeopleName;


//    @ApiModelProperty(value = "适用门店")
//    private String adaptStoreId;
//
//
//    @ApiModelProperty(value = "适用门店name")
//    private String adaptStoreName;

    @ApiModelProperty(value = "跳转类型")
    private Long jumpType;

    @ApiModelProperty(value = "跳转小程序id")
    private String miniAppId;

    @ApiModelProperty(value = "跳转小程序路径")
    private String miniAppPath;

    @ApiModelProperty(value = "视频id")
    private String videoId;

    @ApiModelProperty(value = "h5链接")
    private String h5Link;

    @ApiModelProperty(value = "显示时间类型 0自定义 1按用户生日")
    private Long showTimeType;

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    @ApiModelProperty(value = "0生日前一个月至生日月结束 1仅生日当月")
    private Long birthdayTime;

    @ApiModelProperty(value = "上下架 0上架 1下架")
    private Long enableStatus;


    @ApiModelProperty(value = "说明文案")
    private String expositoryCase;


    @ApiModelProperty(value = "搜索词")
    private String searchTxt;

}
