package org.springcenter.marketing.api.dto.miniapp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel("草稿箱列表项出参")
public class NoteDraftListItemResp implements Serializable {
    
    @ApiModelProperty("草稿ID")
    private String draftId;
    
    @ApiModelProperty("留言内容(超过90字截断)")
    private String content;
    
    @ApiModelProperty("是否超过90字，超过则显示查看全文标记")
    private Boolean showMore;
    
    @ApiModelProperty("草稿更新时间，格式yyyy.MM.dd HH:mm:ss")
    private String updateTime;
}
