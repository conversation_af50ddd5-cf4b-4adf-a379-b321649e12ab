package org.springcenter.marketing.api.dto.admin;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SendAwardDto {


    @ApiModelProperty(value = "配置的奖励id信息")
    private Integer awardConfigId;

    @ApiModelProperty(value = "是否发放成功 true 成功  false 失败")
    private Boolean isSendSuccess = false;

    @ApiModelProperty(value = "通用返回")
    private String commonInfo;

    @ApiModelProperty(value = "外部单号")
    private String refNo;

    private String sendAwardLogId;

}
