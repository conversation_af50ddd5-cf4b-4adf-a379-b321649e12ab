package org.springcenter.marketing.api.dto.miniapp;

import java.util.List;

import lombok.Data;

/**
 * 未展示过的勋章响应DTO
 */
@Data
public class BadgeUnshownResp {

    /**
     * 勋章业务ID
     */
    private String badgeBizId;
    
    /**
     * 勋章名称
     */
    private String badgeName;
    
    /**
     * 勋章图片
     */
    private String badgeImage;
    
    /**
     * 获奖记录列表
     */
    private List<BadgeGainRecord> gainRecords;
    
    /**
     * 勋章获奖记录
     */
    @Data
    public static class BadgeGainRecord {
        
        /**
         * 获奖记录ID
         */
        private String bizId;
        
        /**
         * 排名
         */
        private Integer rank;
        
        /**
         * SKC编号
         */
        private String skc;
        
        /**
         * 商品名称
         */
        private String productName;
    }
}
