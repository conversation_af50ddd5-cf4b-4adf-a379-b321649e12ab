package org.springcenter.marketing.api.dto.miniapp;

import com.google.common.base.Preconditions;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@ApiModel("留言列表查询入参")
public class NoteListQueryReq implements Serializable {
    @ApiModelProperty(value = "活动ID", required = true)
    @NotBlank(message = "活动ID不能为空")
    private String activityId;
    @ApiModelProperty(value = "排序：1=按留言发布时间倒序、2=按点赞量倒序", required = true)
    @NotNull(message = "排序不能为空")
    private Integer sort;
    @ApiModelProperty(value = "是否精选：0=否, 1=是。默认0")
    private Integer top = 0;
    @ApiModelProperty(value = "是否查询当前用户：0=否, 1=是。默认0（我的页面必传、精选页面需要优先查询本人必传）")
    private Integer currentUser = 0;
    @ApiModelProperty(value = "品牌卡号，查询当前用户必传")
    private String cardNo;

    public void check() {
        if (this.currentUser == 1) {
            Preconditions.checkArgument(StringUtils.isNotBlank(this.cardNo), "查询当前用户的精选时，cardNo不能为空");
        }
    }
}

