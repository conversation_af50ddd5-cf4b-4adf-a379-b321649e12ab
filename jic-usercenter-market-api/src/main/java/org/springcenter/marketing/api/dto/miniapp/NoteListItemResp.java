package org.springcenter.marketing.api.dto.miniapp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel("留言列表项出参")
public class NoteListItemResp implements Serializable {
    @ApiModelProperty("用户昵称")
    private String userName;
    @ApiModelProperty("用户头像")
    private String headImgUrl;
    @ApiModelProperty("留言内容")
    private String content;
    @ApiModelProperty("是否超过90字，超过则显示查看全文")
    private Boolean showMore;
    @ApiModelProperty("留言时间，格式yyyy.MM.dd HH:mm")
    private String publishTime;
    @ApiModelProperty("点赞数")
    private Long likeCount;
    @ApiModelProperty("评论数")
    private Long commentCount;
    @ApiModelProperty("是否置顶")
    private Boolean top;
    @ApiModelProperty("是否当前人")
    private Boolean isCurrentUser;
    @ApiModelProperty("留言审核状态：0=待审核, 1=审核通过, 2=审核失败")
    private Integer auditStatus;
    @ApiModelProperty("审核失败原因")
    private String auditRefuseMsg;
    @ApiModelProperty("留言ID")
    private String noteId;
    @ApiModelProperty("留言品牌卡号")
    private String cardNo;
}

