package org.springcenter.marketing.api.dto.admin;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;
import java.io.Serializable;


/**
 * 勋章详情
 *
 * @Author: CodeGenerator
 * @Date: 2025-03-26 09:51:21
 * @Description:
 */
@Data
public class BadgeDetailUpdateReq implements Serializable {
    
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id, 有则更新，无则新增")
    private Integer id;
    @ApiModelProperty(value = "主题ID")
    private Integer badgeThemeId;
    @ApiModelProperty(value = "主题名称")
    private String badgeThemeName;
    @ApiModelProperty(value = "勋章名称")
    private String name;
    @ApiModelProperty(value = "勋章图路径")
    private String imageUrl;
    @ApiModelProperty(value = "勋章简介图路径")
    private String introImageUrl;
    @ApiModelProperty(value = "勋章详情图路径")
    private String detailImageUrl;
    @ApiModelProperty(value = "开始时间")
    private String startTime;
    @ApiModelProperty(value = "结束时间")
    private String endTime;
    @ApiModelProperty(value = "商品包ID，为空代表全部商品")
    private String productPackageId;
    @ApiModelProperty(value = "人群包ID")
    private String crowdPackageId;
    @ApiModelProperty(value = "排序值（越大越靠前）")
    private Integer sort;
    @ApiModelProperty(value = "微商城 是否勾选")
    private Boolean orderTypeWsc = false;
    @ApiModelProperty(value = "线下订单 是否勾选")
    private Boolean orderTypeXxdd = false;
    @ApiModelProperty(value = "BOX 是否勾选")
    private Boolean orderTypeBox = false;
    @ApiModelProperty(value = "是否发消息，默认不发")
    private Boolean sendMsg = false;
    @ApiModelProperty(value = "创建人")
    private String createPerson;
    @ApiModelProperty(value = "小程序配置列表")
    private List<AppConfig> apps;
    
    /**
     * 小程序应用配置
     */
    @Data
    public static class AppConfig {
        @ApiModelProperty(value = "小程序ID")
        private String weId;

        @ApiModelProperty(value = "购买跳转链接")
        private String buyJumpUrl;
    }
}
