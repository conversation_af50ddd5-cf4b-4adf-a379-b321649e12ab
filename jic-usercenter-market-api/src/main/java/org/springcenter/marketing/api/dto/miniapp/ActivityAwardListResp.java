package org.springcenter.marketing.api.dto.miniapp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ActivityAwardListResp {

    @ApiModelProperty(value = "奖励主标题")
    private String title;

    @ApiModelProperty(value = "副标题")
    private String subTitle;


    private List<AwardData> list;


    @Data
    public static class AwardData{

        @ApiModelProperty(value = "昵称")
        private String nickName;

        @ApiModelProperty(value = "签到天数")
        private String signInDays;

        @ApiModelProperty(value = "奖励主键id")
        private String themeActivityAwardId;
    }

}
