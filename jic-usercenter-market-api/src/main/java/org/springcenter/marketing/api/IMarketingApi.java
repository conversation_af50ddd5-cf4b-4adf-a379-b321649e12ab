package org.springcenter.marketing.api;

import com.jnby.common.CommonRequest;
import com.jnby.common.ResponseResult;
import org.springcenter.marketing.api.context.UseRightsContext;
import org.springcenter.marketing.api.dto.*;
import org.springcenter.marketing.api.entity.CheckRightsEntity;
import org.springcenter.marketing.api.fallback.IMarketingApiFallBackFactory;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@Component
@FeignClient(contextId = "marketingRemoteApi", value = "jic-usercenter-market-api-center", fallbackFactory = IMarketingApiFallBackFactory.class)
public interface IMarketingApi {

    @PostMapping("/marketing/api/create")
    public ResponseResult create(@RequestBody CommonRequest<RightsCreateReq> request);


    @PostMapping("/marketing/api/detail")
    public ResponseResult<RightsDetailResp> detail(@RequestBody CommonRequest<QueryRightDetailReq> request);


    @PostMapping("/marketing/api/list")
    public ResponseResult<List<RightsDetailResp>> list(@RequestBody CommonRequest<RightsCreateReq> request);


    @PostMapping(value = "/marketing/api/rights/getRights")
    public ResponseResult<List<RightsResp>> getRights(@RequestBody  CommonRequest<GetRightsReq> request);

    @PostMapping(value = "/marketing/api/findAll")
    public ResponseResult<List<BRightsDto>> findAll(@RequestBody  CommonRequest<BRightsDto> request);



    @PostMapping(value = "/marketing/api/listForCardLog")
    public ResponseResult<List<BUserMemberCardLogDto>> listForCardLog(@RequestBody CommonRequest<ListForBUserMemberCardLogReq> request);





}
