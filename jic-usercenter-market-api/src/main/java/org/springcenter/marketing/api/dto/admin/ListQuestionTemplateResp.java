package org.springcenter.marketing.api.dto.admin;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class ListQuestionTemplateResp {

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;

    /**
     * 场景id
     */
    @ApiModelProperty(value = "场景id")
    private String quesSceneId;

    /**
     * 页面标题
     */
    @ApiModelProperty(value = "页面标题")
    private String pageTitle;

    /**
     * 备注
     */
    @ApiModelProperty(value = "remark")
    private String remark;

    /**
     * 场景数据 （举例 券类的 优惠券awardId）
     */
    @ApiModelProperty(value = "场景数据 （举例 券类的 优惠券awardId）")
    private String sceneParams;

    /**
     * 形式  0 页面  1 组件
     */
    @ApiModelProperty(value = "形式  0 页面  1 组件")
    private Integer type;

    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    private String title;

    /**
     * 副标题
     */
    @ApiModelProperty(value = "副标题")
    private String subTitle;

    /**
     * 组件页面  0 生日权益领取页  1 积分兑换成功页
     */
    @ApiModelProperty(value = "组件页面  0 生日权益领取页  1 积分兑换成功页")
    private Integer assemblyPage;

    /**
     * 配置信息  频率设置和奖励设置  json
     */
    @ApiModelProperty(value = "配置信息  频率设置和奖励设置")
    private String assemblyJson;

    /**
     * 0 不自动  1自动
     */
    @ApiModelProperty(value = "0 不自动  1自动")
    private Integer autoEject;

    /**
     * 0 禁用  1启用
     */
    @ApiModelProperty(value = " 0 禁用  1启用")
    private Integer status;

    /**
     * 跳转类型 0 小程序 1 h5
     */
    @ApiModelProperty(value = "跳转类型 0 小程序 1 h5")
    private Integer jumpType;

    /**
     * appid   跳转小程序必填
     */
    @ApiModelProperty(value = "appid   跳转小程序必填")
    private String appId;

    /**
     * 小程序路径  跳转小程序必填
     */
    @ApiModelProperty(value = "小程序路径  跳转小程序必填")
    private String pagePath;

    /**
     * 跳转h5路径  跳转h5时候必填
     */
    @ApiModelProperty(value = "跳转h5路径  跳转h5时候必填")
    private String h5Url;

    /**
     * 结束按钮文案
     */
    @ApiModelProperty(value = "结束按钮文案")
    private String endButton;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 0 未删除  1 已删除
     */
    @ApiModelProperty(value = " 0 未删除  1 已删除")
    private Integer isDel;

    /**
     * 背景图或者 入口图
     */
    @ApiModelProperty(value = " 背景图或者 入口图")
    private String imgUrl;

    @ApiModelProperty(value = "适用小程序等   小程序为 weid           pos+为  1     搭配师后台为  2 ")
    private List<String> suitParams;

    @ApiModelProperty(value = "问题和答案")
    private List<QuestionAndAnswer> questionAndAnswers;

    @ApiModelProperty(value = "场景信息")
    private ListSceneResp sceneResp;

    @ApiModelProperty(value = " 创建人")
    private String createBy;

    @ApiModelProperty(value = " 0  awradId  1 微信券id")
    private Integer sceneParamsType;

    @ApiModelProperty(value = "结束语文案")
    private String overContent;


    @Data
    public static class QuestionAndAnswer{

        @ApiModelProperty(value = "问题主键id")
        private String id;

        /**
         * 问卷模版id
         */
        @ApiModelProperty(value = "问卷模版id")
        private String questionTemplateId;


        @ApiModelProperty(value = "问题信息")
        private String question;

        /**
         * 排序  从大到小   越大排的越靠前
         */
        @ApiModelProperty(value = "排序  从大到小   越大排的越靠前")
        private Integer orderSort;

        /**
         * 0 非必填   1 必填
         */
        @ApiModelProperty(value = "0 非必填   1 必填")
        private Integer required;

        /**
         * 关联的答案ids， 仅二级问题才需要 （需要的为一级问题的答案id） 一级问题为null
         */
        @ApiModelProperty(value = "关联的答案ids ")
        private String answerId;

        /**
         * 0 单选 1多选  2 打星  3 输入框
         */
        @ApiModelProperty(value = "0 单选 1多选  2 打星  3 输入框")
        private Integer type;

        /**
         * 0 未删除  1 已删除
         */
        @ApiModelProperty(value = "0 未删除  1 已删除")
        private Integer isDel;

        /**
         * 创建时间
         */
        @ApiModelProperty(value = "创建时间")
        private Date createTime;

        /**
         * 更新时间
         */
        @ApiModelProperty(value = "更新时间")
        private Date updateTime;

        /**
         * 父id  如果为null  则为一级问题
         */
        @ApiModelProperty(value = "父id  如果为null  则为一级问题")
        private String parentsId;

        @ApiModelProperty(value = "答案")
        private List<Answer> answers;

        @ApiModelProperty(value = "问题图片url")
        private String imgUrl;

        @ApiModelProperty(value = "限制填写文字输入框文字数量")
        private String limitContentNumber;

        @ApiModelProperty(value = "子问题")
        private List<QuestionAndAnswer> childQuestions;
    }


    @Data
    public static class Answer{

        /**
         * 主键
         */
        @ApiModelProperty(value = "主键id")
        private String id;

        /**
         * 问题id
         */
        @ApiModelProperty(value = "问题id")
        private String questionId;

        /**
         * 答案 中文
         */
        @ApiModelProperty(value = "答案 中文")
        private String answer;

        /**
         * 星级
         */
        @ApiModelProperty(value = "星级")
        private String star;

        /**
         * 0 未删除  1 已删除
         */
        @ApiModelProperty(value = "0 未删除  1 已删除")
        private Integer isDel;

        /**
         * 创建时间
         */
        @ApiModelProperty(value = "创建时间")
        private Date createTime;

        /**
         * 更新时间
         */
        @ApiModelProperty(value = "更新时间")
        private Date updateTime;

        /**
         * 按照前台的顺序进行排序 传递过来的顺序
         */
        @ApiModelProperty(value = "按照前台的顺序进行排序 传递过来的顺序")
        private Integer orderSort;

        @ApiModelProperty(value = "答案图片url")
        private String imgUrl;

        @ApiModelProperty(value = "是否可以跳转  跳转到第几个问题    ( 默认 -1  不跳转    0  为 整个问卷结束作答  直接提交   其他 则为 问题 id )")
        private String jumpOverOrQuestionId;
    }
}
