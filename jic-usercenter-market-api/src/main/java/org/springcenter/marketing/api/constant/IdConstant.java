package org.springcenter.marketing.api.constant;

import lombok.Data;

@Data
public class IdConstant {


    public static final String RIGHTS = "B_RIGHTS";

    public static final String REDIS_CACHE_LOG = "REDIS_CACHE_LOG";

    public static final String WASH_APPOINTMENT = "WASH_APPOINTMENT";


    public static final String BIRTH_COUPON_SEND_LOG = "BIRTH_COUPON_SEND_LOG";


    public static final String WASH_UPDATE_TIME_LOG = "WASH_UPDATE_TIME_LOG";

    public static final String B_USER_RIGHTS_MATERIAL = "B_USER_RIGHTS_MATERIAL";

    public static final String B_NEW_ADD_USER_RIGHT_LOG = "B_NEW_ADD_USER_RIGHT_LOG";

    public static final String B_NEW_USER_RIGHTS = "B_NEW_USER_RIGHTS";

    public static final String B_NEW_USER_MEMBER_CARD = "B_NEW_USER_MEMBER_CARD";

    public static final String CUSTOMIZE = "B_RIGHTS_CUSTOMIZE_STORE";


    public static final String RIGHTS_MEMBER_CARD_RELATION = "RIGHTS_MEMBER_CARD_RELATION";

    public static final String RUCRL = "R_USER_CLICK_RIGHTS_LOG";

    public static final String RUSEREXPAND = "R_USER_EXPAND";

    public static final String B_USER_APPLY_FOR_BOX_LOG = "B_USER_APPLY_FOR_BOX_LOG";


    public static final String B_NEW_USER_RIGHTS_LOG = "B_NEW_USER_RIGHTS_LOG";
    /**
     * 后台开卡
     */
    public static final String HTKK = "HTKK";
    public static final String B_USER_MEMBER_CARD_LOG = "B_USER_MEMBER_CARD_LOG";
    public static final String B_USER_MEMBER_CARD_RENEW_LOG = "B_USER_MEMBER_CARD_RENEW_LOG";


    public static final String CALL_TEMPALTE = "CALL_TEMPALTE";
    public static final String CALL_TASK = "CALL_TASK";
    public static final String CALL_TASK_DETAIL = "CALL_TASK_DETAIL";


    /**
     * 订阅3.0优惠券权益
     */
    public static final String B_NEW_USER_RIGHTS_COUPON = "B_NEW_USER_RIGHTS_COUPON";
    public static final String B_NEW_USER_BOX_GIFT = "B_NEW_USER_BOX_GIFT";


    public static final String B_GOODS = "B_GOODS";
    public static final String B_GOODS_SKU = "B_GOODS_SKU";
    public static final String B_MEMBER_CARD = "B_MEMBER_CARD";
    public static final String B_MEMBER_CARD_TYPE = "B_MEMBER_CARD_TYPE";



    public static final String AI_CREATE_IMG = "AI_CREATE_IMG";
    public static final String AI_CREATE_IMG_DETAILS = "AI_CREATE_IMG_DETAILS";




    // 优惠券发放唯一编号
    public static final String SEND_COUPON_PRIMARY = "SEND_COUPON_PRIMARY";

    public static final String SEND_MATERIAL_COUPON_LOG = "SEND_MATERIAL_COUPON_LOG";




    public static final String QUES_ANSWER = "QUES_ANSWER";
    public static final String QUES_QUESTION = "QUES_QUESTION";
    public static final String QUES_QUESTION_TEMPLATE = "QUES_QUESTION_TEMPLATE";
    public static final String QUES_QUESTION_TEMPLATE_DETAIL = "QUES_QUESTION_TEMPLATE_DETAIL";
    public static final String QUES_SCENE = "QUES_SCENE";
    public static final String QUES_USER_ANSWER = "QUES_USER_ANSWER";


    // 活动订单
    public static final String AC_ACTIVITY_ORDER = "AC_ACTIVITY_ORDER";
    public static final String AC_REPURCHASE_PLAN_DETAIL = "AC_REPURCHASE_PLAN_DETAIL";
    public static final String AC_ACTIVITY_CONFIG = "AC_ACTIVITY_CONFIG";
    public static final String AC_ACTIVITY_CONFIG_DETAILS = "AC_ACTIVITY_CONFIG_DETAILS";
    public static final String AC_ACTIVITY_CONF_DETAIL_GOODS = "AC_ACTIVITY_CONF_DETAIL_GOODS";
    public static final String AC_ASK_SUPPORT_GOODS = "AC_ASK_SUPPORT_GOODS";
    public static final String ACTIVITY_INFO = "ACTIVITY_INFO";
    public static final String CART_SEND_WXOPEN_LOG = "CART_SEND_WXOPEN_LOG";


    public static final String SCAN_CODE_LOG = "SCAN_CODE_LOG";



    public static final String SEND_AWARD_LOG = "SEND_AWARD_LOG";
    public static final String THEME_ACTIVITY_USER_AWARD = "THEME_ACTIVITY_USER_AWARD:";


}
