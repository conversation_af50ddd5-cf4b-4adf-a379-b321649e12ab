package org.springcenter.marketing.api.fallback;

import com.jnby.common.CommonRequest;
import com.jnby.common.ResponseResult;
import org.springcenter.marketing.api.IRightsV3Api;
import org.springcenter.marketing.api.checkRightsDto.CheckRightCouponV3DtoForPre;
import org.springcenter.marketing.api.context.UseRightsContext;
import org.springcenter.marketing.api.dto.*;
import org.springcenter.marketing.api.entity.CheckRightsEntity;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

@Slf4j
public class IRightsV3ApiFallBackFactory implements IRightsV3Api {


    @Setter
    private Throwable cause;

    @Override
    public ResponseResult openCard(CommonRequest<OpenCardAndRightsReq> request) {
        return ResponseResult.success();
    }

    @Override
    public ResponseResult<Boolean> check(CommonRequest<CheckRightsEntity> request) {
        return ResponseResult.success(false);
    }

    @Override
    public ResponseResult<List<PreCalcResp>> preCalcExpansionCoupon(CommonRequest<List<CheckRightCouponV3DtoForPre>> request) {
        return ResponseResult.success(new ArrayList<>());
    }


    @Override
    public ResponseResult<List<BNewUserMemberCardResp>> getUserAvailableCardApplicationParty(CommonRequest<UserAvailableCardApplicationPartyReq> request) {
        return ResponseResult.success(new ArrayList<>());
    }

    @Override
    public ResponseResult<List<BNewUserMemberCardResp>> getUserAllCardApplicationParty(CommonRequest<UserAvailableCardApplicationPartyReq> request) {
        return ResponseResult.success(new ArrayList<>());
    }


    @Override
    public ResponseResult<Boolean> recover(CommonRequest<UseRightsContext> request) {
        return ResponseResult.success(false);
    }

    @Override
    public ResponseResult<Integer> reflectAppIdToApplicationPart(CommonRequest<String> request) {
        return ResponseResult.success(0);
    }

    @Override
    public ResponseResult loseCard(CommonRequest<OpenCardAndRightsReq> request) {
        return ResponseResult.success();
    }

    @Override
    public ResponseResult loseCardByOutNo(CommonRequest<LoseCardByOutNoReq> request) {
        return ResponseResult.success();
    }

    @Override
    public ResponseResult dealTryAfterBuyIsHaveUnFinshBox(CommonRequest<DealTryAfterBuyIsHaveUnFinishBoxReq> request) {
        return ResponseResult.success();
    }

    @Override
    public ResponseResult<Boolean> isHaveAndNoOrderUser(CommonRequest<HaveUnFinishBoxReq> request) {
        return ResponseResult.success(false);
    }

    @Override
    public ResponseResult<List<SubMemberCardRightsResp>> getSubMemberCardRights(CommonRequest<SubMemberCardRightsReq> request) {
        return ResponseResult.success(new ArrayList<>());
    }

    @Override
    public ResponseResult<List<BNewUserBoxGiftResp>> getUserBoxGiftRights(CommonRequest<List<UserBoxGiftRightsReq>> request) {
        return ResponseResult.success(new ArrayList<>());
    }

    @Override
    public ResponseResult<List<BNewUserBoxGiftResp>> getUserBoxGiftRightsByConsumeOutNo(CommonRequest<UserRightsByConsumeOutNoReq> request) {
        return ResponseResult.success(new ArrayList<>());
    }

    @Override
    public ResponseResult<List<BNewUserRightsCouponResp>> getUserSubCouponRightsByConsumeOutNo(CommonRequest<UserRightsByConsumeOutNoReq> request) {
        return ResponseResult.success(new ArrayList<>());
    }

    @Override
    public ResponseResult<List<BNewUserRightsResp>> getUserRightsByOutNo(CommonRequest<GetUserRightsByOutNoReq> request) {
        return ResponseResult.success(new ArrayList<>());
    }

    @Override
    public ResponseResult<BNewUserMemberCardResp> getUserCardBySubId(CommonRequest<GetUserCardBySubId> request) {
        return ResponseResult.success();
    }

    @Override
    public ResponseResult<List<UserSubV3CouponBySubIdResp>> getUserSubV3CouponBySubId(CommonRequest<GetUserCardBySubId> request) {
        return ResponseResult.success();
    }

    @Override
    public ResponseResult<Boolean> batchUseUserRights(CommonRequest<List<BatchUseUserRightsReq>> request) {
        return ResponseResult.success(true);
    }

    @Override
    public ResponseResult<List<BNewUserBoxGiftResp>> getBoxGiftByBoxSns(CommonRequest<GetBoxGiftByBoxSn> request) {
        return ResponseResult.success(new ArrayList<>());
    }


}
