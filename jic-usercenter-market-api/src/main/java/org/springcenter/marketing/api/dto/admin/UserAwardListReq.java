package org.springcenter.marketing.api.dto.admin;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class UserAwardListReq {

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "卡号")
    private String cardno;

    @ApiModelProperty(value = "奖品类型")
    private Integer awardType;

    @ApiModelProperty(value = "奖品名称")
    private String awardName;

    @ApiModelProperty(value = "活动主键id")
    private Integer themeActivityId;

}
