package org.springcenter.marketing.api.dto.miniapp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 勋章详情及获取记录响应DTO
 */
@Data
@ApiModel("勋章详情及获取记录响应")
public class BadgeDetailWithGainsResp {

    @ApiModelProperty(value = "勋章业务ID")
    private String badgeBizId;

    @ApiModelProperty(value = "勋章名称")
    private String badgeName;

    @ApiModelProperty(value = "勋章图片")
    private String badgeImage;
    @ApiModelProperty(value = "勋章简介图路径")
    private String introImageUrl;
    @ApiModelProperty(value = "勋章详情图路径")
    private String detailImageUrl;

    @ApiModelProperty(value = "获得记录列表")
    private List<GainRecord> gainRecords;

    @ApiModelProperty(value = "小程序配置列表")
    private List<AppConfig> apps;

    /**
     * 小程序应用配置
     */
    @Data
    public static class AppConfig {
        @ApiModelProperty(value = "小程序ID")
        private String weId;

        @ApiModelProperty(value = "购买跳转链接")
        private String buyJumpUrl;
    }

    /**
     * 勋章获得记录
     */
    @Data
    public static class GainRecord {

        @ApiModelProperty(value = "记录ID")
        private String bizId;

        @ApiModelProperty(value = "排名")
        private Integer rank;

        @ApiModelProperty(value = "SKC编号")
        private String skc;

        @ApiModelProperty(value = "商品名称")
        private String productName;

        @ApiModelProperty(value = "获得时间")
        private String createTime;

        @ApiModelProperty(value = "是否已展示")
        private Boolean shown;
    }
}
