package org.springcenter.marketing.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class PointsExchangeCouponReq {


    @ApiModelProperty(value = "unionid")
    @NotBlank(message = "unionid不能为空!")
    private String unionid;

    @ApiModelProperty(value = "卡类型主键id   举例 集团卡：  107 108  106银卡  107 金卡 108 白金卡")
    @NotNull(message = "卡类型主键id不能为空!")
    private Integer cardTypeId;

    @ApiModelProperty(value = "卡品牌ID  集团卡  集团卡品牌 固定传递  JT001,JT002,JT003,JT004,JT005  可以不传")
    private String brandId = "JT001,JT002,JT003,JT004,JT005";

    @ApiModelProperty(value = "品牌卡weid")
    private String weid;

    @ApiModelProperty(value = "miniAppOpenId")
    @NotBlank(message = "miniAppOpenId不能为空!")
    private String miniAppOpenId;

    @ApiModelProperty(value = "openId")
    private String openId;

    @ApiModelProperty(value = "集团卡id")
    @NotNull(message = "集团卡id不能为空")
    private Long cClientVipId;

    @ApiModelProperty(value = "发放的凭证全ID")
    private String awardId;

    @ApiModelProperty(value = "凭证券券号")
    private String voucherNo;

}
