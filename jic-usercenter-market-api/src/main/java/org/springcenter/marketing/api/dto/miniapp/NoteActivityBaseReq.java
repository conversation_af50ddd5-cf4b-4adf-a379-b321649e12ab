package org.springcenter.marketing.api.dto.miniapp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("留言活动基础查询入参")
public class NoteActivityBaseReq extends NoteUserBaseReq implements Serializable {
    @ApiModelProperty(value = "活动ID", required = true)
    @NotBlank(message = "活动ID不能为空")
    private String activityId;
}

