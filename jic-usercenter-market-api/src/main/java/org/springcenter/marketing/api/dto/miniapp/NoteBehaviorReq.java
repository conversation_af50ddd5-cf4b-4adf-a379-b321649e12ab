package org.springcenter.marketing.api.dto.miniapp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("行为入参")
public class NoteBehaviorReq extends NoteUserBaseReq implements Serializable {
    @ApiModelProperty(value = "留言ID", required = true)
    @NotBlank(message = "留言ID不能为空")
    private String noteId;
    @ApiModelProperty(value = "类型：1=评论、2=点赞、3=转发", required = true)
    @NotBlank(message = "行为类型不能为空")
    private String type;
    @ApiModelProperty(value = "操作：1=新增、2=取消", required = true)
    @NotNull(message = "操作不能为空")
    private Integer opt;
}

