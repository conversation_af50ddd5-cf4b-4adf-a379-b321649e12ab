package org.springcenter.marketing.api.fallback;

import com.jnby.common.CommonRequest;
import com.jnby.common.ResponseResult;
import org.springcenter.marketing.api.IMarketingApi;
import org.springcenter.marketing.api.context.UseRightsContext;
import org.springcenter.marketing.api.dto.*;
import org.springcenter.marketing.api.entity.CheckRightsEntity;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

@Slf4j
public class IMarketingApiFallBackFactory implements IMarketingApi {


    @Setter
    private Throwable cause;

    @Override
    public ResponseResult create(CommonRequest<RightsCreateReq> request) {
        return ResponseResult.error(-1,"创建失败");
    }

    @Override
    public ResponseResult<RightsDetailResp> detail(CommonRequest<QueryRightDetailReq> request) {
        return ResponseResult.success(new RightsDetailResp());
    }

    @Override
    public ResponseResult<List<RightsDetailResp>> list(CommonRequest<RightsCreateReq> request) {
        return ResponseResult.success(new ArrayList<>());
    }

    @Override
    public ResponseResult<List<RightsResp>> getRights(CommonRequest<GetRightsReq> request) {
        return ResponseResult.success(new ArrayList<>());
    }

    @Override
    public ResponseResult<List<BRightsDto>> findAll(CommonRequest<BRightsDto> request) {
        return ResponseResult.success(new ArrayList<>());
    }

    @Override
    public ResponseResult<List<BUserMemberCardLogDto>> listForCardLog(CommonRequest<ListForBUserMemberCardLogReq> request) {
        return ResponseResult.success(new ArrayList<>());
    }


}
