package org.springcenter.marketing.api.dto.admin;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class ExportExcelUserAwardDto {

//序号   unionid  手机号  cardno  打卡天数  最后打卡时间  奖品名称   省市区  详细地址  物流单号  发放情况

    @ExcelProperty("序号")
    private String id;
    @ExcelProperty("unionid")
    private String unionid;
    @ExcelProperty("手机号")
    private String phone;
    @ExcelProperty("cardno")
    private String cardno;
    @ExcelProperty("打卡天数")
    private String signInDays;
    @ExcelProperty("最后打卡时间")
    private String lastSignInTime;
    @ExcelProperty("奖品名称")
    private String awardName;
    @ExcelProperty("省")
    private String provinces;
    @ExcelProperty("市")
    private String city;
    @ExcelProperty("区")
    private String districts;
    @ExcelProperty("详细地址")
    private String address;
    @ExcelProperty("物流单号")
    private String commonInfo;
    @ExcelProperty("发放情况")
    private String status;
    @ExcelProperty("错误信息 (仅在导入excel时候可能会返回)")
    private String msg;
}
