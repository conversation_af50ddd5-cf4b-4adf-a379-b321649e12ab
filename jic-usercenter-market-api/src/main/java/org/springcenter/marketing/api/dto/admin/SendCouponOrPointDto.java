package org.springcenter.marketing.api.dto.admin;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SendCouponOrPointDto {

    @ApiModelProperty(value = "用户奖励主键id")
    private Integer id;

    @ApiModelProperty(value = "活动主键id")
    private Integer themeActivityId;

    @ApiModelProperty(value = "0  券类   (包含内部和微信)   1 积分 ")
    private Integer sendAwardType;

}
