package org.springcenter.marketing.api.dto.miniapp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("获取草稿箱详情入参")
public class NoteDraftDetailReq extends NoteUserBaseReq implements Serializable {
    
    @ApiModelProperty(value = "草稿ID", required = true)
    @NotNull(message = "草稿ID不能为空")
    private Long draftId;
}
