package org.springcenter.marketing.api.dto;

import org.springcenter.marketing.api.dto.CStoreDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class GetRightsReq {
    //	bmc.APPLICABLE_PARTY = 2
    //	AND bmc.status = 1
    //	AND br.status = 1
    //	AND rmcr.IS_DEL = 0
    //	AND br.IS_DEL = 0
    //	AND bmc.IS_DEL = 0
    //	AND br.rights_type = 5
    //	AND bmc.card_level = 152

    @ApiModelProperty(value = "卡级别ID  ")
    private Integer cardLevel;

    @ApiModelProperty(value = "品牌id  ")
    private String brandId;


    @ApiModelProperty(value = "品牌id s ")
    private List<String> brandIds;

    @ApiModelProperty(value = "权益类型  ")
    private Integer rightsType;

    @ApiModelProperty(value = "门店ID")
    private Long storeId;

    @ApiModelProperty(value = "customerId")
    private String customerId;

    @ApiModelProperty(value = " 默认  1 保持原来的    2 则必须传递storeId和customerId")
    private Integer queryType = 1;

    @ApiModelProperty(value = "box自己封装参数  请求参数不用传递")
    private CStoreDto cStoreDto;

    @ApiModelProperty(value = "用户unionid")
    private String unionid;

}
