package org.springcenter.marketing.api.dto.miniapp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("新增/编辑留言草稿箱入参")
public class NoteDraftSaveReq extends NoteActivityBaseReq implements Serializable {
    
    @ApiModelProperty(value = "草稿ID，新增时不传，编辑时必传")
    private String draftId;
    
    @ApiModelProperty(value = "留言内容", required = true)
    @NotBlank(message = "留言内容不能为空")
    @Size(max = 10000, message = "留言内容不能超过10000字")
    private String content;

}
