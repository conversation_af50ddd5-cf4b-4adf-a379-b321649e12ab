apiVersion: apps/v1
kind: Deployment
metadata:
  name: jic-usercenter-market-api-center
  labels:
    app: jic-usercenter-market-api-center
spec:
  replicas: 1
  template:
    metadata:
      name: jic-usercenter-market-api-center
      labels:
        app: jic-usercenter-market-api-center
    spec:
      containers:
        - name: jic-usercenter-market-api-center
          image: "harbor.jnby.com/jic-usercenter-market/jic-usercenter-market-api-center:latest"
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 9310
              name: jic-uc-market
              protocol: TCP
          lifecycle:
            preStop:
              exec:
                command:
                  - sh
                  - c
                  - "sleep 5"
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 60
            periodSeconds: 20
            successThreshold: 1
            tcpSocket:
              port: 9310
            timeoutSeconds: 1
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 60
            periodSeconds: 20
            successThreshold: 1
            tcpSocket:
              port: 9310
            timeoutSeconds: 1
      restartPolicy: Always
      imagePullSecrets:
        - name: 152harbor

  selector:
    matchLabels:
      app: jic-usercenter-market-api-center
