FROM jnbyharbor.jnby.com/base-images/baseimagejdk8:v1.3

MAINTAINER box-group

RUN mkdir -p /jic-usercenter-market/jic-usercenter-market-back-center

WORKDIR /jic-usercenter-market/jic-usercenter-market-back-center

EXPOSE 9389

EXPOSE 9999

COPY target/jic-usercenter-market-back-center.jar jic-usercenter-market-back-center.jar

##COPY --from=hengyunabc/arthas:latest /opt/arthas /opt/arthas

ENV TZ='Asia/Shanghai'

ENTRYPOINT ["java", "-Xmx4g", "-Xms4g","-XX:NewRatio=3","-Xss512k", "-Xmn2g","-XX:SurvivorRatio=2", "-XX:+UseParallelGC","-Dreactor.netty.pool.leasingStrategy=lifo", "-jar", "jic-usercenter-market-back-center.jar"]

CMD ["--spring.profiles.active=prod"]
