package org.springcenter.backcenter.api.admin;

import com.jnby.common.CommonRequest;
import com.jnby.common.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springcenter.backcenter.modules.service.IBadgeService;
import org.springcenter.backcenter.modules.service.ICombinelookService;
import org.springcenter.backcenter.modules.service.ILookBrandImgService;
import org.springcenter.backcenter.modules.service.ITaskService;
import org.springcenter.backcenter.modules.util.DateUtils;
import org.springcenter.marketing.api.dto.admin.SendWxOpenMsgByActivityIdDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date:2025/1/3 17:22
 */
@Slf4j
@RestController
@RequestMapping("/admin/taskTest")
@Api(value = "taskTest", tags = "任务测试接口")
public class TaskTestController {

    @Autowired
    private ITaskService taskService;
    @Resource
    private IBadgeService badgeService;

    @Autowired
    private ICombinelookService combinelookService;

    @Autowired
    private ILookBrandImgService lookBrandImgService;

    @ResponseBody
    @PostMapping("/switchCostAlreadyHaveStatus")
    @ApiOperation(value = "将任务中的已领取的任务状态进行反转")
    public ResponseResult switchCostAlreadyHaveStatus(@RequestBody CommonRequest<String> commonRequest){
        taskService.switchCostAlreadyHaveStatus(commonRequest.getRequestData());
        return ResponseResult.success();
    }

    @ResponseBody
    @PostMapping("/fillProcessTime")
    @ApiOperation(value = "将任务进行的任务进行时间填充")
    public ResponseResult fillProcessTime(){
        taskService.fillProcessTime();
        return ResponseResult.success();
    }

    @ResponseBody
    @PostMapping("/switchCostProcessStatus")
    @ApiOperation(value = "统计中 -> 待领取")
    public ResponseResult switchCostProcessStatus(){
        taskService.switchCostProcessStatus();
        return ResponseResult.success();
    }

    @ResponseBody
    @PostMapping("/switchCostNotDrawStatus")
    @ApiOperation(value = "待领取 -> 已结束")
    public ResponseResult switchCostNotDrawStatus(){
        taskService.switchCostNotDrawStatus();
        return ResponseResult.success();
    }


    @ResponseBody
    @PostMapping("/switchWaitingChangeStatus")
    @ApiOperation(value = "未领取 -> 已结束")
    public ResponseResult switchWaitingChangeStatus(){
        taskService.switchWaitingChangeStatus();
        return ResponseResult.success();
    }

    @ApiOperation(value = "测试勋章任务")
    @GetMapping("/testJob")
    public ResponseResult<Boolean> testJob(String fromDate, String toDate) {
        log.info("测试勋章任务 fromDate:{}, toDate:{}", fromDate, toDate);
        badgeService.scanOrder(DateUtils.parseByDateTimePattern(fromDate), DateUtils.parseByDateTimePattern(toDate));
        log.info("测试勋章任务 结束");
        return ResponseResult.success(true);
    }

    @ResponseBody
    @PostMapping("/syncLookPackage/{packageId}")
    @ApiOperation(value = "同步look包")
    public ResponseResult syncLookPackage(@PathVariable("packageId") String packageId){
        combinelookService.pullCombineLook(packageId, packageId);
        return ResponseResult.success();
    }

    /**
     * 通知数栈任务已经完成事件
     */
    @ResponseBody
    @GetMapping("/sendBatchWorkNotify")
    @ApiOperation(value = "通知数栈任务已经完成事件")
    public ResponseResult sendBatchWorkNotify(){
        log.info("刷新Look快赏缓存");
        combinelookService.refreshCache();
        return ResponseResult.success();
    }

    public static void main(String[] args) {
        String url = "https://testbzhz.jnbygroup.com:9999/webapp/ehr/ability-dict";
        //匹配http开头
        System.out.println(url.matches("^http.*"));
        System.out.println(url.matches("^/[^/].*"));
    }

    /**
     * 批量刷默认Look到缓存
     * @param
     */
    @ResponseBody
    @GetMapping("/batchLookCache")
    @ApiOperation(value = "批量刷默认Look到缓存")
    public ResponseResult batchLookCache(){
        log.info("批量刷默认Look到缓存");
        lookBrandImgService.cacheAllDataGroupByBrand();
        return ResponseResult.success();
    }
}
