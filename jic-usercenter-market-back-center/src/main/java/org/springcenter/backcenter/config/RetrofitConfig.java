package org.springcenter.backcenter.config;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import okhttp3.ConnectionPool;
import okhttp3.OkHttpClient;
import okhttp3.logging.HttpLoggingInterceptor;
import org.springcenter.backcenter.config.typeAdapter.DoubleDefaultAdapter;
import org.springcenter.backcenter.config.typeAdapter.IntegerDefaultAdapter;
import org.springcenter.backcenter.config.typeAdapter.LongDefaultAdapter;
import org.springcenter.backcenter.modules.webapi.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Configuration
public class RetrofitConfig {


    @Value("${jic.express.url}")
    private String jicExpressUrl;


    @Value("${third.url}")
    private String thirdUrl;



    @Value("${wy.aicall.service}")
    private String wyAicallUrl;


    @Value("${qiyu.call.service}")
    private String qiyuCallService;

    @Value("${bigdata.call.url}")
    private String bigDataCallUrl;

    /**
     * 增加请求返回""和"null"的处理
     * 1.Integer=>null
     * 2.Double=>null
     * 3.Long=>null
     */
    private static Gson gson = new GsonBuilder()
            .registerTypeAdapter(Integer.class, new IntegerDefaultAdapter())
            .registerTypeAdapter(Double.class, new DoubleDefaultAdapter())
            .registerTypeAdapter(Long.class, new LongDefaultAdapter()).create();


    /**
     * 定义Retrofit Map集合
     */
    private static Map<String, Retrofit> retrofitMap = new ConcurrentHashMap<>();


    /**
     * 获取Bean
     *
     * @param url
     * @return
     */
    public Retrofit getRetrofitBean(String url) {
        if (retrofitMap.containsKey(url)) {
            return retrofitMap.get(url);
        }

        //声明日志类
        HttpLoggingInterceptor httpLoggingInterceptor = new HttpLoggingInterceptor();
        //设定日志级别
        httpLoggingInterceptor.setLevel(HttpLoggingInterceptor.Level.BODY);

        int ioWorkerCount = 200;


        Retrofit retrofit = new Retrofit.Builder()
                .baseUrl(url)
                .client(
                        new OkHttpClient.Builder()
                                // 设置完整调用的默认超时。
                                .connectTimeout(5, TimeUnit.SECONDS)
                                .readTimeout(5, TimeUnit.SECONDS)
                                .writeTimeout(5, TimeUnit.SECONDS)
                                // 不重试
                                .retryOnConnectionFailure(false)
                                .connectionPool(new ConnectionPool(ioWorkerCount, 4, TimeUnit.MINUTES))
                                .addInterceptor(httpLoggingInterceptor)
                                .build())
                .addConverterFactory(GsonConverterFactory.create(gson))
                .build();
        retrofitMap.put(url, retrofit);
        return retrofit;
    }




    /**
     * 获取Bean(无cache)
     * @param url
     * @param callTimeOut 设置完整调用的默认超时
     * @return
     */
    public Retrofit getNoCacheRetrofitBean(String url, Long callTimeOut) {
        //声明日志类
        HttpLoggingInterceptor httpLoggingInterceptor = new HttpLoggingInterceptor();
        //设定日志级别
        httpLoggingInterceptor.setLevel(HttpLoggingInterceptor.Level.BODY);

        Retrofit retrofit = new Retrofit.Builder()
                .baseUrl(url)
                .client(
                        new OkHttpClient.Builder()
                                // 设置完整调用的默认超时。
                                .callTimeout(callTimeOut, TimeUnit.SECONDS)
                                // 不重试
                                .retryOnConnectionFailure(false)
                                .addInterceptor(httpLoggingInterceptor)
                                .build())
                .addConverterFactory(GsonConverterFactory.create(gson))
                .build();
        return retrofit;
    }


    @Bean
    public IMessageHttpApi createIMessageHttpApi() {
        return getApi(jicExpressUrl, IMessageHttpApi.class);
    }

    @Bean
    public MessageOpenSendWxHttpApi createMessageOpenSendWxHttpApi() {
        return getApi(thirdUrl, MessageOpenSendWxHttpApi.class);
    }


    /**
     * 获取Bean(无cache)
     * @param url
     * @param connectTimeout 连接时长
     * @param readTimeout 读取时长
     * @param writeTimeout 写入时长
     * @return
     */
    public Retrofit getNoCacheRetrofitBean(String url, Long connectTimeout, Long readTimeout, Long writeTimeout) {
        //声明日志类
        HttpLoggingInterceptor httpLoggingInterceptor = new HttpLoggingInterceptor();
        //设定日志级别
        httpLoggingInterceptor.setLevel(HttpLoggingInterceptor.Level.BODY);

        Retrofit retrofit = new Retrofit.Builder()
                .baseUrl(url)
                .client(
                        new OkHttpClient.Builder()
                                .connectTimeout(connectTimeout, TimeUnit.SECONDS)
                                .readTimeout(readTimeout, TimeUnit.SECONDS)
                                .writeTimeout(writeTimeout, TimeUnit.SECONDS)
                                // 不重试
                                .retryOnConnectionFailure(false)
                                .addInterceptor(httpLoggingInterceptor)
                                .build())
                .addConverterFactory(GsonConverterFactory.create(gson))
                .build();
        return retrofit;
    }


    /**
     * 获取API
     *
     * @param url     地址
     * @param service
     * @param <T>
     * @return
     */
    public <T> T getApi(String url, Class<T> service) {
        return getRetrofitBean(url).create(service);
    }

    @Bean
    public IWyAiCallingHttpApi createWyAiCallingHttpApi() {
        return getApi(wyAicallUrl, IWyAiCallingHttpApi.class);
    }


    @Bean
    public IQiyuPeopleCallingHttpApi createQiyuPeopleCallingHttpApi() {
        return getApi(qiyuCallService, IQiyuPeopleCallingHttpApi.class);
    }

    @Bean
    public WeimoHttpApi createWeimoHttpApi() {
        return getApi("https://jic.jnby.com/", WeimoHttpApi.class);
    }

    @Bean
    public AiCreateImgHttpApi aiCreateImgHttpApi() {
        return getApi(bigDataCallUrl, AiCreateImgHttpApi.class);
    }


}
