package org.springcenter.backcenter.config;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 1/22/21 4:57 PM
 */
@Component
@RefreshScope
public class DataSourceBaseProperties {
    @Value(value = "${marketingJdbc}")
    private String marketJdbc;

    @Value(value = "${marketingUserName}")
    private String marketUserName;

    @Value(value = "${marketingPassword}")
    private String marketPassword;



    @Value(value = "${bojunJdbc}")
    private String bojunJdbc;

    @Value(value = "${bojunUserName}")
    private String bojunUserName;

    @Value(value = "${bojunPassword}")
    private String bojunPassword;



    @Value(value = "${hiveJdbc}")
    private String hiveJdbc;

    @Value(value = "${userWxJdbc}")
    private String userWxJdbc;

    @Value(value = "${userWxUserName}")
    private String userWxUserName;

    @Value(value = "${userWxPassword}")
    private String userWxPassword;


    @Value(value = "${mysqlJdbc}")
    private String mysqlJdbc;

    @Value(value = "${mysqlUserName}")
    private String mysqlUserName;

    @Value(value = "${mysqlPassword}")
    private String mysqlPassword;

    public String getMysqlUserName() {
        return mysqlUserName;
    }

    public void setMysqlUserName(String mysqlUserName) {
        this.mysqlUserName = mysqlUserName;
    }

    public String getMysqlPassword() {
        return mysqlPassword;
    }

    public void setMysqlPassword(String mysqlPassword) {
        this.mysqlPassword = mysqlPassword;
    }

    public String getMysqlJdbc() {
        return mysqlJdbc;
    }

    public void setMysqlJdbc(String mysqlJdbc) {
        this.mysqlJdbc = mysqlJdbc;
    }

    public String getHiveJdbc() {
        return hiveJdbc;
    }

    public void setHiveJdbc(String hiveJdbc) {
        this.hiveJdbc = hiveJdbc;
    }

    // common config value
    private Integer initialSize = 5;
    private Integer maxActive = 5;
    private long maxWait = 3000L;


    private Integer minIdle = 5;
    private long timeBetweenEvictionRunsMillis = 90000L;
    private long removeAbandonedTimeoutMillis = 12*1000L;
    private boolean removeAbandoned = true;
    private boolean testOnBorrow = true;

    // hive库特殊配置
    private long hiveMaxWait = 1000L* 60 *30 ;
    private long haveTimeBetweenEvictionRunsMillis = 900000L;

    public String getUserWxJdbc() {
        return userWxJdbc;
    }

    public void setUserWxJdbc(String userWxJdbc) {
        this.userWxJdbc = userWxJdbc;
    }

    public String getUserWxUserName() {
        return userWxUserName;
    }

    public void setUserWxUserName(String userWxUserName) {
        this.userWxUserName = userWxUserName;
    }

    public String getUserWxPassword() {
        return userWxPassword;
    }

    public void setUserWxPassword(String userWxPassword) {
        this.userWxPassword = userWxPassword;
    }

    public long getHaveTimeBetweenEvictionRunsMillis() {
        return haveTimeBetweenEvictionRunsMillis;
    }

    public void setHaveTimeBetweenEvictionRunsMillis(long haveTimeBetweenEvictionRunsMillis) {
        this.haveTimeBetweenEvictionRunsMillis = haveTimeBetweenEvictionRunsMillis;
    }

    public long getHiveMaxWait() {
        return hiveMaxWait;
    }

    public void setHiveMaxWait(long hiveMaxWait) {
        this.hiveMaxWait = hiveMaxWait;
    }

    public String getMarketJdbc() {
        return marketJdbc;
    }

    public void setMarketJdbc(String marketJdbc) {
        this.marketJdbc = marketJdbc;
    }

    public String getMarketUserName() {
        return marketUserName;
    }

    public void setMarketUserName(String marketUserName) {
        this.marketUserName = marketUserName;
    }

    public String getMarketPassword() {
        return marketPassword;
    }

    public void setMarketPassword(String marketPassword) {
        this.marketPassword = marketPassword;
    }

    public String getBojunJdbc() {
        return bojunJdbc;
    }

    public void setBojunJdbc(String bojunJdbc) {
        this.bojunJdbc = bojunJdbc;
    }

    public String getBojunUserName() {
        return bojunUserName;
    }

    public void setBojunUserName(String bojunUserName) {
        this.bojunUserName = bojunUserName;
    }

    public String getBojunPassword() {
        return bojunPassword;
    }

    public void setBojunPassword(String bojunPassword) {
        this.bojunPassword = bojunPassword;
    }

    public Integer getInitialSize() {
        return initialSize;
    }

    public void setInitialSize(Integer initialSize) {
        this.initialSize = initialSize;
    }

    public Integer getMaxActive() {
        return maxActive;
    }

    public void setMaxActive(Integer maxActive) {
        this.maxActive = maxActive;
    }

    public long getMaxWait() {
        return maxWait;
    }

    public void setMaxWait(long maxWait) {
        this.maxWait = maxWait;
    }

    public Integer getMinIdle() {
        return minIdle;
    }

    public void setMinIdle(Integer minIdle) {
        this.minIdle = minIdle;
    }

    public long getTimeBetweenEvictionRunsMillis() {
        return timeBetweenEvictionRunsMillis;
    }

    public void setTimeBetweenEvictionRunsMillis(long timeBetweenEvictionRunsMillis) {
        this.timeBetweenEvictionRunsMillis = timeBetweenEvictionRunsMillis;
    }

    public long getRemoveAbandonedTimeoutMillis() {
        return removeAbandonedTimeoutMillis;
    }

    public void setRemoveAbandonedTimeoutMillis(long removeAbandonedTimeoutMillis) {
        this.removeAbandonedTimeoutMillis = removeAbandonedTimeoutMillis;
    }

    public boolean isRemoveAbandoned() {
        return removeAbandoned;
    }

    public void setRemoveAbandoned(boolean removeAbandoned) {
        this.removeAbandoned = removeAbandoned;
    }

    public boolean isTestOnBorrow() {
        return testOnBorrow;
    }

    public void setTestOnBorrow(boolean testOnBorrow) {
        this.testOnBorrow = testOnBorrow;
    }
}
