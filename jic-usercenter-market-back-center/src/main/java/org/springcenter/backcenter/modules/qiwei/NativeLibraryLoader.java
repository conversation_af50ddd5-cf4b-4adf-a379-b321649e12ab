package org.springcenter.backcenter.modules.qiwei;

import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;

@Slf4j
public class NativeLibraryLoader {

//    static {
//        loadLibraryFromResource("libWeWorkFinanceSdk_Java.so");
//    }

    public static void loadLibraryFromResource(String libraryName) {
        try {
            // 获取临时目录
            String tempDir = System.getProperty("java.io.tmpdir");
            log.info("查询出临时目录tempDir = {}", tempDir);
            File tempLibraryFile = Paths.get(tempDir, libraryName).toFile();

            // 如果临时目录中没有该文件，则从资源中提取
//            if (!tempLibraryFile.exists()) {
                try (InputStream in = NativeLibraryLoader.class.getClassLoader().getResourceAsStream(libraryName)) {
                    if (in == null) {
                        throw new UnsatisfiedLinkError("Resource not found: " + libraryName);
                    }
                    try (FileOutputStream out = new FileOutputStream(tempLibraryFile)) {
                        byte[] buffer = new byte[1024];
                        int read;
                        while ((read = in.read(buffer)) != -1) {
                            out.write(buffer, 0, read);
                        }
                    }
                }
//            }

            log.info("加载库文件路径为：{}", tempLibraryFile.getAbsolutePath());
            // 加载库文件
            System.load(tempLibraryFile.getAbsolutePath());
        } catch (IOException e) {
            throw new UnsatisfiedLinkError("Failed to load library: " + e.getMessage());
        }
    }
}

