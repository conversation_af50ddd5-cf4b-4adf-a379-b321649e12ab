package org.springcenter.backcenter.modules.qiwei;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class QiweiSession {
    public static final long SDK = Finance.NewSdk();//初始SDK

    public static final String SESSION_KEY = "wx21d0090c65708d51";
    public static final String SECRET = "b7NtsJS_emVXzlAFNmKhB-i6urG_5UuA-ZiK9dG4uho";

    private static int BEFORE_SEQ = 0;//上次拉去之后的序列

    private static int MAX_SEQ_LIMIT = 10;//最大返回条数

    public long getInit() {
        long ret =  Finance.Init(SDK, SESSION_KEY, SECRET);
        if(ret != 0){
            Finance.DestroySdk(SDK);
            log.error("init sdk err ret " + ret);
        }
        return ret;
    }

    public String GetPullChatData(){
        long ret = getInit();
        //每次使用GetChatData拉取存档前需要调用NewSlice获取一个slice，在使用完slice中数据后，还需要调用FreeSlice释放。
        long slice = Finance.NewSlice();
        ret = Finance.GetChatData(SDK, BEFORE_SEQ, MAX_SEQ_LIMIT, null, null, 0, slice);
        if (ret != 0) {
            log.error("getchatdata ret " + ret);
            Finance.FreeSlice(slice);
            return null;
        }
        String content = Finance.GetContentFromSlice(slice);
        Finance.FreeSlice(slice);
        log.info("解密前端的消息体content = {}", content);

        //解密消息体
        long msg = Finance.NewSlice();
        QiweiChatData chatData = JSONObject.parseObject(content, QiweiChatData.class);
        ret = Finance.DecryptData(SDK, chatData.getChatdata().getEncrypt_random_key(), chatData.getChatdata().getEncrypt_chat_msg(), msg);
        if (ret != 0) {
            log.error("getchatdata ret " + ret);
            Finance.FreeSlice(msg);
            return null;
        }

        content = Finance.GetContentFromSlice(msg);
        log.info("解密后的消息体content = {}", content);
        Finance.DestroySdk(SDK);
        return content;
    }


    @lombok.Data
    public static class QiweiChatData{
        private int errcode;
        private String errmsg;
        private ChatData chatdata;

        @lombok.Data
        class ChatData{
            private int seq;
            private String msgid;

            private String encrypt_random_key;

            private String encrypt_chat_msg;
        }
    }

    public static void main(String[] args) {
        QiweiSession session = new QiweiSession();
        session.GetPullChatData();
    }
}
