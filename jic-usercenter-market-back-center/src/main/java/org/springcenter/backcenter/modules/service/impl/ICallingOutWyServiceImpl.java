package org.springcenter.backcenter.modules.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.jnby.common.Page;
import com.jnby.common.util.HttpUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Headers;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springcenter.backcenter.modules.mapper.CallSceneMapper;
import org.springcenter.backcenter.modules.mapper.CallTemplateMapper;
import org.springcenter.backcenter.modules.model.CallScene;
import org.springcenter.backcenter.modules.model.CallTemplate;
import org.springcenter.marketing.api.dto.CallTaskDetailResp;
import org.springcenter.marketing.api.dto.admin.*;
import org.springcenter.backcenter.modules.model.CallTask;
import org.springcenter.backcenter.modules.model.CallTaskDetail;
import org.springcenter.backcenter.modules.service.ICallingOutService;
import org.springcenter.backcenter.modules.util.DateUtils;
import org.springcenter.backcenter.modules.webapi.IWyAiCallingHttpApi;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import retrofit2.Response;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 网易外部系统
 */
@Service
@Slf4j
public class ICallingOutWyServiceImpl implements ICallingOutService {

    @Autowired
    IWyAiCallingHttpApi iWyAiCallingHttpApi;

    @Autowired
    private CallTemplateMapper callTemplateMapper;

    @Autowired
    private CallSceneMapper callSceneMapper;


    @Value("${wy.aicall.appKey}")
    private String appKey;

    @Value("${wy.aicall.appSecret}")
    private String appSecret;

    @Value("${wy.aicall.service}")
    private String wyAicallUrl;

    private static String ys = "ys";


    @Override
    public String getTags() {
        return "00";
    }

    @Override
    public List<BotIdOrStaffIdResp> getBotIdOrStaffId(BotIdOrStaffReq req,Page page) {
        List<BotIdOrStaffIdResp> respList = new ArrayList<>();
        try {
            Map<String,Object> params = new HashMap<>();
            params.put("page",page.getPageNo());
            params.put("pageSize",page.getPageSize());
            params.put("status","FINISHED");
            String botName = "";
            if(StringUtils.isNotBlank(req.getBotName())){
                params.put("botName",req.getBotName());
                botName = "&botName=" + req.getBotName();
            }
            String requestQueryStr = getRequestQueryStr(params);
            String timeSec = System.currentTimeMillis() + "";
            String signature = getSignature(timeSec + requestQueryStr, appSecret);
            log.info("iWyAiCallingHttpApi getBotIdOrStaffId = {} requestQueryStr ={},timeSec = {},signature = {}",page,requestQueryStr,timeSec,signature);

            Map<String,String> map = new HashMap<>();
            map.put("X-YS-APIKEY",appKey);
            map.put("X-YS-TIME",timeSec);
            map.put("X-YS-SIGNATURE",signature);
            map.put("X-YS-APPTYPE",ys);
            Headers of = Headers.of(map);
            String s = HttpUtil.get(wyAicallUrl+"open/api/wecall/v1/bot/getBotList?page="+page.getPageNo()+"&pageSize="+page.getPageSize()+"&status=FINISHED"+botName, of);
            if(StringUtils.isBlank(s)){
                throw new RuntimeException("iWyAiCallingHttpApi getBotIdOrStaffId 异常");
            }
            Gson gson = new Gson();
            WyCustomerBaseResp<WyPage<WyBotDto>> result = gson.fromJson(s, new TypeToken<WyCustomerBaseResp<WyPage<WyBotDto>>>() {}.getType());
//            Response<WyCustomerBaseResp<WyPage<WyBotDto>>> result = iWyAiCallingHttpApi.getBotList(appKey,timeSec,signature,ys,
//                    page.getPageNo(), page.getPageSize(),"FINISHED").execute();
            log.info("iWyAiCallingHttpApi getBotIdOrStaffId = {}",JSONObject.toJSONString(result));
//            if (result.isSuccessful()) {
//                log.info("iWyAiCallingHttpApi getBotIdOrStaffId = {}",JSONObject.toJSONString(result.body()));
                if(result.getCode().equals(200)){
                    WyPage<WyBotDto> data = result.getData();
                    List<WyBotDto> list = data.getList();
                    for (WyBotDto wyBotDto : list) {
                        BotIdOrStaffIdResp botIdOrStaffIdResp = new BotIdOrStaffIdResp();
                        botIdOrStaffIdResp.setId(wyBotDto.getBotId().toString());
                        botIdOrStaffIdResp.setName(wyBotDto.getBotName());
                        botIdOrStaffIdResp.setStatus(wyBotDto.getStatus());
                        botIdOrStaffIdResp.setImportVariables(wyBotDto.getImportVariables());
                        botIdOrStaffIdResp.setIntentTagGroupId(wyBotDto.getIntentTagGroupId());
                        respList.add(botIdOrStaffIdResp);
                    }
                    page.setCount(data.getTotal().longValue());
                }
                return respList;
//            }
        } catch (Exception e) {
            log.error("iWyAiCallingHttpApi getBotIdOrStaffId params:{}  e:{} message:{}", page, e, e.getMessage());
            throw new RuntimeException("iWyAiCallingHttpApi getBotIdOrStaffId 异常");
        }
    }

    @Override
    public String sendThridCreateTask(CallTask callTask, List<CallTaskDetailResp> callTaskDetails) {
        log.info("iWyAiCallingHttpApi sendThridCreateTask  = callTask = {} , callTaskDetails = {}",callTask,callTaskDetails);
        try {
            CreateWyTaskDto createWyTaskDto = new CreateWyTaskDto();
            createWyTaskDto.setTaskName(callTask.getCallTaskName());
            createWyTaskDto.setBotId(Long.parseLong(callTask.getCallOutIdSnapshot()));
            List<String> didNumbers = new ArrayList<>();
            if(StringUtils.isNotBlank(callTask.getDidNumber())){
                didNumbers.addAll(Arrays.asList(callTask.getDidNumber().split(",")));
            }else{
                // 分场景默认
                String callTemplateId = callTask.getCallTemplateId();
                CallTemplate callTemplate = callTemplateMapper.selectByPrimaryKey(callTemplateId);
                String callSceneId = callTemplate.getCallSceneId();
                CallScene callScene = callSceneMapper.selectByPrimaryKey(callSceneId);
                if(callScene.getSuitBuiness().equals(1L)){
                    didNumbers.add("002125074289");
                }else if(callScene.getSuitBuiness().equals(2L)){
                    didNumbers.add("0057122931671");
                }
            }
            createWyTaskDto.setDidNumbers(didNumbers);
            Date now  =new Date();
            String startTimeStr = DateUtils.formatDate(new Date(), "yyyy-MM-dd") + " 00:00:00";
            String endTimeStr = DateUtils.formatDate(new Date(), "yyyy-MM-dd") + " 23:59:59";
            createWyTaskDto.setExecuteBeginTime(DateUtils.parseDate(startTimeStr,"yyyy-MM-dd HH:mm:ss").getTime());
            createWyTaskDto.setExecuteEndTime(DateUtils.parseDate(endTimeStr,"yyyy-MM-dd HH:mm:ss").getTime());
            createWyTaskDto.setHangupSms(callTask.getHangupSms());
            String callOutTimesSnapshot = callTask.getCallOutTimesSnapshot();
            // 默认传00
            List<CreateWyTaskDto.CanCallOutTimes> canCallOutTimes = JSONObject.parseArray(callOutTimesSnapshot, CreateWyTaskDto.CanCallOutTimes.class);
            for (CreateWyTaskDto.CanCallOutTimes canCallOutTime : canCallOutTimes) {
                canCallOutTime.setStartTime(canCallOutTime.getStartTime()+":00");
                canCallOutTime.setEndTime(canCallOutTime.getEndTime()+":00");
            }

            createWyTaskDto.setExecuteTimeInterval(canCallOutTimes);
            // 如果配置了黑名单 则设置上黑名单
            if(StringUtils.isNotBlank(callTask.getBlackList())){
                // 转成json结构
                createWyTaskDto.setBlacklistGroups(JSONObject.parseArray(callTask.getBlackList(), CreateWyTaskDto.BlackListDto.class));
            }

            // 如果发送挂机短信 则设置一些必传字段
            if(createWyTaskDto.getHangupSms().equals(1)){
                //配置挂机短信
                CreateWyTaskDto.HangupSmsConf hangupSmsConf = new CreateWyTaskDto.HangupSmsConf();
                hangupSmsConf.setAnswerSwitch(false);
                hangupSmsConf.setRepeatSwitch(true);
                List<CreateWyTaskDto.HangupSmsConfDetail> list = new ArrayList<>();
                CreateWyTaskDto.HangupSmsConfDetail hangupSmsConfDetail = new CreateWyTaskDto.HangupSmsConfDetail();
                hangupSmsConfDetail.setTemplateId(callTask.getSmsTemplateId());
                //TODO 拿到意向标签
                List<String> intentionIds = new ArrayList<>();
                if(StringUtils.isNotBlank(callTask.getIntentionIds())){
                    intentionIds = Arrays.asList(callTask.getIntentionIds().split(","));
                }
                hangupSmsConfDetail.setIntentionList(intentionIds);
                List<String> smsTemplateParams  = new ArrayList<>();
                // 拿到名
                String smsTemplateNames = callTask.getSmsTemplateNames();
                if(StringUtils.isNotBlank(smsTemplateNames)){
                    String[] split = smsTemplateNames.split(",");
                    for (String str : split) {
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("id",str);
                        jsonObject.put("type",1);
                        jsonObject.put("propMaxLength",30);
                        jsonObject.put("defaultValue","JNBY");
                        smsTemplateParams.add(jsonObject.toJSONString());
                    }
                }
                hangupSmsConfDetail.setSmsTemplateParams(smsTemplateParams);
                list.add(hangupSmsConfDetail);
                hangupSmsConf.setConf(list);
                createWyTaskDto.setHangupSmsConf(hangupSmsConf);
            }

            log.info("iWyAiCallingHttpApi sendThridCreateTask = {}",JSONObject.toJSONString(createWyTaskDto));


            String requestQueryStr = "";
            String timeSec = System.currentTimeMillis() + "";
            String str = timeSec + requestQueryStr + JSONObject.toJSONString(createWyTaskDto);
            String signature = getSignature(str, appSecret);
            log.info("iWyAiCallingHttpApi sendThridCreateTask   ,  str = {} , requestQueryStr = {} , timeSec= {} , signature= {}",
                    str,requestQueryStr,timeSec,signature);

            Response<WyCustomerBaseResp<CreateWyTaskRespDto>> result = iWyAiCallingHttpApi.save(appKey,timeSec,signature,ys,createWyTaskDto).execute();
            log.info("iWyAiCallingHttpApi sendThridCreateTask = {}",JSONObject.toJSONString(result));
            if (result.isSuccessful()) {
                log.info("iWyAiCallingHttpApi sendThridCreateTask = {}",JSONObject.toJSONString(result.body()));
                if(result.body().getCode().equals(200)){
                    CreateWyTaskRespDto data = result.body().getData();

                    // 传输手机号到网易ai外呼
                    // 拼装参数
                    List<List<CallTaskDetailResp>> partition = Lists.partition(callTaskDetails, 90);
                    for (List<CallTaskDetailResp> callTaskDetailResps : partition) {
                        // 一批90个号码
                        SendPhoneToAiReq sendPhoneToAiReq = new SendPhoneToAiReq();
                        sendPhoneToAiReq.setTaskId(data.getTaskId());
                        List<SendPhoneToAiReq.SendPhoneCustomerDetail> collect = callTaskDetailResps.stream().map(e -> {
                            SendPhoneToAiReq.SendPhoneCustomerDetail sendPhoneCustomerDetail = new SendPhoneToAiReq.SendPhoneCustomerDetail();
                            sendPhoneCustomerDetail.setPhone(e.getPhone());
                            sendPhoneCustomerDetail.setName(e.getCallPhoneName());
                            Map<String,String> callParamsMap = new HashMap<>();
                            //增加变量  处理变量
                            if(StringUtils.isNotBlank(e.getCallParams())){
                                // 处理变量
                                String callParams = e.getCallParams();
                                String[] split = callParams.split(",");
                                for (String callStr : split) {
                                    callParamsMap.put(callStr.split(":")[0],callStr.split(":")[1]);
                                }
                                sendPhoneCustomerDetail.setProperties(callParamsMap);
                            }
                            return sendPhoneCustomerDetail;
                        }).collect(Collectors.toList());
                        sendPhoneToAiReq.setCustomerList(collect);
                        //  封装信息
                        String requestQueryStrSendPhone = "";
                        String timeSecSendPhone = System.currentTimeMillis() + "";
                        String strSendPhone = timeSecSendPhone + requestQueryStrSendPhone + JSONObject.toJSONString(sendPhoneToAiReq);
                        String signatureSendPhone = getSignature(strSendPhone, appSecret);
                        log.info("sendPhoneToAi  signatureSendPhone = {}",signatureSendPhone);
                        Response<WyCustomerBaseResp<List<SendPhoneAiResp>>> resultSendPhone = iWyAiCallingHttpApi.sendPhoneToAi(appKey,timeSecSendPhone,signatureSendPhone,ys,sendPhoneToAiReq).execute();
                        log.info("sendPhoneToAi  resultSendPhone.body = {}" ,JSONObject.toJSONString(resultSendPhone.body()));
                    }
                    return data.getTaskId().toString();
                }
            }
            throw new RuntimeException("iWyAiCallingHttpApi sendThridCreateTask" + JSONObject.toJSONString(result.body()));
        } catch (Exception e) {
            log.error("iWyAiCallingHttpApi sendThridCreateTask params:{}  e:{} message:{}", callTask, e, e.getMessage());
//            throw new RuntimeException("iWyAiCallingHttpApi sendThridCreateTask 异常");
        }
        return null;
    }

    @Override
    public QiyuCallTaskInfoResp getCallTaskInfo(String outTaskId) {
        return null;
    }

    @Override
    public WyPage<CallTaskDetailInfoResp> getCallTaskDetailInfo(String outTaskId, int pageNum) {
        try {
            Map<String,Object> params = new HashMap<>();
            params.put("page",pageNum);
            params.put("pageSize",50);
            params.put("taskId",outTaskId);
            String requestQueryStr = getRequestQueryStr(params);
            String timeSec = System.currentTimeMillis() + "";
            String signature = getSignature(timeSec + requestQueryStr, appSecret);
            log.info("iWyAiCallingHttpApi getCallTaskDetailInfo  = outTaskId = {} ,  params = {} , requestQueryStr = {} , timeSec= {} , signature= {}",
                    outTaskId,JSONObject.toJSONString(params),requestQueryStr,timeSec,signature);

            //变更为httpGet请求

            Map<String,String> map = new HashMap<>();
            map.put("X-YS-APIKEY",appKey);
            map.put("X-YS-TIME",timeSec);
            map.put("X-YS-SIGNATURE",signature);
            map.put("X-YS-APPTYPE",ys);
            Headers of = Headers.of(map);
            String s = HttpUtil.get(wyAicallUrl+"open/api/wecall/v1/taskCall/unCallList?page="+pageNum+"&pageSize="+50+"&taskId="+outTaskId, of);
            if(StringUtils.isBlank(s)){
                throw new RuntimeException("iWyAiCallingHttpApi getCallTaskDetailInfo 异常");
            }
            Gson gson = new Gson();
            WyCustomerBaseResp<WyPage<WyUnCallingDetailListResp>> result = gson.fromJson(s, new TypeToken<WyCustomerBaseResp<WyPage<WyUnCallingDetailListResp>>>() {}.getType());

//            Response<WyCustomerBaseResp<WyPage<WyUnCallingDetailListResp>>> result = iWyAiCallingHttpApi.getUnCallListDetail(appKey,timeSec,signature,ys,
//                    pageNum,50,outTaskId).execute();
            log.info("iWyAiCallingHttpApi getCallTaskDetailInfo = {}",JSONObject.toJSONString(result));
//            if (result.isSuccessful()) {
                log.info("iWyAiCallingHttpApi getCallTaskDetailInfo = {}",JSONObject.toJSONString(result));
                if(result.getCode().equals(200)){
                    WyPage<WyUnCallingDetailListResp> data = result.getData();
                    List<WyUnCallingDetailListResp> list = data.getList();
                    List<CallTaskDetailInfoResp> respList = new ArrayList<>();
                    for (WyUnCallingDetailListResp callingDetailListResp : list) {
                        CallTaskDetailInfoResp callTaskDetailInfoResp = new CallTaskDetailInfoResp();
                        callTaskDetailInfoResp.setId(callingDetailListResp.getCallId());
                        callTaskDetailInfoResp.setPhone(callingDetailListResp.getPhone());
                        respList.add(callTaskDetailInfoResp);
                    }
                    WyPage<CallTaskDetailInfoResp> returnResult = new WyPage<>();
                    returnResult.setList(respList);
                    returnResult.setTotal(data.getTotal());
                    return returnResult;
                }else{
                    throw  new RuntimeException("iWyAiCallingHttpApi getCallTaskDetailInfo 异常");
                }
//            }
//            throw new RuntimeException("iWyAiCallingHttpApi getCallTaskDetailInfo" + JSONObject.toJSONString(result.body()));
        } catch (Exception e) {
            log.error("iWyAiCallingHttpApi getCallTaskDetailInfo params:{}  e:{} message:{}", outTaskId, e, e.getMessage());
            throw new RuntimeException("iWyAiCallingHttpApi getCallTaskDetailInfo 异常");
        }
    }

    @Override
    public List<CallTaskDetail> deleteCallDetailById(CallTask callTask, List<CallTaskDetail> callDetailId) {
            //网易AI
//        if(1L != callTask.getOutStatus()){
//            log.info("check 网易AI主任务状态不满足条件  任务主键id = " + callTask.getId());
//            return new ArrayList<>();
//        }

        log.info("iWyAiCallingHttpApi deleteCallDetailById  = callDetailId = {} ,  = {}",JSONObject.toJSONString(callDetailId));
        List<CallTaskDetail> resultList = new ArrayList<>();
        for (CallTaskDetail callTaskDetail : callDetailId) {
            try {
                Map<String,Object> params = new HashMap<>();
                params.put("callId",callTaskDetail.getCallDetailId());
                String requestQueryStr = "";
                String timeSec = System.currentTimeMillis() + "";
                String signature = getSignature(timeSec + requestQueryStr+JSONObject.toJSONString(params), appSecret);
                log.info("iWyAiCallingHttpApi deleteCallDetailById   ,  params = {} , requestQueryStr = {} , timeSec= {} , signature= {}",
                        JSONObject.toJSONString(params),requestQueryStr,timeSec,signature);
                Response<WyCustomerBaseResp<WyDeleteResp>> result = iWyAiCallingHttpApi.deleteCallDetailById(appKey,timeSec,signature,ys,params).execute();
                log.info("iWyAiCallingHttpApi deleteCallDetailById = {}",JSONObject.toJSONString(result));
                if (result.isSuccessful()) {
                    log.info("iWyAiCallingHttpApi deleteCallDetailById = {}",JSONObject.toJSONString(result.body()));
                    if(result.body().getCode().equals(200)){
                        WyDeleteResp data = result.body().getData();
                        if(data.getSuccess()){
                            CallTaskDetail callTaskDetailResult = new CallTaskDetail();
                            callTaskDetailResult.setIsRemove(1L);
                            callTaskDetailResult.setId(callTaskDetail.getId());
                            resultList.add(callTaskDetailResult);
                        }
                    }
                }
                throw new RuntimeException("iWyAiCallingHttpApi deleteCallDetailById" + JSONObject.toJSONString(result.body()));
            } catch (Exception e) {
                log.error("iWyAiCallingHttpApi deleteCallDetailById params:{}  e:{} message:{}", callDetailId, e, e.getMessage());
                continue;
            }
        }
        return resultList;
    }

    @Override
    public List<CallTask> start(CallTask callTask) {
        log.info("iWyAiCallingHttpApi start  = callTask = {} ,  = {}",JSONObject.toJSONString(callTask));
        List<CallTask> resultList = new ArrayList<>();
        try {
            Map<String,Object> params = new HashMap<>();
            params.put("taskId", Long.parseLong(callTask.getOutTaskId()));
            String requestQueryStr = "";
            String timeSec = System.currentTimeMillis() + "";
            String signature = getSignature(timeSec + requestQueryStr+JSONObject.toJSONString(params), appSecret);
            log.info("iWyAiCallingHttpApi start   ,  params = {} , requestQueryStr = {} , timeSec= {} , signature= {}",
                    JSONObject.toJSONString(params),requestQueryStr,timeSec,signature);
            Response<WyCustomerBaseResp<WyDeleteResp>> result = iWyAiCallingHttpApi.start(appKey,timeSec,signature,ys,params).execute();
            log.info("iWyAiCallingHttpApi start = {}",JSONObject.toJSONString(result));
            if (result.isSuccessful()) {
                log.info("iWyAiCallingHttpApi start = {}",JSONObject.toJSONString(result.body()));
                if(result.body().getCode().equals(200)){
                    WyDeleteResp data = result.body().getData();
                    if(data.getSuccess()){
                        resultList.add(callTask);
                    }
                }
            }
            throw new RuntimeException("iWyAiCallingHttpApi start" + JSONObject.toJSONString(result.body()));
        } catch (Exception e) {
            log.error("iWyAiCallingHttpApi start params:{}  e:{} message:{}", callTask.getCallTemplateId(), e, e.getMessage());
        }
        return resultList;
    }

    @Override
    public WyPage<WyTaskList> getTaskList(Page page,Date startTime) {
        // 查询数据
        try {
            Long startTimeLong = 0L;
            if(startTime != null){
                startTimeLong = startTime.getTime() + 1000L;
            }
            Map<String,Object> params = new HashMap<>();
            params.put("page",page.getPageNo());
            params.put("pageSize",page.getPageSize());
            params.put("createStart",startTimeLong);
            String requestQueryStr = getRequestQueryStr(params);
            String timeSec = System.currentTimeMillis() + "";
            String signature = getSignature(timeSec + requestQueryStr, appSecret);
            log.info("iWyAiCallingHttpApi getTaskList 开始" );
            Response<WyCustomerBaseResp<WyPage<WyTaskList>>> execute = iWyAiCallingHttpApi.getTaskList(appKey, timeSec, signature, ys,
                    page.getPageNo(), page.getPageSize(),startTimeLong).execute();
            log.info("iWyAiCallingHttpApi getTaskList = {}",JSONObject.toJSONString(execute));
            if (execute.isSuccessful()) {
                log.info("iWyAiCallingHttpApi getTaskList = {}",JSONObject.toJSONString(execute.body()));
                if(execute.body().getCode().equals(200)){
                    WyPage<WyTaskList> data = execute.body().getData();
                    return data;
                }
            }
        }catch (Exception e){
            log.info("getTaskList 出现错误 ",e);
        }
        return null;
    }

    @Override
    public WyPage<AiCallNotifyDataResp> getAlreadyCallTaskDetailInfo(String taskId, Page page) {

        try {
            // callTime为最近7天
            Date now = new Date();
            Date date = DateUtils.addDays(now, -7);
            Map<String,Object> params = new HashMap<>();
            params.put("page",page.getPageNo());
            params.put("pageSize",page.getPageSize());
            params.put("taskId",taskId);
            params.put("callTime",date.getTime());
            String requestQueryStr = getRequestQueryStr(params);
            String timeSec = System.currentTimeMillis() + "";
            String signature = getSignature(timeSec + requestQueryStr, appSecret);
            log.info("iWyAiCallingHttpApi getAlreadyCallTaskDetailInfo  = outTaskId = {} ,  params = {} , requestQueryStr = {} , timeSec= {} , signature= {}",
                    taskId,JSONObject.toJSONString(params),requestQueryStr,timeSec,signature);

            //变更为httpGet请求
            Response<WyCustomerBaseResp<WyPage<AiCallNotifyDataResp>>> execute = iWyAiCallingHttpApi.getAlreadyCallDetails(appKey, timeSec, signature, ys,
                    page.getPageNo(), page.getPageSize(), taskId,date.getTime()).execute();
            log.info("iWyAiCallingHttpApi getAlreadyCallTaskDetailInfo = {}",JSONObject.toJSONString(execute));
            if (execute.isSuccessful()) {
                log.info("iWyAiCallingHttpApi getAlreadyCallTaskDetailInfo = {}",JSONObject.toJSONString(execute.body()));
                if(execute.body().getCode().equals(200)){
                    WyPage<AiCallNotifyDataResp> data = execute.body().getData();
                    return data;
                }
            }

        } catch (Exception e) {
            log.error("iWyAiCallingHttpApi getAlreadyCallTaskDetailInfo params:{}  e:{} message:{}", taskId, e, e.getMessage(),e);
            throw new RuntimeException("iWyAiCallingHttpApi getAlreadyCallTaskDetailInfo 异常");
        }
        return null;
    }

    @Override
    public List<VaildTemplateResp> getVaildTemplateList(VaildTemplateReq requestData) {
        List<VaildTemplateResp> respList = new ArrayList<>();
        try {
            Map<String,Object> params = new HashMap<>();
            params.put("keyword",requestData.getName());
            String requestQueryStr = getRequestQueryStr(params);
            String timeSec = System.currentTimeMillis() + "";
            String signature = getSignature(timeSec + requestQueryStr, appSecret);
            log.info("iWyAiCallingHttpApi getVaildTemplateList = {} requestQueryStr ={},timeSec = {},signature = {}",JSONObject.toJSONString(requestData),requestQueryStr,timeSec,signature);

            Map<String,String> map = new HashMap<>();
            map.put("X-YS-APIKEY",appKey);
            map.put("X-YS-TIME",timeSec);
            map.put("X-YS-SIGNATURE",signature);
            map.put("X-YS-APPTYPE",ys);
            Headers of = Headers.of(map);
            String s = HttpUtil.get(wyAicallUrl+"open/api/wecall/v1/sms/getValidTemplateList?keyword="+requestData.getName(), of);
            if(StringUtils.isBlank(s)){
                throw new RuntimeException("iWyAiCallingHttpApi getVaildTemplateList 异常");
            }
            Gson gson = new Gson();
            WyCustomerBaseResp<List<VaildTemplateResp>> result = gson.fromJson(s, new TypeToken<WyCustomerBaseResp<List<VaildTemplateResp>>>() {}.getType());
            log.info("iWyAiCallingHttpApi getVaildTemplateList = {}",JSONObject.toJSONString(result));
            if(result.getCode().equals(200)){
                List<VaildTemplateResp> data = result.getData();
                respList  = data;
            }
            return respList;
        } catch (Exception e) {
            log.error("iWyAiCallingHttpApi getVaildTemplateList params:{}  e:{} message:{}", JSONObject.toJSONString(requestData), e, e.getMessage());
            throw new RuntimeException("iWyAiCallingHttpApi getVaildTemplateList 异常");
        }
    }

    @Override
    public GroupDetailResp getGroupDetail(GroupDetailReq requestData) {
        GroupDetailResp respList = new GroupDetailResp();
        try {
            Map<String,Object> params = new HashMap<>();
            params.put("intentTagGroupId",requestData.getIntentTagGroupId());
            String requestQueryStr = getRequestQueryStr(params);
            String timeSec = System.currentTimeMillis() + "";
            String signature = getSignature(timeSec + requestQueryStr, appSecret);
            log.info("iWyAiCallingHttpApi getGroupDetail = {} requestQueryStr ={},timeSec = {},signature = {}",JSONObject.toJSONString(requestData),requestQueryStr,timeSec,signature);

            Map<String,String> map = new HashMap<>();
            map.put("X-YS-APIKEY",appKey);
            map.put("X-YS-TIME",timeSec);
            map.put("X-YS-SIGNATURE",signature);
            map.put("X-YS-APPTYPE",ys);
            Headers of = Headers.of(map);
            String s = HttpUtil.get(wyAicallUrl+"open/api/wecall/v1/intent/getGroupDetail?intentTagGroupId="+requestData.getIntentTagGroupId(), of);
            if(StringUtils.isBlank(s)){
                throw new RuntimeException("iWyAiCallingHttpApi getGroupDetail 异常");
            }
            Gson gson = new Gson();
            WyCustomerBaseResp<GroupDetailResp> result = gson.fromJson(s, new TypeToken<WyCustomerBaseResp<GroupDetailResp>>() {}.getType());
            log.info("iWyAiCallingHttpApi getGroupDetail = {}",JSONObject.toJSONString(result));
            if(result.getCode().equals(200)){
                respList  = result.getData();
            }
            return respList;
        } catch (Exception e) {
            log.error("iWyAiCallingHttpApi getGroupDetail params:{}  e:{} message:{}", JSONObject.toJSONString(requestData), e, e.getMessage());
            throw new RuntimeException("iWyAiCallingHttpApi getGroupDetail 异常");
        }
    }

    @Override
    public List<BlackListAiCallResp> getBlackListDto(BlackListAiCallReq requestData, Page page) {
        List<BlackListAiCallResp> respList = new ArrayList<>();
        try {
            Map<String,Object> params = new HashMap<>();
            params.put("groupName",requestData.getGroupName());
            params.put("page",page.getPageNo());
            params.put("pageSize",page.getPageSize());
            String requestQueryStr = getRequestQueryStr(params);
            String timeSec = System.currentTimeMillis() + "";
            String signature = getSignature(timeSec + requestQueryStr, appSecret);
            log.info("iWyAiCallingHttpApi getBlackListDto = {} requestQueryStr ={},timeSec = {},signature = {}",JSONObject.toJSONString(requestData),requestQueryStr,timeSec,signature);

            Map<String,String> map = new HashMap<>();
            map.put("X-YS-APIKEY",appKey);
            map.put("X-YS-TIME",timeSec);
            map.put("X-YS-SIGNATURE",signature);
            map.put("X-YS-APPTYPE",ys);
            Headers of = Headers.of(map);
            String groupName = "";
            if(StringUtils.isNotBlank(requestData.getGroupName())){
                groupName = "&groupName="+requestData.getGroupName();
            }
            //变更为httpGet请求
            String s = HttpUtil.get(wyAicallUrl+"open/api/wecall/v1/blackUserGroup/list?page="+page.getPageNo()+"&pageSize="+page.getPageSize()+""+groupName, of);

//            Response<WyCustomerBaseResp<WyPage<BlackListAiCallResp>>> execute = iWyAiCallingHttpApi.getBlackListDto(appKey, timeSec, signature, ys,
//                    page.getPageNo(), page.getPageSize(), requestData.getGroupName()).execute();
//            log.info("iWyAiCallingHttpApi getBlackListDto = {}",JSONObject.toJSONString(execute));
//            WyCustomerBaseResp<WyPage<BlackListAiCallResp>> body = execute.body();
            Gson gson = new Gson();
            WyCustomerBaseResp<WyPage<BlackListAiCallResp>> body = gson.fromJson(s, new TypeToken<WyCustomerBaseResp<WyPage<BlackListAiCallResp>>>() {}.getType());
            if(body.getCode().equals(200)){
                respList  =  body.getData().getList();
            }
            return respList;
        } catch (Exception e) {
            log.error("iWyAiCallingHttpApi getBlackListDto params:{}  e:{} message:{}", JSONObject.toJSONString(requestData), e, e.getMessage());
            throw new RuntimeException("iWyAiCallingHttpApi getBlackListDto 异常");
        }

    }


    public static String readContent(HttpServletRequest request) {
        try {
            byte[] byteContent = IOUtils.toByteArray(request.getInputStream());
            if (byteContent != null) {
                return new String(byteContent, StandardCharsets.UTF_8);
            }
        } catch (IOException e) {
            log.error("readcontent fail");
        }
        return "";
    }


    public static String getRequestQueryStr(Map<String,Object> request) {
        // 对 Query 参数按照字典对 Key 进行排序后,按照value1+value2方法拼接
        // 转换一下数据类型并排序
        List<String> reqList = new ArrayList<>();
        Set<String> strings = request.keySet();
        for (String key : strings) {
            reqList.add(key);
        }
        Collections.sort(reqList);
        StringBuilder requestQuery = new StringBuilder();
        for (String key : reqList) {
            Object value = request.get(key);
            requestQuery.append(value == null ? "" : value);
        }
        return requestQuery.toString();
    }

    public static String getRequestQueryStr(HttpServletRequest request) {
        // 对 Query 参数按照字典对 Key 进行排序后,按照value1+value2方法拼接
        // 转换一下数据类型并排序
        List<String> reqList = new ArrayList<>();
        Enumeration<String> reqEnu = request.getParameterNames();
        while (reqEnu.hasMoreElements()) {
            reqList.add(reqEnu.nextElement());
        }
        Collections.sort(reqList);
        StringBuilder requestQuery = new StringBuilder();
        for (String key : reqList) {
            String value = request.getParameter(key);
            requestQuery.append(value == null ? "" : value);
        }
        return requestQuery.toString();
    }


    /***
     * 计算请求签名值
     *
     * @param message 待签名字符串
     * @param secret  密钥APP KEY
     * @return HmacSHA256计算后摘要值的16进制字符串
     * @throws Exception 加密过程中的异常信息
     */
    public static String getSignature(String message, String secret) throws Exception {
        String algorithm = "HmacSHA256";
        Mac hmacSha256;
        try {
            hmacSha256 = Mac.getInstance(algorithm);
            byte[] keyBytes = secret.getBytes("UTF-8");
            byte[] messageBytes = message.getBytes("UTF-8");
            hmacSha256.init(new SecretKeySpec(keyBytes, 0, keyBytes.length, algorithm));
            // 使用HmacSHA256对二进制数据消息Bytes计算摘要
            byte[] digestBytes = hmacSha256.doFinal(messageBytes);
            return byte2hex(digestBytes);
        } catch (NoSuchAlgorithmException e) {
            String msg = MessageFormat.format("不支持此算法: {0}", e.getMessage());
            Exception ex = new Exception(msg);
            ex.initCause(e);
            throw ex;
        } catch (UnsupportedEncodingException e) {
            String msg = MessageFormat.format("不支持的字符编码: {0}", e.getMessage());
            Exception ex = new Exception(msg);
            ex.initCause(e);
            throw ex;
        } catch (InvalidKeyException e) {
            String msg = MessageFormat.format("无效的密钥规范: {0}", e.getMessage());
            Exception ex = new Exception(msg);
            ex.initCause(e);
            throw ex;
        }
    }

    /***
     * 将byte[]转成16进制字符串
     *
     * @param data
     *
     * @return 16进制字符串
     */
    public static String byte2hex(byte[] data) {
        StringBuilder hash = new StringBuilder();
        String stmp;
        for (int n = 0; data != null && n < data.length; n++) {
            stmp = Integer.toHexString(data[n] & 0XFF);
            if (stmp.length() == 1)
                hash.append('0');
            hash.append(stmp);
        }
        return hash.toString();
    }



}
