package org.springcenter.backcenter.modules.webapi;

import com.jnby.common.CommonRequest;
import com.xxl.job.core.handler.IJobHandler;
import org.springcenter.marketing.api.dto.CustomerBaseResponse;
import org.springcenter.marketing.api.dto.admin.*;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.Header;
import retrofit2.http.POST;

import java.util.List;
import java.util.Map;

public interface AiCreateImgHttpApi {

    @POST("/prod/image/clotheschangev2/")
    Call<CreateAiImgResp<CreateAiImgResp.AiResult>> clotheschangev2(@Body CreateAiImgReq createAiImgReq);


    @POST("/prod/image/getClothes/")
    Call<BigDataCustomerResp<GetTaskDataDto>> getClothes(@Body GetAiImgByTaskIdReq getAiImgByTaskIdReq);


    @GET("/prod/image/queue/")
    Call<GetQueueDto> getQueue();

    @POST("/prod/image/queryGoods/")
    Call<BigDataCustomerDataResp<List<Object>>> getGoods(@Body CommonRequest<GetGoodsReq> commonRequest);
}
