<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.backcenter.modules.mapper.TaskPartDetailsMapper">

    <resultMap id="BaseResultMap" type="org.springcenter.backcenter.modules.model.TaskPartDetails">
            <id property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="unionid" column="UNIONID" jdbcType="VARCHAR"/>
            <result property="taskTempId" column="TASK_TEMP_ID" jdbcType="VARCHAR"/>
            <result property="taskTempType" column="TASK_TEMP_TYPE" jdbcType="DECIMAL"/>
            <result property="weid" column="WEID" jdbcType="VARCHAR"/>
            <result property="nickname" column="NICKNAME" jdbcType="VARCHAR"/>
            <result property="phone" column="PHONE" jdbcType="VARCHAR"/>
            <result property="vipLevel" column="VIP_LEVEL" jdbcType="DECIMAL"/>
            <result property="finishStatus" column="FINISH_STATUS" jdbcType="DECIMAL"/>
            <result property="recieveTime" column="RECIEVE_TIME" jdbcType="TIMESTAMP"/>
            <result property="completionNum" column="COMPLETION_NUM" jdbcType="DECIMAL"/>
            <result property="inReviewNum" column="IN_REVIEW_NUM" jdbcType="DECIMAL"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="isDeleted" column="IS_DELETED" jdbcType="DECIMAL"/>
            <result property="inCycleStartTime" column="IN_CYCLE_START_TIME" jdbcType="TIMESTAMP"/>
            <result property="inCycleEndTime" column="IN_CYCLE_END_TIME" jdbcType="TIMESTAMP"/>
            <result property="taskItemNo" column="TASK_ITEM_NO" jdbcType="VARCHAR"/>
            <result property="dealTime" column="DEAL_TIME" jdbcType="TIMESTAMP"/>
            <result property="version" column="VERSION" jdbcType="VARCHAR"/>
            <result property="status" column="STATUS" jdbcType="DECIMAL"/>
            <result property="taskInfoId" column="TASK_INFO_ID" jdbcType="VARCHAR"/>
            <result property="taskStatus" column="TASK_STATUS" jdbcType="DECIMAL"/>
            <result property="taskStatusStartTime" column="TASK_STATUS_START_TIME" jdbcType="TIMESTAMP"/>
            <result property="taskStatusEndTime" column="TASK_STATUS_END_TIME" jdbcType="TIMESTAMP"/>
    </resultMap>


    <resultMap id="BaseResultMap1" type="org.springcenter.backcenter.modules.entity.TaskPartDetailsInfo">
        <id property="id" column="ID" jdbcType="VARCHAR"/>
        <result property="unionid" column="UNIONID" jdbcType="VARCHAR"/>
        <result property="taskTempId" column="TASK_TEMP_ID" jdbcType="VARCHAR"/>
        <result property="taskTempType" column="TASK_TEMP_TYPE" jdbcType="DECIMAL"/>
        <result property="weid" column="WEID" jdbcType="VARCHAR"/>
        <result property="nickname" column="NICKNAME" jdbcType="VARCHAR"/>
        <result property="phone" column="PHONE" jdbcType="VARCHAR"/>
        <result property="vipLevel" column="VIP_LEVEL" jdbcType="DECIMAL"/>
        <result property="finishStatus" column="FINISH_STATUS" jdbcType="DECIMAL"/>
        <result property="recieveTime" column="RECIEVE_TIME" jdbcType="TIMESTAMP"/>
        <result property="completionNum" column="COMPLETION_NUM" jdbcType="DECIMAL"/>
        <result property="inReviewNum" column="IN_REVIEW_NUM" jdbcType="DECIMAL"/>
        <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
        <result property="isDeleted" column="IS_DELETED" jdbcType="DECIMAL"/>
        <result property="inCycleStartTime" column="IN_CYCLE_START_TIME" jdbcType="TIMESTAMP"/>
        <result property="inCycleEndTime" column="IN_CYCLE_END_TIME" jdbcType="TIMESTAMP"/>
        <result property="taskItemNo" column="TASK_ITEM_NO" jdbcType="VARCHAR"/>
        <result property="dealTime" column="DEAL_TIME" jdbcType="TIMESTAMP"/>
        <result property="version" column="VERSION" jdbcType="VARCHAR"/>
        <result property="status" column="STATUS" jdbcType="DECIMAL"/>
        <result property="taskInfoId" column="TASK_INFO_ID" jdbcType="VARCHAR"/>
        <result property="taskStatus" column="TASK_STATUS" jdbcType="DECIMAL"/>
        <result property="taskStatusStartTime" column="TASK_STATUS_START_TIME" jdbcType="TIMESTAMP"/>
        <result property="taskStatusEndTime" column="TASK_STATUS_END_TIME" jdbcType="TIMESTAMP"/>
        <result property="taskRange" column="TASK_RANGE" jdbcType="DECIMAL"/>
        <result property="rewardStock" column="REWARD_STOCK" jdbcType="DECIMAL"/>
        <result property="preExpendStock" column="PRE_EXPEND_STOCK" jdbcType="DECIMAL"/>
        <result property="inFactExpendStock" column="IN_FACT_EXPEND_STOCK" jdbcType="DECIMAL"/>
        <result property="costFee" column="COST_FEE" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,UNIONID,TASK_TEMP_ID,
        TASK_TEMP_TYPE,WEID,NICKNAME,
        PHONE,VIP_LEVEL,FINISH_STATUS,
        RECIEVE_TIME,COMPLETION_NUM,IN_REVIEW_NUM,
        CREATE_TIME,UPDATE_TIME,IS_DELETED,
        IN_CYCLE_START_TIME,IN_CYCLE_END_TIME,TASK_ITEM_NO,DEAL_TIME,STATUS,VERSION,
        TASK_INFO_ID, TASK_STATUS, TASK_STATUS_START_TIME, TASK_STATUS_END_TIME
    </sql>

    <sql id="Base_Column_List1">
        a.ID,a.UNIONID,a.TASK_TEMP_ID,
        a.TASK_TEMP_TYPE,a.WEID,a.NICKNAME,
        a.PHONE,a.VIP_LEVEL,a.FINISH_STATUS,
        a.RECIEVE_TIME,a.COMPLETION_NUM,a.IN_REVIEW_NUM,
        a.CREATE_TIME,a.UPDATE_TIME,a.IS_DELETED,
        a.IN_CYCLE_START_TIME,a.IN_CYCLE_END_TIME,a.TASK_ITEM_NO,a.DEAL_TIME,a.STATUS,a.VERSION,
        a.TASK_INFO_ID, a.TASK_STATUS, a.TASK_STATUS_START_TIME, a.TASK_STATUS_END_TIME, b.TASK_RANGE,
        b.REWARD_STOCK, b.PRE_EXPEND_STOCK, b.IN_FACT_EXPEND_STOCK, b.COST_FEE
    </sql>

    <update id="batchUpdateTaskStatus">
        update task_part_details
        <set>
            UPDATE_TIME = case
            <foreach collection="list" item="item" index="index">
                when id = #{item.id} then #{item.updateTime,jdbcType=TIMESTAMP}
            </foreach>
            end,
            TASK_STATUS = case
            <foreach collection="list" item="item" index="index">
                when id = #{item.id} then #{item.taskStatus,jdbcType=DECIMAL}
            </foreach>
            end,
            TASK_STATUS_START_TIME = case
            <foreach collection="list" item="item" index="index">
                when id = #{item.id} then #{item.taskStatusStartTime,jdbcType=TIMESTAMP}
            </foreach>
            end,
            TASK_STATUS_END_TIME = case
            <foreach collection="list" item="item" index="index">
                when id = #{item.id} then  #{item.taskStatusEndTime,jdbcType=TIMESTAMP}
            </foreach>
            end,
            COMPLETION_NUM = case
            <foreach collection="list" item="item" index="index">
                when id = #{item.id} then #{item.completionNum,jdbcType=DECIMAL}
            </foreach>
            end,
            FINISH_STATUS = case
            <foreach collection="list" item="item" index="index">
                when id = #{item.id} then #{item.finishStatus,jdbcType=DECIMAL}
            </foreach>
            end
        </set>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id}
        </foreach>

    </update>

    <update id="expireUserTask">
        update task_part_details
            set status = 1
        where status = 0
          and (IN_CYCLE_END_TIME is not null and IN_CYCLE_END_TIME <![CDATA[ <= ]]> now())
    </update>

    <select id="selectSwitchStatusTask" resultMap="BaseResultMap1">
        SELECT <include refid="Base_Column_List1"></include>
            FROM task_part_details a
            LEFT JOIN task_info b
            ON a.TASK_INFO_ID = b.ID
        WHERE a.IS_DELETED= 0 AND a.STATUS = 0 AND a.TASK_TEMP_TYPE = 7 AND a.TASK_STATUS = #{taskStatus}
          <if test="date != null">
              AND (a.TASK_STATUS_END_TIME is not null and a.TASK_STATUS_END_TIME <![CDATA[ <= ]]> #{date})
          </if>
          <if test="tempId != null and tempId != ''">
              AND a.TASK_TEMP_ID = #{tempId}
          </if>
          AND b.STATUS = 0 and b.IS_DELETED = 0
        order by a.create_time desc
    </select>

    <select id="selectSwitchStatusAlreadyOutTask"
            resultType="org.springcenter.backcenter.modules.entity.TaskPartDetailsInfo">
        SELECT a.TASK_INFO_ID as taskInfoId, b.REWARD_STOCK as rewardStock,
        b.PRE_EXPEND_STOCK as preExpendStock, b.IN_FACT_EXPEND_STOCK as inFactExpendStock, count(1) as count
        FROM task_part_details a
        LEFT JOIN task_info b
        ON a.TASK_INFO_ID = b.ID
        WHERE a.IS_DELETED= 0 AND a.STATUS = 0 AND a.TASK_TEMP_TYPE = 7 AND a.TASK_STATUS = #{taskStatus}
        AND (a.TASK_STATUS_END_TIME is not null and a.TASK_STATUS_END_TIME <![CDATA[ <= ]]> now())
        AND b.STATUS = 0 and b.IS_DELETED = 0
        group by a.TASK_INFO_ID, b.REWARD_STOCK, b.PRE_EXPEND_STOCK, b.IN_FACT_EXPEND_STOCK
    </select>

    <select id="selectSwitchStatusTaskAndDayIsNull" resultMap="BaseResultMap1">
        SELECT <include refid="Base_Column_List1"></include>
        FROM task_part_details a
        LEFT JOIN task_info b
        ON a.TASK_INFO_ID = b.ID
        WHERE a.IS_DELETED= 0 AND a.STATUS = 0 AND a.TASK_TEMP_TYPE = 7 AND a.TASK_STATUS = #{taskStatus}
        AND b.STATUS = 0 and b.IS_DELETED = 0
        AND a.TASK_STATUS_END_TIME is null
        order by a.create_time desc
    </select>

    <update id="switchTaskStatus">
        update task_part_details
        set TASK_STATUS = #{newStatus},
            UPDATE_TIME = now()
        where IS_DELETED = 0
        and STATUS = 0
        and TASK_TEMP_TYPE = 7
        and TASK_STATUS = #{oldStatus}
        and (TASK_STATUS_END_TIME is not null and TASK_STATUS_END_TIME <![CDATA[ <= ]]> now())
    </update>
</mapper>
