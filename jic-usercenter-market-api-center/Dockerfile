FROM jnbyharbor.jnby.com/base-images/baseimagejdk8:v1.5

MAINTAINER box-group

RUN mkdir -p /jic-usercenter-market/jic-usercenter-market-api-center

WORKDIR /jic-usercenter-market/jic-usercenter-market-api-center

EXPOSE 9300

EXPOSE 9999

COPY target/jic-usercenter-market-api-center.jar jic-usercenter-market-api-center.jar

#COPY --from=hengyunabc/arthas:latest /opt/arthas /opt/arthas

# 下载 Arthas
RUN wget https://alibaba.github.io/arthas/arthas-boot.jar -O arthas-boot.jar

# 暴露 Arthas 默认端口
EXPOSE 8563

ENV TZ='Asia/Shanghai'

ENTRYPOINT ["java", "-Xmx4g", "-Xms4g","-XX:NewRatio=3","-Xss512k", "-Xmn2g","-XX:SurvivorRatio=2", "-XX:+UseParallelGC","-Dreactor.netty.pool.leasingStrategy=lifo", "-jar", "jic-usercenter-market-api-center.jar"]

CMD ["--spring.profiles.active=prod"]
