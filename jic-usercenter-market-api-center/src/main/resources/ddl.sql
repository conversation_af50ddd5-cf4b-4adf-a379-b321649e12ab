
CREATE TABLE `ac_activity_conf_detail_goods` (
                                                 `ID` varchar(64) NOT NULL COMMENT '主键id',
                                                 `CARD_TYPE_ID` varchar(64) DEFAULT NULL COMMENT '卡id  (c_viptype id)',
                                                 `<PERSON>XCHANGE_POINT` int(11) DEFAULT NULL COMMENT '纯 积分兑换需要的积分',
                                                 `PAY_AMOUNT` decimal(8,2) DEFAULT NULL COMMENT '支付金额 （元）',
                                                 `PAY_POINT` int(11) DEFAULT NULL COMMENT '支付积分',
                                                 `AC_ACTIVITY_CONFIG_DETAILS_ID` varchar(64) DEFAULT NULL COMMENT '配置详情主键id',
                                                 `CARD_NAME` varchar(255) DEFAULT NULL COMMENT '卡名称',
                                                 PRIMARY KEY (`ID`),
                                                 KEY `IDX_AACD_AACDI` (`AC_ACTIVITY_CONFIG_DETAILS_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `ac_activity_config` (
                                      `ID` varchar(64) NOT NULL COMMENT '主键',
                                      `ACTIVITY_NAME` varchar(255) DEFAULT NULL COMMENT '活动名称',
                                      `ACTIVITY_TYPE` int(11) DEFAULT NULL COMMENT '活动类型  0  复购计划活动',
                                      `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
                                      `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间',
                                      `IS_DEL` int(11) DEFAULT NULL COMMENT '0 未删除  1 已删除',
                                      `REMARK` varchar(255) DEFAULT NULL COMMENT '备注',
                                      `ACTIVITY_START_TIME` bigint(20) DEFAULT NULL COMMENT '活动开始时间  仅到年月日',
                                      `ACTIVITY_END_TIME` bigint(20) DEFAULT NULL COMMENT '活动结束时间  仅到年月日',
                                      `ACTIVITY_RULE_IMG` varchar(255) DEFAULT NULL COMMENT '活动规则图片',
                                      `USE_RULE_IMG` varchar(255) DEFAULT NULL COMMENT '使用规则图片',
                                      `SHARE_IMG` varchar(255) DEFAULT NULL COMMENT '图片素材',
                                      `MINI_APP_IMG` varchar(255) DEFAULT NULL COMMENT '小程序卡片图',
                                      `IS_OPEN_ACTIVITY_SHARE` int(11) DEFAULT '1' COMMENT '是否开启活动分享  0 不开启  1 开启',
                                      `SHARE_IMG_TYPE` int(11) DEFAULT NULL COMMENT '图片素材类型  0 仅图片  1 带参数二维码',
                                      `MINI_FORWARD_FRI` int(11) DEFAULT '1' COMMENT '小程序端系统转发好友功能   0  不可转发  1 可转发',
                                      `MINI_SHARE_FRI` int(11) DEFAULT '1' COMMENT '小程序端系统分享朋友圈功能  0 不可分享  1 可分享',
                                      `MINI_APP_TITLE` varchar(255) DEFAULT NULL COMMENT '小程序卡片标题',
                                      `UPDATE_BY` varchar(50) DEFAULT NULL COMMENT '更新人',
                                      `SHARE_IMG_CONTENT` varchar(255) DEFAULT NULL COMMENT '如果图片素材类型为  带参数二维码 此字段有值',
                                      PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='活动配置表 ';

CREATE TABLE `ac_ask_support_goods` (
                                        `ID` varchar(64) NOT NULL COMMENT '主键',
                                        `UNIONID` varchar(64) DEFAULT NULL,
                                        `STORE_ID` int(11) DEFAULT NULL COMMENT '门店id',
                                        PRIMARY KEY (`ID`),
                                        KEY `IDX_AASG_U` (`UNIONID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `ai_create_img` (
                                 `ID` varchar(64) NOT NULL COMMENT '主键id',
                                 `IMG_URL` varchar(255) DEFAULT NULL COMMENT '图片url',
                                 `REMARK` varchar(500) DEFAULT NULL COMMENT '备注',
                                 `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
                                 `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间',
                                 `CHANNEL` varchar(255) DEFAULT NULL COMMENT '渠道   1000 管理后台  1001  box有搭',
                                 `CREATE_BY` varchar(255) DEFAULT NULL COMMENT '创建人',
                                 `IS_DEL` int(11) DEFAULT NULL COMMENT '0 未删除  1 已删除',
                                 PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `ai_create_img_details` (
                                         `ID` varchar(64) NOT NULL COMMENT '主键',
                                         `PRODUCT_CODE` varchar(255) DEFAULT NULL COMMENT '商品款号',
                                         `PRODUCT_ID` varchar(64) DEFAULT NULL COMMENT '商品id',
                                         `COLOR` varchar(255) DEFAULT NULL COMMENT '颜色编码',
                                         `PRODUCT_NAME` varchar(255) DEFAULT NULL COMMENT '商品名称',
                                         `PRODUCT_IMG_URL` varchar(255) DEFAULT NULL COMMENT '商品图片',
                                         `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
                                         `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间',
                                         `IS_DEL` int(11) DEFAULT '0' COMMENT '是否删除  0 未删除  1已删除',
                                         `AI_CREATE_IMG_ID` varchar(64) DEFAULT NULL COMMENT '关联主表id',
                                         `SKC` varchar(255) DEFAULT NULL COMMENT 'SKC',
                                         PRIMARY KEY (`ID`),
                                         KEY `IDX_ACID_PRODUCT_ID` (`PRODUCT_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `b_new_user_member_card` (
                                          `ID` varchar(64) NOT NULL,
                                          `USER_ID` varchar(64) DEFAULT NULL COMMENT '用户ID',
                                          `B_MEMBER_CARD_ID` varchar(64) DEFAULT NULL COMMENT '会员卡外键ID',
                                          `START_TIME` datetime DEFAULT NULL COMMENT '会员生效时间',
                                          `END_TIME` datetime DEFAULT NULL COMMENT '会员过期时间',
                                          `STATUS` int(11) DEFAULT '1' COMMENT '会员卡状态：1正常，2暂停，3禁用',
                                          `IS_DEL` int(11) DEFAULT '0' COMMENT '是否删除：0否，1是',
                                          `CREATE_TIME` datetime DEFAULT NULL,
                                          `UPDATE_TIME` datetime DEFAULT NULL,
                                          `CARD_TYPE` int(11) DEFAULT '1' COMMENT '卡类型：1正式，2体验',
                                          `PULL_STATUS` int(11) DEFAULT '0' COMMENT '订阅服务计划相关，默认为0，没有推送订阅服务计划，推送过后改为1',
                                          `OUT_NO` varchar(64) DEFAULT NULL COMMENT '开卡的outNo',
                                          `SUBSCRIBE_ID` varchar(64) DEFAULT NULL COMMENT '订阅id',
                                          `APPLICABLE_PARTY` int(11) DEFAULT NULL COMMENT '卡适用方类型',
                                          `UNIONID` varchar(64) DEFAULT NULL,
                                          PRIMARY KEY (`ID`),
                                          KEY `B_NEW_USER_USERID_INDEX` (`USER_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `b_new_user_rights_coupon` (
                                            `ID` varchar(64) NOT NULL COMMENT '主键',
                                            `RIGHTS_TYPE` int(11) DEFAULT NULL COMMENT '权益类型',
                                            `UNIONID` varchar(64) DEFAULT NULL COMMENT 'unionid',
                                            `STATUS` int(11) DEFAULT NULL COMMENT '权益状态：1正常，0禁用，2暂停,3已过期，4已用完',
                                            `COUPON_AMOUNT` varchar(255) DEFAULT NULL COMMENT '优惠券金额',
                                            `AWARDID` varchar(255) DEFAULT NULL COMMENT '优惠券awardid',
                                            `NUM` int(11) DEFAULT NULL COMMENT '优惠券发送数量',
                                            `CALC_PARAM` varchar(2000) DEFAULT NULL COMMENT '模拟计算参数 （膨胀计算）',
                                            `OUT_NO` varchar(64) DEFAULT NULL COMMENT '外部来源ID',
                                            `B_RIGHTS_ID` varchar(64) DEFAULT NULL COMMENT '模板ID',
                                            `COUPON_JSON_ID` varchar(64) DEFAULT NULL COMMENT 'B_RIGHTS表中json串中的id 唯一id',
                                            `CREATE_TIME` datetime DEFAULT NULL,
                                            `UPDATE_TIME` datetime DEFAULT NULL,
                                            `IS_DEL` int(11) DEFAULT '0' COMMENT '是否删除：0否，1是',
                                            `SEND_STATUS` int(11) DEFAULT NULL COMMENT '0 未发送  1 发送中  2 已发送',
                                            `START_TIME` datetime DEFAULT NULL COMMENT '开始生效时间',
                                            `END_TIME` datetime DEFAULT NULL COMMENT '过期时间',
                                            `VOUCHER_NOS` varchar(2000) DEFAULT NULL COMMENT '优惠券nos',
                                            `CONSUME_OUT_NO` varchar(255) DEFAULT NULL COMMENT '外部消耗id',
                                            `USE_RULE_SNAPSHOT` varchar(2000) DEFAULT NULL COMMENT '快照',
                                            `IS_FIRST` int(11) DEFAULT '0' COMMENT '是否抽次  0 非首次  1 首次',
                                            `IS_EXPANSION` int(11) DEFAULT '0' COMMENT '是否膨胀  0 没膨胀  1 膨胀',
                                            PRIMARY KEY (`ID`),
                                            KEY `BNURC_OUT_NO` (`OUT_NO`),
                                            KEY `BNURC_UNIONID` (`UNIONID`),
                                            KEY `BNURC_END_TIME` (`END_TIME`),
                                            KEY `BNURC_START_TIME` (`START_TIME`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订阅3.0 膨胀券类权益';

CREATE TABLE `b_right_config` (
                                  `ID` varchar(36) NOT NULL COMMENT '主键ID',
                                  `GROUP_NAME` varchar(100) DEFAULT NULL COMMENT '组名称',
                                  `TYPE` varchar(36) DEFAULT NULL COMMENT '组类型',
                                  `RIGHT_TYPE` int(11) DEFAULT NULL COMMENT '权益类型',
                                  `IS_DEL` int(11) DEFAULT '0' COMMENT '是否删除  0 正常  1 删除',
                                  `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
                                  `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间',
                                  `STATUS` int(11) DEFAULT '1' COMMENT '0 禁用 1 正常'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `b_rights_customize_store` (
                                            `ID` varchar(64) NOT NULL COMMENT 'id',
                                            `B_RIGHTS_ID` varchar(64) DEFAULT NULL COMMENT 'B_RIGHTS 主键ID',
                                            `STORE_ID` varchar(64) DEFAULT NULL COMMENT '门店ID',
                                            `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
                                            `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间',
                                            `STORE_CODE` varchar(255) DEFAULT NULL COMMENT '门店编码',
                                            PRIMARY KEY (`ID`),
                                            KEY `b_rights_customize_store_B_RIGHTS_ID_IDX` (`B_RIGHTS_ID`,`STORE_ID`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `b_source_pos_template` (
                                         `ID` varchar(64) NOT NULL,
                                         `APP_ID` varchar(255) NOT NULL,
                                         `APP_NAME` varchar(255) DEFAULT NULL,
                                         `POS_MANAGER_ID` varchar(255) DEFAULT NULL,
                                         `POS_MANAGER_NAME` varchar(255) DEFAULT NULL,
                                         `IMAGE` varchar(255) DEFAULT NULL,
                                         `SORT_NO` int(11) DEFAULT NULL,
                                         `TITLE_TXT` varchar(255) DEFAULT NULL,
                                         `REMARK` varchar(2550) DEFAULT NULL,
                                         `ADAPT_PEOPLE_ID` varchar(255) DEFAULT NULL,
                                         `ADAPT_STORE_ID` varchar(255) DEFAULT NULL,
                                         `JUMP_TYPE` int(11) DEFAULT NULL,
                                         `SHOW_TIME_TYPE` int(11) DEFAULT NULL,
                                         `START_TIME` datetime DEFAULT NULL,
                                         `END_TIME` datetime DEFAULT NULL,
                                         `BIRTHDAY_TIME` int(11) DEFAULT NULL,
                                         `ENABLE_STATUS` int(11) DEFAULT '0',
                                         `CREATE_TIME` datetime DEFAULT NULL,
                                         `CREATE_BY` varchar(100) DEFAULT NULL,
                                         `UPDATE_BY` varchar(100) DEFAULT NULL,
                                         `UPDATE_TIME` datetime DEFAULT NULL,
                                         `DEL_FLAG` int(11) DEFAULT '0',
                                         `ADAPT_PEOPLE_NAME` varchar(255) DEFAULT NULL,
                                         `ADAPT_STORE_NAME` varchar(255) DEFAULT NULL,
                                         `POSITION` int(11) DEFAULT NULL,
                                         `MINI_APP_ID` varchar(255) DEFAULT NULL,
                                         `MINI_APP_PATH` varchar(255) DEFAULT NULL,
                                         `VIDEO_ID` varchar(255) DEFAULT NULL,
                                         `H5_LINK` varchar(255) DEFAULT NULL,
                                         `POP_TODAY_TIMES` int(11) DEFAULT NULL,
                                         `POP_ACTIVITY_TIMES` int(11) DEFAULT NULL COMMENT '活动弹出次数',
                                         `RECEIVE_CONFIG` longtext COMMENT '领取配置',
                                         `EXPOSITORY_CASE` longtext COMMENT '说明文案',
                                         PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `b_user_member_card_log` (
                                          `ID` varchar(64) NOT NULL COMMENT '主键',
                                          `OUT_NO` varchar(64) DEFAULT NULL COMMENT '外部订单号',
                                          `MEMBER_CARD_ID` varchar(64) DEFAULT NULL COMMENT '卡ID',
                                          `USER_ID` varchar(64) DEFAULT NULL COMMENT '用户ID',
                                          `USER_NAME` varchar(200) DEFAULT NULL COMMENT '用户名称',
                                          `CARD_NAME` varchar(200) DEFAULT NULL COMMENT '卡名称',
                                          `STATUS` int(11) DEFAULT '1' COMMENT '状态  0 未开通  1 开通',
                                          `CREATE_TIME` datetime DEFAULT NULL,
                                          `UPDATE_TIME` datetime DEFAULT NULL,
                                          `CREATE_BY` varchar(255) DEFAULT NULL COMMENT '创建人',
                                          `REMARK` varchar(255) DEFAULT NULL COMMENT '备注',
                                          PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `b_user_member_card_renew_log` (
                                                `ID` varchar(64) NOT NULL,
                                                `USER_ID` varchar(64) DEFAULT NULL COMMENT '用户ID',
                                                `ORDER_ID` varchar(64) DEFAULT NULL COMMENT '交易订单号',
                                                `B_MEMBER_CARD_ID` varchar(64) DEFAULT NULL COMMENT '会员卡ID',
                                                `PAYMENT_TYPE` int(11) DEFAULT NULL COMMENT '支付类型:1普通支付，2积分兑换',
                                                `CREATE_DATE` datetime DEFAULT NULL COMMENT '创建时间',
                                                `RENEW_DAY` int(11) DEFAULT NULL COMMENT '续费天数',
                                                `PAYMENT_PRICE` int(11) DEFAULT NULL COMMENT '支付价格',
                                                `DISCOUNT_COIN` int(11) DEFAULT NULL COMMENT '金币抵扣数',
                                                PRIMARY KEY (`ID`),
                                                KEY `b_user_member_card_renew_log_USER_ID_IDX` (`USER_ID`) USING BTREE,
                                                KEY `b_user_member_card_renew_log_ORDER_ID_IDX` (`ORDER_ID`) USING BTREE,
                                                KEY `b_user_member_card_renew_log_B_MEMBER_CARD_ID_IDX` (`B_MEMBER_CARD_ID`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `b_user_rights_material` (
                                          `ID` varchar(64) NOT NULL,
                                          `USER_ID` varchar(64) DEFAULT NULL COMMENT '用户ID',
                                          `B_RIGHTS_ID` varchar(64) DEFAULT NULL COMMENT '模板ID',
                                          `IS_DEL` int(11) DEFAULT '0' COMMENT '是否删除  0 正常   1 删除',
                                          `CREATE_TIME` datetime DEFAULT NULL,
                                          `UPDATE_TIME` datetime DEFAULT NULL,
                                          `ACTIVITY_ID` varchar(64) DEFAULT NULL COMMENT '活动ID',
                                          `STATUS` int(11) DEFAULT '1' COMMENT '权益状态：1正常，0禁用，2暂停,3已过期，4已用完',
                                          `OUT_NO` varchar(64) DEFAULT NULL COMMENT '外部ID',
                                          `START_TIME` datetime DEFAULT NULL,
                                          `END_TIME` datetime DEFAULT NULL,
                                          `UNIONID` varchar(64) DEFAULT NULL,
                                          PRIMARY KEY (`ID`),
                                          KEY `idxx_out_no` (`OUT_NO`),
                                          KEY `idxx_user_id` (`USER_ID`),
                                          KEY `idxx_b_rights_id` (`B_RIGHTS_ID`),
                                          KEY `idxx_start_end_time` (`START_TIME`,`END_TIME`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `badge_detail` (
                                `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增主键',
                                `biz` varchar(64) NOT NULL COMMENT '业务ID',
                                `badge_theme_id` int(10) unsigned NOT NULL COMMENT '主题ID',
                                `badge_theme_name` varchar(64) NOT NULL COMMENT '主题名称',
                                `name` varchar(64) NOT NULL COMMENT '勋章名称',
                                `image_url` varchar(255) NOT NULL COMMENT '勋章图路径',
                                `intro_image_url` varchar(255) DEFAULT NULL COMMENT '勋章简介图路径',
                                `detail_image_url` varchar(255) DEFAULT NULL COMMENT '勋章详情图路径',
                                `start_time` datetime NOT NULL COMMENT '开始时间',
                                `end_time` datetime NOT NULL COMMENT '结束时间',
                                `product_package_id` varchar(64) DEFAULT NULL COMMENT '商品包ID，为空代表全部商品',
                                `crowd_package_id` varchar(64) DEFAULT NULL COMMENT '人群包ID',
                                `sort` int(10) DEFAULT '0' COMMENT '排序值（越大越靠前）',
                                `order_type_wsc` tinyint(1) DEFAULT '0' COMMENT '微商城 是否勾选',
                                `order_type_xxdd` tinyint(1) DEFAULT '0' COMMENT '线下订单 是否勾选',
                                `order_type_box` tinyint(1) DEFAULT '0' COMMENT 'BOX 是否勾选',
                                `buy_jump_url_wsc` varchar(255) DEFAULT NULL COMMENT '微商城 购买链接',
                                `buy_jump_url_xxdd` varchar(255) DEFAULT NULL COMMENT '线下订单 购买链接',
                                `buy_jump_url_box` varchar(255) DEFAULT NULL COMMENT 'BOX 购买链接',
                                `miniapp_wsc` varchar(255) DEFAULT NULL COMMENT '参活小程序。多个逗号分割',
                                `send_msg` tinyint(1) DEFAULT '0' COMMENT '是否发消息，默认不发',
                                `is_delete` tinyint(1) DEFAULT '0' COMMENT '是否删除（0否，1是）',
                                `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                `create_person` varchar(64) DEFAULT NULL COMMENT '创建人',
                                PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `badge_opt_log` (
                                 `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增主键',
                                 `biz` varchar(64) NOT NULL COMMENT '业务ID',
                                 `OPT_TYPE` int(4) DEFAULT NULL COMMENT '操作类型',
                                 `OPT_PERSON` varchar(100) DEFAULT NULL COMMENT '操作人：system=系统, xxx=人工',
                                 `OPT_CONTENT` text COMMENT '操作内容json',
                                 `ORIGIN_CONTENT` text COMMENT '原内容json',
                                 `is_delete` tinyint(1) DEFAULT '0' COMMENT '是否删除（0否，1是）',
                                 `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                 `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                 PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `badge_theme` (
                               `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增主键',
                               `biz` varchar(64) NOT NULL COMMENT '业务ID',
                               `name` varchar(64) NOT NULL COMMENT '主题名称',
                               `description` text COMMENT '说明文案',
                               `sort` int(11) NOT NULL COMMENT '排序：数字越大越靠前',
                               `rule_img` varchar(256) DEFAULT NULL COMMENT '规则图片',
                               `is_delete` tinyint(1) DEFAULT '0' COMMENT '是否删除（0否，1是）',
                               `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                               `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                               `type` int(4) DEFAULT '1' COMMENT '1=下单得',
                               PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `bus_events` (
                              `RECORD_ID` bigint(20) NOT NULL AUTO_INCREMENT,
                              `CLASS_NAME` varchar(128) NOT NULL COMMENT '类名',
                              `USER_TOKEN` varchar(36) DEFAULT NULL COMMENT '识别态',
                              `CREATED_DATE` datetime NOT NULL COMMENT '创建时间',
                              `CREATING_OWNER` varchar(50) NOT NULL COMMENT '创建者',
                              `PROCESSING_OWNER` varchar(50) DEFAULT NULL,
                              `PROCESSING_AVAILABLE_DATE` datetime DEFAULT NULL,
                              `ERROR_COUNT` int(11) DEFAULT '0',
                              `PROCESSING_STATE` varchar(14) DEFAULT 'AVAILABLE',
                              `SEARCH_KEY1` bigint(20) DEFAULT NULL,
                              `SEARCH_KEY2` bigint(20) DEFAULT NULL,
                              `EVENT_JSON` longtext,
                              PRIMARY KEY (`RECORD_ID`),
                              KEY `IDX_BUS_WHERE` (`PROCESSING_STATE`,`PROCESSING_OWNER`,`PROCESSING_AVAILABLE_DATE`),
                              KEY `IDX_BUS_RECORD` (`SEARCH_KEY2`,`SEARCH_KEY1`),
                              KEY `ids_BUS_EVENTS_RECORD_ID_1` (`RECORD_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `bus_events_history` (
                                      `RECORD_ID` bigint(20) NOT NULL AUTO_INCREMENT,
                                      `CLASS_NAME` varchar(128) NOT NULL,
                                      `USER_TOKEN` varchar(36) DEFAULT NULL,
                                      `CREATED_DATE` datetime NOT NULL,
                                      `CREATING_OWNER` varchar(50) NOT NULL,
                                      `PROCESSING_OWNER` varchar(50) DEFAULT NULL,
                                      `PROCESSING_AVAILABLE_DATE` datetime DEFAULT NULL,
                                      `PROCESSING_STATE` varchar(14) DEFAULT 'AVAILABLE',
                                      `ERROR_COUNT` int(11) DEFAULT '0',
                                      `SEARCH_KEY1` bigint(20) DEFAULT NULL,
                                      `SEARCH_KEY2` bigint(20) DEFAULT NULL,
                                      `EVENT_JSON` longtext,
                                      PRIMARY KEY (`RECORD_ID`),
                                      KEY `IDX_BUS_HISTORY_RECORD` (`SEARCH_KEY2`,`SEARCH_KEY1`),
                                      KEY `ids_BUS_EVENTS_HISTORY_RECORD_ID_2` (`RECORD_ID`)
) ENGINE=InnoDB AUTO_INCREMENT=380293 DEFAULT CHARSET=utf8mb4;

CREATE TABLE `call_scene_config` (
                                     `ID` varchar(64) NOT NULL COMMENT 'ID',
                                     `CONFIG_NAME` varchar(255) DEFAULT NULL COMMENT '名称',
                                     `CONFIG_KEY` varchar(255) DEFAULT NULL COMMENT 'key',
                                     `CONFIG_VALUE` varchar(255) DEFAULT NULL COMMENT 'value',
                                     `CONFIG_CODE` varchar(255) DEFAULT NULL COMMENT '编码',
                                     PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='场景可选字段配置表';

CREATE TABLE `cart_send_wxopen_log` (
                                        `ID` varchar(64) NOT NULL COMMENT '主键',
                                        `WID` varchar(64) DEFAULT NULL COMMENT 'wid',
                                        `ACTIVITY_ID` varchar(64) DEFAULT NULL COMMENT '活动id',
                                        `SEND_TIME` varchar(64) DEFAULT NULL COMMENT '发送时间',
                                        `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
                                        `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间',
                                        `IS_DEL` int(11) DEFAULT '0' COMMENT '0 未删除 1 已删除',
                                        `WEID` varchar(64) DEFAULT NULL COMMENT 'weid',
                                        `WEIMO_SKU_ID` varchar(64) DEFAULT NULL COMMENT '微盟skuid',
                                        `BOJUN_SKU_ID` varchar(64) DEFAULT NULL COMMENT '伯俊skuid',
                                        `VID` varchar(64) DEFAULT NULL COMMENT 'VID',
                                        `WXOPENID` varchar(64) DEFAULT NULL COMMENT '公众号openid',
                                        `PRODUCT_ID` varchar(64) DEFAULT NULL COMMENT '商品id',
                                        PRIMARY KEY (`ID`),
                                        KEY `WSW_IDX_CSWL` (`WID`,`SEND_TIME`,`WEID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `less_book_config` (
                                    `ID` varchar(64) NOT NULL COMMENT '主键',
                                    `IS_SHOW` int(11) DEFAULT NULL COMMENT '0 不展示  1 展示',
                                    `IMG_URL` varchar(255) DEFAULT NULL COMMENT '图片链接地址',
                                    `BUTTON_ONE_JUMP_TYPE` int(11) DEFAULT NULL COMMENT '跳转类型  0  小程序  1 H5',
                                    `BUTTON_ONE_APPID` varchar(64) DEFAULT NULL COMMENT '小程序appid',
                                    `BUTTON_ONE_JUMP_PATH` varchar(255) DEFAULT NULL COMMENT '小程序路径或者h5路径',
                                    `CREATE_TIME` datetime DEFAULT NULL,
                                    `UPDATE_TIME` datetime DEFAULT NULL,
                                    `BUTTON_TWO_JUMP_TYPE` int(11) DEFAULT NULL COMMENT '跳转类型  0  小程序  1 H5',
                                    `BUTTON_TWO_APPID` varchar(64) DEFAULT NULL COMMENT '小程序appid',
                                    `BUTTON_TWO_JUMP_PATH` varchar(255) DEFAULT NULL COMMENT '小程序路径或者h5路径',
                                    `BUTTON_ONE_TEXT` varchar(255) DEFAULT NULL COMMENT '按钮1文案',
                                    `BUTTON_TWO_TEXT` varchar(255) DEFAULT NULL COMMENT '按钮2文案',
                                    PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `member_card_style` (
                                     `ID` varchar(64) NOT NULL COMMENT '主键',
                                     `TYPE` int(11) DEFAULT NULL COMMENT '类型1 银卡 2金卡 3历史白金卡 0生日卡背景',
                                     `IMG` varchar(500) DEFAULT NULL COMMENT '图片链接',
                                     `IS_DELETED` int(11) DEFAULT '0' COMMENT '是否删除 0正常 1删除',
                                     `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
                                     `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间',
                                     PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='品牌会员卡背景';

CREATE TABLE `people_community_comment` (
                                            `USER_ID` varchar(255) DEFAULT NULL,
                                            `COMMENT_USER_ID` varchar(255) DEFAULT NULL,
                                            `COMMENT_WEID` varchar(255) DEFAULT NULL,
                                            `LIKE_USER_ID` varchar(255) DEFAULT NULL,
                                            `LIKE_WEID` varchar(255) DEFAULT NULL,
                                            `FOCUS_ID` varchar(255) DEFAULT NULL,
                                            `FOCUS_WEID` varchar(100) DEFAULT NULL,
                                            `COMMENT_USER_NAME` varchar(255) DEFAULT NULL,
                                            `LIKE_USER_NAME` varchar(255) DEFAULT NULL,
                                            `FOCUS_NAME` varchar(255) DEFAULT NULL,
                                            `UNIONID` varchar(255) DEFAULT NULL,
                                            `CREATE_TIME` datetime DEFAULT NULL,
                                            `CREATE_BY` varchar(100) DEFAULT NULL,
                                            `UPDATE_BY` varchar(100) DEFAULT NULL,
                                            `UPDATE_TIME` datetime DEFAULT NULL,
                                            `DEL_FLAG` int(11) DEFAULT '0',
                                            `TYPE` int(11) DEFAULT NULL,
                                            `WEID` varchar(255) DEFAULT NULL,
                                            `NICK_NAME` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `ques_answer` (
                               `ID` varchar(64) NOT NULL COMMENT '主键',
                               `QUESTION_ID` varchar(64) DEFAULT NULL COMMENT '问题id',
                               `ANSWER` varchar(255) DEFAULT NULL COMMENT '答案 中文',
                               `STAR` varchar(64) DEFAULT NULL COMMENT '星级',
                               `IS_DEL` int(11) DEFAULT '0' COMMENT '0 未删除  1 已删除',
                               `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
                               `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间',
                               `ORDER_SORT` int(11) DEFAULT NULL COMMENT '按照前台的顺序进行排序 传递过来的顺序',
                               PRIMARY KEY (`ID`),
                               KEY `QA_QI_IDX` (`QUESTION_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='问卷调查 - 答案';

CREATE TABLE `ques_question_template_detail` (
                                                 `ID` varchar(64) NOT NULL COMMENT '主键id',
                                                 `QUESTION_TEMPLATE_ID` varchar(64) DEFAULT NULL COMMENT '问卷模版id',
                                                 `SUIT_PARAM` varchar(255) DEFAULT NULL COMMENT '适用类型  如果是小程序  则里面是 小程序的weid  问卷表1 v 多',
                                                 `IS_DEL` int(11) DEFAULT '0' COMMENT '0 未删除 1 已删除',
                                                 `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
                                                 `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间',
                                                 PRIMARY KEY (`ID`),
                                                 KEY `QQTD_QTI_IDX` (`QUESTION_TEMPLATE_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='问卷关联适用方表 (小程序  后台 等等)';

CREATE TABLE `ques_scene` (
                              `ID` varchar(64) NOT NULL COMMENT '主键',
                              `NAME` varchar(255) DEFAULT NULL COMMENT '场景名称',
                              `SUIT_PLAFORM` varchar(64) DEFAULT NULL COMMENT '适用平台   0  微商城小程序  1 POS+  2 搭配师后台    多个以英文逗号分隔',
                              `RECORD_MSG` varchar(255) DEFAULT NULL COMMENT '记录字段',
                              `SCENE_CODE` varchar(255) DEFAULT NULL COMMENT '场景码   VOUCHER_CODE  券类 ，RIGHTS_CODE 权益类 ， ORDER_CODE  订单类  ， FREE_CODE  自由问卷， AI_CODE  AI生图， RESERVATION_CODE  预约试衣',
                              `STATUS` int(11) DEFAULT NULL COMMENT '0  禁用  1 启用',
                              `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
                              `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间',
                              `IS_DEL` int(11) DEFAULT '0' COMMENT '是否删除  0 未删除  1 删除',
                              PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='问题反馈 场景表';

CREATE TABLE `ques_user_answer` (
                                    `ID` varchar(64) NOT NULL COMMENT '主键id',
                                    `QUESTION_ID` varchar(64) DEFAULT NULL COMMENT '问卷id',
                                    `ANSWER_ID` varchar(64) DEFAULT NULL COMMENT '答案id',
                                    `BIZ_USER_ID` varchar(64) DEFAULT NULL COMMENT '用户unionid 或者 员工id',
                                    `CONTENT` varchar(255) DEFAULT NULL COMMENT '答案内容',
                                    `SUIT_PARAM` varchar(255) DEFAULT NULL COMMENT '适用类型  如果是小程序  则里面是 小程序的weid 其他则为 pos+的类型或者 搭配师后台类型',
                                    `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
                                    `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间',
                                    `IS_DEL` int(11) DEFAULT '0' COMMENT '0 未删除  1 已删除',
                                    `QUESTION_TEMPLATE_ID` varchar(64) DEFAULT NULL COMMENT '问卷模版id',
                                    `RECORD_MSG` varchar(255) DEFAULT NULL COMMENT '记录业务参数',
                                    `BIZ_ID` varchar(255) DEFAULT NULL COMMENT '业务id',
                                    PRIMARY KEY (`ID`),
                                    KEY `QUA_QI_IDX` (`QUESTION_ID`),
                                    KEY `QUA_BUI_IDX` (`BIZ_USER_ID`),
                                    KEY `QUA_QTI_IDX` (`QUESTION_TEMPLATE_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户提交答案表';

CREATE TABLE `r_user_click_rights_log` (
                                           `ID` varchar(64) NOT NULL,
                                           `B_RIGHTS_ID` varchar(64) DEFAULT NULL,
                                           `UNIONID` varchar(64) DEFAULT NULL,
                                           `IS_DEL` int(11) DEFAULT '0',
                                           `CREATE_TIME` datetime DEFAULT NULL,
                                           `UPDATE_TIME` datetime DEFAULT NULL,
                                           PRIMARY KEY (`ID`),
                                           KEY `r_unionid_idx` (`UNIONID`),
                                           KEY `r_bRightsId_idx` (`B_RIGHTS_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `r_user_expand` (
                                 `ID` varchar(64) NOT NULL COMMENT '主键',
                                 `UNIONID` varchar(64) DEFAULT NULL,
                                 `C_CLIENT_VIP_ID` bigint(20) DEFAULT NULL COMMENT 'c_client_vip 表id',
                                 `WEID` varchar(64) DEFAULT NULL COMMENT 'weid',
                                 `INTIMACY` int(11) DEFAULT NULL COMMENT '亲密值',
                                 `CONSUME_POINT_MONTH` decimal(15,2) DEFAULT NULL COMMENT '6个月积分消费记录',
                                 `CREATE_TIME` datetime DEFAULT NULL,
                                 `UPDATE_TIME` datetime DEFAULT NULL,
                                 PRIMARY KEY (`ID`),
                                 UNIQUE KEY `vipid_idx` (`C_CLIENT_VIP_ID`),
                                 UNIQUE KEY `unionidweid_idx` (`UNIONID`,`WEID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `r_viptype` (
                             `ID` int(11) NOT NULL,
                             `AD_CLIENT_ID` int(11) DEFAULT NULL,
                             `AD_ORG_ID` int(11) DEFAULT NULL,
                             `ISACTIVE` char(1) DEFAULT 'Y',
                             `MODIFIERID` int(11) DEFAULT NULL,
                             `CREATIONDATE` datetime DEFAULT NULL,
                             `MODIFIEDDATE` datetime DEFAULT NULL,
                             `OWNERID` int(11) DEFAULT NULL,
                             `NAME` varchar(255) DEFAULT NULL,
                             `DESCRIPTION` varchar(255) DEFAULT NULL,
                             `DISCOUNT` decimal(10,2) DEFAULT NULL,
                             `INTEGRALRATE` decimal(16,4) DEFAULT NULL,
                             `REDISCOUNT` decimal(10,2) DEFAULT NULL,
                             `CANUPGRADE` char(1) DEFAULT NULL,
                             `C_VIPTYPEUP_ID` int(11) DEFAULT NULL,
                             `NEEDINTG` int(11) DEFAULT NULL,
                             `CHECKOFFINTG` int(11) DEFAULT NULL,
                             `DEFAULTVALID` int(11) DEFAULT '12',
                             `DBINTDAY` char(1) DEFAULT NULL,
                             `DBINTDNUM` decimal(14,2) DEFAULT NULL,
                             `DBINTMON` char(1) DEFAULT NULL,
                             `DBINTMNUM` decimal(14,2) DEFAULT NULL,
                             `CODE` varchar(20) DEFAULT NULL,
                             `INTL_ONCE_UP` int(11) DEFAULT NULL,
                             `INTL_HALFYEAR_UP` int(11) DEFAULT NULL,
                             `INTL_YEAR_UP` int(11) DEFAULT NULL,
                             `INTL_ONCE_LST` int(11) DEFAULT NULL,
                             `INTL_HALFYEAR_LST` int(11) DEFAULT NULL,
                             `INTL_YEAR_LST` int(11) DEFAULT NULL,
                             `INTL_GREATER_OFF` int(11) DEFAULT NULL,
                             `INTL_SMALLER_OFF` int(11) DEFAULT NULL,
                             `INTL_TWOYEAR_UP` int(11) DEFAULT NULL,
                             `INTL_TWOYEAR_LST` int(11) DEFAULT NULL,
                             `INTL_TYGREATER_OFF` int(11) DEFAULT NULL,
                             `INTL_TYSMALLER_OFF` int(11) DEFAULT NULL,
                             `DEFAULTVALID_INTG` int(11) DEFAULT '365',
                             `INTL_ONCE_UP2` int(11) DEFAULT NULL,
                             `INTL_YEAR_UP2` int(11) DEFAULT NULL,
                             `INTL_TWOYEAR_UP2` int(11) DEFAULT NULL,
                             `INTL_GREATER_OFF2` int(11) DEFAULT NULL,
                             `INTL_SMALLER_OFF2` int(11) DEFAULT NULL,
                             `INTL_TYGREATER_OFF2` int(11) DEFAULT NULL,
                             `INTL_TYSMALLER_OFF2` int(11) DEFAULT NULL,
                             `DEFAULTVALID_UPINTG` int(11) DEFAULT '365',
                             `BRITHDISATM` decimal(12,2) DEFAULT NULL,
                             `BRITHDAYDIS` decimal(12,2) DEFAULT NULL,
                             `ISSHARE_BIRTHDAY` int(11) DEFAULT '1',
                             `VALIDDAILY_INTG` int(11) DEFAULT NULL,
                             `VALIDDAILY_UPINTG` int(11) DEFAULT NULL,
                             `VALID_DAILY` int(11) DEFAULT NULL,
                             `XKVALIDDAILY_UPINTG` int(11) DEFAULT NULL,
                             `XKVALIDDAILY_INTG` int(11) DEFAULT NULL,
                             `XKVALID_DAILY` int(11) DEFAULT NULL,
                             `INTEGRAL_UPDEAL` int(11) DEFAULT NULL,
                             `INTEGRAL_DEAL` char(1) DEFAULT NULL,
                             `IFCHARGE` char(1) DEFAULT NULL,
                             `IS_LIMITMININTL` char(1) DEFAULT 'N',
                             `DE_VALIDDAILY_OFFINTG` int(11) DEFAULT NULL,
                             `DE_VALIDDAILY_INTG` int(11) DEFAULT NULL,
                             `DE_VALIDDAILY_OFF` int(11) DEFAULT NULL,
                             `IS_PASSWORD_CHECK` char(1) DEFAULT 'N',
                             `BRITHDAYQTY` int(11) DEFAULT NULL,
                             `MON1` int(11) DEFAULT NULL,
                             `INTL_MON1_UP` int(11) DEFAULT NULL,
                             `MON2` int(11) DEFAULT NULL,
                             `INTL_MON2_UP` int(11) DEFAULT NULL,
                             `IS_LIMITMIN` char(1) DEFAULT 'N',
                             `WECHATFANS` char(1) DEFAULT 'N',
                             `IS_AUTOACTIVE` char(1) DEFAULT 'N',
                             `IS_SMSCODE` char(1) DEFAULT 'Y',
                             `ISSCAN` char(1) DEFAULT 'N',
                             `DISCOUNT_LIMIT` decimal(14,2) DEFAULT NULL,
                             `C_VIP_DALEI` int(11) DEFAULT NULL,
                             `C_CONSUMEAREA_ID` int(11) DEFAULT NULL,
                             `SUBACCOUNT_DEFAULT_TYPE` char(1) DEFAULT NULL,
                             `IS_LIST_LIMIT` char(1) DEFAULT NULL,
                             PRIMARY KEY (`ID`),
                             UNIQUE KEY `IDXVIPTYPECODE` (`AD_CLIENT_ID`,`CODE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `red_dot_record` (
                                  `ID` varchar(64) NOT NULL,
                                  `APP_ID` varchar(255) NOT NULL,
                                  `UNIONID` varchar(255) DEFAULT NULL,
                                  `RED_DOT_TYPE` int(11) DEFAULT NULL COMMENT '红点类型（0先试后买 1亲密值 2积分签到）',
                                  `ACTIVITY_ID` varchar(255) DEFAULT '0',
                                  `DAY_FORMAT` varchar(100) DEFAULT NULL,
                                  `CREATE_TIME` datetime DEFAULT NULL,
                                  `CREATE_BY` varchar(100) DEFAULT NULL,
                                  `UPDATE_BY` varchar(100) DEFAULT NULL,
                                  `UPDATE_TIME` datetime DEFAULT NULL,
                                  `DEL_FLAG` int(11) DEFAULT '0',
                                  `BRAND_ID` varchar(255) DEFAULT NULL,
                                  PRIMARY KEY (`ID`),
                                  KEY `RED_DOT_TYPE_INDEX` (`RED_DOT_TYPE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `redis_cache_log` (
                                   `REDIS_CACHE_LOG_ID` varchar(64) NOT NULL,
                                   `KEY` varchar(255) DEFAULT NULL,
                                   `VALUE` varchar(255) DEFAULT NULL,
                                   `IS_DEAL` int(11) DEFAULT NULL,
                                   `CREATE_TIME` datetime DEFAULT NULL,
                                   `UPDATE_TIME` datetime DEFAULT NULL,
                                   PRIMARY KEY (`REDIS_CACHE_LOG_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `rights_member_card_relation` (
                                               `ID` varchar(64) NOT NULL COMMENT '主键id',
                                               `MEMBER_CARD_ID` varchar(64) DEFAULT NULL COMMENT '权益包id',
                                               `RIGHTS_ID` varchar(64) DEFAULT NULL COMMENT '权益id',
                                               `CREATE_TIME` datetime DEFAULT NULL,
                                               `UPDATE_TIME` datetime DEFAULT NULL,
                                               `IS_DEL` int(11) DEFAULT '0' COMMENT '是否删除  0未删除  1 删除',
                                               PRIMARY KEY (`ID`),
                                               KEY `rights_member_card_relation_MEMBER_CARD_ID_IDX` (`MEMBER_CARD_ID`,`RIGHTS_ID`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `send_material_coupon_log` (
                                            `ID` varchar(64) NOT NULL COMMENT '主键',
                                            `OUT_ID` varchar(255) DEFAULT NULL COMMENT '外部关联id  可以关联发送券和实物表的id',
                                            `NUM` int(11) DEFAULT NULL COMMENT '数量',
                                            `NAME` varchar(255) DEFAULT NULL COMMENT '名称',
                                            `SKU` varchar(255) DEFAULT NULL COMMENT 'SKU码',
                                            `TYPE` int(11) DEFAULT NULL COMMENT '0  优惠券   1 实物',
                                            `AWARDID` varchar(255) DEFAULT NULL COMMENT '优惠券id',
                                            `PRICE` varchar(255) DEFAULT NULL COMMENT '金额 优惠券为优惠券金额  实物为实物金额',
                                            `CREATE_TIME` datetime DEFAULT NULL,
                                            `UPDATE_TIME` datetime DEFAULT NULL,
                                            `IS_DEL` int(11) DEFAULT '0' COMMENT '0 未删除  1删除',
                                            `VOUCHERNOS` varchar(255) DEFAULT NULL,
                                            PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `sync_ai_call_task` (
                                     `ID` varchar(64) NOT NULL COMMENT '主键id',
                                     `TASK_ID` varchar(128) DEFAULT NULL COMMENT '任务id',
                                     `TASK_NAME` varchar(255) DEFAULT NULL COMMENT '任务名称',
                                     `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
                                     `IMPORT_TASK_CALL_NUM` int(11) DEFAULT NULL COMMENT '运营在任务中手动导入的用户名单数量',
                                     `CALL_NUM` int(11) DEFAULT NULL COMMENT '已呼数量',
                                     `UN_CALL_NUM` int(11) DEFAULT NULL COMMENT '未呼数',
                                     `FILTERED_NUM` int(11) DEFAULT NULL COMMENT '拦截数量',
                                     `HANGUP_NUM` int(11) DEFAULT NULL COMMENT '挂机通话数',
                                     `HIGH_INTENTION_NUM` int(11) DEFAULT NULL COMMENT '高意向电话通数',
                                     `DISTINCT_USER` int(11) DEFAULT NULL COMMENT '去重用户数',
                                     `DISTINCT_USER_REPLY` int(11) DEFAULT NULL COMMENT '去重接听用户数',
                                     `DISTINCT_USER_HANGUP` int(11) DEFAULT NULL COMMENT '去重用户挂机人数',
                                     `DISTINCT_USER_HIGH_INTENTION` int(11) DEFAULT NULL COMMENT '去重高意向用户数',
                                     `AVG_CALL_TIME` int(11) DEFAULT NULL COMMENT '有效通话的平均通话时长(秒)',
                                     `TOTAL_CALL_TIME` int(11) DEFAULT NULL COMMENT '有效通话总时长(秒)',
                                     PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `sync_ai_call_task_detail` (
                                            `ID` varchar(64) NOT NULL COMMENT '主键id',
                                            `TASK_ID` int(11) DEFAULT NULL COMMENT '任务id',
                                            `CALL_ID` varchar(64) DEFAULT NULL COMMENT '通话id uuid',
                                            `TASK_CALL_ID` int(11) DEFAULT NULL COMMENT '10位数id（和现在网易后台数据看板里的一致）',
                                            `USER_NAME` varchar(255) DEFAULT NULL COMMENT '用户名',
                                            `PHONE` varchar(255) DEFAULT NULL COMMENT '手机号',
                                            `IMPORT_TIME` datetime DEFAULT NULL COMMENT '导入时间',
                                            `CALL_TIME` datetime DEFAULT NULL COMMENT '呼叫开始时间',
                                            `REPLY_STATUS` varchar(255) DEFAULT NULL COMMENT 'ANSWER("已接听"),NO_REPLY("无应答"),BUSY("忙线中"),CLOSE("关机"),SHUT_DOWN("停机"),REJECT("拒接"),NOT_EXIST("空号"),UN_CONNECTED("无法接通"),OWE_MONEY("欠费"),OUT_CALL_FAIL("外呼失败"),CALL_ING("呼叫中"),NO_CALLBACK("外呼失败(无挂断回调)")',
                                            `HANG_UP_STATUS` varchar(255) DEFAULT NULL COMMENT 'DEFAULT("未知"),CUSTOMER_HANGUP("客户挂断"),AI_HANGUP( "AI挂断")',
                                            `CALL_DURATION` int(11) DEFAULT NULL COMMENT '通话时长(单位：秒)',
                                            `RING_DURATION` varchar(255) DEFAULT NULL COMMENT '响铃时长  秒',
                                            `LINE_NUMBER` varchar(255) DEFAULT NULL COMMENT '外呼电话的号码',
                                            `RECALL_NUM` int(11) DEFAULT NULL COMMENT '重播次数',
                                            `ORI_CALL_ID` varchar(64) DEFAULT NULL COMMENT '关联的原始CallId，重播的记录存在此字段',
                                            `INTENT_TAG_ID` int(11) DEFAULT NULL COMMENT '意向标签id',
                                            `INTENT_TAG_GRADE` varchar(255) DEFAULT NULL COMMENT '意向标签等级',
                                            `INTENT_TAG_GRADE_DESC` varchar(255) DEFAULT NULL COMMENT '意向标签等级描述',
                                            PRIMARY KEY (`ID`),
                                            KEY `SACTD_TI_IDX` (`TASK_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- jnby_usercenter_market.task_part_details definition

CREATE TABLE `task_part_details` (
                                     `ID` varchar(100) NOT NULL COMMENT '主键',
                                     `UNIONID` varchar(100) DEFAULT NULL,
                                     `TASK_ITEM_NO` varchar(200) DEFAULT NULL COMMENT '任务明细编号',
                                     `TASK_TEMP_ID` varchar(200) DEFAULT NULL COMMENT '任务模板id',
                                     `TASK_TEMP_TYPE` int(11) DEFAULT NULL COMMENT '任务类型0:签到 1:订单评价  2:社区发帖  3:自定义',
                                     `WEID` varchar(200) DEFAULT NULL COMMENT '小程序品牌',
                                     `NICKNAME` varchar(200) DEFAULT NULL COMMENT '昵称',
                                     `PHONE` varchar(11) DEFAULT NULL COMMENT '手机号',
                                     `VIP_LEVEL` int(11) DEFAULT NULL COMMENT '会员等级',
                                     `FINISH_STATUS` int(11) DEFAULT '0' COMMENT '完成状态 0未完成 1已完成',
                                     `RECIEVE_TIME` datetime DEFAULT NULL COMMENT '领取时间',
                                     `COMPLETION_NUM` int(11) DEFAULT NULL COMMENT '完成次数',
                                     `IN_REVIEW_NUM` int(11) DEFAULT NULL COMMENT '审核中的数量',
                                     `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
                                     `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间',
                                     `IS_DELETED` int(11) DEFAULT '0' COMMENT '0正常 1已删除',
                                     `IN_CYCLE_START_TIME` datetime DEFAULT NULL COMMENT '隶属周期开始时间',
                                     `IN_CYCLE_END_TIME` datetime DEFAULT NULL COMMENT '隶属周期结束时间',
                                     `STATUS` int(11) DEFAULT '0' COMMENT '0进行中 1已过期',
                                     `VERSION` varchar(100) DEFAULT NULL COMMENT '版本',
                                     `DEAL_TIME` datetime DEFAULT NULL COMMENT '处理时间',
                                     `TASK_INFO_ID` varchar(200) DEFAULT NULL COMMENT '任务id',
                                     `STORE_ID` varchar(200) DEFAULT NULL COMMENT '只有网店店仓会填写',
                                     `TASK_STATUS` int(11) DEFAULT '1' COMMENT '用户任务的状态 0未领取任务 1已领取任务 2统计中[此状态只有消费才有，是已完成但是不能领取] 3待领取奖励 4已领取奖励 5用户任务状态结束',
                                     `TASK_STATUS_START_TIME` datetime DEFAULT NULL COMMENT '任务状态开始时间',
                                     `TASK_STATUS_END_TIME` datetime DEFAULT NULL COMMENT '任务状态结束时间',
                                     PRIMARY KEY (`ID`),
                                     KEY `task_part_details_UNIONID_IDX` (`UNIONID`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='参与任务明细';


-- jnby_usercenter_market.task_part_details_item definition

CREATE TABLE `task_part_details_item` (
                                          `ID` varchar(100) NOT NULL COMMENT '主键',
                                          `UNIONID` varchar(100) DEFAULT NULL COMMENT '用户id',
                                          `TASK_TEMP_ID` varchar(200) DEFAULT NULL,
                                          `TASK_TEMP_TYPE` int(11) DEFAULT NULL COMMENT '任务类型0:签到 1:订单评价  2:社区发帖  3:自定义',
                                          `WEID` varchar(100) DEFAULT NULL COMMENT '小程序品牌',
                                          `THIRD_PART_ID` varchar(200) DEFAULT NULL COMMENT '第三方id',
                                          `STATUS` int(11) DEFAULT '0' COMMENT '完成状态 1已完成 2审核中',
                                          `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
                                          `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间',
                                          `IS_DELETED` int(11) DEFAULT '0' COMMENT '0正常 1已删除',
                                          `IN_CYCLE_START_TIME` datetime DEFAULT NULL COMMENT '隶属周期开始时间',
                                          `IN_CYCLE_END_TIME` datetime DEFAULT NULL COMMENT '隶属周期结束时间',
                                          `TASK_PART_DETAIL_ID` varchar(255) DEFAULT NULL COMMENT '用户参与id',
                                          PRIMARY KEY (`ID`),
                                          KEY `task_part_details_item_UNIONID_IDX` (`UNIONID`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='参与任务明细条目';


-- jnby_usercenter_market.task_reminder definition

CREATE TABLE `task_reminder` (
                                 `ID` varchar(64) NOT NULL COMMENT '主键',
                                 `OPEN_ID` varchar(200) DEFAULT NULL COMMENT '公众号openId',
                                 `UNIONID` varchar(100) DEFAULT NULL COMMENT '用户id',
                                 `WEID` int(11) DEFAULT NULL COMMENT '品牌id',
                                 `IS_REMINDER` int(11) DEFAULT NULL COMMENT '0不提醒 1提醒',
                                 `IS_DELETED` int(11) DEFAULT NULL COMMENT '0正常 1删除',
                                 `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
                                 `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间',
                                 `IS_FINISH` int(11) DEFAULT '0' COMMENT '是否完成提醒',
                                 PRIMARY KEY (`ID`),
                                 KEY `TASK_WEID_REMINDR_INDEX` (`WEID`,`IS_REMINDER`),
                                 KEY `task_reminder_UNIONID_IDX` (`UNIONID`,`WEID`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='消息提醒';


-- jnby_usercenter_market.task_template definition

CREATE TABLE `task_template` (
                                 `ID` varchar(64) NOT NULL COMMENT '主键',
                                 `TASK_ALIAS_NAME` varchar(500) DEFAULT NULL COMMENT '任务别名',
                                 `WEID` varchar(100) DEFAULT NULL COMMENT '小程序品牌',
                                 `TASK_TYPE` int(11) DEFAULT NULL COMMENT '任务类型 0:签到 1:订单评价  2:社区发帖  3:自定义 4:生日 5:公众号 6:企微',
                                 `VERSION` varchar(50) DEFAULT NULL COMMENT '版本号',
                                 `STATUS` int(11) DEFAULT '0' COMMENT '状态 0上架 1下架',
                                 `SORT` int(11) DEFAULT NULL COMMENT '排序',
                                 `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
                                 `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间',
                                 `IS_DELETED` int(11) DEFAULT '0' COMMENT '0正常 1已删除',
                                 `IS_DESIGNATED_POPULATION` int(11) DEFAULT '0' COMMENT '是否指定人群 0:不指定 1:指定',
                                 `START_TIME` datetime DEFAULT NULL COMMENT '开始时间',
                                 `END_TIME` datetime DEFAULT NULL COMMENT '结束时间',
                                 `EXECUTION_MODE` int(11) DEFAULT '0' COMMENT '执行方式 0:周期内定期循环  1: 周期内始终循环 2、新人任务',
                                 `TASK_NO` varchar(10) DEFAULT NULL COMMENT '任务编号',
                                 `PEOPLE_CROWD_ID` int(11) DEFAULT NULL COMMENT '人群包组件id',
                                 `IS_DRAW_TYPE` int(11) DEFAULT '0' COMMENT '是否需要领取 0不需要领取 1需要领取',
                                 `DRAW_DAYS` int(11) DEFAULT NULL COMMENT '领取后几天完成',
                                 `COST_FEE` decimal(10,2) DEFAULT NULL COMMENT '消费金额',
                                 `TASK_RANGE` int(11) DEFAULT '0' COMMENT '任务范围 0无范围 1微商城 2全渠道',
                                 PRIMARY KEY (`ID`),
                                 KEY `TASK_COM_IDX` (`STATUS`,`START_TIME`,`END_TIME`,`WEID`,`IS_DELETED`,`IS_DESIGNATED_POPULATION`,`ID`),
                                 KEY `TASK_WEID_INX` (`WEID`),
                                 KEY `TASK_END_TIME_INX` (`END_TIME`),
                                 KEY `TASK_START_TIME_INX` (`START_TIME`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务模板';



-- jnby_usercenter_market.task_template_limitation definition

CREATE TABLE `task_template_limitation` (
                                            `ID` varchar(100) NOT NULL,
                                            `VALUE` varchar(50) DEFAULT NULL COMMENT '卡level/人群包/门店包 id',
                                            `TYPE` int(11) DEFAULT NULL COMMENT '0:卡等级 1:人群包 2:门店包',
                                            `TASK_TEMP_ID` varchar(500) DEFAULT NULL COMMENT '任务id',
                                            `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
                                            `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间',
                                            `IS_DELETED` int(11) DEFAULT '0' COMMENT '0正常 1已删除',
                                            `VALUE_NAME` varchar(100) DEFAULT NULL COMMENT '卡level/人群包/门店包 name',
                                            PRIMARY KEY (`ID`),
                                            KEY `TASK_LIMI_VALUE_INX` (`VALUE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务模板限制范围';


-- jnby_usercenter_market.task_template_log definition

CREATE TABLE `task_template_log` (
                                     `ID` varchar(64) NOT NULL COMMENT '主键',
                                     `TASK_ALIAS_NAME` varchar(500) DEFAULT NULL COMMENT '任务别名',
                                     `WEID` varchar(100) DEFAULT NULL COMMENT '小程序品牌',
                                     `TASK_TYPE` int(11) DEFAULT NULL COMMENT '任务类型 0:签到 1:订单评价  2:社区发帖  3:自定义',
                                     `TASK_NAME` varchar(500) DEFAULT NULL COMMENT '任务名称',
                                     `TASK_DESCRIPTION` varchar(500) DEFAULT NULL COMMENT '任务说明',
                                     `BUTTON_NAME` varchar(500) DEFAULT NULL COMMENT '按钮文案',
                                     `TASK_LINK` varchar(1000) DEFAULT NULL COMMENT '任务跳转链接',
                                     `VERSION` varchar(50) DEFAULT NULL COMMENT '版本号',
                                     `STATUS` int(11) DEFAULT '0' COMMENT '状态 0:上架 2:下架',
                                     `REQUIRED_TIMES` int(11) DEFAULT NULL COMMENT '应完成次数',
                                     `GAIN_POINTS` int(11) DEFAULT NULL COMMENT '完成活得积分',
                                     `SORT` int(11) DEFAULT NULL COMMENT '排序',
                                     `PEOPLE_PAG` varchar(500) DEFAULT NULL COMMENT '人群包',
                                     `STORE_PAG` varchar(500) DEFAULT NULL COMMENT '门店包',
                                     `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
                                     `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间',
                                     `OPERATOR` varchar(50) DEFAULT NULL COMMENT '操作人',
                                     `IS_DELETED` int(11) DEFAULT '0' COMMENT '0正常 1已删除',
                                     `IS_DESIGNATED_POPULATION` int(11) DEFAULT '0' COMMENT '是否指定人群 0:不指定 1:指定',
                                     `START_TIME` datetime DEFAULT NULL COMMENT '开始时间',
                                     `END_TIME` datetime DEFAULT NULL COMMENT '结束时间',
                                     `EXECUTION_MODE` int(11) DEFAULT '0' COMMENT '执行方式 0:周期内定期循环  1: 周期内始终循环',
                                     `EXECUTION_CYCLE` int(11) DEFAULT NULL COMMENT '执行周期0:每天 1:每周 2:每月',
                                     `EXECUTION_CYCLE_ATTR` varchar(100) DEFAULT NULL COMMENT '实际执行【第几天、第几周】, 用","分割',
                                     `EXECUTION_START_TIME` varchar(100) DEFAULT NULL COMMENT '执行生效开始时间',
                                     `EXECUTION_END_TIME` varchar(100) DEFAULT NULL COMMENT '执行生效结束时间',
                                     `TASK_NO` varchar(100) DEFAULT NULL COMMENT '任务编号',
                                     `ICON` varchar(500) DEFAULT NULL COMMENT 'icon',
                                     `CARD_LEVELS` varchar(500) DEFAULT NULL COMMENT '品牌卡等级',
                                     `TASK_TEMPLATE_ID` varchar(100) DEFAULT NULL COMMENT '任务模板id',
                                     `IS_DRAW_TYPE` int(11) DEFAULT '0' COMMENT '0不用领取 1需要领取',
                                     `DRAW_DAYS` int(11) DEFAULT NULL COMMENT '领取时间',
                                     `COST_FEE` int(11) DEFAULT NULL COMMENT '消费门槛',
                                     `TASK_RANGE` int(11) DEFAULT '0' COMMENT '0默认 1微商城 2全渠道',
                                     PRIMARY KEY (`ID`),
                                     KEY `task_template_log_WEID_IDX` (`WEID`,`TASK_TEMPLATE_ID`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务模板日志';


-- jnby_usercenter_market.task_template_operator definition

CREATE TABLE `task_template_operator` (
                                          `ID` varchar(55) NOT NULL COMMENT '主键',
                                          `OPERATOR_NAME` varchar(50) DEFAULT NULL COMMENT '操作人名称',
                                          `OPT_TYPE` int(11) DEFAULT NULL COMMENT '操作类型',
                                          `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
                                          `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间',
                                          `IS_DELETED` int(11) DEFAULT NULL COMMENT '0正常 1已删除',
                                          `TASK_TEMP_ID` varchar(100) DEFAULT NULL COMMENT '任务id',
                                          PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务模板操作人记录';


-- jnby_usercenter_market.task_template_out_info definition

CREATE TABLE `task_template_out_info` (
                                          `ID` varchar(64) NOT NULL COMMENT '主键',
                                          `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
                                          `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间',
                                          `IS_DELETED` int(11) DEFAULT '0' COMMENT '0正常 1已删除',
                                          `TASK_TEMP_ID` varchar(100) DEFAULT NULL COMMENT '任务模板id',
                                          `TASK_NAME` varchar(500) DEFAULT NULL COMMENT '任务名称',
                                          `TASK_DESCRIPTION` varchar(500) DEFAULT NULL COMMENT '任务说明',
                                          `BUTTON_NAME` varchar(500) DEFAULT NULL COMMENT '按钮文案',
                                          `TASK_LINK` varchar(1000) DEFAULT NULL COMMENT '任务跳转链接',
                                          `ICON` varchar(500) DEFAULT NULL COMMENT 'icon',
                                          PRIMARY KEY (`ID`),
                                          KEY `task_template_out_info_TASK_TEMP_ID_IDX` (`TASK_TEMP_ID`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务模板-外露信息';


-- jnby_usercenter_market.task_template_pop_info definition

CREATE TABLE `task_template_pop_info` (
                                          `ID` varchar(64) NOT NULL,
                                          `TASK_TEMPLATE_ID` varchar(100) DEFAULT NULL COMMENT '任务模板id',
                                          `POP_IMG` varchar(2000) DEFAULT NULL COMMENT '弹窗图片',
                                          `APP_ID` varchar(100) DEFAULT NULL COMMENT '小程序Id',
                                          `POP_BUTTON_NAME` varchar(200) DEFAULT NULL COMMENT '按钮名称',
                                          `POP_ID` varchar(500) DEFAULT NULL COMMENT '弹窗处的id',
                                          `IS_INNER` int(11) DEFAULT NULL,
                                          `IS_JUMP` varchar(500) DEFAULT NULL,
                                          `REDIRECT_ID` varchar(500) DEFAULT NULL,
                                          `REDIRECT_NAME` varchar(2000) DEFAULT NULL,
                                          `REDIRECT_URL` varchar(2000) DEFAULT NULL,
                                          `TYPE` int(11) DEFAULT NULL COMMENT '1小程序 2H5 3视频号',
                                          `IS_DELETED` int(11) DEFAULT '0' COMMENT '0正常 1删除',
                                          `CREATE_TIME` datetime DEFAULT NULL,
                                          `UPDATE_TIME` datetime DEFAULT NULL,
                                          PRIMARY KEY (`ID`),
                                          KEY `task_template_pop_info_TASK_TEMPLATE_ID_IDX` (`TASK_TEMPLATE_ID`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务模板弹窗信息';


-- jnby_usercenter_market.task_template_reward_info definition

CREATE TABLE `task_template_reward_info` (
                                             `ID` varchar(64) NOT NULL COMMENT '主键',
                                             `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
                                             `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间',
                                             `IS_DELETED` int(11) DEFAULT '0' COMMENT '0正常 1已删除',
                                             `TASK_TEMP_ID` varchar(100) DEFAULT NULL COMMENT '任务模板id',
                                             `TYPE` int(11) DEFAULT NULL COMMENT '奖励类型 0完成任务门槛 1奖励积分 2奖励券 3奖励库存 4奖励发放时间 5领奖时效',
                                             `VALUE` varchar(500) DEFAULT NULL COMMENT '奖励值',
                                             `DRAW_TYPE` int(11) DEFAULT '0' COMMENT '领取方式 1:自动发放 0:用户手动领取',
                                             `NUM` int(11) DEFAULT NULL COMMENT '券张数',
                                             PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务模板-奖励信息';

CREATE TABLE `wash_send_ques_log` (
                                      `ID` int(11) NOT NULL COMMENT '主键id',
                                      PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `wash_update_time_log` (
                                        `ID` varchar(64) NOT NULL COMMENT '主键',
                                        `WASH_ID` varchar(100) DEFAULT NULL COMMENT '洗护券主键id',
                                        `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
                                        `UPDATE_TIME` datetime DEFAULT NULL COMMENT '修改时间',
                                        PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;