package org.springcenter.marketing.common;

/**
 * <AUTHOR>
 */

public enum PositionEnum {

    /**
     * 头部背景图 1L
     */
    HEAD_BACKGROUND(1L,"头部背景图"),
    /**
     * 九宫格 2L
     */
    JIU_GONG_GE(2L,"九宫格"),
    /**
     * 个人中心资源位一 3L
     */
    PERSON_SOURCE_POS_ONE(3L,"个人中心资源位一"),
    /**
     * 个人中心资源位二 4L
     */
    PERSON_SOURCE_POS_TWO(4L,"个人中心资源位二"),

    /**
     * 权益中心资源位 5L
     */
    RIGHTS_CENTER_SOURCE_POS(5L,"权益中心资源位"),



    /**
     * 十年维修-背景图
     */
    TEN_YEARS_BACKGROUND(10L,"十年维修-背景图"),
    /**
     * 十年维修-头图
     */
    TEN_YEARS_HEAD(11L,"十年维修-头图"),
    /**
     * 十年维修-底图
     */
    TEN_YEARS_BOTTOM(12L,"十年维修-底图"),

    /**
     *  POS+支付成功页
     */
    POS_PLUS_PAY(15L,"POS+支付成功页"),


    /**
     * 搜索词
     */
    SEARCH_TXT(21L,"搜索词"),
    ;
    private Long code;
    private String desc;

    PositionEnum(Long code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Long getCode() {
        return code;
    }

    public void setCode(Long code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}

