package org.springcenter.marketing.modules.wxMapper;

import org.apache.ibatis.annotations.Param;
import org.springcenter.marketing.modules.model.JicCardType;
import org.springcenter.marketing.modules.model.JicCardTypeR;

public interface JicCardTypeMapper {

    public JicCardTypeR getById(@Param("id") Long id);

    JicCardTypeR getByWeidAndLevel(@Param("weid") String weid, @Param("levelId") String brandCardLel);

    JicCardTypeR getByNullAndLevel(@Param("levelId") String memberLvl);
}
