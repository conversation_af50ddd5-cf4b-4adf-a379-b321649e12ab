package org.springcenter.marketing.modules.repository.impl;

import org.apache.commons.lang3.ObjectUtils;
import org.springcenter.marketing.modules.bojunMapper.CStoreMapper;
import org.springcenter.marketing.modules.model.CStore;
import org.springcenter.marketing.modules.repository.ICStoreRepository;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 3/12/21 4:24 PM
 */
@Repository
public class CStoreRepositoryImpl implements ICStoreRepository {

    @Autowired
    CStoreMapper cStoreMapper;



    @Override
    public List<CStore> selectCStoreByIds(List<Long> ids) {
        return cStoreMapper.selectCStoreByIds(ids);
    }


    @Override
    public List<CStore> selectListByCodes(List<String> stringList) {
        return cStoreMapper.selectListByCodes(stringList);
    }
}
