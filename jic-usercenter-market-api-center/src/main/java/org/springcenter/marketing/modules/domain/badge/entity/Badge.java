package org.springcenter.marketing.modules.domain.badge.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springcenter.marketing.api.dto.admin.BadgeDetailUpdateReq;

import java.util.Date;
import java.util.List;

/**
 * 勋章领域实体
 * 代表一个可发放给用户的勋章
 */
@Data
public class Badge {

    /**
     * 勋章业务ID
     */
    private String bizId;

    /**
     * 勋章名称
     */
    private String name;

    /**
     * 勋章图片URL
     */
    private String imageUrl;
    /**
     * 勋章简介图URL
     */
    private String introImageUrl;

    /**
     * 勋章详情图URL
     */
    private String detailImageUrl;

    /**
     * 勋章所属主题ID
     */
    private Integer themeId;
    /**
     * 勋章所属主题业务ID
     */
    private String themeBizId;
    /**
     * 勋章主题名称
     */
    private String themeName;

    /**
     * 勋章开始时间
     */
    private Date startTime;

    /**
     * 勋章结束时间
     */
    private Date endTime;

    /**
     * 人群包ID
     */
    private String crowdPackageId;

    /**
     * 商品包ID
     */
    private String productPackageId;

    /**
     * 排序值
     */
    private Integer sort;

    /**
     * 是否活跃
     */
    private boolean active;

    private Date createTime;

    /**
     * 活动状态
     */
    private ActivityStatus activityStatus;

    /**
     * 小程序配置应用列表
     */
    private List<BadgeDetailUpdateReq.AppConfig> apps;

    /**
     * 是否发消息
     */
    private Boolean sendMsg;

    /**
     * 微商城 是否勾选
     */
    private Boolean orderTypeWsc;
    /**
     * 线下订单 是否勾选
     */
    private Boolean orderTypeXxdd;
    /**
     * BOX 是否勾选
     */
    private Boolean orderTypeBox;

    /**
     * 勋章活动状态枚举
     */
    public enum ActivityStatus {
        NOT_START(1, "未开始"),
        RUNNING(2, "进行中"),
        ENDING(3, "已结束");

        private final int code;
        private final String message;

        ActivityStatus(int code, String message) {
            this.code = code;
            this.message = message;
        }

        public int getCode() {
            return code;
        }

        public String getMessage() {
            return message;
        }

        public static ActivityStatus getFrontStatusEnum(Date startTime, Date endTime) {
            Date now = new Date();
            if (now.before(startTime)) {
                return Badge.ActivityStatus.NOT_START;
            } else if (now.after(endTime)) {
                return Badge.ActivityStatus.ENDING;
            } else {
                return Badge.ActivityStatus.RUNNING;
            }
        }

    }
} 