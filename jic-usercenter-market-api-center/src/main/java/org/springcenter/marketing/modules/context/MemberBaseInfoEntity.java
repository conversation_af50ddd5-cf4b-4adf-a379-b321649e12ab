package org.springcenter.marketing.modules.context;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date:2023/3/6 15:28
 */
@Data
public class MemberBaseInfoEntity {

    @ApiModelProperty(value = "品牌id")
    private String brandId;
    @ApiModelProperty(value = "会员卡号")
    private String cardNo;
    @ApiModelProperty(value = "开卡时间")
    private String creationDate;
    @ApiModelProperty(value = "集团卡开卡时间")
    private String groupCreationDate;
    @ApiModelProperty(value = "集团卡等级id")
    private Long groupVipTypeId;
    @ApiModelProperty(value = "集团卡等级名称")
    private String groupVipTypeName;
    @ApiModelProperty(value = "伯俊会员唯一标识")
    private Integer id;
    @ApiModelProperty(value = "总积分")
    private Integer integral;
    @ApiModelProperty(value = "公众号openid")
    private String openId;
    @ApiModelProperty(value = "集团卡号")
    private String remark;
    @ApiModelProperty(value = "专属导购")
    private Integer salesrepId;
    @ApiModelProperty(value = "专属导购名称")
    private String salesrepName;
    @ApiModelProperty(value = "服务门店编码")
    private String storeCode;
    @ApiModelProperty(value = "服务门店")
    private Long storeId;
    @ApiModelProperty(value = "服务门店名称")
    private String storeName;
    @ApiModelProperty(value = "手机号")
    private String tel;
    @ApiModelProperty(value = "openid对应的微信开放平台的unionid")
    private String unionId;
    @ApiModelProperty(value = "名称")
    private String userName;
    @ApiModelProperty(value = "品牌卡等级id")
    private Integer vipTypeId;
    @ApiModelProperty(value = "品牌卡等级名称")
    private String vipTypeName;
    @ApiModelProperty(value = "微盟会员id")
    private String wid;
    @ApiModelProperty(value = "小程序openid")
    private String wxOpenId;

    @ApiModelProperty(value = "生日")
    private String birthDay;

    @ApiModelProperty(value = "昵称")
    private String nickName;


    @ApiModelProperty(value = "会员卡状态：Y正常，N冻结")
    private String status;


    @ApiModelProperty(value = "是否注销：Y-是；N-否")
    private String isZhux;

    @ApiModelProperty("集团卡等级")
    private Integer groupLevelId;
    @ApiModelProperty("集团卡折扣")
    private Double groupVipTypeDiscount;

    @ApiModelProperty("集团卡ID")
    private Long groupId;

    @ApiModelProperty("当前卡等级")
    private String levelId;

    @ApiModelProperty("会员卡折扣")
    private Double vipTypeDiscount;

    @ApiModelProperty("经销商")
    private Integer customerId;


    @ApiModelProperty(value = "贵宾邀请制 10-贵宾")
    private String cardStatusExp;

}
