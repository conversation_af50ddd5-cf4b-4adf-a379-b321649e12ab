<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.marketing.modules.bojunMapper.CStoreMapper">
  <resultMap id="BaseResultMap" type="org.springcenter.marketing.modules.model.CStore">
    <id column="ID" jdbcType="DECIMAL" property="id" />
    <result column="ISACTIVE" jdbcType="CHAR" property="isactive" />
    <result column="NAME" jdbcType="VARCHAR" property="name" />
    <result column="DESCRIPTION" jdbcType="VARCHAR" property="description" />
    <result column="C_CUSTOMER_ID" jdbcType="DECIMAL" property="cCustomerId" />
    <result column="CODE" jdbcType="VARCHAR" property="code" />
    <result column="C_UNIONSTORE_ID" jdbcType="DECIMAL" property="cUnionstoreId" />
    <result column="C_ARCBRAND_ID" jdbcType="DECIMAL" property="cArcbrandId" />
    <result column="isnegative" jdbcType="VARCHAR" property="isNegative" />
    <result column="mobil" jdbcType="VARCHAR" property="mobil"/>
    <result column="phone" jdbcType="VARCHAR" property="phone"/>
    <result column="CALCULATION" jdbcType="DECIMAL" property="calculation"/>
    <result column="C_PROVINCE_ID" jdbcType="DECIMAL" property="cProvinceId"/>
    <result column="C_CITY_ID" jdbcType="DECIMAL" property="cCityId"/>
    <result column="C_DISTRICT_ID" jdbcType="DECIMAL" property="cDistrictId"/>

    <result column="mobil" jdbcType="VARCHAR" property="mobil"/>
    <result column="LONGITUDE" jdbcType="DECIMAL" property="longitude"/>
    <result column="LATITUDE" jdbcType="DECIMAL" property="latitude"/>
    <result column="ZT_MONTHCODE" jdbcType="VARCHAR" property="ztMonthcode"/>
  </resultMap>
  <sql id="Base_Column_List">
    ID, ISACTIVE,  NAME, DESCRIPTION, C_CUSTOMER_ID, CODE,C_UNIONSTORE_ID,C_ARCBRAND_ID,isnegative,mobil,phone,CALCULATION,LONGITUDE,	LATITUDE, ZT_MONTHCODE,C_PROVINCE_ID,C_CITY_ID,C_DISTRICT_ID
  </sql>


  <select id="selectCStoreByIds"  resultMap="BaseResultMap" >
    select
    <include refid="Base_Column_List" />
    from C_STORE
    where ID in
    <foreach collection="ids" item="id" open="(" close=")" separator=",">
        #{id}
    </foreach>
  </select>

    <select id="selectListByCodes" resultMap="BaseResultMap">
      select     <include refid="Base_Column_List" /> from C_STORE where code in
      <foreach collection="codes" item="item" open="(" close=")" separator=",">
        #{item}
      </foreach>

    </select>












</mapper>
