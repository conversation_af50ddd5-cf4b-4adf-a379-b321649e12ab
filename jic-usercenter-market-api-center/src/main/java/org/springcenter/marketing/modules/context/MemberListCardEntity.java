package org.springcenter.marketing.modules.context;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 对象字段新增可参考接口文档：
 * http://swagger.jnby.com/jictest/sdk/doc.html#/SDK-%E4%BC%9A%E5%91%98/2.0%E4%BC%9A%E5%91%98%E6%9C%8D%E5%8A%A1%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/SDKGetMemberCardListUsingPOST_1
 * {
 *     "cliqueMemberVo": {},
 *     "memberShipVo": {
 *         "cardId": 25608954,
 *         "cardNo": "**********",
 *         "cardTypeId": 151,
 *         "cardTypeName": "江南布衣+金卡",
 *         "cardLvl": 2,
 *         "openTime": 20240426,
 *         "phone": "",
 *         "vipShareTypeId": 151,
 *         "vipShareTypeName": "江南布衣+金卡",
 *         "vipShareTypeDiscount": 0.9,
 *         "vipShareTypeLvl": -1,
 *         "customerId": 176,
 *         "storeId": 421703,
 *         "storeName": "(263)杭州天目里江南布衣+",
 *         "storeCode": "JDA26321",
 *         "clerkId": 382243,
 *         "clerkCode": "479-1811122",
 *         "clerkName": "尹洁敏",
 *         "clerkPhone": "15868080986",
 *         "brandId": "5",
 *         "memberCode": "G012875655",
 *         "status": "Y",
 *         "updateTime": 20250716093301
 *     },
 *     "memberChannelVo": {
 *         "userName": "万小鱼",
 *         "headImgUrl": "https://thirdwx.qlogo.cn/mmopen/vi_32/9sU5O0AbBcb2WjvE2P8BeI7lFTmMqbe5UVEc77iaVKVh1ibMJDvKXKlFbnf7XdjLcO3d8iafvicyMfriamRjmt8UI0Z8PSNnO6AkTcOaxeermnG8/132",
 *         "sex": "M",
 *         "birthday": "1996-02-22 00:00:00.0",
 *         "mpOpenId": "oAMoM1Ed66tgnqBE4wH5xfzmMx9I",
 *         "maOpenId": "oyqWZ5YUCs6mmOz1ABsSdE0KZY6I",
 *         "openChannel": "3",
 *         "unionId": "oZpUxs7E-g7VZkK5Qsb_QmKxwWUU",
 *         "wid": -1
 *     },
 *     "memberExtVo": {},
 *     "storePackageVoList": []
 * }
 *
 */
@ApiModel("会员信息")
@Data
public class MemberListCardEntity {
    @ApiModelProperty("会员基础信息")
    private MemberShip memberShipVo;
    @ApiModelProperty("会员渠道信息")
    private MemberChannel memberChannelVo;

    @Data
    public static class MemberChannel {
        @ApiModelProperty("用户昵称")
        private String userName;
        @ApiModelProperty("用户头像")
        private String headImgUrl;
    }
    @Data
    public static class MemberShip {
        @ApiModelProperty("卡号")
        private String cardNo;
    }
}
