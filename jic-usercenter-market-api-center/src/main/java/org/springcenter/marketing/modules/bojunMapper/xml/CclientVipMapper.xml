<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.marketing.modules.bojunMapper.CclientVipMapper">
  <resultMap id="BaseResultMap" type="org.springcenter.marketing.modules.model.CclientVip">
    <id column="ID" jdbcType="DECIMAL" property="id" />
    <result column="ISACTIVE" jdbcType="CHAR" property="isactive" />
    <result column="CREATIONDATE" jdbcType="TIMESTAMP" property="creationdate" />
    <result column="MODIFIEDDATE" jdbcType="TIMESTAMP" property="modifieddate" />
    <result column="OWNERID" jdbcType="DECIMAL" property="ownerid" />
    <result column="CARDNO" jdbcType="VARCHAR" property="cardno" />
    <result column="C_VIPTYPE_ID" jdbcType="DECIMAL" property="cViptypeId" />
    <result column="IDNO" jdbcType="VARCHAR" property="idno" />
    <result column="VIPNAME" jdbcType="VARCHAR" property="vipname" />
    <result column="VIPENAME" jdbcType="VARCHAR" property="vipename" />
    <result column="SEX" jdbcType="CHAR" property="sex" />
    <result column="COUNTRY" jdbcType="VARCHAR" property="country" />
    <result column="C_CITY_ID" jdbcType="DECIMAL" property="cCityId" />
    <result column="ADDRESS" jdbcType="VARCHAR" property="address" />
    <result column="PHONE" jdbcType="VARCHAR" property="phone" />
    <result column="MOBIL" jdbcType="VARCHAR" property="mobil" />
    <result column="C_STORE_ID" jdbcType="DECIMAL" property="cStoreId" />
    <result column="C_CUSTOMER_ID" jdbcType="DECIMAL" property="cCustomerId" />
    <result column="C_CUSTOMERUP_ID" jdbcType="DECIMAL" property="cCustomerupId" />
    <result column="VIPNO" jdbcType="VARCHAR" property="vipno" />
    <result column="STORECARDNO" jdbcType="VARCHAR" property="storecardno" />
    <result column="M_RETAIL_ID" jdbcType="DECIMAL" property="mRetailId" />
    <result column="C_OLDVIP_ID" jdbcType="DECIMAL" property="cOldvipId" />
    <result column="ENTERTYPE" jdbcType="CHAR" property="entertype" />
    <result column="C_OLDVIP" jdbcType="VARCHAR" property="cOldvip" />
    <result column="SALER" jdbcType="VARCHAR" property="saler" />
    <result column="DESCRIPTION" jdbcType="VARCHAR" property="description" />
    <result column="DESCRIPTION2" jdbcType="VARCHAR" property="description2" />
    <result column="NICKNAME" jdbcType="VARCHAR" property="nickname" />
    <result column="WXOPENID" jdbcType="VARCHAR" property="wxopenid" />
    <result column="MEMBERNO" jdbcType="DECIMAL" property="memberno" />
    <result column="OPENID" jdbcType="VARCHAR" property="openid" />
    <result column="UNIONID" jdbcType="VARCHAR" property="unionid" />
    <result column="VIP_DESCRIPTION" jdbcType="VARCHAR" property="vipDescription" />
    <result column="IS_OPENBOX" jdbcType="CHAR" property="isOpenbox" />
    <result column="OPENBOXDATE" jdbcType="DECIMAL" property="openboxdate" />
    <result column="UNICODEID" jdbcType="VARCHAR" property="unicodeid" />
    <result column="C_VIP_ID_JSR" jdbcType="DECIMAL" property="cVipIdJsr" />
    <result column="C_VIPTYPE_ID_JSR" jdbcType="DECIMAL" property="cViptypeIdJsr" />
    <result column="ISSUBBOX" jdbcType="CHAR" property="issubbox" />
    <result column="INTEGRAL" jdbcType="DECIMAL" property="integral" />
    <result column="AGE" jdbcType="DECIMAL" property="age" />
    <result column="SALESREP_ID" jdbcType="DECIMAL" property="salesrepId" />
    <result column="BIRTHDAY" jdbcType="INTEGER" property="birthday" />
  </resultMap>
  <sql id="Base_Column_List">
    ID,ISACTIVE,CREATIONDATE,MODIFIEDDATE,OWNERID,CARDNO,C_VIPTYPE_ID,IDNO,VIPNAME,VIPENAME,SEX,COUNTRY,C_CITY_ID,ADDRESS,
    PHONE,MOBIL,C_STORE_ID,C_CUSTOMER_ID,C_CUSTOMERUP_ID,VIPNO,STORECARDNO,M_RETAIL_ID,C_OLDVIP_ID,ENTERTYPE,C_OLDVIP,
    SALER,DESCRIPTION,DESCRIPTION2,NICKNAME,WXOPENID,MEMBERNO,OPENID,UNIONID,VIP_DESCRIPTION,IS_OPENBOX,OPENBOXDATE,
    UNICODEID,C_VIP_ID_JSR,C_VIPTYPE_ID_JSR,ISSUBBOX,INTEGRAL,AGE,SALESREP_ID,BIRTHDAY
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from C_CLIENT_VIP
    where ID = #{id,jdbcType=DECIMAL}
  </select>


  <select id="selectListBySelective" parameterType="org.springcenter.marketing.modules.model.CclientVip" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from C_CLIENT_VIP
        <where>
          <if test="id != null">
            and ID = #{id,jdbcType=DECIMAL}
          </if>
          <if test="isactive != null">
            and ISACTIVE = #{isactive,jdbcType=CHAR}
          </if>
          <if test="cardno != null">
            and CARDNO = #{cardno,jdbcType=VARCHAR}
          </if>
          <if test="cViptypeId != null">
            and C_VIPTYPE_ID = #{cViptypeId,jdbcType=DECIMAL}
          </if>
          <if test="mobil != null">
            and MOBIL = #{mobil,jdbcType=VARCHAR}
          </if>
          <if test="unionid != null">
            and UNIONID = #{unionid,jdbcType=VARCHAR}
          </if>
          <if test="cCustomerId != null">
            and C_CUSTOMER_ID = #{cCustomerId,jdbcType=VARCHAR}
          </if>
          <if test="memberno != null">
            and MEMBERNO = #{memberno,jdbcType=DECIMAL}
          </if>
        </where>
  </select>
  <select id="selectByPrimaryKeys" resultMap="BaseResultMap">
    select id, C_VIPTYPE_ID from C_CLIENT_VIP where  id in
    <foreach collection="ids" separator="," open="(" close=")" item="item">
      #{item}
    </foreach>
  </select>
  <select id="selectByCardNos" resultMap="BaseResultMap">
    select id, C_VIPTYPE_ID from C_CLIENT_VIP where  CARDNO in
    <foreach collection="cardNos" separator="," open="(" close=")" item="item">
      #{item}
    </foreach>

  </select>
    <select id="selectByCardTypeIdAndIsActive"
            resultMap="BaseResultMap">
      select ID from C_CLIENT_VIP where ISACTIVE = 'Y' and C_VIPTYPE_ID = '151'
    </select>


</mapper>
