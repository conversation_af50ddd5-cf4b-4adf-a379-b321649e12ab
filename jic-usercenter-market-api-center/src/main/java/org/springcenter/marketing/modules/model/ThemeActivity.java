package org.springcenter.marketing.modules.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 互动留言板活动信息表
 * @TableName theme_activity
 */
@Data
@TableName(value ="theme_activity")
public class ThemeActivity implements Serializable {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 主题名称
     */
    @TableField(value = "theme_name")
    private String themeName;

    /**
     * 主题介绍
     */
    @TableField(value = "theme_introduce")
    private String themeIntroduce;

    /**
     * 主题图片
     */
    @TableField(value = "theme_img")
    private String themeImg;

    /**
     * 主题背景
     */
    @TableField(value = "theme_background")
    private String themeBackground;

    /**
     * 参活小程序weid
     */
    @TableField(value = "weid")
    private String weid;

    /**
     * 活动开始时间
     */
    @TableField(value = "activity_start_time")
    private Date activityStartTime;

    /**
     * 活动结束时间
     */
    @TableField(value = "activity_end_time")
    private Date activityEndTime;

    /**
     * 0  开始    1 暂停   （状态在时间后判断， 时间是第一优先级）
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 活动规则
     */
    @TableField(value = "activity_rule")
    private String activityRule;

    /**
     * 留言排序规则   0  发布时间倒序   1  点赞量从多到少
     */
    @TableField(value = "leave_message_sort")
    private Integer leaveMessageSort;

    /**
     * 留言评论回复 最少字数
     */
    @TableField(value = "leave_message_min_num")
    private Integer leaveMessageMinNum;

    /**
     * 留言评论回复 最大字数
     */
    @TableField(value = "leave_message_max_num")
    private Integer leaveMessageMaxNum;

    /**
     * 活动结束后x天，运营发放奖品
     */
    @TableField(value = "activity_end_send_award_day")
    private Integer activityEndSendAwardDay;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 0  正常   1 删除
     */
    @TableField(value = "is_del")
    private Integer isDel;

    /**
     * 更新人
     */
    @TableField(value = "update_by")
    private String updateBy;

    /**
     * 创建人
     */
    @TableField(value = "create_by")
    private String createBy;

    /**
     * 0 未生成奖励  1 已经生成奖励
     */
    @TableField(value = "is_gen_award")
    private Integer isGenAward;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}