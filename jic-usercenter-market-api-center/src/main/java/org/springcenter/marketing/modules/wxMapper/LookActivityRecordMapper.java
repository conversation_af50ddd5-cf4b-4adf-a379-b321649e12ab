package org.springcenter.marketing.modules.wxMapper;

import org.apache.ibatis.annotations.Param;
import org.springcenter.marketing.api.dto.look.LookUserAwardReqDto;
import org.springcenter.marketing.modules.model.look.LookActivityRecord;

/**
 * 文件名: org.springcenter.marketing.modules.mapper-LookActivityRecordMapper.java
 * 文件简介:
 *
 * <AUTHOR>
 * @date 2025/5/12 10:15
 *       Copyright (c) 1994 JNBY. All Rights Reserved
 */
public interface LookActivityRecordMapper {

    void updateLookActivityRecordStatus(@Param("reqDto") LookUserAwardReqDto reqDto, @Param("sort") int sort);

    void insertLookActivityRecord(LookActivityRecord lookActivityRecord);

    void updateLookActivityRecord(LookActivityRecord lookActivityRecord);

    int selectListByUnionId(@Param("luckyId") Long luckyId, @Param("unionId") String unionId,@Param("reqId") String reqId);

    int selectLookActivityRecordCount(@Param("luckyId") Long luckyId, @Param("unionId") String unionId,@Param("reqId") String reqId,@Param("requestNum") int requestNum);

}
