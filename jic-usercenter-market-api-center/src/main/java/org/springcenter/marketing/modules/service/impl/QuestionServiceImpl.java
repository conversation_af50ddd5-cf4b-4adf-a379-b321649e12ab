package org.springcenter.marketing.modules.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.StringUtil;
import com.jnby.common.CommonConstant;
import com.jnby.common.Page;
import com.jnby.common.util.IdLeaf;
import com.jnbyframework.boot.api.ISysBaseAPI;
import com.jnbyframework.boot.common.api.dto.LogDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springcenter.marketing.api.constant.BirthCouponSendLogStatusConstatnt;
import org.springcenter.marketing.api.constant.IdConstant;
import org.springcenter.marketing.api.context.SendWxOffMsgContext;
import org.springcenter.marketing.api.dto.admin.*;
import org.springcenter.marketing.api.dto.miniapp.*;
import org.springcenter.marketing.api.enums.SceneCodeEnum;
import org.springcenter.marketing.modules.bojunMapper.CclientVipMapper;
import org.springcenter.marketing.modules.bojunMapper.MRetailMapper;
import org.springcenter.marketing.modules.mapper.*;
import org.springcenter.marketing.modules.model.*;
import org.springcenter.marketing.modules.service.IQuesUserAnswerService;
import org.springcenter.marketing.modules.service.MessageService;
import org.springcenter.marketing.modules.service.QuestionService;
import org.springcenter.marketing.modules.util.DateUtil;
import org.springcenter.marketing.modules.util.DateUtils;
import org.springcenter.marketing.modules.wxMapper.PosRepairOrderMapper;
import org.springcenter.marketing.modules.wxMapper.StoreReservationOrderMapper;
import org.springcenter.marketing.modules.wxMapper.VoucherBaseMapper;
import org.springcenter.marketing.modules.wxMapper.VoucherVirtualBaseMapper;
import org.springcenter.product.api.enums.IsDeleteEnum;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@RefreshScope
public class QuestionServiceImpl implements QuestionService {

    @Autowired
    private QuesSceneMapper quesSceneMapper;

    @Autowired
    private QuesQuestionTemplateMapper quesQuestionTemplateMapper;

    @Autowired
    private QuesQuestionTemplateDetailMapper quesQuestionTemplateDetailMapper;

    @Autowired
    private QuesQuestionMapper quesQuestionMapper;

    @Autowired
    private QuesAnswerMapper quesAnswerMapper;

    @Autowired
    private QuesUserAnswerMapper quesUserAnswerMapper;

    @Autowired
    private BirthCouponSendLogMapper birthCouponSendLogMapper;

    @Autowired
    private BRightsMapper bRightsMapper;

    @Autowired
    private VoucherBaseMapper voucherBaseMapper;

    @Autowired
    private MRetailMapper mRetailMapper;

    @Autowired
    private CclientVipMapper cclientVipMapper;

    @Autowired
    @Qualifier("marketingTransactionTemplate")
    private TransactionTemplate template;

    @Autowired
    private ISysBaseAPI iSysBaseAPI;

    @Autowired
    private VoucherVirtualBaseMapper voucherVirtualBaseMapper;

    @Autowired
    private WashSendQuesLogMapper washSendQuesLogMapper;

    @Autowired
    private IQuesUserAnswerService iQuesUserAnswerService;


    @Value("${wx.brand.commonConfigs}")
    private String commonConfigs;

    @Autowired
    private MessageService messageService;

    @Autowired
    private PosRepairOrderMapper posRepairOrderMapper;

    @Autowired
    private StoreReservationOrderMapper storeReservationOrderMapper;

    @Override
    public List<ListSceneResp> listScene(ListSceneReq requestData, Page page) {
        com.github.pagehelper.Page<QuesScene> hPageTotal = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        if(StringUtils.isNotBlank(requestData.getSuitPlaform())){
            requestData.setSuitPlaforms(Arrays.asList(requestData.getSuitPlaform().split(",")));
        }
        quesSceneMapper.selectListByParams(requestData);
        PageInfo<QuesScene> pageInfoTotal = new PageInfo(hPageTotal);
        long total = pageInfoTotal.getTotal();
        page.setCount(total);
        page.setPages(pageInfoTotal.getPages());
        List<QuesScene> list = pageInfoTotal.getList();
        if(CollectionUtils.isEmpty(list)){
            return new ArrayList<>();
        }
        String jsonString = JSONObject.toJSONString(list);
        return JSONObject.parseArray(jsonString, ListSceneResp.class);
    }

    @Override
    public List<ListQuestionTemplateResp> listTemplate(ListQuestionTemplateReq requestData, Page page) {
        List<ListQuestionTemplateResp> list = new ArrayList<>();

        com.github.pagehelper.Page<QuesQuestionTemplate> hPageTotal = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        quesQuestionTemplateMapper.selectListByParams(requestData);
        PageInfo<QuesQuestionTemplate> pageInfoTotal = new PageInfo(hPageTotal);
        long total = pageInfoTotal.getTotal();
        page.setCount(total);
        page.setPages(pageInfoTotal.getPages());
        if(CollectionUtils.isEmpty(pageInfoTotal.getList())){
            return new ArrayList<>();
        }
        // 查询到数据
        List<QuesQuestionTemplate> quesQuestionTemplates = pageInfoTotal.getList();
//        if(requestData.getIsOpen()){
//            quesQuestionTemplates = quesQuestionTemplates.stream().filter(r->r.getStatus() == 1).collect(Collectors.toList());
//        }
        List<String> collect1 = pageInfoTotal.getList().stream().map(r -> r.getId()).collect(Collectors.toList());
        // 查询筛选过滤数据
        List<QuesQuestionTemplateDetail> quesQuestionTemplateDetails = quesQuestionTemplateDetailMapper.selectByQuestionIds(collect1);
        Map<String, List<QuesQuestionTemplateDetail>> collect = quesQuestionTemplateDetails.stream().collect(Collectors.groupingBy(r -> r.getQuestionTemplateId()));

        //封装数据
        for (QuesQuestionTemplate quesQuestionTemplate : quesQuestionTemplates) {
            ListQuestionTemplateResp listQuestionTemplateResp = new ListQuestionTemplateResp();
            BeanUtils.copyProperties(quesQuestionTemplate,listQuestionTemplateResp);
            List<QuesQuestionTemplateDetail> quesQuestionTemplateDetails1 = collect.get(quesQuestionTemplate.getId());
            listQuestionTemplateResp.setSuitParams(quesQuestionTemplateDetails1.stream().map(r->r.getSuitParam()).collect(Collectors.toList()));
            // 如果id不为空  那么进行把问卷数据等进行回显
            if(StringUtil.isNotEmpty(requestData.getId())){
                // 查询问卷和答案
                List<ListQuestionTemplateResp.QuestionAndAnswer> questionAndAnswers = getQuestionAndAnswer(quesQuestionTemplate.getId());
                listQuestionTemplateResp.setQuestionAndAnswers(questionAndAnswers);
                // 查询场景
                QuesScene quesScene = quesSceneMapper.selectById(quesQuestionTemplate.getQuesSceneId());
                ListSceneResp sceneResp = new ListSceneResp();
                BeanUtils.copyProperties(quesScene,sceneResp);
                listQuestionTemplateResp.setSceneResp(sceneResp);
            }
            list.add(listQuestionTemplateResp);
        }

        return list;
    }

    private List<ListQuestionTemplateResp.QuestionAndAnswer> getQuestionAndAnswer(String quesTemplateId) {
        List<ListQuestionTemplateResp.QuestionAndAnswer> questionAndAnswers = new ArrayList<>();
        // 根据模版id查询到所有的问题  查询时候直接排序好    从大到小
        List<QuesQuestion> quesQuestions = quesQuestionMapper.selectByQuestionTemplateId(quesTemplateId);
        // 根据所有的问题查询到所有的答案
        if(CollectionUtils.isNotEmpty(quesQuestions)){
            // 拿出二级问题 根据父id进行分组
            Map<String, List<QuesQuestion>> groupByPartensId = quesQuestions.stream().filter(r -> StringUtils.isNotBlank(r.getParentsId())).collect(Collectors.groupingBy(r -> r.getParentsId()));

            List<String> questionIds = quesQuestions.stream().map(r -> r.getId()).collect(Collectors.toList());

            List<QuesQuestion> collect = quesQuestions.stream().filter(r -> StringUtils.isBlank(r.getParentsId())).collect(Collectors.toList());

            List<QuesAnswer> quesAnswers  = quesAnswerMapper.selectByQuestionIds(questionIds);
            // 根据questionId 分组
            Map<String, List<QuesAnswer>> groupbyQuestionId = quesAnswers.stream().collect(Collectors.groupingBy(r -> r.getQuestionId()));
            selfMethod(collect,groupbyQuestionId,questionAndAnswers,groupByPartensId);
        }
        // 最终进行排序   答案 排序   问题排序
        return questionAndAnswers;
    }

    private void selfMethod(List<QuesQuestion> quesQuestions,
                            Map<String, List<QuesAnswer>> groupbyQuestionId,
                            List<ListQuestionTemplateResp.QuestionAndAnswer> questionAndAnswers,
                            Map<String, List<QuesQuestion>> groupByPartensId) {

        for (QuesQuestion quesQuestion : quesQuestions) {
                ListQuestionTemplateResp.QuestionAndAnswer questionAndAnswer = new ListQuestionTemplateResp.QuestionAndAnswer();
                BeanUtils.copyProperties(quesQuestion, questionAndAnswer);
                List<QuesAnswer> quesAnswers1 = groupbyQuestionId.get(quesQuestion.getId());
                // 排序 从大到小 答案
                if(CollectionUtils.isNotEmpty(quesAnswers1)){
                    List<ListQuestionTemplateResp.Answer> answers = quesAnswers1.stream().sorted(new Comparator<QuesAnswer>() {
                        @Override
                        public int compare(QuesAnswer o1, QuesAnswer o2) {
                            return o2.getOrderSort() - o1.getOrderSort();
                        }
                    }).map(r -> {
                        ListQuestionTemplateResp.Answer answer = new ListQuestionTemplateResp.Answer();
                        BeanUtils.copyProperties(r, answer);
                        return answer;
                    }).collect(Collectors.toList());
                    questionAndAnswer.setAnswers(answers);
                }

                List<QuesQuestion> quesQuestions1 = groupByPartensId.get(quesQuestion.getId());
                if (CollectionUtils.isNotEmpty(quesQuestions1)) {
                    questionAndAnswer.setChildQuestions(new ArrayList<>());
                    selfMethod(quesQuestions1, groupbyQuestionId, questionAndAnswer.getChildQuestions(),groupByPartensId);
                }
                questionAndAnswers.add(questionAndAnswer);
        }
    }


    @Override
    public String createOrUpdateTemplate(CreateOrUpdateQuesTemplateReq requestData,String userId) {
        List<ListQuestionTemplateResp.QuestionAndAnswer> result  =new ArrayList<>();
        // 校验问题数量
        List<ListQuestionTemplateResp.QuestionAndAnswer> questionAndAnswers = requestData.getQuestionAndAnswers();
        getQuestion(questionAndAnswers,result);
        if(requestData.getType() == 1){
           //组件最多三个
            if(result.size() > 3){
                throw new RuntimeException("组件形式最多可支持3个问题(1级和2级的总数)");
            }
        }else{
           // 最多10个1级问题
            if(questionAndAnswers.size() > 50){
                 throw new RuntimeException("页面形式最多可支持50个问题(包含50个)");
            }
        }
        // 应该插入的数据
        QuesQuestionTemplate quesQuestionTemplate = new QuesQuestionTemplate();
        List<QuesQuestionTemplateDetail> quesQuestionTemplateDetails = new ArrayList<>();
        // 问题和答案需要删除了之后再重新新增
        List<QuesQuestion> quesQuestions = new ArrayList<>();
        List<QuesAnswer> answers = new ArrayList<>();
        String quesQuestionTemplateId = IdLeaf.getId(IdConstant.QUES_QUESTION_TEMPLATE);

        // 设置配置

        for (String suitParam : requestData.getSuitParams()) {
            QuesQuestionTemplateDetail quesQuestionTemplateDetail = new QuesQuestionTemplateDetail();
            quesQuestionTemplateDetail.setId(IdLeaf.getId(IdConstant.QUES_QUESTION_TEMPLATE_DETAIL));
            quesQuestionTemplateDetail.setCreateTime(new Date());
            quesQuestionTemplateDetail.setUpdateTime(new Date());
            quesQuestionTemplateDetail.setIsDel(IsDeleteEnum.NORMAL.getCode());
            quesQuestionTemplateDetail.setSuitParam(suitParam);
            if(StringUtils.isBlank(requestData.getId())){
                quesQuestionTemplateDetail.setQuestionTemplateId(quesQuestionTemplateId);
            }else{
                quesQuestionTemplateDetail.setQuestionTemplateId(requestData.getId());
            }
            quesQuestionTemplateDetails.add(quesQuestionTemplateDetail);
        }

        // 设置问题 和 答案
        List<ListQuestionTemplateResp.QuestionAndAnswer> params  =new ArrayList<>();
        getQuestionV2(questionAndAnswers,params,IdLeaf.getId(IdConstant.QUES_QUESTION),null);

        for (ListQuestionTemplateResp.QuestionAndAnswer questionAndAnswer : params) {
            QuesQuestion quesQuestion = new QuesQuestion();
            BeanUtils.copyProperties(questionAndAnswer,quesQuestion);
            quesQuestion.setCreateTime(new Date());
            quesQuestion.setIsDel(IsDeleteEnum.NORMAL.getCode());
            quesQuestion.setUpdateTime(new Date());
            quesQuestion.setQuestionTemplateId(quesQuestionTemplateId);
            String quesQuestionId = "";
            if(StringUtils.isNotBlank(quesQuestion.getId())){
                quesQuestionId = quesQuestion.getId();
            }else{
                quesQuestionId = IdLeaf.getId(IdConstant.QUES_QUESTION);
            }
            quesQuestion.setId(quesQuestionId);
            quesQuestions.add(quesQuestion);

            // 处理答案
            List<ListQuestionTemplateResp.Answer> answers1 = questionAndAnswer.getAnswers();
            for (ListQuestionTemplateResp.Answer answer : answers1) {
                QuesAnswer quesAnswer = new QuesAnswer();
                BeanUtils.copyProperties(answer,quesAnswer);
                quesAnswer.setIsDel(IsDeleteEnum.NORMAL.getCode());
                quesAnswer.setCreateTime(new Date());
                quesAnswer.setUpdateTime(new Date());
                quesAnswer.setQuestionId(quesQuestionId);
                quesAnswer.setId(IdLeaf.getId(IdConstant.QUES_ANSWER));
                answers.add(quesAnswer);
            }

            // 处理关联的 answerId
        }

        try {
            String questId = "";
            if(StringUtils.isNotBlank(requestData.getId())){
                questId = "QUESTION" + requestData.getId();
            }else{
                questId = "QUESTION" + quesQuestionTemplateId;
            }
            LogDTO logDTO = new LogDTO( StringUtils.isBlank(requestData.getId())? "新增":"编辑",
                    com.jnbyframework.boot.common.constant.CommonConstant.LOG_TYPE_2, CommonConstant.OPERATE_TYPE_2, questId);
            logDTO.setUsername(userId);
            iSysBaseAPI.addLog(logDTO);
        }catch (Exception e){
            log.info("打印数据  调用远程失败");
        }


        // 插入数据
        template.execute(action->{
            // 插入模版
            // 新增
            if(StringUtils.isBlank(requestData.getId())){
                // 设置id
                BeanUtils.copyProperties(requestData,quesQuestionTemplate);
                quesQuestionTemplate.setId(quesQuestionTemplateId);
                quesQuestionTemplate.setCreateTime(new Date());
                quesQuestionTemplate.setUpdateTime(new Date());
                quesQuestionTemplate.setIsDel(IsDeleteEnum.NORMAL.getCode());
                quesQuestionTemplateMapper.insert(quesQuestionTemplate);
            }else{
                //编辑
                BeanUtils.copyProperties(requestData,quesQuestionTemplate);
                quesQuestionTemplate.setUpdateTime(new Date());
                // 编辑不更新创建人
                quesQuestionTemplate.setCreateBy(null);
                quesQuestionTemplateMapper.updateById(quesQuestionTemplate);
//                List<QuesQuestion> quesQuestions1 = quesQuestionMapper.selectByQuestionTemplateId(quesQuestionTemplate.getId());
                // 删除配置等
                quesQuestionTemplateDetailMapper.updateByQuesQuestionTemplateId(quesQuestionTemplate.getId());
//                quesQuestionMapper.updateByQuesQuestionTemplateId(quesQuestionTemplate.getId());
//                if(CollectionUtils.isNotEmpty(quesQuestions1)){
//                    List<String> questionIds = quesQuestions1.stream().map(r -> r.getId()).collect(Collectors.toList());
//                    quesAnswerMapper.updateIsDelByQuestionIds(questionIds);
//                }
            }
            // 插入模版配置
            if(CollectionUtils.isNotEmpty(quesQuestionTemplateDetails)){
                for (QuesQuestionTemplateDetail quesQuestionTemplateDetail : quesQuestionTemplateDetails) {
                    quesQuestionTemplateDetailMapper.insert(quesQuestionTemplateDetail);
                }
            }
//            // 插入问题
//            if(CollectionUtils.isNotEmpty(quesQuestions)){
//                for (QuesQuestion quesQuestion : quesQuestions) {
//                    quesQuestionMapper.insert(quesQuestion);
//                }
//            }
//            // 插入答案
//            if(CollectionUtils.isNotEmpty(answers)){
//                for (QuesAnswer quesAnswer : answers) {
//                    quesAnswerMapper.insert(quesAnswer);
//                }
//            }
            return action;
        });
        return quesQuestionTemplate.getId();
    }

    @Override
    public List<ListQuestionTemplateResp.Answer> findQuestionAnswerList(List<String> questionIds) {
        if(CollectionUtils.isEmpty(questionIds)){
            return new ArrayList<>();
        }
        List<QuesAnswer> quesAnswers = quesAnswerMapper.selectByQuestionIds(questionIds);
        String jsonString = JSONObject.toJSONString(quesAnswers);
        return JSONObject.parseArray(jsonString, ListQuestionTemplateResp.Answer.class);
    }

    @Override
    public void changeStatus(ChangeStatus requestData,String userId) {
        if(StringUtils.isBlank(requestData.getId())){
            return ;
        }
        QuesQuestionTemplate quesQuestionTemplate1 = quesQuestionTemplateMapper.selectById(requestData.getId());
        if(quesQuestionTemplate1.getType().equals(1)){
            //需要判断  一个小程序  +  一个调用页面  + 场景  + 启用
            List<QuesQuestionTemplateDetail> quesQuestionTemplateDetails = quesQuestionTemplateDetailMapper.selectByQuestionIds(Arrays.asList(quesQuestionTemplate1.getId()));

            List<QuesQuestionTemplate> resList = quesQuestionTemplateMapper.selectByTypeAndSuitParamsAndSceneNotThis(quesQuestionTemplate1.getId(),quesQuestionTemplate1.getType(),quesQuestionTemplate1.getQuesSceneId(),
                    1,quesQuestionTemplateDetails.stream().map(r->r.getSuitParam()).collect(Collectors.toList()));
            if(resList.size() > 0){
                throw  new RuntimeException("调用页面已有别的启用中的模板");
            }
        }


        // 根据id进行更新状态
        QuesQuestionTemplate quesQuestionTemplate = new QuesQuestionTemplate();
        quesQuestionTemplate.setId(requestData.getId());
        quesQuestionTemplate.setStatus(requestData.getStatus());
        quesQuestionTemplate.setUpdateTime(new Date());

        try {
            LogDTO logDTO = new LogDTO(requestData.getStatus() == 1 ? "启用":"禁用",
                    com.jnbyframework.boot.common.constant.CommonConstant.LOG_TYPE_2, CommonConstant.OPERATE_TYPE_2, "QUESTION"+requestData.getId());
            logDTO.setUsername(userId);
            iSysBaseAPI.addLog(logDTO);
        }catch (Exception e){
            log.info("插入日志报错 = ",e);
        }
        quesQuestionTemplateMapper.updateById(quesQuestionTemplate);
    }

    @Override
    public Boolean checkCanSubmit(CheckCanSubmitReq requestData) {
        // 查询数据 看看是否已经提交过问卷  如果已经提交过问卷 那么直接返回false
        List<QuesUserAnswer> list = quesUserAnswerMapper.selectByQuestionTemplateIdAndUserId(requestData.getQuestionTemplateId(),requestData.getBizUserId(),requestData.getBizId());
        if(CollectionUtils.isNotEmpty(list)){
            return true;
        }
        return false;
    }

    @Override
    public FindRecordMsgByTemplateIdResp findRecordMsgByTemplateId(FindRecordMsgByTemplateId requestData) {
        FindRecordMsgByTemplateIdResp findRecordMsgByTemplateIdResp = new FindRecordMsgByTemplateIdResp();
        QuesQuestionTemplate quesQuestionTemplate = quesQuestionTemplateMapper.selectById(requestData.getQuestionTemplateId());
        if(quesQuestionTemplate.getStatus() != 1){
            log.info("当前id 未启用! id = {}",requestData.getQuestionTemplateId());
            return new FindRecordMsgByTemplateIdResp();
        }
        //
        List<SceneCodeEnum> orderList = SceneCodeEnum.getOrderList();
        List<String> sceneCodes = orderList.stream().map(r -> r.getCode()).collect(Collectors.toList());

        Date createTime = quesQuestionTemplate.getCreateTime();
        QuesScene quesScene = quesSceneMapper.selectById(quesQuestionTemplate.getQuesSceneId());
        findRecordMsgByTemplateIdResp.setSceneCode(quesScene.getSceneCode());
        String bizId = "";

        if(SceneCodeEnum.RIGHTS_CODE.getCode().equals(quesScene.getSceneCode())){
            // 生日优惠券权益类  查询该用户领取的生日券信息 今年领取的
            String nowYearNowMonth = DateUtil.parseDate(new Date(), "yyyy") +"01" ;
            String findBirthSendLogEndYearMonth = DateUtil.parseDate(new Date(), "yyyy")+"12";
            List<BirthCouponSendLog> birthCouponSendLogs  = birthCouponSendLogMapper.selectByCardNoSendMonthByStartAndEndOrderByCreateTime(Integer.parseInt(nowYearNowMonth),
                    Integer.parseInt(findBirthSendLogEndYearMonth),
                    requestData.getCardNo(), BirthCouponSendLogStatusConstatnt.SEND);
            if(CollectionUtils.isNotEmpty(birthCouponSendLogs)){
                // 需要排序rightsId
                List<String> collect = birthCouponSendLogs.stream().map(r -> r.getRightsId()).sorted().collect(Collectors.toList());
                List<String> voucherNos = birthCouponSendLogs.stream().map(r -> r.getVoucherNos()).sorted().collect(Collectors.toList());
                // 获取awardId
                List<BRights> bRights1 = bRightsMapper.selectByPrimaryKeys(collect);
                List<String> awardIds  = new ArrayList<>();
                for (BRights bRights : bRights1) {
                    // 获取awardId  生日优惠券
                    Map map = JSONObject.parseObject(bRights.getUseRule(), Map.class);
                    Object rule = map.get("rule");
                    List<Map> mapList = JSONObject.parseArray(JSONObject.toJSONString(rule), Map.class);
                    for (Map map1 : mapList) {
                        String couponId = map1.get("couponId").toString();
                        awardIds.add(couponId);
                    }
                }
                bizId = awardIds.stream().collect(Collectors.joining(","));
                findRecordMsgByTemplateIdResp.setBizId(bizId);
                findRecordMsgByTemplateIdResp.setRecordMsg(voucherNos.stream().collect(Collectors.joining(",")));
            }
        }else if(SceneCodeEnum.VOUCHER_CODE.getCode().equals(quesScene.getSceneCode())){
            // 券核销类
            String awardId = quesQuestionTemplate.getSceneParams();
            // 根据awardId 和 unionid  和 核销时间进行查询  核销时间要大于 问卷模版创建时间
            List<VoucherBase> list = voucherBaseMapper.selectVerifyedByUnionidAndAwardId(awardId,requestData.getUnionid(),createTime);
            if(CollectionUtils.isNotEmpty(list)){
                bizId = awardId;
                findRecordMsgByTemplateIdResp.setBizId(awardId);
                findRecordMsgByTemplateIdResp.setRecordMsg(list.get(0).getLinksource());
            }
        }else if(SceneCodeEnum.AI_CODE.getCode().equals(quesScene.getSceneCode())){
            // TODO AI生图比较特殊   因为这个在库里查不到   所以需要前端自己拼接   提交问卷时候弄   拼接 caseId,productId,productId
        }else if(sceneCodes.contains(quesScene.getSceneCode())){
            // 拿出来
            SceneCodeEnum sceneCodeEnum = orderList.stream().filter(r -> r.getCode().equals(quesScene.getSceneCode())).findFirst().get();
            // 查询clientVip  查询出来这个cardNo
            List<CclientVip> cclientVips = cclientVipMapper.selectByCardNos(Arrays.asList(requestData.getCardNo()));
            Long cVipId = cclientVips.get(0).getId();

            if(SceneCodeEnum.ORDER_REFUND_CODE.getCode().equals(sceneCodeEnum.getCode())){
                // 查询退货订单
                List<FindRecordMsgByTemplateIdResp> mretails = mRetailMapper.selectMretailByVipIdAndOrderType(cVipId,createTime);
                if(CollectionUtils.isNotEmpty(mretails)){
                    bizId = mretails.get(0).getBizId();
                    findRecordMsgByTemplateIdResp.setBizId(mretails.get(0).getBizId());
                    findRecordMsgByTemplateIdResp.setRecordMsg(mretails.get(0).getRecordMsg());
                }
            }else{
                // 去查询
                String ddly = sceneCodeEnum.getDdly();
                // 多个数据  然后进行查询
                List<String> ddlys = Arrays.asList(ddly.split(","));
                List<FindRecordMsgByTemplateIdResp> mretails = mRetailMapper.selectMretailByVipIdAndDdlys(cVipId,ddlys,createTime);
                if(CollectionUtils.isNotEmpty(mretails)){
                    bizId = mretails.get(0).getBizId();
                    findRecordMsgByTemplateIdResp.setBizId(mretails.get(0).getBizId());
                    findRecordMsgByTemplateIdResp.setRecordMsg(mretails.get(0).getRecordMsg());
                }
            }
        }else if(SceneCodeEnum.RIGHTS_WASH_CODE.getCode().equals(quesScene.getSceneCode())){
            // 洗护权益  券核销类  洗护 不需要调用这个  直接公众号发送的时候 直接把参数带给前端  awardId 和 券码
        }else if(SceneCodeEnum.RESERVATION_CODE.getCode().equals(quesScene.getSceneCode())){
            List<StoreReservationOrder> list = storeReservationOrderMapper.selectByCardNo(requestData.getCardNo());
            if(CollectionUtils.isNotEmpty(list)){
                Long id = list.get(0).getId();
                List<Long> productIds = storeReservationOrderMapper.selectProductIdByOrderId(id);
                if(CollectionUtils.isNotEmpty(productIds)){
                    findRecordMsgByTemplateIdResp.setRecordMsg(productIds.stream().map(r->r+"").collect(Collectors.joining(",")));
                }
                bizId = list.get(0).getOrderNo();
                findRecordMsgByTemplateIdResp.setBizId(bizId);
            }
        }else if(SceneCodeEnum.FIX_CLOTH_CODE.getCode().equals(quesScene.getSceneCode())){
            List<PosRepairOrder> posRepairOrders = posRepairOrderMapper.selectByCardno(requestData.getCardNo());
            if(CollectionUtils.isNotEmpty(posRepairOrders)){
                Long id = posRepairOrders.get(0).getId();
                List<Long> productIds =  posRepairOrderMapper.selectProductIdsByOrderId(id);
                if(CollectionUtils.isNotEmpty(productIds)){
                    findRecordMsgByTemplateIdResp.setRecordMsg(productIds.stream().map(r->r+"").collect(Collectors.joining(",")));
                }
                bizId = posRepairOrders.get(0).getOrderno();
                findRecordMsgByTemplateIdResp.setBizId(bizId);
//            findRecordMsgByTemplateIdResp.setRecordMsg();
            }
        }

        List<QuesUserAnswer> list = quesUserAnswerMapper.selectByQuestionTemplateIdAndUserId(requestData.getQuestionTemplateId(),requestData.getUnionid(),bizId);
        if(CollectionUtils.isNotEmpty(list)){
            findRecordMsgByTemplateIdResp.setCanSubmit(false);
        }else{
            findRecordMsgByTemplateIdResp.setCanSubmit(true);
        }

        return findRecordMsgByTemplateIdResp;
    }

    @Override
    public void submitQuestion(List<SubmitQuestionReq> requestData) {
        // 需要查询是否启用
        String questionTemplateId = requestData.get(0).getQuestionTemplateId();
        QuesQuestionTemplate quesQuestionTemplate = quesQuestionTemplateMapper.selectById(questionTemplateId);
        if(quesQuestionTemplate.getStatus().equals(0)){
            throw new RuntimeException("5000");
        }
        // 查看是否已经提交
        List<QuesUserAnswer> quesUserAnswers = quesUserAnswerMapper.selectByQuestionTemplateIdAndUserId(requestData.get(0).getQuestionTemplateId(),
                requestData.get(0).getBizUserId(),requestData.get(0).getBizId());
        if(CollectionUtils.isNotEmpty(quesUserAnswers)){
            throw new RuntimeException("5001");
        }

        // 如果非自由问卷  判断 bizId  如果非 自由问卷  bizId 为空  则不允许提交
        QuesScene quesScene1 = quesSceneMapper.selectById(quesQuestionTemplate.getQuesSceneId());
        if(!quesScene1.getSceneCode().equals(SceneCodeEnum.FREE_CODE.getCode())){
            List<SubmitQuestionReq> collect = requestData.stream().filter(r -> StringUtils.isBlank(r.getBizId())).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(collect)){
                throw new RuntimeException("未查询到业务id，不能提交问卷!");
            }
        }

        List<QuesUserAnswer> list = new ArrayList<>();

        for (SubmitQuestionReq requestDatum : requestData) {
            QuesUserAnswer quesUserAnswer = new QuesUserAnswer();
            BeanUtils.copyProperties(requestDatum,quesUserAnswer);
            // 查询问题
            QuesQuestion quesQuestion = quesQuestionMapper.selectById(requestDatum.getQuestionId());
            // 如果是多选
            if(quesQuestion.getType().equals(1)){
                String answerIds = requestDatum.getAnswerId();
                String[] split = answerIds.split(",");
                for (String answerId : split) {
                    // 查询答案
                    QuesUserAnswer quesUserAnswer2 = new QuesUserAnswer();
                    BeanUtils.copyProperties(requestDatum,quesUserAnswer2);
                    QuesAnswer quesAnswer = quesAnswerMapper.selectById(answerId);
                    quesUserAnswer2.setCreateTime(new Date());
                    quesUserAnswer2.setUpdateTime(new Date());
                    quesUserAnswer2.setContent(quesAnswer.getAnswer());
                    quesUserAnswer2.setAnswerId(answerId);
                    quesUserAnswer2.setIsDel(IsDeleteEnum.NORMAL.getCode());
                    quesUserAnswer2.setId(IdLeaf.getId(IdConstant.QUES_USER_ANSWER));
                    list.add(quesUserAnswer2);
                }
            }else if(quesQuestion.getType().equals(3) || quesQuestion.getType().equals(4) || quesQuestion.getType().equals(5) || quesQuestion.getType().equals(6)){
                quesUserAnswer.setCreateTime(new Date());
                quesUserAnswer.setUpdateTime(new Date());
                quesUserAnswer.setIsDel(IsDeleteEnum.NORMAL.getCode());
                quesUserAnswer.setId(IdLeaf.getId(IdConstant.QUES_USER_ANSWER));
                list.add(quesUserAnswer);
            }else{
                QuesAnswer quesAnswer = quesAnswerMapper.selectById(requestDatum.getAnswerId());
                quesUserAnswer.setCreateTime(new Date());
                quesUserAnswer.setUpdateTime(new Date());
                quesUserAnswer.setIsDel(IsDeleteEnum.NORMAL.getCode());
                quesUserAnswer.setId(IdLeaf.getId(IdConstant.QUES_USER_ANSWER));
                quesUserAnswer.setContent(quesAnswer.getAnswer());
                list.add(quesUserAnswer);
            }
        }

        if(CollectionUtils.isNotEmpty(list)){
            template.execute(action->{
                iQuesUserAnswerService.saveBatch(list);
                return action;
            });
        }
    }

    @Override
    public String createOrUpdateQuestion(ListQuestionTemplateResp.QuestionAndAnswer requestData, String userId) {
        if(StringUtils.isBlank(requestData.getQuestionTemplateId())){
            throw new RuntimeException("模版id未传递!");
        }
        // 限制总数量  一级 和 二级 加在一起
        QuesQuestionTemplate quesQuestionTemplate = quesQuestionTemplateMapper.selectById(requestData.getQuestionTemplateId());
        // 查询总数量 + 1
        List<QuesQuestion> quesQuestions = quesQuestionMapper.selectByQuestionTemplateId(quesQuestionTemplate.getId());

        if(quesQuestionTemplate.getType() == 1){
            //组件最多三个
            if(quesQuestions.size() + 1 > 3){
                throw new RuntimeException("组件形式最多可支持3个问题(1级和2级的总数)");
            }
        }else{
            if(StringUtils.isBlank(requestData.getId())){
//                // 筛选出来一级问题
//                List<QuesQuestion> collect = quesQuestions.stream().filter(r -> StringUtils.isBlank(r.getParentsId())).collect(Collectors.toList());
//                // 添加一级问题
//                if(StringUtils.isBlank(requestData.getParentsId())){
//                    // 最多10个1级问题
//                    if(collect.size() +1  > 10){
//                        throw new RuntimeException("页面形式最多可支持10个1级问题(包含10个)");
//                    }
//                }

                if(quesQuestions.size() + 1 > 50){
                    throw new RuntimeException("页面形式最多可支持50个问题(包含50个)");
                }
            }
        }
        // 如果是主问题  看每一个问题 是否关联了子问题，如果关联了  则不允许删除

        //新增
        List<QuesAnswer> answers = new ArrayList<>();
        // 编辑
        List<QuesAnswer> updateAwsers = new ArrayList<>();

        QuesQuestion quesQuestion = new QuesQuestion();
        BeanUtils.copyProperties(requestData,quesQuestion);
        quesQuestion.setCreateTime(new Date());
        quesQuestion.setIsDel(IsDeleteEnum.NORMAL.getCode());
        quesQuestion.setUpdateTime(new Date());
        String quesQuestionId = "";
        if(StringUtils.isNotBlank(quesQuestion.getId())){
            quesQuestionId = quesQuestion.getId();
        }else{
            quesQuestionId = IdLeaf.getId(IdConstant.QUES_QUESTION);
        }
        quesQuestion.setId(quesQuestionId);

        // 处理答案  updateAnswer  修改的答案    无数据是新增的答案   需要跟老的答案进行对比
        List<ListQuestionTemplateResp.Answer> answers1 = requestData.getAnswers();
        for (ListQuestionTemplateResp.Answer answer : answers1) {
            QuesAnswer quesAnswer = new QuesAnswer();
            BeanUtils.copyProperties(answer,quesAnswer);
            quesAnswer.setIsDel(IsDeleteEnum.NORMAL.getCode());
            quesAnswer.setCreateTime(new Date());
            quesAnswer.setUpdateTime(new Date());
            quesAnswer.setQuestionId(quesQuestionId);
            if(StringUtils.isNotBlank(quesAnswer.getId())){
                updateAwsers.add(quesAnswer);
            }else{
                quesAnswer.setId(IdLeaf.getId(IdConstant.QUES_ANSWER));
                answers.add(quesAnswer);
            }
        }

        List<String> questionIds = new ArrayList<>();
        questionIds.add(quesQuestion.getId());
        List<QuesAnswer> quesAnswers = quesAnswerMapper.selectByQuestionIds(questionIds);
        // 删除数据   去除编辑的都是删除
        List<String> collect = updateAwsers.stream().map(x -> x.getId()).collect(Collectors.toList());
        List<String> shouldDelete = quesAnswers.stream().filter(r -> !collect.contains(r.getId())).map(r -> r.getId()).collect(Collectors.toList());
        // 如果删除的问题关联了子问题， 则不允许删除  需要 处理完子问题才可以进行删除 查询子问题
        List<QuesQuestion> quesQuestions1 = quesQuestionMapper.selectByPartenrsId(quesQuestion.getId());
        if(CollectionUtils.isNotEmpty(quesQuestions1)){
            for (QuesQuestion question : quesQuestions1) {
                // 关联的答案ids
                List<String> list = Arrays.asList(question.getAnswerId().split(","));
                if(new HashSet<>(shouldDelete).containsAll(list)){
                    // 删除当前问题
                    QuesQuestion quesQuestionUpdate = new QuesQuestion();
                    quesQuestionUpdate.setId(question.getId());
                    quesQuestionUpdate.setIsDel(IsDeleteEnum.IS_DELETED.getCode());
                    quesQuestionMapper.updateById(quesQuestionUpdate);
                }else{
                    // 非删除的 保存一下
                    List<String> collect1 = new HashSet<>(list).stream().filter(r -> !shouldDelete.contains(r)).collect(Collectors.toList());
                    if(CollectionUtils.isNotEmpty(collect1)){
                        // 设置问题答案
                        QuesQuestion quesQuestionUpdate = new QuesQuestion();
                        quesQuestionUpdate.setId(question.getId());
                        quesQuestionUpdate.setAnswerId(collect1.stream().collect(Collectors.joining(",")));
                        quesQuestionMapper.updateById(quesQuestionUpdate);
                    }
                }
            }
        }

        // 插入问题
        template.execute(action->{
            if(StringUtils.isNotBlank(requestData.getId())){
                quesQuestionMapper.updateById(quesQuestion);
            }else{
                quesQuestionMapper.insert(quesQuestion);
            }
            // 根据问题查询答案id

            if(CollectionUtils.isEmpty(quesAnswers)){
                // 插入数据
                if(CollectionUtils.isNotEmpty(answers)){
                    for (QuesAnswer quesAnswer : answers) {
                        quesAnswerMapper.insert(quesAnswer);
                    }
                }
            }else{
                // 编辑数据
                if(CollectionUtils.isNotEmpty(updateAwsers)){
                    for (QuesAnswer quesAnswer : updateAwsers) {
                        quesAnswerMapper.updateById(quesAnswer);
                    }
                }

                if(CollectionUtils.isNotEmpty(shouldDelete)){
                    for (String quesAnswerId : shouldDelete) {
                        QuesAnswer updateq = new QuesAnswer();
                        updateq.setId(quesAnswerId);
                        updateq.setIsDel(IsDeleteEnum.IS_DELETED.getCode());
                        quesAnswerMapper.updateById(updateq);
                    }
                }
                // 新增数据
                if(CollectionUtils.isNotEmpty(answers)){
                    for (QuesAnswer quesAnswer : answers) {
                        quesAnswerMapper.insert(quesAnswer);
                    }
                }
            }
            return action;
        });
        return quesQuestion.getId();
    }

    @Override
    public void deleteQuestionAndAnswer(String questionId, String userId) {
        QuesQuestion updateQuestion = new QuesQuestion();
        updateQuestion.setId(questionId);
        updateQuestion.setIsDel(IsDeleteEnum.IS_DELETED.getCode());

        // 根据问题id删除答案
        List<String> questionIds = new ArrayList<>();
        questionIds.add(questionId);

        template.execute(action->{
            quesQuestionMapper.updateById(updateQuestion);
            // 删除所有子问题
            quesQuestionMapper.updateIsDelByParentsId(questionId);
            quesAnswerMapper.updateIsDelByQuestionIds(questionIds);
            return action;
        });
    }

    @Override
    public void washSendMsgFinishJob(String openId) {
        // 查询模板是否开启呢
        List<QuesQuestionTemplate> list = quesQuestionTemplateMapper.selectActiveBySceneCode(SceneCodeEnum.RIGHTS_WASH_CODE.getCode());
        if(CollectionUtils.isEmpty(list)){
            log.info("洗护权益问卷模版未开启!");
            return ;
        }
        // 获取到模板id  awardId  券号
        String quesTemplateId = list.get(0).getId();
        // 查询需要发消息的数据  期限为 30天
        com.github.pagehelper.Page<VoucherVirtualBase> hPage = PageHelper.startPage(1, 2000);
        List<VoucherVirtualBase> voucherVirtualBases = voucherVirtualBaseMapper.selectListByCreateTime(openId);
        PageInfo<VoucherVirtualBase> pageInfo = new PageInfo(hPage);
        List<VoucherVirtualBase> list1 = pageInfo.getList();
        // 更改标记已经发送过公众号消息
        for (VoucherVirtualBase voucherVirtualBase : list1) {
            // 插入数据
            WashSendQuesLog washSendQuesLog = new WashSendQuesLog();
            washSendQuesLog.setId(voucherVirtualBase.getId().longValue());
            washSendQuesLogMapper.insert(washSendQuesLog);
            // 发送公众号消息
            sendWxOpenMsg(quesTemplateId,voucherVirtualBase.getAwarid(),voucherVirtualBase.getLinkno(),voucherVirtualBase.getWeid(),voucherVirtualBase.getOpenid(),
                    voucherVirtualBase.getConsumeday(),voucherVirtualBase.getTradeid());
        }
    }

    @Override
    public void deleteTemplateById(String templateId, String userId) {
        QuesQuestionTemplate quesQuestionTemplate = new QuesQuestionTemplate();
        quesQuestionTemplate.setIsDel(IsDeleteEnum.IS_DELETED.getCode());
        quesQuestionTemplate.setId(templateId);
        quesQuestionTemplateMapper.updateById(quesQuestionTemplate);

        // 删除问题
        QuesQuestion quesQuestion = quesQuestionMapper.selectById(templateId);
        quesQuestionMapper.updateByQuesQuestionTemplateId(templateId);
        // 删除问题答案
        quesAnswerMapper.updateIsDelByQuestionIds(Arrays.asList(quesQuestion.getId()));
    }

    @Override
    public String findRefundTemplateId(FindRefundTemplateIdReq requestData) {
        List<QuesQuestionTemplate> list = quesQuestionTemplateMapper.selectBySceneCode(SceneCodeEnum.ORDER_REFUND_CODE.getCode(),requestData.getWeid());
        if(CollectionUtils.isEmpty(list)){
            return null;
        }
        return list.get(0).getId();
    }

    @Override
    public String findTemplateIdBySceneCode(String sceneCode) {
        List<QuesQuestionTemplate> list = quesQuestionTemplateMapper.selectBySceneCode(sceneCode,null);
        if(CollectionUtils.isEmpty(list)){
            return null;
        }
        return list.get(0).getId();
    }

    @Override
    public String findTemplateId(FindTemplateIdReq requestData) {
        List<QuesQuestionTemplate> list = quesQuestionTemplateMapper.selectBySceneCode(requestData.getCode(),requestData.getWeid());
        if(CollectionUtils.isEmpty(list)){
            return null;
        }
        return list.get(0).getId();
    }

    @Override
    public List<QuesUserAnswer> findUserSubmitAnswerList(CheckCanSubmitReq requestData) {
        List<QuesUserAnswer> list = quesUserAnswerMapper.selectByQuestionTemplateIdAndUserId(requestData.getQuestionTemplateId(),requestData.getBizUserId(),requestData.getBizId());
        return list;
    }

    private void sendWxOpenMsg(String quesTemplateId, BigDecimal awarid, String linkno,BigDecimal weid,String openId,Date consumeDay ,String tradeId) {
        // 发送公众号消息  公众号appid
        String appid = null;
        String miniappid = null;
        String brandName = "";
        String offTemplateId = "";
        String jumpUrl =  "";
        // 根据品牌映射成appid
        List<Map> brandConfigs = JSONObject.parseArray(commonConfigs, Map.class);
        for (Map brandConfig : brandConfigs) {
            if(brandConfig.get("weid").toString().equals(weid+"")){
                // 公众号appid
                appid = brandConfig.get("offAppId").toString();
                miniappid = brandConfig.get("appid").toString();
                offTemplateId  =  brandConfig.get("offWashTemplateId").toString();
                jumpUrl = brandConfig.get("offWashUrl").toString();
            }
        }
        if(appid == null){
            log.info("未能匹配到正确的weid = {}",weid);
            return ;
        }

        List<SendWxOffMsgContext> sendWxOffMsgContexts = new ArrayList<>();
        Map<String,String> params = new HashMap<>();
        params.put("appId",miniappid);
        params.put("path",jumpUrl+"="+quesTemplateId+"&awardId="+awarid+"&linkNo="+linkno);
        params.put("first","您好,您的洗衣订单已完成。");
        params.put("keyword1",tradeId);
        params.put("keyword2", DateUtils.formatDate(consumeDay,"yyyy-MM-dd"));
        params.put("remark","点击链接对洗衣服务进行评价。");

        SendWxOffMsgContext sendWxOffMsgContext = new SendWxOffMsgContext();
        SendWxOffMsgContext.WxTemplateMsgSendDTO build = SendWxOffMsgContext.build(openId, offTemplateId, params);
        sendWxOffMsgContext.setWxTemplateMsgSendDTO(build);
        sendWxOffMsgContext.setAppid(appid);
//        sendWxOffMsgContext.setUnionId(openId);
        sendWxOffMsgContexts.add(sendWxOffMsgContext);
        messageService.sendWxOffMsg(sendWxOffMsgContexts);
    }


    private void getQuestionV2(List<ListQuestionTemplateResp.QuestionAndAnswer> questionAndAnswers,
                               List<ListQuestionTemplateResp.QuestionAndAnswer> result,
                               String id,
                               String partensId) {
        if(CollectionUtils.isNotEmpty(questionAndAnswers)){
            for (ListQuestionTemplateResp.QuestionAndAnswer questionAndAnswer : questionAndAnswers) {
                String idAll = IdLeaf.getId(IdConstant.QUES_QUESTION);
                questionAndAnswer.setId(idAll);
                questionAndAnswer.setParentsId(partensId);
                result.add(questionAndAnswer);
                getQuestionV2(questionAndAnswer.getChildQuestions(),result,idAll,idAll);
            }
        }
    }

    private void getQuestion(List<ListQuestionTemplateResp.QuestionAndAnswer> questionAndAnswers, List<ListQuestionTemplateResp.QuestionAndAnswer> result) {
        for (ListQuestionTemplateResp.QuestionAndAnswer questionAndAnswer : questionAndAnswers) {
            result.add(questionAndAnswer);
            if(CollectionUtils.isNotEmpty(questionAndAnswer.getChildQuestions())){
                getQuestion(questionAndAnswer.getChildQuestions(),result);
            }
        }
    }
}
