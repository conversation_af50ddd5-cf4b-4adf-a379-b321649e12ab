package org.springcenter.marketing.modules.model;

import com.baomidou.mybatisplus.annotation.TableName;

import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * @Author: lwz
 * @Date: 2023-03-06 21:14:15
 * @Description: B_SOURCE_POS_TEMPLATE
 */
@TableName("b_source_pos_template")
@Accessors(chain = true)
@ApiModel(value="BSourcePosTemplate对象", description="")
public class BSourcePosTemplate implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "ID")
    private String id;

    @ApiModelProperty(value = "应用id")
    @TableField("APP_ID")
    private String appId;

    @ApiModelProperty(value = "应用名字")
    @TableField("APP_NAME")
    private String appName;

    @ApiModelProperty(value = "位置管理id")
    @TableField("POS_MANAGER_ID")
    private String posManagerId;

    @ApiModelProperty(value = "位置管理名字")
    @TableField("POS_MANAGER_NAME")
    private String posManagerName;

    @ApiModelProperty(value = "位置(1头部背景图,2九宫格,3个人中心资源位一,4个人中心资源位二, 5权益中心资源位)'")
    @TableField("POSITION")
    private Long position;

    @ApiModelProperty(value = "图片")
    @TableField("IMAGE")
    private String image;

    @ApiModelProperty(value = "排序")
    @TableField("SORT_NO")
    private Long sortNo;

    @ApiModelProperty(value = "标题文案")
    @TableField("TITLE_TXT")
    private String titleTxt;

    @ApiModelProperty(value = "备注")
    @TableField("REMARK")
    private String remark;

    @ApiModelProperty(value = "适用人群包id")
    @TableField("ADAPT_PEOPLE_ID")
    private String adaptPeopleId;

    @ApiModelProperty(value = "适用人群包名字")
    @TableField("ADAPT_PEOPLE_NAME")
    private String adaptPeopleName;

    @ApiModelProperty(value = "适用门店")
    @TableField("ADAPT_STORE_ID")
    private String adaptStoreId;

    @ApiModelProperty(value = "适用门店名字")
    @TableField("ADAPT_STORE_NAME")
    private String adaptStoreName;

    @ApiModelProperty(value = "跳转类型")
    @TableField("JUMP_TYPE")
    private Long jumpType;


    @ApiModelProperty(value = "跳转小程序id")
    @TableField("MINI_APP_ID")
    private String miniAppId;


    @ApiModelProperty(value = "跳转小程序路径")
    @TableField("MINI_APP_PATH")
    private String miniAppPath;


    @ApiModelProperty(value = "视频id")
    @TableField("VIDEO_ID")
    private String videoId;


    @ApiModelProperty(value = "h5链接")
    @TableField("H5_LINK")
    private String h5Link;


    @ApiModelProperty(value = "显示时间类型 0自定义 1按用户生日")
    @TableField("SHOW_TIME_TYPE")
    private Long showTimeType;


    @ApiModelProperty(value = "开始时间")
    @TableField("START_TIME")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    @TableField("END_TIME")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    @ApiModelProperty(value = "0生日前一个月至生日月结束 1仅生日当月")
    @TableField("BIRTHDAY_TIME")
    private Long birthdayTime;

    @ApiModelProperty(value = "上下架 0上架 1下架")
    @TableField("ENABLE_STATUS")
    private Long enableStatus;

    @ApiModelProperty(value = "说明文案")
    @TableField("EXPOSITORY_CASE")
    private String expositoryCase;

    @ApiModelProperty(value = "搜索词")
    @TableField("SEARCH_TXT")
    private String searchTxt;

    @TableField("CREATE_TIME")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @TableField("CREATE_BY")
    private String createBy;

    @TableField("UPDATE_BY")
    private String updateBy;

    @TableField("UPDATE_TIME")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @ApiModelProperty(value = "删除（0否 1是）")
    @TableField("DEL_FLAG")
    private Long delFlag;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public String getPosManagerId() {
        return posManagerId;
    }

    public void setPosManagerId(String posManagerId) {
        this.posManagerId = posManagerId;
    }

    public String getPosManagerName() {
        return posManagerName;
    }

    public void setPosManagerName(String posManagerName) {
        this.posManagerName = posManagerName;
    }

    public String getImage() {
        return image;
    }

    public void setImage(String image) {
        this.image = image;
    }

    public Long getSortNo() {
        return sortNo;
    }

    public void setSortNo(Long sortNo) {
        this.sortNo = sortNo;
    }

    public String getTitleTxt() {
        return titleTxt;
    }

    public void setTitleTxt(String titleTxt) {
        this.titleTxt = titleTxt;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getAdaptPeopleId() {
        return adaptPeopleId;
    }

    public void setAdaptPeopleId(String adaptPeopleId) {
        this.adaptPeopleId = adaptPeopleId;
    }

    public String getAdaptStoreId() {
        return adaptStoreId;
    }

    public void setAdaptStoreId(String adaptStoreId) {
        this.adaptStoreId = adaptStoreId;
    }

    public Long getJumpType() {
        return jumpType;
    }

    public void setJumpType(Long jumpType) {
        this.jumpType = jumpType;
    }

    public void setShowTimeType(Long showTimeType) {
        this.showTimeType = showTimeType;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Long getBirthdayTime() {
        return birthdayTime;
    }

    public void setBirthdayTime(Long birthdayTime) {
        this.birthdayTime = birthdayTime;
    }

    public Long getEnableStatus() {
        return enableStatus;
    }

    public void setEnableStatus(Long enableStatus) {
        this.enableStatus = enableStatus;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Long delFlag) {
        this.delFlag = delFlag;
    }

    public String getAdaptPeopleName() {
        return adaptPeopleName;
    }

    public void setAdaptPeopleName(String adaptPeopleName) {
        this.adaptPeopleName = adaptPeopleName;
    }

    public String getAdaptStoreName() {
        return adaptStoreName;
    }

    public void setAdaptStoreName(String adaptStoreName) {
        this.adaptStoreName = adaptStoreName;
    }

    public Long getPosition() {
        return position;
    }

    public void setPosition(Long position) {
        this.position = position;
    }

    public String getMiniAppId() {
        return miniAppId;
    }

    public void setMiniAppId(String miniAppId) {
        this.miniAppId = miniAppId;
    }

    public String getMiniAppPath() {
        return miniAppPath;
    }

    public void setMiniAppPath(String miniAppPath) {
        this.miniAppPath = miniAppPath;
    }

    public String getVideoId() {
        return videoId;
    }

    public void setVideoId(String videoId) {
        this.videoId = videoId;
    }

    public String getH5Link() {
        return h5Link;
    }

    public void setH5Link(String h5Link) {
        this.h5Link = h5Link;
    }

    public Long getShowTimeType() {
        return showTimeType;
    }

    public String getExpositoryCase() {
        return expositoryCase;
    }

    public void setExpositoryCase(String expositoryCase) {
        this.expositoryCase = expositoryCase;
    }


    public String getSearchTxt() {
        return searchTxt;
    }

    public void setSearchTxt(String searchTxt) {
        this.searchTxt = searchTxt;
    }

    @Override
    public String toString() {
        return "BSourcePosTemplate{" +
                "id='" + id + '\'' +
                ", appId='" + appId + '\'' +
                ", appName='" + appName + '\'' +
                ", posManagerId='" + posManagerId + '\'' +
                ", posManagerName='" + posManagerName + '\'' +
                ", position=" + position +
                ", image='" + image + '\'' +
                ", sortNo=" + sortNo +
                ", titleTxt='" + titleTxt + '\'' +
                ", remark='" + remark + '\'' +
                ", adaptPeopleId='" + adaptPeopleId + '\'' +
                ", adaptPeopleName='" + adaptPeopleName + '\'' +
                ", adaptStoreId='" + adaptStoreId + '\'' +
                ", adaptStoreName='" + adaptStoreName + '\'' +
                ", jumpType=" + jumpType +
                ", miniAppId='" + miniAppId + '\'' +
                ", miniAppPath='" + miniAppPath + '\'' +
                ", videoId='" + videoId + '\'' +
                ", h5Link='" + h5Link + '\'' +
                ", showTimeType=" + showTimeType +
                ", startTime=" + startTime +
                ", endTime=" + endTime +
                ", birthdayTime=" + birthdayTime +
                ", enableStatus=" + enableStatus +
                ", expositoryCase='" + expositoryCase + '\'' +
                ", searchTxt='" + searchTxt + '\'' +
                ", createTime=" + createTime +
                ", createBy='" + createBy + '\'' +
                ", updateBy='" + updateBy + '\'' +
                ", updateTime=" + updateTime +
                ", delFlag=" + delFlag +
                '}';
    }
}
