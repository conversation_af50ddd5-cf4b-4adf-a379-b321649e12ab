package org.springcenter.marketing.modules.repository;

import org.springcenter.marketing.api.dto.*;
import org.springcenter.marketing.modules.context.PeoplePagEntity;
import org.springcenter.marketing.modules.model.*;
import org.apache.commons.lang3.tuple.Pair;

import java.text.ParseException;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date:2023/2/9 10:15
 */
public interface ITaskRepository {
    List<TaskTemplateOperator> queryTaskOperators(TaskOperatorsReq requestData);


    List<TaskTemplateInfo> querySameTypeUnFinishTask(Integer taskType, String weId);

    /**
     * 获取任务编号
     * @return 返回
     */
    String getTaskTemplateNo();

    /**
     * 获取任务编号
     * @return 返回
     */
    String getTaskPartDetailNo();

    /**
     * 批量插入模板和日志
     * @param insertTaskTemplates
     * @param insertLogs
     */
    void batchInsertDataAndLogs(List<TaskTemplate> insertTaskTemplates, List<TaskTemplateLog> insertLogs,
                                List<TaskTemplateLimitation> insertPeopleInfo, List<TaskTemplateOutInfo> outInfos,
                                List<TaskTemplateExecution> executions, List<TaskTemplateRewardInfo> rewardInfos);

    /**
     * 查询签到类型的任务
     * @param code 签到code
     * @return 返回
     */
    TaskTemplate querySignInTask(Integer code, String weId);

    /**
     * 查询任务模板列表
     * @param requestData 请求参数
     * @return 返回
     */
    List<TaskTemplate> queryTaskLists(TaskTemplateListReq requestData);

    /**
     * 根据模板id查找任务模板信息
     * @param id 任务模板id
     * @return 返回模板信息
     */
    TaskTemplateDto selectTaskTemplateById(String id);

    /**
     * 更新数据
     *
     * @param taskTemplate         任务模板
     * @param taskTemplateLog      任务模板日志
     * @param insertPeopleInfo     任务模板人群限制
     * @param taskTemplateOperator 任务模板操作者
     * @param oldPeopleInfo        任务模板人群更新
     * @param oldVersion           旧版本
     */
    void updateTaskTemplateAndOther(TaskTemplate taskTemplate, TaskTemplateLog taskTemplateLog,
                            List<TaskTemplateLimitation> insertPeopleInfo, TaskTemplateOperator taskTemplateOperator,
                            List<TaskTemplateLimitation> oldPeopleInfo, String oldVersion,
                            TaskTemplateOutInfo outInfo,  TaskTemplateExecution execution,
                            List<TaskTemplateRewardInfo> rewardInfos);

    /**
     * 根据类型和状态筛选当前类型正在进行的任务
     * @param taskType 任务类型
     * @param code 状态
     * @return 返回
     */
    TaskTemplateInfo selectTaskByStatusAndType(Integer taskType, Integer code, String weId);

    /**
     * 操作更新数据
     * @param taskTemplate 模板
     * @param taskTemplateLog 模板日志
     * @param taskTemplateOperator 操作人日志
     */
    void operateTaskTemplate(TaskTemplate taskTemplate, TaskTemplateLog taskTemplateLog, TaskTemplateOperator taskTemplateOperator);

    /**
     * 根据条件查询任务明细列表
     * @param requestData 返回
     */
    List<TaskTemplateDetailsResp> queryTaskTemplateDetails(TaskTemplateDetailsReq requestData);

    /**
     * 查找未结束的任务
     * @return 返回
     */
    List<TaskTemplate> selectInProcessTask();

    /**
     * 未开始的任务
     * @return 返回
     */
    List<TaskTemplate> selectUnStartTask();

    /**
     * 更新模板
     * @param v 模板
     */
    void updateTaskTemplate(TaskTemplate v);

    /**
     * 更新任务模板日志
     * @param taskTemplateLog 模板日志
     */
    void insertTaskTemplateLog(TaskTemplateLog taskTemplateLog);

    /**
     * 更新所有当前任务且是当前周期的任务状态为失效
     * @param id 任务id
     */
    void updateTaskTemplatePartDetailByTaskTempId(String id);


    /**
     * 获取当前所有有效且在循环周期的活动
     * @return 出参
     */
    List<TaskTemplateInfo> selectInProcessTaskAndInCycle(UserTaskListReq req);


    /**
     * 查询用户是否存在当前任务
     * @param v 任务模板信息
     * @param requestData 查询参数
     * @return 返回
     */
    TaskPartDetails selectUserPartByTaskInfo(TaskInfo v, UserTaskListReq requestData);

    /**
     * 用户参与模板插入
     * @param inserts 插入数据
     */
    void batchInsertTaskPartDetails(List<TaskPartDetails> inserts);

    /**
     * 用户参与的模板修改
     * @param updates 更新数据
     */
    void batchUpdateTaskPartDetails(List<TaskPartDetails> updates);

    /**
     * 根据用户和活动类型查询
     * @param type 类型
     * @param unionId 用户
     * @return 返回信息
     */
    TaskPartDetails selectTaskPartDetailsByUnionIdAndType(Integer type, String unionId, String weId);

    /**
     * 根据任务id查询任务
     * @param taskTempId 任务id
     * @return 返回任务
     */
    TaskTemplateInfo getTaskTemplateById(String taskTempId);

    /**
     * 更新用户任务信息
     * @param taskPartDetails 用户任务信息
     */
    void updateTaskPartDetails(TaskPartDetails taskPartDetails);

    /**
     * 插入用户任务详情表
     * @param taskPartDetailsItem
     */
    void insertTaskPartDetailsItem(TaskPartDetailsItem taskPartDetailsItem);

    /**
     * 根据任务id更新任务限制人群信息
     * @param id 任务id
     */
    void updateTaskTemplateLimitationStatus(String id);


    /**
     * 根据id查询用户参与任务
     * @param id id
     * @return 返回
     */
    TaskPartDetails queryTaskPartDetailsById(String id);

    /**
     * 获取进行中和未开始的数量
     * @return
     */
    Pair queryNumRunningAndWaiting(TaskTemplateListReq req);


    /**
     * 下架已结束的任务
     */
    void takeOffTheShelf();

    /**
     * 过期用户任务
     */
    void expireUserTask();

    /**
     * 根据第三方信息查询明细
     * @param requestData 入参
     * @return 反参
     */
    TaskPartDetailsItem selectItemByThirdPartId(ListenerEvent requestData);

    /**
     * 根据模板id查找积分信息
     * @param taskTempId 模板id
     * @return 返回
     */
    List<TaskTemplateRewardInfo> queryTaskTempRewardInfo(String taskTempId);

    /**
     * 根据门店获取门店包id集合
     * @param storeCode 用户绑定门店
     * @return 返回该门店所在的门店包ids
     */
    List<String> selectUserStoreInfo(String storeCode);

    /**
     * 查询用户签到任务
     * @param unionId 用户
     * @param code 类型
     * @return 返回
     */
    List<TaskPartDetails> querySignInTaskByUnionId(String unionId, Integer code);


    /**
     * 根据品牌id查询任务
     * @param weId
     * @return
     */
    List<TaskTemplateInfo> selectTaskByWeId(String weId);

    /**
     * 根据日志查询是否有签到任务
     * @param unionId
     * @param code
     * @return
     */
    List<TaskPartDetailsItem> querySignLogByUnionId(String unionId, Integer code);

    /**
     * 单纯插入
     * @param backLog 插入
     */
    void insertDataByTaskPartDetailsItem(TaskPartDetailsItem backLog);

    /**
     * 查询前六天的任务列表
     * @param unionId 用户id
     * @param weId 品牌id
     * @return 返回
     */
    List<TaskPartDetails> queryBeforeSixTaskList(String unionId, String weId, Date start, Date end);

    /**
     * 获取当天已生成的任务
     * @param weId 品牌id
     * @param unionId 用户id
     * @return 返回
     */
    List<TaskPartDetails> queryTodayTaskList(String weId, String unionId) throws ParseException;

    /**
     * 获取所有完成过的新手任务
     * @param weId 品牌id
     * @param unionId 用户id
     * @return 返回
     */
    List<TaskPartDetails> queryAllNewerTaskList(String weId, String unionId);

    /**
     * 获取作废的消费任务
     * @return 返回
     */
    Map<String, Integer> selectCostTaskCount();

    /**
     * 更新任务操作人
     * @param operators
     */
    void batchInsertOperator(List<TaskTemplateOperator> operators);
}
