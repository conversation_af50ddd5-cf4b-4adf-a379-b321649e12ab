package org.springcenter.marketing.modules.wxMapper;


import org.apache.ibatis.annotations.Param;
import org.springcenter.marketing.modules.model.JicSendCouponLog;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【JIC_SEND_COUPON_LOG(发券中间表)】的数据库操作Mapper
* @createDate 2024-03-18 16:59:56
* @Entity generator.domain.JicSendCouponLog
*/
public interface JicSendCouponLogMapper {

    int deleteByPrimaryKey(Long id);

    int insert(JicSendCouponLog record);

    int insertSelective(JicSendCouponLog record);

    JicSendCouponLog selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(JicSendCouponLog record);

    int updateByPrimaryKey(JicSendCouponLog record);

    List<JicSendCouponLog> selectSendCouponUser(@Param("openId") String openId,
                                                @Param("yearStart") Integer findBirthSendLogStartYearMonth,
                                                @Param("yearEnd") Integer findBirthSendLogEndYearMonth);

    List<JicSendCouponLog> selectSendCouponUserBatch(@Param("openIds") List<String> openIds,
                                                @Param("yearStart") Integer findBirthSendLogStartYearMonth,
                                                @Param("yearEnd") Integer findBirthSendLogEndYearMonth);
}
