package org.springcenter.marketing.modules.bojunMapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.marketing.modules.model.MRetailitem;

import java.util.List;


/**
 * @Author: wangchun
 * @Date: 2021-12-22 15:47:48
 * @Description: Mapper
 */
public interface MRetailitemMapper extends BaseMapper<MRetailitem> {

    List<MRetailitem> selectDataByMreatilIdAndProductId(@Param("productId") String productId, @Param("mRetailIds") List<Long> collect);

    List<MRetailitem> selectCanRefundItemByMRetailIds(@Param("list") List<Long> list);

    MRetailitem selectRefundDataByDocNo(@Param("docNo") String docNos);

    List<MRetailitem> selectByMretailId(@Param("mretailid") Long mretailid);


}
