package org.springcenter.marketing.modules.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 留言活动打卡记录
 */
@Data
@TableName("theme_activity_join_record")
public class ThemeActivityJoinRecord {
    @ApiModelProperty("主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    @ApiModelProperty("活动ID")
    @TableField("activity_id")
    private Long activityId;
    @ApiModelProperty("打卡时间")
    @TableField("join_date")
    private Date joinDate;
    @ApiModelProperty("用户unionId")
    @TableField("union_id")
    private String unionId;
    @ApiModelProperty("品牌卡号")
    @TableField("card_no")
    private String cardNo;
    @ApiModelProperty("是否删除")
    @TableField("is_delete")
    private Integer isDelete;
    @ApiModelProperty("创建时间")
    @TableField("create_time")
    private Date createTime;
    @ApiModelProperty("更新时间")
    @TableField("update_time")
    private Date updateTime;
}

