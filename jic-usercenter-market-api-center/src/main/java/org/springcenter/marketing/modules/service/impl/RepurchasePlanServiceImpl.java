package org.springcenter.marketing.modules.service.impl;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.jnby.common.CommonRequest;
import com.jnby.common.Page;
import com.jnby.common.ResponseResult;
import com.jnby.common.util.IdLeaf;
import com.jnby.common.util.QiniuUtil;
import com.jnby.common.util.excel.BaseNoModelDataAbstractListener;
import com.jnby.common.util.excel.EasyExcelUtil;
import com.jnby.common.util.excel.IWriteDataExcel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.killbill.bus.api.PersistentBus;
import org.springcenter.marketing.api.activityUseRule.RepurchaseUseRuleCastDto;
import org.springcenter.marketing.api.checkRightsDto.CheckBoxGiftRightsDto;
import org.springcenter.marketing.api.constant.IdConstant;
import org.springcenter.marketing.api.constant.RedisKeyConstant;
import org.springcenter.marketing.api.context.SendMaterialBoxGiftContext;
import org.springcenter.marketing.api.context.SendWxOffMsgContext;
import org.springcenter.marketing.api.context.SmsSendContext;
import org.springcenter.marketing.api.dto.*;
import org.springcenter.marketing.api.dto.admin.*;
import org.springcenter.marketing.api.dto.miniapp.*;
import org.springcenter.marketing.api.entity.SubtractUserIntegralContext;
import org.springcenter.marketing.common.constant.ImageConstant;
import org.springcenter.marketing.common.handler.SelectSheetWriteHandler;
import org.springcenter.marketing.modules.bojunMapper.*;
import org.springcenter.marketing.modules.context.*;
import org.springcenter.marketing.modules.entity.PacketProductReq;
import org.springcenter.marketing.modules.entity.PeoplePacketReq;
import org.springcenter.marketing.modules.entity.PeoplePacketResp;
import org.springcenter.marketing.modules.entity.ProductPacketEntity;
import org.springcenter.marketing.modules.event.CreateRetailEvent;
import org.springcenter.marketing.modules.event.bus.CreateRetailEventBus;
import org.springcenter.marketing.modules.mapper.*;
import org.springcenter.marketing.modules.model.*;
import org.springcenter.marketing.modules.service.*;
import org.springcenter.marketing.modules.util.*;
import org.springcenter.marketing.modules.webapi.IJicInfoHttpApi;
import org.springcenter.marketing.modules.webapi.IPeopleInfoHttpApi;
import org.springcenter.marketing.modules.wxMapper.*;
import org.springcenter.paycenter.api.IPayCenterApi;
import org.springcenter.paycenter.api.req.CashierRefundReq;
import org.springcenter.paycenter.api.req.CreateOrderGoods;
import org.springcenter.paycenter.api.req.CreateOrderReq;
import org.springcenter.paycenter.api.resp.CashierRefundResp;
import org.springcenter.paycenter.api.resp.CreateOrderResp;
import org.springcenter.product.api.enums.IsDeleteEnum;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import retrofit2.Response;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.ZoneId;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@RefreshScope
public class RepurchasePlanServiceImpl implements RepurchasePlanService {

    @Autowired
    private AcActivityConfigMapper acActivityConfigMapper;

    @Autowired
    private AcActivityOrderMapper acActivityOrderMapper;

    @Autowired
    private AcRepurchasePlanDetailMapper acRepurchasePlanDetailMapper;

    @Autowired
    private IVoucherService iVoucherService;

    @Autowired
    private IUserVipService iUserVipService;

    @Autowired
    private IAcActivityConfDetailGoodsService iAcActivityConfDetailGoodsService;

    @Autowired
    private AcActivityConfigDetailsMapper acActivityConfigDetailsMapper;

    @Autowired
    private IAcActivityConfigDetailsService iAcActivityConfigDetailsService;

    @Autowired
    private AcActivityConfDetailGoodsMapper acActivityConfDetailGoodsMapper;

    @Autowired
    @Qualifier("marketingTransactionTemplate")
    private TransactionTemplate template;

    @Autowired
    private VoucherVirtualReferConfigMapper voucherVirtualReferConfigMapper;

    @Value("${wx.brand.commonConfigs}")
    private String commonConfigs;

    @Autowired
    private MessageService messageService;


    @Value("${send.sms.template}")
    private String sendSmsTemplate;

    @Value("${pre.create.order.mchid}")
    private String preCreateOrderMchid;

    @Value("${repurchase.goods.sku}")
    private String repurchaseGoodsSku;


    @Value("${repurchase.create.retail.sku}")
    private String repurchaseCreateRetailSku;

    @Autowired
    private IPackageStoresService iPackageStoresService;


    @Autowired
    private VoucherRuleMapper voucherRuleMapper;

    @Autowired
    private MProductMapper mProductMapper;

    @Autowired
    private IPayCenterApi payCenterApi;

    @Autowired
    private AcAskSupportGoodsMapper acAskSupportGoodsMapper;

    @Autowired
    private CreateRetailEventBus createRetailEventBus;

    @Autowired
    private IJicInfoHttpApi iJicInfoHttpApi;

    @Autowired
    private JicVoucherSendRecordMapper jicVoucherSendRecordMapper;

    @Autowired
    private RightsV3Service rightsV3Service;

    @Autowired
    private EmployeeBaseMapper employeeBaseMapper;

    @Autowired
    private HrEmployeeMapper hrEmployeeMapper;

    @Autowired
    private IPeopleInfoHttpApi iPeopleInfoHttpApi;

    @Autowired
    private MRetailMapper mRetailMapper;

    @Autowired
    private AcActivityConfigDetailsStoreQtyMapper acActivityConfigDetailsStoreQtyMapper;

    @Autowired
    private RedissonUtil redissonUtil;

    @Resource
    private QiniuUtil qiniuUtil;

    @Autowired
    private IAcActivityConfigDetailsStoreQtyService iAcActivityConfigDetailsStoreQtyService;

    @Autowired
    private CVouchersMapper cVouchersMapper;

    @Autowired
    private CVoucherStoreMapper cVoucherStoreMapper;

    @Autowired
    private CStoreMapper cStoreMapper;

    @Autowired
    private AcActivityConfigDetailsStoreQtyLogMapper acActivityConfigDetailsStoreQtyLogMapper;


    @Autowired
    private JicShareBaseConfMapper jicShareBaseConfMapper;

    @Override
    public List<QueryCouponInfoResp> queryCouponInfo(QueryCouponInfoReq requestData) {
        List<QueryCouponInfoResp> result = new ArrayList<>();
        // 查询用户信息
        if(StringUtils.isNotBlank(requestData.getUnionid())){
            // 查询用户信息 如果用户有订购，展示用户的
            List<AcRepurchasePlanDetail> list = acRepurchasePlanDetailMapper.selectUserRepurchasePlan(requestData.getUnionid(),requestData.getWeid(),new Date());
            if(CollectionUtils.isNotEmpty(list)){
                String activityOrderId = list.get(0).getActivityOrderId();
                AcActivityOrder acActivityOrder = acActivityOrderMapper.selectById(activityOrderId);
                // 查询优惠券信息  拼装status字段状态 返回的是key 是券号
                Map<String,Integer> statusMap = queryVoucherByVoucherNos(list);

                for (AcRepurchasePlanDetail acRepurchasePlanDetail : list) {
                    QueryCouponInfoResp queryCouponInfoResp = new QueryCouponInfoResp();
                    queryCouponInfoResp.setPayAmount(acActivityOrder.getPayAmount());
                    queryCouponInfoResp.setPayPoint(acActivityOrder.getPayPoint());
                    queryCouponInfoResp.setAwardId(acRepurchasePlanDetail.getAwardId().toString());

                    // 获取详情信息
                    String voucherNos = acRepurchasePlanDetail.getVoucherNos();
                    if(StringUtils.isNotBlank(voucherNos)){
                        String[] split = voucherNos.split(",");
                        List<QueryCouponInfoResp.VoucherStatusAndVouchers> voucherStatusAndVouchers = new ArrayList<>();
                        for (String voucherNo : split) {
                            QueryCouponInfoResp.VoucherStatusAndVouchers voucherStatusAndVouchers1 = new QueryCouponInfoResp.VoucherStatusAndVouchers();
                            voucherStatusAndVouchers1.setVoucherNo(voucherNo);
                            voucherStatusAndVouchers1.setStatus(statusMap.get(voucherNo) == null ? 0 :statusMap.get(voucherNo));
                            voucherStatusAndVouchers1.setIsExpire(statusMap.get(voucherNo+"isExpire") == null ? 0 : statusMap.get(voucherNo+"isExpire"));
                            voucherStatusAndVouchers.add(voucherStatusAndVouchers1);
                        }
                        queryCouponInfoResp.setVoucherStatusAndVouchers(voucherStatusAndVouchers);
                    }
                    queryCouponInfoResp.setVoucherNos(acRepurchasePlanDetail.getVoucherNos());
                    // 拼装信息
                    queryCouponInfoResp.setStartMonth(Integer.valueOf(acRepurchasePlanDetail.getStartTime().toString().substring(4, 6)));
                    queryCouponInfoResp.setEndMonth(Integer.valueOf(acRepurchasePlanDetail.getEndTime().toString().substring(4, 6)));
                    queryCouponInfoResp.setStartTime(acRepurchasePlanDetail.getStartTime());
                    queryCouponInfoResp.setEndTime(acRepurchasePlanDetail.getEndTime());
                    queryCouponInfoResp.setIsCanSendAlert(acActivityOrder.getIsCanSendAlert());
                    queryCouponInfoResp.setId(acActivityOrder.getId());
                    queryCouponInfoResp.setOutName(acRepurchasePlanDetail.getOutName());
                    result.add(queryCouponInfoResp);
                }
            }
        }
        return result;
    }

    /**
     *
     * @param useRule  规则
     * @param joinTimes  次数
     * @param cycleType   类型  周期  0 按周  1 按月  2 按季度   3 老的数据兼容  类型    4  固定时间   5 相对时间
     * @return
     */
    private List<RepurchaseUseRuleCastDto> formatUseRule(String useRule, Integer joinTimes, Integer cycleType) {
        if(joinTimes == null){
            joinTimes = 4;
        }
        List<RepurchaseUseRuleCastDto> result = new ArrayList<>();

        // 转化useRule
        List<RepurchaseUseRuleCastDto> repurchaseUseRuleCastDtos = JSONObject.parseArray(useRule, RepurchaseUseRuleCastDto.class);
        // 根据第一第二第三次分组
        Map<Integer, List<RepurchaseUseRuleCastDto>> collect = new HashMap<>();
        // 区分老的和新的  老的只有一条
        if(repurchaseUseRuleCastDtos.size() == 1){
            // 更新为多条
            collect.put(1,repurchaseUseRuleCastDtos);
            collect.put(2,repurchaseUseRuleCastDtos);
            collect.put(3,repurchaseUseRuleCastDtos);
            collect.put(4,repurchaseUseRuleCastDtos);
        }else{
            // 新的
            collect = repurchaseUseRuleCastDtos.stream().collect(Collectors.groupingBy(r -> r.getSortNum()));
        }


        // 获取本周一
        // 获取本周的周一日期
        LocalDate today = LocalDate.now();
        LocalDate mondayOfThisWeek = today.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
        // 转化为 Date
        java.time.Instant instant = mondayOfThisWeek.atStartOfDay(ZoneId.systemDefault()).toInstant();
        //  开始日期 结束日期 为 开始日期 + 6
        Date mondayDate = Date.from(instant);

        // 获取本月第一天
        LocalDate firstDay = today.with(TemporalAdjusters.firstDayOfMonth());
        java.time.Instant monthOfFirstDay = firstDay.atStartOfDay(ZoneId.systemDefault()).toInstant();
        //  开始日期 结束日期 为 开始日期 + 6
        Date monthOfFirstDayDate = Date.from(monthOfFirstDay);


        // 获取本季度第一天
        int month = today.getMonthValue(); // 获取当前月份的数字表示（1-12）
        int year = today.getYear(); // 获取当前年份
        // 计算当前季度
        int quarter = (month - 1) / 3 + 1;
        // 计算季度的开始和结束月份
        int startMonth = (quarter - 1) * 3 + 1; // 季度的开始月份
        int endMonth = quarter * 3; // 季度的结束月份（下一个季度的开始月份的前一个月）
        // 获取季度的第一天和最后一天
        LocalDate quarterStart = YearMonth.of(year, startMonth).atDay(1); // 季度的第一天
        java.time.Instant seasonOfFirstDay = quarterStart.atStartOfDay(ZoneId.systemDefault()).toInstant();
        //   季度第一天
        Date seasonOfFirstDayDate = Date.from(seasonOfFirstDay);

        // 自定义  TODO 未处理

        for( int i = 1 ; i<= joinTimes ; i ++){
            // 便利数据
            List<RepurchaseUseRuleCastDto> repurchaseUseRuleCastDtos1 = collect.get(i);
            if(CollectionUtils.isNotEmpty(repurchaseUseRuleCastDtos1)){
                // 赋值数据 主要是赋值 startTime 和 endTime
                RepurchaseUseRuleCastDto repurchaseUseRuleCastDto = repurchaseUseRuleCastDtos1.get(0);
                if(repurchaseUseRuleCastDtos1.size() > 1){
                    //特殊处理
                    List<String> numbers = new ArrayList<>();
                    List<String> awardIds = new ArrayList<>();
                    for (RepurchaseUseRuleCastDto useRuleCastDto : repurchaseUseRuleCastDtos1) {
                        String number = useRuleCastDto.getNumber();
                        String awardId = useRuleCastDto.getAwardId();
                        awardIds.add(awardId);
                        numbers.add(number);
                    }
                    repurchaseUseRuleCastDto.setNumber(numbers.stream().collect(Collectors.joining(",")));
                    repurchaseUseRuleCastDto.setAwardId(awardIds.stream().collect(Collectors.joining(",")));
                }
                
                if(cycleType == null){
                    cycleType = 3;
                }
                // 赋值 如果是周为周期的  则 为 每周一发送   获取当前时间   获取 当前日期是周几  获取本周周一  从本周周一开始算
                // 周期 为 一周
                if(cycleType.equals(0)){
                    // 日期处理完毕
                    Date startTime = DateUtil.addDate(mondayDate, (i - 1) * 7);
                    Date endTime = DateUtil.addDate(startTime, 6);
                    // 设置时间
                    repurchaseUseRuleCastDto.setStartTime(DateUtil.formatToStr(startTime,"MMdd"));
                    repurchaseUseRuleCastDto.setEndTime(DateUtil.formatToStr(endTime,"MMdd"));
                    result.add(repurchaseUseRuleCastDto);
                }else if(cycleType.equals(1)){
                    // 周期为月
                    Date startTime = DateUtil.addMonth(monthOfFirstDayDate, (i - 1));
                    // 结束日期要  开始日期 +1个月  -1 天
                    Date endTime = DateUtil.addDate(DateUtil.addMonth(startTime,1), -1);
                    repurchaseUseRuleCastDto.setStartTime(DateUtil.formatToStr(startTime,"MMdd"));
                    repurchaseUseRuleCastDto.setEndTime(DateUtil.formatToStr(endTime,"MMdd"));
                    result.add(repurchaseUseRuleCastDto);
                }else if(cycleType.equals(2)){
                    // 周期为季度
                    Date startTime = DateUtil.addMonth(seasonOfFirstDayDate, (i - 1) * 3 );
                    Date endTime = DateUtil.addDate(DateUtil.addMonth(startTime, 3), -1);
                    repurchaseUseRuleCastDto.setStartTime(DateUtil.formatToStr(startTime,"MMdd"));
                    repurchaseUseRuleCastDto.setEndTime(DateUtil.formatToStr(endTime,"MMdd"));
                    result.add(repurchaseUseRuleCastDto);
                }else if(cycleType.equals(4)){
                    // 绝对时间
                    if(i == 1){
                        Date endTime = DateUtil.addDate(new Date(), 0);
                        repurchaseUseRuleCastDto.setStartTime(DateUtil.formatToStr(new Date(),"yyyyMMdd").substring(4));
                        repurchaseUseRuleCastDto.setEndTime(DateUtil.formatToStr(endTime,"MMdd"));
                        result.add(repurchaseUseRuleCastDto);
                    }else{
                        String absoluteTime = repurchaseUseRuleCastDto.getAbsoluteTime();
                        Date endTime = DateUtil.addDate(DateUtil.parseDate(absoluteTime,"yyyyMMdd"), 0);
                        repurchaseUseRuleCastDto.setStartTime(absoluteTime.substring(4));
                        repurchaseUseRuleCastDto.setEndTime(DateUtil.formatToStr(endTime,"MMdd"));
                        result.add(repurchaseUseRuleCastDto);
                    }
                }else if(cycleType.equals(5)){
                    // 相对时间  相对第一次发放的时间
                    Integer relativeNum = repurchaseUseRuleCastDto.getRelativeNum();
                    // 获取当前时间
                    Date startTime = DateUtil.addDate(new Date(), relativeNum);
                    repurchaseUseRuleCastDto.setStartTime(DateUtil.formatToStr(startTime,"MMdd"));
                    Date endTime = DateUtil.addDate(startTime, 0);
                    repurchaseUseRuleCastDto.setEndTime(DateUtil.formatToStr(endTime,"MMdd"));
                    result.add(repurchaseUseRuleCastDto);

                }else{
                    // 按老的
                    // 转化为 4个
                    if(i == 1){
                        RepurchaseUseRuleCastDto repurchaseUseRuleCastDto1 = new RepurchaseUseRuleCastDto();
                        repurchaseUseRuleCastDto1.setAwardId(repurchaseUseRuleCastDto.getAwardId());
                        repurchaseUseRuleCastDto1.setStartTime("0101");
                        repurchaseUseRuleCastDto1.setEndTime("0331");
                        repurchaseUseRuleCastDto1.setNumber(repurchaseUseRuleCastDto.getNumber());
                        result.add(repurchaseUseRuleCastDto1);
                    }
                    if(i == 2){
                        RepurchaseUseRuleCastDto repurchaseUseRuleCastDto2 = new RepurchaseUseRuleCastDto();
                        repurchaseUseRuleCastDto2.setAwardId(repurchaseUseRuleCastDto.getAwardId());
                        repurchaseUseRuleCastDto2.setStartTime("0401");
                        repurchaseUseRuleCastDto2.setEndTime("0630");
                        repurchaseUseRuleCastDto2.setNumber(repurchaseUseRuleCastDto.getNumber());
                        result.add(repurchaseUseRuleCastDto2);
                    }

                    if(i == 3){
                        RepurchaseUseRuleCastDto repurchaseUseRuleCastDto3 = new RepurchaseUseRuleCastDto();
                        repurchaseUseRuleCastDto3.setAwardId(repurchaseUseRuleCastDto.getAwardId());
                        repurchaseUseRuleCastDto3.setStartTime("0701");
                        repurchaseUseRuleCastDto3.setEndTime("0930");
                        repurchaseUseRuleCastDto3.setNumber(repurchaseUseRuleCastDto.getNumber());
                        result.add(repurchaseUseRuleCastDto3);
                    }

                    if(i == 4){
                        RepurchaseUseRuleCastDto repurchaseUseRuleCastDto4 = new RepurchaseUseRuleCastDto();
                        repurchaseUseRuleCastDto4.setAwardId(repurchaseUseRuleCastDto.getAwardId());
                        repurchaseUseRuleCastDto4.setStartTime("1001");
                        repurchaseUseRuleCastDto4.setEndTime("1231");
                        repurchaseUseRuleCastDto4.setNumber(repurchaseUseRuleCastDto.getNumber());
                        result.add(repurchaseUseRuleCastDto4);
                    }
                }
            }
        }
        return result;
    }

    private RealPayContext getRealPayMap(AcActivityConfigDetails acActivityConfigDetails,
                                                  String unionid,
                                                  String weid) {
        RealPayContext realPayContext = new RealPayContext();

        Map<String,BigDecimal> map = new HashMap<>();
        // 获取售价字段
        if(acActivityConfigDetails.getIsSameSells().equals(0)){
            // 不统一售价  需要查询真正售价  查询当前品牌卡的共享卡等级
            MemberCardQueryContext req = new MemberCardQueryContext();
            req.setUnionId(unionid);
            req.setBrandId(weid);
            CustomerBaseResponse<MemberCardEntity> memberCard = iUserVipService.getMemberCard(req);
            if(memberCard != null && memberCard.isSuccess() && memberCard.getData() != null){
                // 根据共享卡cardId 进行查询
                List<AcActivityConfDetailGoods> acActivityConfDetailGoods = acActivityConfDetailGoodsMapper.
                        selectByDetailsIdAndCardTypeId(acActivityConfigDetails.getId(),memberCard.getData().getVipShareTypeId());
                if(CollectionUtils.isEmpty(acActivityConfDetailGoods)){
                    log.info("未配置 真正售价 cardTypeId = {} , acDetailsId = {}",memberCard.getData().getVipShareTypeId(),acActivityConfigDetails.getId());
                    throw  new RuntimeException("配置错误,请检查配置后再调用!");
                }
                realPayContext.setPayAmount(acActivityConfDetailGoods.get(0).getPayAmount());
                realPayContext.setPayPoint(acActivityConfDetailGoods.get(0).getPayPoint());
                realPayContext.setExchangePoint(acActivityConfDetailGoods.get(0).getExchangePoint());
                AdminCreateRepurchaseDto.AdminCreateRepurchaseGoodsReq adminCreateRepurchaseGoodsReq = new AdminCreateRepurchaseDto.AdminCreateRepurchaseGoodsReq();
                BeanUtils.copyProperties(acActivityConfDetailGoods.get(0),adminCreateRepurchaseGoodsReq);
                realPayContext.setAdminCreateRepurchaseGoodsReq(adminCreateRepurchaseGoodsReq);
            }
        }else{
            // 统一售价
            realPayContext.setPayAmount(acActivityConfigDetails.getPayAmount());
            realPayContext.setPayPoint(acActivityConfigDetails.getPayPoint());
            realPayContext.setExchangePoint(acActivityConfigDetails.getExchangePoint());
        }
        return realPayContext;
    }

    @Override
    public List<QueryOrderListResp> queryOrderList(QueryOrderListReq requestData) {
        List<QueryOrderListResp>  result = new ArrayList<>();
        List<AcActivityOrder> list = acActivityOrderMapper.selectEfftiveOrderByUnionid(requestData.getUnionid());
        for (AcActivityOrder acActivityOrder : list) {
            QueryOrderListResp queryOrderListResp = new QueryOrderListResp();
            BeanUtils.copyProperties(acActivityOrder,queryOrderListResp);
            queryOrderListResp.setActivityConfigName(acActivityOrder.getBrandTitle());
            queryOrderListResp.setSubOrderSn(acActivityOrder.getId());
            result.add(queryOrderListResp);
        }
        return result;
    }

    @Override
    public ResponseResult checkSubmitOrder(PreSubmitOrderReq requestData) {
        // 验证用户积分是否够用
        MemberCardQueryContext req = new MemberCardQueryContext();
        req.setUnionId(requestData.getUnionid());
        req.setBrandId(requestData.getWeid());
        CustomerBaseResponse<MemberIntegralDto> memberIntegralDtoCustomerBaseResponse = iUserVipService.getMemberIntegral(req);
        Long totalIntegral = memberIntegralDtoCustomerBaseResponse.getData().getTotalIntegral();
        AcActivityConfigDetails acActivityConfigDetails = acActivityConfigDetailsMapper.selectById(requestData.getActivityConfigDetailId());
        AcActivityConfig acActivityConfig = acActivityConfigMapper.selectById(acActivityConfigDetails.getAcActivityConfigId());
        if(acActivityConfigDetails.getIsLimitQty().equals(1)){
            QueryWrapper<AcActivityConfigDetailsStoreQty> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("ac_activity_config_details_id",acActivityConfigDetails.getId());
            queryWrapper.eq("is_del",0);
            queryWrapper.eq("store_code",requestData.getStoreCode());
            List<AcActivityConfigDetailsStoreQty> acActivityConfigDetailsStoreQties = acActivityConfigDetailsStoreQtyMapper.selectList(queryWrapper);
            if(CollectionUtils.isEmpty(acActivityConfigDetailsStoreQties)){
                return ResponseResult.error(-1,"门店库存未配置！");
            }
            if(acActivityConfigDetailsStoreQties.get(0).getLeftQty() <= 0){
                return ResponseResult.error(-1,"门店库存不足！");
            }
        }

        if(acActivityConfigDetails == null){
            return ResponseResult.error(-1,"活动错误，请重试!");
        }
        RealPayContext payMap = getRealPayMap(acActivityConfigDetails,requestData.getUnionid(),requestData.getWeid());
        if(payMap == null){
            return ResponseResult.error(-1,"未正确配置价格");
        }
        // 如果用户已经 有了 已经支付的单子  那么不允许再次订阅
        List<AcActivityOrder> acActivityOrders = acActivityOrderMapper.selectEfftiveOrderByUnionidAndWeidAndDate(requestData.getUnionid(),requestData.getWeid(),new Date());
        if(CollectionUtils.isNotEmpty(acActivityOrders)){
            return ResponseResult.error(-1,"当前品牌用户已经购买过，不允许重复购买!");
        }

        Long activityStartTime = acActivityConfig.getActivityStartTime();
        Long activityEndTime = acActivityConfig.getActivityEndTime();
        long yyyyMMdd = Long.parseLong(DateUtils.formatDate(new Date(), "yyyyMMdd"));
        if(yyyyMMdd < activityStartTime ||  yyyyMMdd > activityEndTime){
            return ResponseResult.error(-1,"不在活动时间内!");
        }
        if(acActivityConfig.getStatus() == null || 0 == acActivityConfig.getStatus()){
            return ResponseResult.error(-1,"活动暂停中，请稍后重试!");
        }

        if(requestData.getPayType().equals(0)){
            if(totalIntegral < payMap.getExchangePoint().longValue()){
                return ResponseResult.error(-1,"用户积分不足，暂时无法兑换!");
            }
        }else{
            if(totalIntegral < payMap.getPayPoint().longValue()){
                return ResponseResult.error(-1,"用户积分不足，暂时无法兑换!");
            }
        }

        MemberBaseInfoQueryContext memberBaseInfoQueryContext = new MemberBaseInfoQueryContext();
        memberBaseInfoQueryContext.setUnionId(requestData.getUnionid());
        memberBaseInfoQueryContext.setBrandId(requestData.getWeid());
        MemberBaseInfoEntity memberBaseInfoEntity =  Preconditions.checkNotNull(iUserVipService.getUserBaseInfo(memberBaseInfoQueryContext),"用户信息不能为空");
        // 验证参活门店
        if(memberBaseInfoEntity.getStoreId() == null){
            return ResponseResult.error(-1,"用户未绑定门店，无法购买!");
        }
        //验证参活门店
        String packetStoreId = acActivityConfigDetails.getPacketStoreId();
        List<Integer> storeIds = new ArrayList<>();
        storeIds.add(memberBaseInfoEntity.getStoreId().intValue());
        List<Integer> integers = rightsV3Service.targetStoreIds(packetStoreId, storeIds);
        if(CollectionUtils.isEmpty(integers)){
            return ResponseResult.error(-1,"用户门店未在活动门店内，无法购买!");
        }

        // 验证人群包  如果没有则不验证
        String packetPeopleId = acActivityConfigDetails.getPacketPeopleId();
        if(StringUtils.isNotBlank(packetPeopleId)){
            // 验证人群包
            PeoplePacketReq peoplePacketReq = new PeoplePacketReq();
            List<String> crowdIds = new ArrayList<>();
            crowdIds.add(packetPeopleId);
            peoplePacketReq.setCrowdIds(crowdIds);
            peoplePacketReq.setCardNo(memberBaseInfoEntity.getCardNo());
            //  groupByPeopleCrowdIds  key 就是全部的需要判断的 人群包id
            List<PeoplePacketResp> peoplePacketResps = iPackageStoresService.peopleBatchHit(peoplePacketReq);
            if(CollectionUtils.isNotEmpty(peoplePacketResps)){
                // 处理人群包返回数据
                for (PeoplePacketResp peoplePacketResp : peoplePacketResps) {
                    if(!peoplePacketResp.isHit()){
                        // 命中人群  可以展示
                        return ResponseResult.error(-1,"该用户未匹配人群，无法购买!");
                    }
                }
            }
        }

        return ResponseResult.success();
    }

    @Override
    public PreSubmitOrderResp perSubmitOrder(PreSubmitOrderReq requestData) {
        PreSubmitOrderResp preSubmitOrderResp = new PreSubmitOrderResp();
        // 获取活动信息
        AcActivityConfigDetails acActivityConfigDetails = acActivityConfigDetailsMapper.selectById(requestData.getActivityConfigDetailId());
        // 查询主表信息
        AcActivityConfig acActivityConfig = acActivityConfigMapper.selectById(acActivityConfigDetails.getAcActivityConfigId());

        // 判断是否是纯积分支付
        RealPayContext payMap = getRealPayMap(acActivityConfigDetails,requestData.getUnionid(), requestData.getWeid());
        // 支付单号  正常来说应该是支付成功
        AcActivityOrder order = createOrder(acActivityConfigDetails,acActivityConfig, requestData.getUnionid(), requestData.getWeid(), requestData.getPayType()
                , requestData.getWxopenid(), requestData.getMiniopenid(), payMap.getExchangePoint(), payMap.getPayPoint(), payMap.getPayAmount(), null,requestData);
        // 调用纯的预支付产生订单
        String orderSn = createOrderByPayCenter(order,requestData.getAppid());
        preSubmitOrderResp.setOrderSn(orderSn);
        preSubmitOrderResp.setActivityOrderId(order.getId());
        return preSubmitOrderResp;
    }

    private String createOrderByPayCenter(AcActivityOrder order, String appid) {
        CommonRequest<CreateOrderReq> request = new CommonRequest();
        CreateOrderReq createOrderReq = new CreateOrderReq();
        List<CreateOrderGoods> goodsList = new ArrayList<>();
        CreateOrderGoods createOrderGoods = new CreateOrderGoods();
        // 设置参数
        createOrderGoods.setGoodsId(repurchaseGoodsSku);
        createOrderGoods.setGoodsName(repurchaseGoodsSku);
        createOrderGoods.setQuantity(1L);
        createOrderGoods.setSku(repurchaseGoodsSku);
        createOrderGoods.setSkuId(repurchaseGoodsSku);
        createOrderGoods.setItemId(order.getId());
        createOrderReq.setAppId(appid);
        createOrderReq.setOutPayNo(order.getId());
        createOrderReq.setType(1);
        createOrderReq.setBusinessType(4L);
        // 组合支付
        if(order.getPayType() == 1){
            createOrderReq.setIntegral(order.getPayPoint().longValue());
            createOrderReq.setTotAmount(order.getPayAmount());
            createOrderGoods.setPrice(order.getPayAmount());
            createOrderGoods.setUseIntegral(order.getPayPoint().longValue());
        }else{
            createOrderReq.setIntegral(order.getPayPoint().longValue());
            createOrderReq.setTotAmount(BigDecimal.ZERO);
            createOrderGoods.setPrice(BigDecimal.ZERO);
            createOrderGoods.setUseIntegral(order.getPayPoint().longValue());
        }
        goodsList.add(createOrderGoods);
        createOrderReq.setGoodsList(goodsList);
        createOrderReq.setUnionId(order.getUnionid());
        createOrderReq.setOpenId(order.getMiniopenid());
        createOrderReq.setMchId(preCreateOrderMchid);
        request.setRequestData(createOrderReq);
        log.info("预生成订单 请求参数 = {}",JSONObject.toJSONString(request));
        ResponseResult<CreateOrderResp> responseResult = payCenterApi.createOrder(request);
        log.info("预生成订单 返回参数 = {}",JSONObject.toJSONString(responseResult));
        if(responseResult != null && responseResult.getCode() == 0){
            CreateOrderResp data = responseResult.getData();
            return data.getOrderSn();
        }
        throw new RuntimeException("预生成订单异常,请重试!");
    }

    @Override
    public String createOrUpdate(AdminCreateRepurchaseDto requestData, String userId) {
        AcActivityConfig acActivityConfig = new AcActivityConfig();
        acActivityConfig.setUpdateBy(userId);
        BeanUtils.copyProperties(requestData,acActivityConfig);
        // 设置时间
        acActivityConfig.setActivityStartTime(Long.parseLong(DateUtils.formatDate(requestData.getActivityStartTime(),"yyyyMMdd")));
        acActivityConfig.setActivityEndTime(Long.parseLong(DateUtils.formatDate(requestData.getActivityEndTime(),"yyyyMMdd")));

        // 有id 则认为是修改， 无id 认为新增
        // 新增
        List<AcActivityConfigDetails> insertResult  = new ArrayList<>();
        List<AcActivityConfDetailGoods> insertAcActivityConfDetailGoodsList  = new ArrayList<>();
        // 修改
        List<AcActivityConfigDetails> updateResult  = new ArrayList<>();
        List<AcActivityConfDetailGoods> updateAcActivityConfDetailGoodsList  = new ArrayList<>();
        // 需要删除的
        List<AcActivityConfigDetails> delteResult  = new ArrayList<>();
        List<AcActivityConfDetailGoods> deleteResultGoods  = new ArrayList<>();

        // 判断是新增or修改
        if(StringUtils.isBlank(requestData.getId())){
            // 获取id
            acActivityConfig.setId(IdLeaf.getId(IdConstant.AC_ACTIVITY_CONFIG));
            acActivityConfig.setCreateTime(new Date());
            acActivityConfig.setUpdateTime(new Date());
        }
        acActivityConfig.setUpdateTime(new Date());
        // 查询所有的
        List<String> activityConfigIds = new ArrayList<>();
        activityConfigIds.add(acActivityConfig.getId());
        List<AcActivityConfigDetails> acActivityConfigDetails1 = acActivityConfigDetailsMapper.selectByActivityConfigIds(activityConfigIds);
        if(CollectionUtils.isNotEmpty(acActivityConfigDetails1)){
            List<AdminCreateRepurchaseDto.AdminCreateRepuchaseDetailReq> createRepuchaseDetailReqList = requestData.getCreateRepuchaseDetailReqList();
            List<String> insertOrUpdateOrDetete = createRepuchaseDetailReqList.stream().filter(r->StringUtils.isNotBlank(r.getId())).map(r -> r.getId()).collect(Collectors.toList());
            List<AcActivityConfigDetails> shouldDelte = acActivityConfigDetails1.stream().filter(r -> !insertOrUpdateOrDetete.contains(r.getId())).collect(Collectors.toList());
            delteResult.addAll(shouldDelte);
        }

        for (AdminCreateRepurchaseDto.AdminCreateRepuchaseDetailReq adminCreateRepuchaseDetailReq : requestData.getCreateRepuchaseDetailReqList()) {
            AcActivityConfigDetails acActivityConfigDetails = new AcActivityConfigDetails();
            BeanUtils.copyProperties(adminCreateRepuchaseDetailReq,acActivityConfigDetails);
            acActivityConfigDetails.setAcActivityConfigId(acActivityConfig.getId());

            if(StringUtils.isBlank(acActivityConfigDetails.getId())){
                String activityDetailId = IdLeaf.getId(IdConstant.AC_ACTIVITY_CONFIG_DETAILS);
                acActivityConfigDetails.setId(activityDetailId);
                insertResult.add(acActivityConfigDetails);
            }else{
                updateResult.add(acActivityConfigDetails);
            }

            if(CollectionUtils.isNotEmpty(adminCreateRepuchaseDetailReq.getAdminCreateRepurchaseGoodsReqs())){
                List<AdminCreateRepurchaseDto.AdminCreateRepurchaseGoodsReq> adminCreateRepurchaseGoodsReqs = adminCreateRepuchaseDetailReq.getAdminCreateRepurchaseGoodsReqs();
                for (AdminCreateRepurchaseDto.AdminCreateRepurchaseGoodsReq adminCreateRepurchaseGoodsReq : adminCreateRepurchaseGoodsReqs) {
                    // 封装数据
                    AcActivityConfDetailGoods acActivityConfDetailGoods = new AcActivityConfDetailGoods();
                    BeanUtils.copyProperties(adminCreateRepurchaseGoodsReq,acActivityConfDetailGoods);
                    if(StringUtils.isBlank(acActivityConfDetailGoods.getId())){
                        acActivityConfDetailGoods.setId(IdLeaf.getId(IdConstant.AC_ACTIVITY_CONF_DETAIL_GOODS));
                        acActivityConfDetailGoods.setAcActivityConfigDetailsId(acActivityConfigDetails.getId());
                        insertAcActivityConfDetailGoodsList.add(acActivityConfDetailGoods);
                    }else{
                        updateAcActivityConfDetailGoodsList.add(acActivityConfDetailGoods);
                    }

                }
            }
        }

        template.execute(action->{
            if(StringUtils.isBlank(requestData.getId())){
                acActivityConfigMapper.insert(acActivityConfig);
            }else{
                acActivityConfigMapper.updateById(acActivityConfig);
            }

            if(CollectionUtils.isNotEmpty(insertResult)){
                iAcActivityConfigDetailsService.saveBatch(insertResult);
            }
            if(CollectionUtils.isNotEmpty(insertAcActivityConfDetailGoodsList)){
                Set<String> collect = insertAcActivityConfDetailGoodsList.stream().map(r -> r.getAcActivityConfigDetailsId()).collect(Collectors.toSet());
                acActivityConfDetailGoodsMapper.deleteByActivityDetailIds(new ArrayList<>(collect));
                iAcActivityConfDetailGoodsService.saveBatch(insertAcActivityConfDetailGoodsList);
            }

            if(CollectionUtils.isNotEmpty(delteResult)){
                for (AcActivityConfigDetails acActivityConfigDetails : delteResult) {
                    acActivityConfigDetailsMapper.deleteById(acActivityConfigDetails.getId());
                }
            }

            if(CollectionUtils.isNotEmpty(updateResult)){
                for (AcActivityConfigDetails acActivityConfigDetails : updateResult) {
                    if(acActivityConfigDetails.getPayAmount() == null || acActivityConfigDetails.getPayPoint() == null || acActivityConfigDetails.getExchangePoint() == null){
                        acActivityConfigDetailsMapper.deleteById(acActivityConfigDetails.getId());
                        acActivityConfigDetailsMapper.insert(acActivityConfigDetails);
                    }else{
                        acActivityConfigDetailsMapper.updateById(acActivityConfigDetails);
                    }
                }
            }
            if(CollectionUtils.isNotEmpty(updateAcActivityConfDetailGoodsList)){
                for (AcActivityConfDetailGoods acActivityConfDetailGoods : updateAcActivityConfDetailGoodsList) {
                    // 删除 然后新增
                    if(acActivityConfDetailGoods.getPayAmount() == null || acActivityConfDetailGoods.getPayPoint() == null || acActivityConfDetailGoods.getExchangePoint() == null){
                        acActivityConfDetailGoodsMapper.deleteById(acActivityConfDetailGoods.getId());
                        acActivityConfDetailGoodsMapper.insert(acActivityConfDetailGoods);
                    }else{
                        acActivityConfDetailGoodsMapper.updateById(acActivityConfDetailGoods);
                    }
                }
            }
            return action;
        });
        return acActivityConfig.getId();
    }

    @Override
    public List<AdminCreateRepurchaseDto> repurchaseDetails(List<String> activityConfigIds,String unionid, String weid,String storeCode) {
        List<AdminCreateRepurchaseDto> result = new ArrayList<>();

        if(CollectionUtils.isEmpty(activityConfigIds)){
            return result;
        }
        // 查询主配置详情
        List<AcActivityConfig> acActivityConfigs = acActivityConfigMapper.selectBatchIds(activityConfigIds);
        // 活动主表主键ids
        List<String> activityConfigIdData = acActivityConfigs.stream().map(r -> r.getId()).collect(Collectors.toList());
        // 活动详情表信息
        List<AcActivityConfigDetails> acActivityConfigDetailsData =  acActivityConfigDetailsMapper.selectByActivityConfigIds(activityConfigIdData);
        // 根据活动主表主键id分组的详情表
        Map<String, List<AcActivityConfigDetails>> collect = acActivityConfigDetailsData.stream().collect(Collectors.groupingBy(r -> r.getAcActivityConfigId()));

        String nowDate = DateUtils.formatDate(new Date(), "yyyyMMdd");
        acActivityConfigs =  acActivityConfigs.stream().sorted(new Comparator<AcActivityConfig>() {
            @Override
            public int compare(AcActivityConfig o1, AcActivityConfig o2) {
                long l = o2.getUpdateTime().getTime() - o1.getUpdateTime().getTime();
                if(l> 0){
                    return 1;
                }else if(l < 0 ){
                    return -1;
                }
                return   0;
            }
        }).collect(Collectors.toList());

        Map<Object, List<JicShareBaseConf>> shareIdAndUUid = new HashMap<>();

        // 查询shareUuid
        List<String> collect2 = acActivityConfigs.stream().filter(r -> StringUtils.isNotBlank(r.getShareId())).map(r->r.getShareId()).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(collect2)){
            List<JicShareBaseConf> jicShareBaseConfs = jicShareBaseConfMapper.selectByIds(collect2);
            shareIdAndUUid = jicShareBaseConfs.stream().collect(Collectors.groupingBy(r -> r.getId()));
        }


        for (AcActivityConfig acActivityConfig : acActivityConfigs) {
            AdminCreateRepurchaseDto adminCreateRepurchaseDto = new AdminCreateRepurchaseDto();
            BeanUtils.copyProperties(acActivityConfig,adminCreateRepurchaseDto);
            // 设置时间信息
            adminCreateRepurchaseDto.setActivityStartTime(DateUtils.parseDate(acActivityConfig.getActivityStartTime().toString(),"yyyyMMdd"));
            adminCreateRepurchaseDto.setActivityEndTime(DateUtils.parseDate(acActivityConfig.getActivityEndTime().toString(),"yyyyMMdd"));
            adminCreateRepurchaseDto.setCreateRepuchaseDetailReqList(new ArrayList<>());
            adminCreateRepurchaseDto.setStartTime(acActivityConfig.getActivityStartTime());
            adminCreateRepurchaseDto.setEndTime(acActivityConfig.getActivityEndTime());
            if(StringUtils.isNotBlank(acActivityConfig.getShareId())){
                List<JicShareBaseConf> jicShareBaseConfs = shareIdAndUUid.get(acActivityConfig.getShareId());
                if(CollectionUtils.isNotEmpty(jicShareBaseConfs)){
                    adminCreateRepurchaseDto.setShareUuid(jicShareBaseConfs.get(0).getUuid());
                }
            }

            List<AcActivityConfigDetails> acActivityConfigDetails = collect.get(acActivityConfig.getId());
//            if(StringUtils.isNotBlank(weid)){
//                acActivityConfigDetails = acActivityConfigDetails.stream().filter(r->r.getWeid().equals(weid)).collect(Collectors.toList());
//            }


            if(CollectionUtils.isNotEmpty(acActivityConfigDetails)){
                //排序
                acActivityConfigDetails = acActivityConfigDetails.stream().sorted(new Comparator<AcActivityConfigDetails>() {
                    @Override
                    public int compare(AcActivityConfigDetails o1, AcActivityConfigDetails o2) {
                        return o1.getSortNum() - o2.getSortNum();
                    }
                }).collect(Collectors.toList());

                // 查询配置信息
                List<AdminCreateRepurchaseDto.AdminCreateRepuchaseDetailReq> createRepuchaseDetailReqList = new ArrayList<>();
                List<String> activityConfigDetailIds = acActivityConfigDetails.stream().map(r -> r.getId()).collect(Collectors.toList());
                //查询商品信息
                List<AcActivityConfDetailGoods> acActivityConfDetailGoods = acActivityConfDetailGoodsMapper.selectByDetailIds(activityConfigDetailIds);
                // 分组
                Map<String, List<AcActivityConfDetailGoods>> groupByDetailIds = acActivityConfDetailGoods.stream().collect(Collectors.groupingBy(r -> r.getAcActivityConfigDetailsId()));
                // 库存分组
                Map<String, List<AcActivityConfigDetailsStoreQty>> qtyGroupByDetailsIds = new HashMap<>();

                // 查询库存信息
                if(StringUtils.isNotBlank(storeCode)) {
                    QueryWrapper<AcActivityConfigDetailsStoreQty> queryWrapper = new QueryWrapper<>();
                    queryWrapper.in("ac_activity_config_details_id", activityConfigDetailIds);
                    queryWrapper.eq("store_code", storeCode);
                    queryWrapper.eq("is_del", 0);
                    List<AcActivityConfigDetailsStoreQty> acActivityConfigDetailsStoreQtiesList = acActivityConfigDetailsStoreQtyMapper.selectList(queryWrapper);
                    // 分组
                    qtyGroupByDetailsIds = acActivityConfigDetailsStoreQtiesList.stream().collect(Collectors.groupingBy(r -> r.getAcActivityConfigDetailsId()));
                }

                for (AcActivityConfigDetails acActivityConfigDetail : acActivityConfigDetails) {

                    List<RepurchaseUseRuleCastDto> repurchaseUseRuleCastDtos  = formatUseRule(acActivityConfigDetail.getUseRule(),acActivityConfigDetail.getJoinTimes(),acActivityConfigDetail.getCycleType());
                    repurchaseUseRuleCastDtos = getFormatTimeCycle(repurchaseUseRuleCastDtos).getRepurchaseUseRuleCastDtos();
                    AdminCreateRepurchaseDto.AdminCreateRepuchaseDetailReq adminCreateRepuchaseDetailReq = new AdminCreateRepurchaseDto.AdminCreateRepuchaseDetailReq();
                    // 增加一个appid
                    List<Map> brandConfigs = JSONObject.parseArray(commonConfigs, Map.class);
                    for (Map brandConfig : brandConfigs) {
                        if(brandConfig.get("weid").toString().equals(acActivityConfigDetail.getWeid())){
                            adminCreateRepuchaseDetailReq.setAppid(brandConfig.get("appid").toString());
                        }
                    }

                    BeanUtils.copyProperties(acActivityConfigDetail,adminCreateRepuchaseDetailReq);
                    // 查询信息
                    List<AcActivityConfDetailGoods> acActivityConfDetailGoods1 = groupByDetailIds.get(acActivityConfigDetail.getId());
                    //库存信息
                    List<AcActivityConfigDetailsStoreQty> acActivityConfigDetailsStoreQties = qtyGroupByDetailsIds.get(acActivityConfigDetail.getId());
                    String jsonString = JSONObject.toJSONString(acActivityConfigDetailsStoreQties);
                    adminCreateRepuchaseDetailReq.setAcActivityConfigDetailsStoreQtyResps(JSONObject.parseArray(jsonString, AcActivityConfigDetailsStoreQtyResp.class));

                    // 默认初始化
                    adminCreateRepuchaseDetailReq.setAdminCreateRepurchaseGoodsReqs(new ArrayList<>());
                    // 设置使用规则
                    adminCreateRepuchaseDetailReq.setRepurchaseUseRuleCastDtos(repurchaseUseRuleCastDtos);
                    if(CollectionUtils.isNotEmpty(acActivityConfDetailGoods1)){
                        List<AdminCreateRepurchaseDto.AdminCreateRepurchaseGoodsReq> adminCreateRepurchaseGoodsReqs = new ArrayList<>();
                        if(StringUtils.isNotBlank(unionid) && StringUtils.isNotBlank(weid) && acActivityConfigDetail.getIsSameSells() == 0 && weid
                                .equals(acActivityConfigDetail.getWeid())){
                            RealPayContext realPayContext = getRealPayMap(acActivityConfigDetail,unionid,weid);
                            if(realPayContext == null){
                                throw new RuntimeException("配置错误  activityDetailId = "+acActivityConfigDetail.getId());
                            }
                            AdminCreateRepurchaseDto.AdminCreateRepurchaseGoodsReq adminCreateRepurchaseGoodsReq = realPayContext.getAdminCreateRepurchaseGoodsReq();
                            adminCreateRepurchaseGoodsReqs.add(adminCreateRepurchaseGoodsReq);
                        }else{
                            for (AcActivityConfDetailGoods activityConfDetailGoods : acActivityConfDetailGoods1) {
                                AdminCreateRepurchaseDto.AdminCreateRepurchaseGoodsReq adminCreateRepurchaseGoodsReq = new AdminCreateRepurchaseDto.AdminCreateRepurchaseGoodsReq();
                                BeanUtils.copyProperties(activityConfDetailGoods,adminCreateRepurchaseGoodsReq);
                                adminCreateRepurchaseGoodsReqs.add(adminCreateRepurchaseGoodsReq);
                            }
                        }
                        adminCreateRepuchaseDetailReq.setAdminCreateRepurchaseGoodsReqs(adminCreateRepurchaseGoodsReqs);
                    }
                    createRepuchaseDetailReqList.add(adminCreateRepuchaseDetailReq);
                }

                // 如果用户信息不为空  那么进行筛选参活门店
                if(StringUtils.isNotBlank(unionid)){
//                    List<AcActivityConfigDetails> filterActivityConfigDetail = new ArrayList<>();
                    // 查询用户拥有的卡列表    如果用户当前品牌有卡   并且有绑定的 门店  并且 在参活门店内
                    MemberQueryContext memberQueryContext = new MemberQueryContext();
                    memberQueryContext.setUnionId(unionid);
                    CustomerBaseResponse<List<MemberCardEntity>> memberCardList = iUserVipService.getMemberCardList(memberQueryContext);
                    if(memberCardList != null && CollectionUtils.isNotEmpty(memberCardList.getData())){
                        // 筛选过滤  取出门店包和品牌
                        for (MemberCardEntity datum : memberCardList.getData()) {
                            if(datum.getStoreId() != null){
                                List<AdminCreateRepurchaseDto.AdminCreateRepuchaseDetailReq> collect1 = createRepuchaseDetailReqList.stream().filter(r -> r.getWeid().equals(datum.getBrandId())).collect(Collectors.toList());
                                if(CollectionUtils.isNotEmpty(collect1)){
                                    String packetStoreId = collect1.get(0).getPacketStoreId();
                                    List<Integer> storeIds = new ArrayList<>();
                                    storeIds.add(datum.getStoreId());
                                    List<Integer> integers = rightsV3Service.targetStoreIds(packetStoreId, storeIds);
                                    if(CollectionUtils.isNotEmpty(integers)){
                                        //过滤人群包

                                        // 验证人群包  如果没有则不验证
                                        String packetPeopleId = collect1.get(0).getPacketPeopleId();
                                        if(StringUtils.isNotBlank(packetPeopleId)){
                                            // 验证人群包
                                            PeoplePacketReq peoplePacketReq = new PeoplePacketReq();
                                            List<String> crowdIds = new ArrayList<>();
                                            crowdIds.add(packetPeopleId);
                                            peoplePacketReq.setCrowdIds(crowdIds);
                                            peoplePacketReq.setCardNo(datum.getCardNo());
                                            //  groupByPeopleCrowdIds  key 就是全部的需要判断的 人群包id
                                            List<PeoplePacketResp> peoplePacketResps = iPackageStoresService.peopleBatchHit(peoplePacketReq);
                                            if(CollectionUtils.isNotEmpty(peoplePacketResps)){
                                                // 处理人群包返回数据
                                                for (PeoplePacketResp peoplePacketResp : peoplePacketResps) {
                                                    if(peoplePacketResp.isHit()){
                                                        // 命中人群  可以展示
                                                        collect1.get(0).setIsCanBuy("1");
                                                    }
                                                }
                                            }
                                        }else{
                                            collect1.get(0).setIsCanBuy("1");
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                adminCreateRepurchaseDto.setCreateRepuchaseDetailReqList(createRepuchaseDetailReqList);
            }
            adminCreateRepurchaseDto.setServerTime(Long.parseLong(nowDate));
            result.add(adminCreateRepurchaseDto);
        }
        return result;
    }

    @Override
    public List<AdminCreateRepurchaseDto> list(Object requestData, Page page) {
        // 分页查询 所有活动 未删除的
        com.github.pagehelper.Page<AcActivityConfig> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        acActivityConfigMapper.selectNotDelData();
        PageInfo<AcActivityConfig> pageInfo = new PageInfo<>(hPage);
        page.setCount(hPage.getTotal());
        page.setPages(pageInfo.getPages());
        List<AcActivityConfig> list = pageInfo.getList();
        // 封装里面的数据
        return  repurchaseDetails(list.stream().map(r->r.getId()).collect(Collectors.toList()),null,null,null);
    }

    @Override
    public String copyById(String activityConfigId, String userId) {
        AcActivityConfig acActivityConfig = acActivityConfigMapper.selectById(activityConfigId);
        if(acActivityConfig == null){
            return "-1";
        }
        // 查询基本信息
        List<AcActivityConfigDetails> acActivityConfigDetails = acActivityConfigDetailsMapper.selectByActivityConfigIdAndWeid(acActivityConfig.getId(), null);
        List<String> activityConfigDetailIds = acActivityConfigDetails.stream().map(r -> r.getId()).collect(Collectors.toList());
        // 查询配置库存基本信息
//        QueryWrapper<AcActivityConfigDetailsStoreQty> queryWrapper = new QueryWrapper<>();
//        queryWrapper.in("ac_activity_config_details_id", activityConfigDetailIds);
//        queryWrapper.eq("is_del", 0);
//        List<AcActivityConfigDetailsStoreQty> acActivityConfigDetailsStoreQtiesList = acActivityConfigDetailsStoreQtyMapper.selectList(queryWrapper);
//        Map<String, List<AcActivityConfigDetailsStoreQty>> storeQtyGroupByDetailsId = acActivityConfigDetailsStoreQtiesList.stream().collect(Collectors.groupingBy(r -> r.getAcActivityConfigDetailsId()));

        // 查询商品基本信息
        List<AcActivityConfDetailGoods> acActivityConfDetailGoods = acActivityConfDetailGoodsMapper.selectByDetailIds(activityConfigDetailIds);
        Map<String, List<AcActivityConfDetailGoods>> groupbyDetailId = acActivityConfDetailGoods.stream().collect(Collectors.groupingBy(r -> r.getAcActivityConfigDetailsId()));

        // 开始复制
        String id = IdLeaf.getId(IdConstant.AC_ACTIVITY_CONFIG);
        acActivityConfig.setId(id);
        acActivityConfig.setUpdateBy(userId);
        acActivityConfig.setUpdateTime(new Date());
        acActivityConfig.setCreateTime(new Date());
        acActivityConfig.setActivityName(acActivityConfig.getActivityName()+"(复制)");
        acActivityConfig.setStatus(1);
        acActivityConfig.setShareId(null);
        // 复制子数据
        List<AcActivityConfigDetails> insertDetailData = new ArrayList<>();
        List<AcActivityConfDetailGoods> insertDetailDataGoods = new ArrayList<>();
        List<AcActivityConfigDetailsStoreQty> insertDetailDataStoreQty = new ArrayList<>();

        for (AcActivityConfigDetails acActivityConfigDetail : acActivityConfigDetails) {
            AcActivityConfigDetails insertData = new AcActivityConfigDetails();
            BeanUtils.copyProperties(acActivityConfigDetail,insertData);
            String detailId  = IdLeaf.getId(IdConstant.AC_ACTIVITY_CONFIG_DETAILS);
            insertData.setId(detailId);
            insertData.setAcActivityConfigId(id);
            // 处理商品信息
            List<AcActivityConfDetailGoods> acActivityConfDetailGoods1 = groupbyDetailId.get(acActivityConfigDetail.getId());
            if(CollectionUtils.isNotEmpty(acActivityConfDetailGoods1)){
                for (AcActivityConfDetailGoods activityConfDetailGoods : acActivityConfDetailGoods1) {
                    AcActivityConfDetailGoods acActivityConfDetailGoodsData = new AcActivityConfDetailGoods();
                    BeanUtils.copyProperties(activityConfDetailGoods,acActivityConfDetailGoodsData);
                    acActivityConfDetailGoodsData.setId(IdLeaf.getId(IdConstant.AC_ACTIVITY_CONF_DETAIL_GOODS));
                    acActivityConfDetailGoodsData.setAcActivityConfigDetailsId(detailId);
                    insertDetailDataGoods.add(acActivityConfDetailGoodsData);
                }
            }
//            List<AcActivityConfigDetailsStoreQty> acActivityConfigDetailsStoreQties = storeQtyGroupByDetailsId.get(acActivityConfigDetail.getId());
//            if(CollectionUtils.isNotEmpty(acActivityConfigDetailsStoreQties)){
//                for (AcActivityConfigDetailsStoreQty acActivityConfigDetailsStoreQty : acActivityConfigDetailsStoreQties) {
//                    AcActivityConfigDetailsStoreQty acActivityConfigDetailsStoreQtyResult  = new AcActivityConfigDetailsStoreQty();
//                    BeanUtils.copyProperties(acActivityConfigDetailsStoreQty,acActivityConfigDetailsStoreQtyResult);
//                    acActivityConfigDetailsStoreQtyResult.setCreateTime(new Date());
//                    acActivityConfigDetailsStoreQtyResult.setUpdateTime(new Date());
//                    acActivityConfigDetailsStoreQtyResult.setAcActivityConfigDetailsId(detailId);
//                    insertDetailDataStoreQty.add(acActivityConfigDetailsStoreQtyResult);
//                }
//            }

            insertDetailData.add(insertData);
        }

        template.execute(action->{
           acActivityConfigMapper.insert(acActivityConfig);
           if(CollectionUtils.isNotEmpty(insertDetailData)){
               iAcActivityConfigDetailsService.saveBatch(insertDetailData);
           }
            if(CollectionUtils.isNotEmpty(insertDetailDataGoods)){
                iAcActivityConfDetailGoodsService.saveBatch(insertDetailDataGoods);
            }
//            if(CollectionUtils.isNotEmpty(insertDetailDataStoreQty)){
//                iAcActivityConfigDetailsStoreQtyService.saveBatch(insertDetailDataStoreQty);
//            }
            return action;
        });
        return acActivityConfig.getId();
    }

    @Override
    public void deleteById(String requestData, String userId) {
        if(StringUtils.isBlank(requestData)){
            return ;
        }
        AcActivityConfig acActivityConfig1 = acActivityConfigMapper.selectById(requestData);
        if(acActivityConfig1 == null){
            return ;
        }
        AcActivityConfig acActivityConfig = new AcActivityConfig();
        acActivityConfig.setId(requestData);
        acActivityConfig.setUpdateTime(new Date());
        acActivityConfig.setUpdateBy(userId);
        acActivityConfig.setIsDel(IsDeleteEnum.IS_DELETED.getCode());
        acActivityConfigMapper.updateById(acActivityConfig);
    }

    @Override
    public void repurchaseSendVoucherJob(String acRepurchaseDetailId) {
        //发放优惠券
        if(StringUtils.isNotBlank(acRepurchaseDetailId)){
            AcRepurchasePlanDetail acRepurchasePlanDetail = acRepurchasePlanDetailMapper.selectById(acRepurchaseDetailId);
            AcRepurchasePlanDetail update = new AcRepurchasePlanDetail();
            update.setId(acRepurchasePlanDetail.getId());
            AcActivityOrder acActivityOrder = acActivityOrderMapper.selectById(acRepurchasePlanDetail.getActivityOrderId());
            String referOrder = IdConstant.AC_REPURCHASE_PLAN_DETAIL + acRepurchasePlanDetail.getId();
            update.setVoucherNos(sendCoupon(acRepurchasePlanDetail.getAwardId().toString(),
                    null,acRepurchasePlanDetail.getSendNumber().toString(),referOrder,1,acRepurchasePlanDetail.getId(),
                    acActivityOrder.getUnionid(),acActivityOrder.getWeid(),acActivityOrder.getStoreCode()));
            // 更新
            acRepurchasePlanDetailMapper.updateById(update);
        }else{
            long yyyyMMdd = Long.parseLong(DateUtils.formatDate(new Date(), "yyyyMMdd"));
            Date date = new Date();
            // 分页进行发放
            com.github.pagehelper.Page<Object> hPage = PageHelper.startPage(1, 1000);
            acRepurchasePlanDetailMapper.selectShouldSendVouhcerAcRepurchaseDetailId(date,yyyyMMdd);
            PageInfo<AcRepurchasePlanDetail> retPage = new PageInfo(hPage);
            // 开始发放优惠券
            long total = retPage.getTotal();
            long pageTotal = 0;
            int pageSize = 100;
            if(total % pageSize == 0){
                pageTotal = total / pageSize;
            }else{
                pageTotal = total / pageSize + 1;
            }

            for(int i = 1 ; i <= pageTotal; i++){
                com.github.pagehelper.Page<Object> hPage1 = PageHelper.startPage(1, 500);
                acRepurchasePlanDetailMapper.selectShouldSendVouhcerAcRepurchaseDetailId(date,yyyyMMdd);
                PageInfo<AcRepurchasePlanDetail> retPage2 = new PageInfo(hPage1);
                List<AcRepurchasePlanDetail> list1 = retPage2.getList();
                for (AcRepurchasePlanDetail acRepurchasePlanDetail : list1) {
                    // 查询主的
                    AcRepurchasePlanDetail update = new AcRepurchasePlanDetail();
                    update.setId(acRepurchasePlanDetail.getId());
                    AcActivityOrder acActivityOrder = acActivityOrderMapper.selectById(acRepurchasePlanDetail.getActivityOrderId());
                    String referOrder = IdConstant.AC_REPURCHASE_PLAN_DETAIL + acRepurchasePlanDetail.getId();
                    update.setVoucherNos(sendCoupon(acRepurchasePlanDetail.getAwardId().toString(),
                            null,acRepurchasePlanDetail.getSendNumber().toString(),referOrder,1,acRepurchasePlanDetail.getId(),
                            acActivityOrder.getUnionid(),acActivityOrder.getWeid(),acActivityOrder.getStoreCode()));
                    // 更新
                    acRepurchasePlanDetailMapper.updateById(update);
                    // 如果当前的是最后一个  那么直接结束 查看是否均已经发放完毕
                    List<AcRepurchasePlanDetail> acRepurchasePlanDetails = acRepurchasePlanDetailMapper.selectByOrderId(acActivityOrder.getId());
                    if(CollectionUtils.isNotEmpty(acRepurchasePlanDetails)){
                        // 筛选已经发放完优惠券的  非null  空字符串也算发完了
                        int size = acRepurchasePlanDetails.stream().filter(r -> r.getVoucherNos() != null).collect(Collectors.toList()).size();
                        if(size == acRepurchasePlanDetail.getJoinTimes()){
                            AcActivityOrder updateAc = new AcActivityOrder();
                            updateAc.setId(acRepurchasePlanDetail.getActivityOrderId());
                            updateAc.setUpdateTime(new Date());
                            updateAc.setStatus(2);
                            acActivityOrderMapper.updateById(updateAc);
                        }
                    }

//                     发送公众号消息  去除公众号消息和短信消息  由MA去进行发送
//                    try {
//                        sendOpenMsgOrSms(acRepurchasePlanDetail.getId());
//                    }catch (Exception e){
//                        log.info("发送短信或者公众号失败 ，acRepurchasePlanId = {}",acRepurchasePlanDetail.getId(),e);
//                    }
                }
            }
        }
    }

    @Override
    public List<AdminActivityOrderDto> activityOrderList(AdminActivityOrderReq requestData, Page page) {
        // 分页查询 所有活动 未删除的
        com.github.pagehelper.Page<AcActivityOrder> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        acActivityOrderMapper.selectAlreadyPayOrderList(requestData);
        PageInfo<AcActivityOrder> pageInfo = new PageInfo<>(hPage);
        page.setCount(hPage.getTotal());
        page.setPages(pageInfo.getPages());
        List<AcActivityOrder> list = pageInfo.getList();
        // 封装里面的数据
        return JSONObject.parseArray(JSONObject.toJSONString(list),AdminActivityOrderDto.class);
    }

    @Override
    public void setAlert(SetAlertReq requestData) {
        AcActivityOrder acActivityOrder = acActivityOrderMapper.selectById(requestData.getId());
        if(acActivityOrder == null){
            return ;
        }
        AcActivityOrder update = new AcActivityOrder();
        update.setUpdateTime(new Date());
        update.setId(acActivityOrder.getId());
        update.setIsCanSendAlert(requestData.getIsCanSendAlert());
        acActivityOrderMapper.updateById(update);
    }

    @Override
    public List<GetStoreListResp> getStoreList(GetStoreListReq getStoreListReq) {
        List<GetStoreListResp> result = new ArrayList<>();

        AcActivityConfigDetails acActivityConfigDetails = acActivityConfigDetailsMapper.selectById(getStoreListReq.getActivityConfigDetailsId());
        // 拿到门店包  这个是参活门店
        getStoreListReq.setStorePackageId(acActivityConfigDetails.getPacketStoreId());
        List<GetStoreListResp> storeList = iPackageStoresService.getStoreList(getStoreListReq);
        if(CollectionUtils.isNotEmpty(storeList)){
            if(StringUtils.isNotBlank(getStoreListReq.getUnionid())){
                // 查询是否已经求补货
                List<AcAskSupportGoods>  list = acAskSupportGoodsMapper.selectByUnionid(getStoreListReq.getUnionid());
                // 设置对应的 数据
                Map<Long, List<AcAskSupportGoods>> collect1 = list.stream().collect(Collectors.groupingBy(r -> r.getStoreId()));
                // 查看用户 进行中的单子
                List<AcActivityOrder> acActivityOrders = acActivityOrderMapper.selectEfftiveStatusOneOrderByUnionid(getStoreListReq.getUnionid());
                if(CollectionUtils.isNotEmpty(acActivityOrders)){
                    // 筛选 符合的weid
                    List<AcActivityOrder> collect2 = acActivityOrders.stream().filter(r -> r.getWeid().equals(getStoreListReq.getBrandId())).collect(Collectors.toList());
                    if(CollectionUtils.isNotEmpty(collect2)){
                        String storeCode = collect2.get(0).getStoreCode();
                        List<GetStoreListResp> collect = storeList.stream().filter(r -> r.getCode().equals(storeCode)).collect(Collectors.toList());
                        if(CollectionUtils.isNotEmpty(collect)){
                            collect.get(0).setFlag(false);
                            result.add(collect.get(0));
                        }
                    }
                }
                if(CollectionUtils.isEmpty(result)){
                    // 查询用户信息
                    MemberCardQueryContext req = new MemberCardQueryContext();
                    req.setUnionId(getStoreListReq.getUnionid());
                    req.setBrandId(getStoreListReq.getBrandId());
                    CustomerBaseResponse<MemberCardEntity> memberCard = iUserVipService.getMemberCard(req);
                    if(memberCard != null && memberCard.getData() != null){
                        String storeCode = memberCard.getData().getStoreCode();
                        List<GetStoreListResp> collect = storeList.stream().filter(r -> r.getCode().equals(storeCode)).collect(Collectors.toList());
                        if(CollectionUtils.isNotEmpty(collect)){
                            collect.get(0).setFlag(true);
                            result.add(collect.get(0));
                        }
                        // 加后面的门店
                    }
                }

                // 设置数据
                for (GetStoreListResp getStoreListResp : result) {
                    List<AcAskSupportGoods> acAskSupportGoods = collect1.get(getStoreListResp.getId());
                    if(CollectionUtils.isNotEmpty(acAskSupportGoods)){
                        getStoreListResp.setIsSetAskSupportGoods(true);
                    }
                }
            }
        }
        return result;
    }

    @Override
    public List<AwardStoreStockResp> getAwardStoreStock(AwardStoreStockReq requestData) {
        List<AwardStoreStockResp> result = new ArrayList<>();
        //商品包id  获取商品
        String productPacketId = voucherRuleMapper.getGoodsFilterByAwardId(requestData.getAwardId());
        if(StringUtils.isNotBlank(productPacketId)){
            // 根据商品包id 获取商品id
            PacketProductReq packetProductReq = new PacketProductReq();
            packetProductReq.setProductPkgId(productPacketId);
            List<ProductPacketEntity> data = new ArrayList<>();
            try {
                log.info("调用 operate/product/pkg/list/by/pkgId  req = {}",JSONObject.toJSONString(packetProductReq));
                Response<CustomerBaseResponse<CustomerRecord<List<ProductPacketEntity>>>> execute = iPeopleInfoHttpApi.getProductPacket(packetProductReq).execute();
                log.info("调用 operate/product/pkg/list/by/pkgId  resp = {}",JSONObject.toJSONString(execute.body()));
                if(execute.body().getCode() == 200){
                     data = execute.body().getData().getRecords();
                }
            }catch (Exception e){
                log.info("调用商品包出现错误 = {}",JSONObject.toJSONString(packetProductReq),e);
            }
//            List<String> productIdsByGoodsFilter = mProductMapper.getProductIdsByGoodsFilter(goodsFilterByAwardId);
            if(CollectionUtils.isNotEmpty(data)){
                // 依次查询
                List<String> productIdsByGoodsFilter = data.stream().map(r -> r.getProductId()).collect(Collectors.toList());
                if(productIdsByGoodsFilter.size() > 900){
                    List<List<String>> partition = Lists.partition(productIdsByGoodsFilter, 900);
                    for (String storeId : requestData.getStoreIds()) {
                        AwardStoreStockResp awardStoreStockResp = new AwardStoreStockResp();
                        awardStoreStockResp.setStoreId(storeId);
                        awardStoreStockResp.setNumber(0L);
                        for (List<String> strings : partition) {
                            Long l = mProductMapper.sumByStoreIdAndProudctIds(storeId, strings);
                            awardStoreStockResp.setNumber(awardStoreStockResp.getNumber() + l);
                        }
                        result.add(awardStoreStockResp);
                    }
                }else{
                    for (String storeId : requestData.getStoreIds()) {
                        AwardStoreStockResp awardStoreStockResp = new AwardStoreStockResp();
                        awardStoreStockResp.setStoreId(storeId);
                        awardStoreStockResp.setNumber(0L);
                        Long l = mProductMapper.sumByStoreIdAndProudctIds(storeId, productIdsByGoodsFilter);
                        awardStoreStockResp.setNumber(awardStoreStockResp.getNumber() + l);
                        result.add(awardStoreStockResp);
                    }
                }
            }
        }else{
            for (String storeId : requestData.getStoreIds()) {
                AwardStoreStockResp awardStoreStockResp = new AwardStoreStockResp();
                awardStoreStockResp.setStoreId(storeId);
                awardStoreStockResp.setNumber(0L);
                Long l = mProductMapper.sumByStoreIdAndProudctIds(storeId, null);
                awardStoreStockResp.setNumber(awardStoreStockResp.getNumber() + l);
                result.add(awardStoreStockResp);
            }
        }
        // 如果是空的 则是全部商品  直接查库存就行
        return result;
    }

    @Override
    public PrePayDetailResp getPerPayDetail(PrePayDetailReq requestData) {
        if(requestData == null ||StringUtils.isBlank(requestData.getActivityConfigId())
                || StringUtils.isBlank(requestData.getWeid())
                || StringUtils.isBlank(requestData.getUnionid())){
            return null;
        }
        PrePayDetailResp prePayDetailResp = new PrePayDetailResp();
        List<AcActivityConfigDetails> acActivityConfigDetails = acActivityConfigDetailsMapper.selectByActivityConfigIdAndWeid(requestData.getActivityConfigId(), requestData.getWeid());
        if(CollectionUtils.isEmpty(acActivityConfigDetails)){
            return null;
        }
        RealPayContext realPayMap = getRealPayMap(acActivityConfigDetails.get(0), requestData.getUnionid(), requestData.getWeid());
        if(realPayMap != null){
            prePayDetailResp.setPayAmount(realPayMap.getPayAmount());
            prePayDetailResp.setPayPoint(realPayMap.getPayPoint());
            prePayDetailResp.setExchangePoint(realPayMap.getExchangePoint());
        }
        // 设置信息
        List<RepurchaseUseRuleCastDto> repurchaseUseRuleCastDtos = formatUseRule(acActivityConfigDetails.get(0).getUseRule(), acActivityConfigDetails.get(0).getJoinTimes(),
                acActivityConfigDetails.get(0).getCycleType());
        FormatTimeCycle formatTimeCycle = getFormatTimeCycle(repurchaseUseRuleCastDtos);
        prePayDetailResp.setStartCycle(DateUtils.formatDate(formatTimeCycle.getStartCycle(),"yyyy.MM.dd"));
        prePayDetailResp.setEndCycle(DateUtils.formatDate(formatTimeCycle.getEndCycle(),"yyyy.MM.dd"));
        return prePayDetailResp;
    }

    @Override
    public ResponseResult dealPaySuccess(NotifyPayMsgReq requestData) {
        // 校验数据
        if(StringUtils.isBlank(requestData.getOutPayNo())){
            log.info("dealPaySuccess 无外部单号 校验失败!");
            return ResponseResult.success("FAIL");
        }

        AcActivityOrder acActivityOrder = acActivityOrderMapper.selectById(requestData.getOutPayNo());
        if(acActivityOrder == null){
            log.info("dealPaySuccess 根据外部单号未查询到订单 ! outTradeNo = {}",requestData.getOutPayNo());
            return ResponseResult.success("FAIL");
        }
        // 已经处理完成
        if(acActivityOrder.getStatus().equals(1)){
            log.info("dealPaySuccess 已经处理完成 ! outTradeNo = {}",requestData.getOutPayNo());
            return ResponseResult.success("SUCCESS");
        }

        if(!acActivityOrder.getStatus().equals(0)){
            log.info("dealPaySuccess  状态非待支付状态! outTradeNo = {}",requestData.getOutPayNo());
            return ResponseResult.success("FAIL");
        }

        // 校验金额 TODO
        if(!(acActivityOrder.getPayPoint().intValue() == requestData.getUseIntegral().intValue())){
            log.info("dealPaySuccess 支付积分不相同 ! outTradeNo = {}",requestData.getUseIntegral());
            return ResponseResult.success("FAIL");
        }
        if(acActivityOrder.getPayType().equals(1)){
            // 校验支付金额
            if(!(acActivityOrder.getPayAmount().multiply(new BigDecimal("100")).longValue() ==
                    (requestData.getPriceActual().multiply(new BigDecimal("100"))).longValue())){
                log.info("dealPaySuccess 支付金额不相同 ! outTradeNo = {}",requestData.getPriceActual());
                return ResponseResult.success("FAIL");
            }
        }
        if(requestData.getStatus().equals(1L)){
            // 更新订单状态和 tradeNo
            AcActivityOrder update  = new AcActivityOrder();
            update.setId(acActivityOrder.getId());
            update.setPayTime(new Date(Long.parseLong(requestData.getSuccessTime()) * 1000));
            update.setStatus(1);
            update.setUpdateTime(new Date());
            update.setTradeNo(requestData.getTransactionId());
            // 发送优惠券 并且创建信息
            List<AcRepurchasePlanDetail> acRepurchasePlanDetails = buildRepurchaseList(acActivityOrder);

            template.execute(action->{
                acActivityOrderMapper.updateById(update);
                if(CollectionUtils.isNotEmpty(acRepurchasePlanDetails)){
                    for (AcRepurchasePlanDetail acRepurchasePlanDetail : acRepurchasePlanDetails) {
                        acRepurchasePlanDetailMapper.insert(acRepurchasePlanDetail);
                    }
                }
                return action;
            });
            // 提交零售单信息
            try {
                CreateRetailEvent event = new CreateRetailEvent(acActivityOrder,
                        System.currentTimeMillis(),
                        System.currentTimeMillis(),
                        UUID.randomUUID()
                );
                createRetailEventBus.post(event);
            } catch (PersistentBus.EventBusException e) {
                log.info("发送提交零售单异步错误 = ",e);
            }
            return ResponseResult.success("SUCCESS");
        }else{
            // 回调成功 未成功支付
            return ResponseResult.success("FAIL");
        }
    }

    @Override
    public Boolean getIsPaySuccess(String activityOrderId) {
        AcActivityOrder acActivityOrder = acActivityOrderMapper.selectById(activityOrderId);
        if(acActivityOrder != null){
            if(1 == acActivityOrder.getStatus()){
                return true;
            }
        }
        return false;
    }

    @Override
    public void setAskSupportGoods(AskSupportGoodsReq requestData) {
        AcAskSupportGoods acAskSupportGoods = new AcAskSupportGoods();
        acAskSupportGoods.setId(IdLeaf.getId(IdConstant.AC_ASK_SUPPORT_GOODS));
        acAskSupportGoods.setUnionid(requestData.getUnionid());
        acAskSupportGoods.setStoreId(requestData.getStoreId());
        acAskSupportGoodsMapper.insert(acAskSupportGoods);
    }

    @Override
    public List<AskSupportGoodsReq> queryAskSupportGoods(AskSupportGoodsReq requestData) {
        List<AcAskSupportGoods>  list = acAskSupportGoodsMapper.selectByUnionid(requestData.getUnionid());
        return JSONObject.parseArray(JSONObject.toJSONString(list),AskSupportGoodsReq.class);
    }

    @Override
    public void cancelActivityOrder() {
        // 查询需要取消订单的数据
        // 分组活动详情id和门店code  分别加锁  进行更新
        List<AcActivityOrder> list = acActivityOrderMapper.selectShouldCancelActivityOrder();
        // 处理没有门店的
        List<AcActivityOrder> storeCodeIsNullList = list.stream().filter(r -> StringUtils.isBlank(r.getStoreCode())).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(storeCodeIsNullList)){
            QueryWrapper<AcActivityOrder> queryWrapper = new QueryWrapper<>();
            queryWrapper.in("id",storeCodeIsNullList.stream().map(r->r.getId()).collect(Collectors.toList()));
            AcActivityOrder params = new AcActivityOrder();
            params.setStatus(-1);
            acActivityOrderMapper.update(params,queryWrapper);
        }


        // 筛选过滤 获得门店不为空的
        List<AcActivityOrder> storeCodeIsNotNullList = list.stream().filter(r -> StringUtils.isNotBlank(r.getStoreCode())).collect(Collectors.toList());
        // 分组
        Map<String, List<AcActivityOrder>> collect = storeCodeIsNotNullList.stream().collect(Collectors.groupingBy(r -> r.getActivityConfDetailId() +","+ r.getStoreCode()));
        for (String  detailIdAndStoreCode: collect.keySet()) {
            String[] split = detailIdAndStoreCode.split(",");
            String configDetailId = split[0];
            String storeCode = split[1];
            // 加锁
            String key = RedisKeyConstant.REPURCHASE_KEY.join( configDetailId,storeCode);
            try {
                if(redissonUtil.tryLock(key, 5, 10)){
                    // 处理数据  查询到当前的门店

                    List<AcActivityOrder> acActivityOrders = collect.get(detailIdAndStoreCode);
                    if(CollectionUtils.isNotEmpty(acActivityOrders)){
                        //需要修改的个数
                        int size = acActivityOrders.size();
                        template.execute(action->{
                            QueryWrapper<AcActivityOrder> queryWrapper2 = new QueryWrapper<>();
                            queryWrapper2.in("id",acActivityOrders.stream().map(r->r.getId()).collect(Collectors.toList()));
                            AcActivityOrder params = new AcActivityOrder();
                            params.setStatus(-1);
                            acActivityOrderMapper.update(params,queryWrapper2);
                            //回退库存
                            changeStoreQty(configDetailId,storeCode,size);
                            return action;
                        });
                    }
                }else{
                    log.info("回退库存未能成功加锁  key = {}",key);
                }
            }catch (Exception e){
                log.info("回退库存报错 ",e);
            }finally {
                redissonUtil.unlock(key);
            }
        }
    }

    /**
     *
     * @param configDetailId
     * @param storeCode
     * @param number   负数代表扣除   正数代表回退
     */
    private void changeStoreQty(String configDetailId, String storeCode,int number) {
        QueryWrapper<AcActivityConfigDetailsStoreQty> queryWrapper = new QueryWrapper<>();
        List<String> configDetailsIds = new ArrayList<>();
        configDetailsIds.add(configDetailId);
        queryWrapper.in("ac_activity_config_details_id", configDetailsIds);
        queryWrapper.eq("store_code", storeCode);
        queryWrapper.eq("is_del", 0);
        List<AcActivityConfigDetailsStoreQty> acActivityConfigDetailsStoreQtiesList = acActivityConfigDetailsStoreQtyMapper.selectList(queryWrapper);
        if(CollectionUtils.isEmpty(acActivityConfigDetailsStoreQtiesList)){
            // 可能是不限制库存  直接结束
            log.info("不限制库存  直接结束 configDetailId = {}",configDetailId);
            return ;
        }
        // 需要更改库存
        Integer leftQty = acActivityConfigDetailsStoreQtiesList.get(0).getLeftQty();
        Long id = acActivityConfigDetailsStoreQtiesList.get(0).getId();
        log.info("changeStoreQty 更改库存  门店库存主键id = {}, 门店编码  = {},  原库存数量 = {}, 更改库存数量 = {}",id,storeCode,leftQty,number);
        acActivityConfigDetailsStoreQtyMapper.incrLeftQty(id,leftQty,leftQty+number);

    }

    @Override
    public void createRetailOrder(AcActivityOrder info) {
        log.info("createRetailOrder = {}",JSONObject.toJSONString(info));
        if(StringUtils.isBlank(info.getId())){
            return ;
        }
        //查询数据
        AcActivityOrder acActivityOrder1 = acActivityOrderMapper.selectById(info.getId());
        if(acActivityOrder1  == null){
            return ;
        }
        // 创建零售单
        CheckBoxGiftRightsDto.SendMaterialDto sendMaterialDto = new CheckBoxGiftRightsDto.SendMaterialDto();
        // 获取当前用户的weid 和unionid
        MemberCardQueryContext req = new MemberCardQueryContext();
        req.setUnionId(info.getUnionid());
        req.setBrandId(info.getWeid());
        Long cVipId = null;
        CustomerBaseResponse<MemberCardEntity> memberCard = iUserVipService.getMemberCard(req);
        if(memberCard != null && memberCard.isSuccess() && memberCard.getData() != null){
            // 判断手机号不为空
            if(memberCard.getData().getId() != null ){
                cVipId =memberCard.getData().getId();
            }
        }
        if(cVipId == null){
            log.info("createRetailOrder  未查询到用户的vipid  = {}  " , JSONObject.toJSONString(info));
            return ;
        }
        // 根据品牌进行发商品
        List<RepurchaseBrandProductDto> repurchaseBrandProductDtos = JSONObject.parseArray(repurchaseCreateRetailSku, RepurchaseBrandProductDto.class);
        if(CollectionUtils.isEmpty(repurchaseBrandProductDtos)){
            return ;
        }
        RepurchaseBrandProductDto finalRepurchase = null;
        for (RepurchaseBrandProductDto repurchaseBrandProductDto : repurchaseBrandProductDtos) {
            if(info.getWeid().equals(repurchaseBrandProductDto.getWeid())){
                finalRepurchase = repurchaseBrandProductDto;
            }
        }
        if(finalRepurchase == null){
            log.info("createRetailOrder  当前品牌未配置信息，无法发送礼品信息" );
            return ;
        }
//        String[] split = repurchaseCreateRetailSku.split(",");
        String productId = finalRepurchase.getProductId();
        String skuId = finalRepurchase.getSkuId();
        String sku = finalRepurchase.getSku();
        String storeId = finalRepurchase.getStoreId();

        sendMaterialDto.setVouNo(null);
        sendMaterialDto.setChannelType(null);
        sendMaterialDto.setCount(null);
        sendMaterialDto.setC_vip_id(cVipId.toString());    // 用户vipid
        sendMaterialDto.setC_store_id(storeId);  //JTA00001-(GT)集团会员部活动礼品仓
        sendMaterialDto.setSourceCode(info.getId());
        sendMaterialDto.setDescription("复购计划权益");
        sendMaterialDto.setSource("MART");
        // 2025-03-24 增加商户号
        sendMaterialDto.setMerchantId(preCreateOrderMchid);
        if(StringUtils.isBlank(acActivityOrder1.getTradeNo())){
            sendMaterialDto.setOms_ebonum(acActivityOrder1.getId());
        }else{
            sendMaterialDto.setOms_ebonum(acActivityOrder1.getTradeNo());
        }
        sendMaterialDto.setOrderSource("JFHG");
        sendMaterialDto.setIsRequiredIntegral("N");
        sendMaterialDto.setOrderType("2");

        //封装product
        List<CheckBoxGiftRightsDto.ProductItem> productItems = new ArrayList<>();
        CheckBoxGiftRightsDto.ProductItem productItem = new CheckBoxGiftRightsDto.ProductItem();
        productItem.setId(productId);       //productId
        productItem.setCodeId(skuId);   //skuid
        productItem.setSkuCode(sku);
        productItem.setQty("1");
        productItem.setPriceactual(info.getPayAmount() != null ? info.getPayAmount().toString():"0");
        productItems.add(productItem);

        List<CheckBoxGiftRightsDto.Payitem> payitems = new ArrayList<>();
        CheckBoxGiftRightsDto.Payitem payitem = new CheckBoxGiftRightsDto.Payitem();
        payitem.setId(skuId);  // skuid
        payitem.setPayamount(info.getPayAmount() != null ? info.getPayAmount().toString():"0");
        payitems.add(payitem);
        sendMaterialDto.setProductItem(productItems);
        sendMaterialDto.setPayitem(payitems);

        try {
            log.info("createRetailOrder  SendMaterialDto = {} AcActivityOrder = {}",JSONObject.toJSONString(sendMaterialDto),JSONObject.toJSONString(info));
            // 发送快递
            Response<CustomerBaseResponse<SendMaterialBoxGiftContext>> execute = iJicInfoHttpApi.sendMaterialBoxGift(sendMaterialDto).execute();
            log.info("createRetailOrder = {}",JSONObject.toJSONString(execute));
            log.info("createRetailOrder = {}",JSONObject.toJSONString(execute.body()));
            CustomerBaseResponse<SendMaterialBoxGiftContext> body = execute.body();
            if(execute.isSuccessful()){
                boolean b = execute.body().getCode() == 200;
                if(b){
                    SendMaterialBoxGiftContext data = execute.body().getData();
                    template.execute(action->{
                        AcActivityOrder acActivityOrder = new AcActivityOrder();
                        acActivityOrder.setId(info.getId());
                        acActivityOrder.setEbOrder(data.getErrMsg());
                        acActivityOrderMapper.updateById(acActivityOrder);
                        return action;
                    });
                }
            }
        }catch (Exception e){
            log.error("发送复购权益生成re单异常   SendMaterialDto = {}, AcActivityOrder = {}",JSONObject.toJSONString(sendMaterialDto),
                    JSONObject.toJSONString(info),e);
            throw new RuntimeException("重试发送复购计划权益，可能是超时!");
        }
    }

    @Override
    public List<EmployeeBaseDto> getSalesListByStoreId(String requestData) {
        if(StringUtils.isBlank(requestData)){
            return new ArrayList<>();
        }
        //根据门店查询导购信息
        List<EmployeeBaseDto> employeeBaseDtos = hrEmployeeMapper.selectByStoreId(requestData);
        if(CollectionUtils.isNotEmpty(employeeBaseDtos)){
            List<EmployeeBaseDto> employeeBaseDtos1 = employeeBaseMapper.selectByHandSet(employeeBaseDtos.stream().map(r -> r.getPhone()).collect(Collectors.toList()));
            return employeeBaseDtos1;
        }
        return new ArrayList<>();
    }

    @Override
    public void refundActivityOrder(String activityOrderId) {
        // 1.  查看订单状态是否在进行中 就是已支付
        AcActivityOrder acActivityOrder = acActivityOrderMapper.selectById(activityOrderId);

        if(acActivityOrder == null){
            log.info("根据id未查询到订单信息 ={}",activityOrderId);
            return ;
        }
        Integer status = acActivityOrder.getStatus();
        if(!status.equals(1)){
            log.info("该订单状态不在进行中状态 ={}",activityOrderId);
            return ;
        }
        if(acActivityOrder.getIsDel().equals(1)){
            log.info("订单已删除 ={}",activityOrderId);
            return ;
        }

        // 开始执行退钱+退积分 + 失效优惠券 TODO  申请退款
        CommonRequest<CashierRefundReq> request  = new CommonRequest<>();
        CashierRefundReq cashierRefundReq = new CashierRefundReq();
        cashierRefundReq.setOutRefundNo("REFUND_"+activityOrderId);
        cashierRefundReq.setOutPayNo(activityOrderId);
        cashierRefundReq.setIntegral(acActivityOrder.getPayPoint() == null ? BigDecimal.ZERO : acActivityOrder.getPayPoint());
        cashierRefundReq.setRefundAmount(acActivityOrder.getPayAmount() == null ? BigDecimal.ZERO : acActivityOrder.getPayAmount());
        cashierRefundReq.setBusinessType(4L);

        CreateOrderGoods createOrderGoods = new CreateOrderGoods();
        // 设置参数
        createOrderGoods.setGoodsId(repurchaseGoodsSku);
        createOrderGoods.setGoodsName(repurchaseGoodsSku);
        createOrderGoods.setQuantity(1L);
        createOrderGoods.setSku(repurchaseGoodsSku);
        createOrderGoods.setSkuId(repurchaseGoodsSku);
        createOrderGoods.setItemId(activityOrderId);
        createOrderGoods.setPrice(acActivityOrder.getPayAmount() == null ? BigDecimal.ZERO : acActivityOrder.getPayAmount());
        createOrderGoods.setUseIntegral(acActivityOrder.getPayPoint() == null ? 0 : acActivityOrder.getPayPoint().longValue());

        List<CreateOrderGoods> goodsList = new ArrayList<>();
        goodsList.add(createOrderGoods);
        cashierRefundReq.setGoodsList(goodsList);
        request.setRequestData(cashierRefundReq);
        log.info("申请退款请求参数 = {}",JSONObject.toJSONString(request));
        ResponseResult<CashierRefundResp> cashierRefundRespResponseResult = payCenterApi.cashierRefund(request);
        log.info("申请退款返回参数 = {}",JSONObject.toJSONString(cashierRefundRespResponseResult));
        // 成功申请
        if(cashierRefundRespResponseResult != null && cashierRefundRespResponseResult.getCode() == 0){
            // 加锁  然后进行修改库存
            String configDetailId = acActivityOrder.getActivityConfDetailId();
            String storeCode = acActivityOrder.getStoreCode();
            if(StringUtils.isNotBlank(storeCode)){
                // 加锁
                String key = RedisKeyConstant.REPURCHASE_KEY.join( configDetailId,storeCode);
                // 处理数据
                try {
                    if(redissonUtil.tryLock(key,5,10)){
                        changeStoreQty(configDetailId,storeCode,1);
                    }
                }finally {
                    redissonUtil.unlock(key);
                }

            }

            // 更改为已售后状态
            AcActivityOrder update = new AcActivityOrder();
            update.setId(acActivityOrder.getId());
            update.setStatus(3);
            update.setUpdateTime(new Date());
            acActivityOrderMapper.updateById(update);
        }
    }

    @Override
    public void notifyRefund(CashierRefundResp requestData) {
        String outRefundNo = requestData.getOutRefundNo();
        if(StringUtils.isBlank(outRefundNo)){
            return ;
        }
        String activityOrderId = outRefundNo.substring(7);
        if(requestData.getStatus().equals(1L)){
            // 处理199的积分
            AcActivityOrder acActivityOrder = acActivityOrderMapper.selectById(activityOrderId);
            if(acActivityOrder != null){

                // 根据品牌进行发商品
                List<RepurchaseBrandProductDto> repurchaseBrandProductDtos = JSONObject.parseArray(repurchaseCreateRetailSku,
                        RepurchaseBrandProductDto.class);
                if(CollectionUtils.isEmpty(repurchaseBrandProductDtos)){
                    return ;
                }
                RepurchaseBrandProductDto finalRepurchase = null;
                for (RepurchaseBrandProductDto repurchaseBrandProductDto : repurchaseBrandProductDtos) {
                    if(acActivityOrder.getWeid().equals(repurchaseBrandProductDto.getWeid())){
                        finalRepurchase = repurchaseBrandProductDto;
                    }
                }
                if(finalRepurchase == null){
                    log.info("复购计划零售单退款  当前品牌未配置信息，无法发送礼品信息" );
                    return ;
                }

                String productId = finalRepurchase.getProductId();
                String skuId = finalRepurchase.getSkuId();
                String sku = finalRepurchase.getSku();
                String storeId = finalRepurchase.getStoreId();

                // 处理199送的100积分
                Map<String, Object> params = new HashMap<>();
                params.put("source", "MART");
                params.put("sourceCode", acActivityOrder.getId());
                if(StringUtils.isBlank(acActivityOrder.getTradeNo())){
                    params.put("refNo", ImageConstant.REFUND + acActivityOrder.getId());
                }else{
                    params.put("refNo", ImageConstant.REFUND + acActivityOrder.getTradeNo());
                }
                params.put("tot_qty", "");

                CheckBoxGiftRightsDto.ProductItem productItem = new CheckBoxGiftRightsDto.ProductItem();
                //封装product
                List<CheckBoxGiftRightsDto.ProductItem> productItems = new ArrayList<>();
                productItem.setId(productId);       //productId
                productItem.setCodeId(skuId);   //skuid
                productItem.setQty("1");
                productItem.setPriceactual(acActivityOrder.getPayAmount() != null ? acActivityOrder.getPayAmount().toString():"0");
                productItems.add(productItem);

                params.put("productItem", productItems);
                params.put("payitem", "");

                Map<String, Object> paramMap = new HashMap<>();
                paramMap.put("IN_JSON", JSONObject.toJSONString(params));
                log.info("复购计划零售单退款调用存储过程 请求参数 = {}",JSONObject.toJSONString(params));
                mRetailMapper.callCreateRetailRefundOrder(paramMap);
                log.info("复购计划零售单退款调用存储过程 返回参数 = {}", JSONObject.toJSONString(paramMap));
                if (!"1".equals(paramMap.get("RET")+"")){
                    // 打印log
                    log.info("复购计划零售单退款调用存储过程 生成退款失败 ao单号 = {}",acActivityOrder.getId());
                }else{
                    //
                    try {
                        Object orderIdRe = paramMap.get("ORDERID");
                        if(orderIdRe != null){
                            // 修改备注为复购计划退款
                            Long mretailId = mRetailMapper.selectIdByDocNo(orderIdRe.toString());
                            if(mretailId != null){
                                MRetail mRetail = new MRetail();
                                mRetail.setId(mretailId);
                                mRetail.setDescription("复购计划权益");
                                mRetailMapper.updateById(mRetail);
                            }
                        }
                    }catch (Exception e){
                        log.info("复购计划零售单退款调用存储过程 修改复购计划权益退款备注失败! ao单号 = {}",acActivityOrder.getId());
                    }
                }
            }

            // 默认认为已经退款
            List<AcRepurchasePlanDetail> list = acRepurchasePlanDetailMapper.selectByOrderId(activityOrderId);
            List<AcRepurchasePlanDetail> collect = list.stream().filter(r -> StringUtils.isNotBlank(r.getVoucherNos())).collect(Collectors.toList());
            List<String> voucherList = new ArrayList<>();
            for (AcRepurchasePlanDetail acRepurchasePlanDetail : collect) {
                String voucherNos = acRepurchasePlanDetail.getVoucherNos();
                String[] split = voucherNos.split(",");
                List<String> list1 = Arrays.asList(split);
                voucherList.addAll(list1);
            }
            if(CollectionUtils.isNotEmpty(voucherList)){
                // 失效优惠券
                VoucherInvalidContext voucherInvalidContext = new VoucherInvalidContext();
                voucherInvalidContext.setVoucherNos(voucherList);
                Boolean invalid = iVoucherService.invalid(voucherInvalidContext);
                if(!invalid){
                    log.info("失效优惠券失败，复购计划! orderId = " +activityOrderId);
                }
            }
        }
    }

    @Override
    public List<AcActivityConfigDetailsStoreQtyResp> findQtyByDetailsId(FindQtyByDetailsIdReq requestData, Page page) {
        // 分页查询 所有活动 未删除的
        com.github.pagehelper.Page<AcActivityConfigDetailsStoreQty> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        QueryWrapper<AcActivityConfigDetailsStoreQty> queryWrapper = new QueryWrapper();
        AcActivityConfigDetailsStoreQty params = new AcActivityConfigDetailsStoreQty();
        params.setIsDel(0);
        params.setAcActivityConfigDetailsId(requestData.getAcActivityConfigDetailsId());
        queryWrapper.setEntity(params);
        if(StringUtils.isNotBlank(requestData.getName())){
            queryWrapper.like("store_name",requestData.getName()+"%");
        }

        acActivityConfigDetailsStoreQtyMapper.selectList(queryWrapper);
        PageInfo<AcActivityConfigDetailsStoreQty> pageInfo = new PageInfo<>(hPage);
        page.setCount(hPage.getTotal());
        page.setPages(pageInfo.getPages());
        List<AcActivityConfigDetailsStoreQty> list = pageInfo.getList();
        String jsonString = JSONObject.toJSONString(list);
        return JSONObject.parseArray(jsonString,AcActivityConfigDetailsStoreQtyResp.class);
    }

    @Override
    public void deleteStoreQtyById(String storeQtyId, Page page) {
        AcActivityConfigDetailsStoreQty update = new AcActivityConfigDetailsStoreQty();
        update.setId(Long.parseLong(storeQtyId));
        update.setIsDel(IsDeleteEnum.IS_DELETED.getCode());
        acActivityConfigDetailsStoreQtyMapper.updateById(update);
    }

    @Override
    public FindStoreAllQtyResp findStoreAllQty(FindQtyByDetailsIdReq requestData, Page page) {
        FindStoreAllQtyResp findStoreAllQtyResp = new FindStoreAllQtyResp();
        QueryWrapper<AcActivityConfigDetailsStoreQty> queryWrapper = new QueryWrapper();
        AcActivityConfigDetailsStoreQty params = new AcActivityConfigDetailsStoreQty();
        params.setIsDel(0);
        params.setAcActivityConfigDetailsId(requestData.getAcActivityConfigDetailsId());
        queryWrapper.setEntity(params);
        List<AcActivityConfigDetailsStoreQty> acActivityConfigDetailsStoreQties = acActivityConfigDetailsStoreQtyMapper.selectList(queryWrapper);
        if(CollectionUtils.isNotEmpty(acActivityConfigDetailsStoreQties)){
            findStoreAllQtyResp.setQtyNum(acActivityConfigDetailsStoreQties.stream().mapToLong(r->r.getQty()).sum());
        }else{
            findStoreAllQtyResp.setQtyNum(0L);
        }
        return findStoreAllQtyResp;
    }

    @Override
    public void updateStoreQtyById(List<UpdateStoreQtyByIdReq> requestData, String userId,List<AcActivityConfigDetailsStoreQty> insertList,List<UpdateStoreQtyByIdReq> reqs) {
        if(CollectionUtils.isEmpty(requestData) && CollectionUtils.isEmpty(insertList)){
            return ;
        }

        // 更改主id为暂停中
        if(CollectionUtils.isNotEmpty(requestData)){
            AcActivityConfig acActivityConfig = new AcActivityConfig();
            acActivityConfig.setId(requestData.get(0).getAcActivityConfigId());
            acActivityConfig.setStatus(0);
            acActivityConfig.setUpdateTime(new Date());
        }

        // 不需要加锁更新  直接更新即可  前端是已经拦住的
        template.execute(action->{
            // 使用更改库存数量进行修改
            if(CollectionUtils.isNotEmpty(requestData)){
                for (UpdateStoreQtyByIdReq requestDatum : requestData) {
                    AcActivityConfigDetailsStoreQty acActivityConfigDetailsStoreQty = acActivityConfigDetailsStoreQtyMapper.selectById(requestDatum.getId());
                    acActivityConfigDetailsStoreQtyMapper.updateQtyById(requestDatum.getId(),requestDatum.getChangeNum());
                    // 记录日志
                    AcActivityConfigDetailsStoreQtyLog acActivityConfigDetailsStoreQtyLog = new AcActivityConfigDetailsStoreQtyLog();
                    acActivityConfigDetailsStoreQtyLog.setAcActivityConfigDetailsStoreQtyId(requestDatum.getId());
                    acActivityConfigDetailsStoreQtyLog.setBeforeNum(acActivityConfigDetailsStoreQty.getLeftQty().longValue());
                    acActivityConfigDetailsStoreQtyLog.setAfterNum(acActivityConfigDetailsStoreQty.getLeftQty().longValue() + requestDatum.getChangeNum());
                    acActivityConfigDetailsStoreQtyLog.setChangeUser(userId);
                    acActivityConfigDetailsStoreQtyLogMapper.insert(acActivityConfigDetailsStoreQtyLog);
                }
            }
            // 使用门店库存总数进行修改
            if(CollectionUtils.isNotEmpty(reqs)){
                for (UpdateStoreQtyByIdReq requestDatum : reqs) {
                    AcActivityConfigDetailsStoreQty acActivityConfigDetailsStoreQty = acActivityConfigDetailsStoreQtyMapper.selectById(requestDatum.getId());
                    acActivityConfigDetailsStoreQtyMapper.updateStoreQtyById(requestDatum.getId(),requestDatum.getChangeNum());
                    // 记录日志
                    AcActivityConfigDetailsStoreQtyLog acActivityConfigDetailsStoreQtyLog = new AcActivityConfigDetailsStoreQtyLog();
                    acActivityConfigDetailsStoreQtyLog.setAcActivityConfigDetailsStoreQtyId(requestDatum.getId());
                    acActivityConfigDetailsStoreQtyLog.setBeforeNum(acActivityConfigDetailsStoreQty.getLeftQty().longValue());
                    acActivityConfigDetailsStoreQtyLog.setAfterNum(requestDatum.getChangeNum().longValue());
                    acActivityConfigDetailsStoreQtyLog.setChangeUser(userId);
                    acActivityConfigDetailsStoreQtyLogMapper.insert(acActivityConfigDetailsStoreQtyLog);
                }
            }

            if(CollectionUtils.isNotEmpty(insertList)){
                // 查询当前这个门店是否存在 如果已经存在 则不处理   不存在则新增
                List<List<AcActivityConfigDetailsStoreQty>> partition = Lists.partition(insertList, 500);
                for (List<AcActivityConfigDetailsStoreQty> acActivityConfigDetailsStoreQtyList : partition) {
                    QueryWrapper<AcActivityConfigDetailsStoreQty> queryWrapper = new QueryWrapper<>();
                    queryWrapper.eq("ac_activity_config_details_id",insertList.get(0).getAcActivityConfigDetailsId());
                    queryWrapper.eq("is_del",0);
                    queryWrapper.in("store_code",acActivityConfigDetailsStoreQtyList.stream().map(r->r.getStoreCode()).collect(Collectors.toList()));
                    List<AcActivityConfigDetailsStoreQty> acActivityConfigDetailsStoreQtiesResult = acActivityConfigDetailsStoreQtyMapper.selectList(queryWrapper);
                    if(CollectionUtils.isNotEmpty(acActivityConfigDetailsStoreQtiesResult)){
                        List<String> collect = acActivityConfigDetailsStoreQtiesResult.stream().map(r -> r.getStoreCode()).collect(Collectors.toList());
                        List<AcActivityConfigDetailsStoreQty> shouldInsert = acActivityConfigDetailsStoreQtyList.stream().filter(r -> !collect.contains(r.getStoreCode())).collect(Collectors.toList());
                        iAcActivityConfigDetailsStoreQtyService.saveBatch(shouldInsert);
                    }else{
                        iAcActivityConfigDetailsStoreQtyService.saveBatch(acActivityConfigDetailsStoreQtyList);
                    }
                }
            }
            if(CollectionUtils.isNotEmpty(requestData)){
                String acActivityConfigId = requestData.get(0).getAcActivityConfigId();
                AcActivityConfig acActivityConfig = new AcActivityConfig();
                acActivityConfig.setId(acActivityConfigId);
                acActivityConfig.setStatus(1);
                acActivityConfig.setUpdateTime(new Date());
                acActivityConfigMapper.updateById(acActivityConfig);
            }

            return action;
        });
    }

    @Override
    public String importUpdateOrSaveQty(ImportUpdateOrSaveQtyReq importUpdateOrSaveQtyReq, String userId) {
        String acActivityConfigDetailsId = importUpdateOrSaveQtyReq.getAcActivityConfigDetailsId();
        // 导入库存
        //库存id，门店编码、门店名称、门店库存、剩余库存、库存调整方式（下拉：增加、减少），调整库存
        List<Map<String,String>> importData  = new ArrayList<>();
        //返回数据
        String filePath = FileParseUtil.downLoadExcel(importUpdateOrSaveQtyReq.getUrl());
        EasyExcelUtil.read(filePath, new BaseNoModelDataAbstractListener() {

            @Override
            protected void saveData() {
                List<Map<Integer, String>> cachedDataList1 = this.cachedDataList;
                for (Map<Integer, String> integerStringMap : cachedDataList1) {
                    Map<String,String> result = new HashMap<>();
                    //库存id，门店编码、门店名称、门店库存、剩余库存、库存调整方式（下拉：增加、减少），调整库存
                    result.put("storeCode",integerStringMap.get(0));
                    result.put("storeName",integerStringMap.get(1));
                    result.put("qty",integerStringMap.get(2));
                    result.put("leftQty",integerStringMap.get(3));
                    result.put("changeType",integerStringMap.get(4));
                    result.put("changeQtyNum",integerStringMap.get(5));
                    importData.add(result);
                }
            }
        });
        // 数据整合
        List<UpdateStoreQtyByIdReq> reqs= new ArrayList<>();
        List<AcActivityConfigDetailsStoreQty> insertList= new ArrayList<>();
        List<ExportAcActivityConfigDetailsStoreQtyDto> errorList =  new ArrayList<>();

        for (Map<String, String> importDatum : importData) {
            //
            try {
                if(StringUtils.isBlank(importDatum.get("storeCode"))){
                    errorList.add(buildErrorDto("门店编码未填写!",importDatum));
                    continue;
                }

                QueryWrapper<AcActivityConfigDetailsStoreQty> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("ac_activity_config_details_id",importUpdateOrSaveQtyReq.getAcActivityConfigDetailsId());
                queryWrapper.eq("is_del",0);
                queryWrapper.eq("store_code",importDatum.get("storeCode"));
                List<AcActivityConfigDetailsStoreQty> acActivityConfigDetailsStoreQtiesResult = acActivityConfigDetailsStoreQtyMapper.selectList(queryWrapper);
                // 修改
                if(CollectionUtils.isNotEmpty(acActivityConfigDetailsStoreQtiesResult)){
                    if(StringUtils.isBlank(importDatum.get("changeType"))){
                        // 没有类型
                        log.info("处理导入库存数据报错无增加还是减少类型 json = {} 错误信息 = ",JSONObject.toJSONString(importDatum));
                        errorList.add(buildErrorDto("修改类型不存在，请调整!",importDatum));
                        continue;
                    }
                    // 判断数字
                    if(StringUtils.isBlank(importDatum.get("changeQtyNum"))  || !isNumber(importDatum.get("changeQtyNum"))){
                        errorList.add(buildErrorDto("未填写更改数量或者更改数量不为整数数字，请调整!",importDatum));
                        continue;
                    }

                    int num = 0;
                    if(importDatum.get("changeType").equals("增加")){
                        num = Integer.parseInt(importDatum.get("changeQtyNum"));
                    }else {
                        num = - Integer.parseInt(importDatum.get("changeQtyNum"));
                    }
                    // 查询编码是否存在 如果存在则拿出来

                    UpdateStoreQtyByIdReq updateStoreQtyByIdReq = new UpdateStoreQtyByIdReq();
                    updateStoreQtyByIdReq.setId(acActivityConfigDetailsStoreQtiesResult.get(0).getId());
                    updateStoreQtyByIdReq.setAcActivityConfigId(importUpdateOrSaveQtyReq.getAcActivityConfigId());
                    updateStoreQtyByIdReq.setChangeNum(num);
                    reqs.add(updateStoreQtyByIdReq);

                }else{
                    // 验证门店编码是否存在   验证数字是否有
                    if(StringUtils.isBlank(importDatum.get("storeCode"))){
                        errorList.add(buildErrorDto("门店编码未填写!",importDatum));
                        continue;
                    }
                    if(StringUtils.isBlank(importDatum.get("qty"))){
                        errorList.add(buildErrorDto("总库存未填写!",importDatum));
                        continue;
                    }
                    if(StringUtils.isBlank(importDatum.get("storeName"))){
                        errorList.add(buildErrorDto("门店名称未填写!",importDatum));
                        continue;
                    }

                    List<String> storeCodes = new ArrayList<>();
                    storeCodes.add(importDatum.get("storeCode"));
                    List<CStore> cStores = cStoreMapper.selectListByCodes(storeCodes);
                    if(CollectionUtils.isEmpty(cStores)){
                        errorList.add(buildErrorDto("门店编码不正确!",importDatum));
                        continue;
                    }

                    if(!isNumber(importDatum.get("qty"))){
                        errorList.add(buildErrorDto("总库存或者剩余库存不是整数数字!",importDatum));
                        continue;
                    }

                    // 新增
                    AcActivityConfigDetailsStoreQty acActivityConfigDetailsStoreQty = new AcActivityConfigDetailsStoreQty();
                    acActivityConfigDetailsStoreQty.setStoreCode(importDatum.get("storeCode"));
                    acActivityConfigDetailsStoreQty.setLeftQty(Integer.parseInt(importDatum.get("qty")));
                    acActivityConfigDetailsStoreQty.setQty(Integer.parseInt(importDatum.get("qty")));
                    acActivityConfigDetailsStoreQty.setStoreName(cStores.get(0).getName());
                    acActivityConfigDetailsStoreQty.setIsDel(IsDeleteEnum.NORMAL.getCode());
                    acActivityConfigDetailsStoreQty.setCreateTime(new Date());
                    acActivityConfigDetailsStoreQty.setUpdateTime(new Date());
                    acActivityConfigDetailsStoreQty.setAcActivityConfigDetailsId(acActivityConfigDetailsId);
                    insertList.add(acActivityConfigDetailsStoreQty);
                }
            }catch (Exception e){
                log.info("处理导入库存数据报错 json = {} 错误信息 = ",JSONObject.toJSONString(importDatum),e);
                // 失败的需要处理
                continue;
            }
        }
        updateStoreQtyById(reqs,userId,insertList,null);

        // 上传七牛云 将地址发给前端
        if (CollectionUtils.isNotEmpty(errorList)) {
            List<List<String>> headListFinal = new ArrayList<>();
            List<String> headList2 = new ArrayList<>();
            headList2.add("门店编码");
            headListFinal.add(headList2);
            List<String> headList3 = new ArrayList<>();
            headList3.add("门店名称");
            headListFinal.add(headList3);
            List<String> headList4 = new ArrayList<>();
            headList4.add("门店库存");
            headListFinal.add(headList4);
            List<String> headList5 = new ArrayList<>();
            headList5.add("剩余库存");
            headListFinal.add(headList5);
            List<String> headList6 = new ArrayList<>();
            headList6.add("变更方式  增加 or 减少");
            headListFinal.add(headList6);
            List<String> headList7 = new ArrayList<>();
            headList7.add("调整数量");
            headListFinal.add(headList7);
            List<String> headList8 = new ArrayList<>();
            headList8.add("错误信息");
            headListFinal.add(headList8);
            // 下拉框
            Map<Integer, List<String>> selectMap = new HashMap<>();
            List<String> sexList = new ArrayList<>();
            sexList.add("增加");
            sexList.add("减少");
            selectMap.put(4, sexList);

            String fileName = System.currentTimeMillis() + ".xlsx";
            // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
            ((ExcelWriterBuilder)EasyExcelFactory.write(fileName,ExportAcActivityConfigDetailsStoreQtyDto.class)
                    .registerWriteHandler(new SelectSheetWriteHandler(selectMap))
                    .head(ExportAcActivityConfigDetailsStoreQtyDto.class))
                    .sheet("sheet1")
                    .doWrite(errorList);

            File file = new File(fileName);
            String param = qiniuUtil.upload(file.getPath(), "复购计划错误的导入门店库存信息"+System.currentTimeMillis() + ".xlsx");
            file.delete();
            log.info("错误门店导入信息 = {}", param);
            return param;
        }
        try {
            Files.deleteIfExists(Paths.get(filePath));
        }catch (Exception e){
            log.error("e= ",e);
        }
        return "";
    }

    private ExportAcActivityConfigDetailsStoreQtyDto buildErrorDto(String errorMsg, Map<String, String> importDatum) {
        ExportAcActivityConfigDetailsStoreQtyDto exportAcActivityConfigDetailsStoreQtyDto = new ExportAcActivityConfigDetailsStoreQtyDto();
        exportAcActivityConfigDetailsStoreQtyDto.setStoreCode(StringUtils.isBlank(importDatum.get("storeCode"))? null : importDatum.get("storeCode") );
        exportAcActivityConfigDetailsStoreQtyDto.setStoreName(StringUtils.isBlank(importDatum.get("storeName"))? null :importDatum.get("storeName") );
        exportAcActivityConfigDetailsStoreQtyDto.setQty(StringUtils.isBlank(importDatum.get("qty"))? null :importDatum.get("qty") );
        exportAcActivityConfigDetailsStoreQtyDto.setLeftQty(StringUtils.isBlank(importDatum.get("leftQty"))? null : importDatum.get("leftQty") );
        exportAcActivityConfigDetailsStoreQtyDto.setChangeType(StringUtils.isBlank(importDatum.get("changeType"))? null : importDatum.get("changeType") );
        exportAcActivityConfigDetailsStoreQtyDto.setChangeNum(StringUtils.isBlank(importDatum.get("changeQtyNum"))? null : importDatum.get("changeQtyNum") );
        exportAcActivityConfigDetailsStoreQtyDto.setErrorMsg(errorMsg);
        return exportAcActivityConfigDetailsStoreQtyDto;

    }


    public static boolean isNumber(String str) {
        return str.matches("^-?[1-9]\\d*$");
    }

    @Override
    public String exportStoreQty(String acActivityConfigDetailsId, Page page, HttpServletResponse response)throws Exception {
        QueryWrapper<AcActivityConfigDetailsStoreQty> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("ac_activity_config_details_id", acActivityConfigDetailsId);
        queryWrapper.eq("is_del", 0);
        List<AcActivityConfigDetailsStoreQty> acActivityConfigDetailsStoreQtiesList = acActivityConfigDetailsStoreQtyMapper.selectList(queryWrapper);
        // 导出字段为
        // 主键id , 门店编码、门店名称、门店库存、剩余库存、库存调整方式（下拉：增加、减少），调整库存
        if(CollectionUtils.isNotEmpty(acActivityConfigDetailsStoreQtiesList)){
            List<ExportAcActivityConfigDetailsStoreQtyDto> list = buildExportData(acActivityConfigDetailsStoreQtiesList);

            List<List<String>> headListFinal = new ArrayList<>();
            List<String> headList2 = new ArrayList<>();
            headList2.add("门店编码");
            headListFinal.add(headList2);
            List<String> headList3 = new ArrayList<>();
            headList3.add("门店名称");
            headListFinal.add(headList3);
            List<String> headList4 = new ArrayList<>();
            headList4.add("门店库存");
            headListFinal.add(headList4);
            List<String> headList5 = new ArrayList<>();
            headList5.add("剩余库存");
            headListFinal.add(headList5);
            List<String> headList6 = new ArrayList<>();
            headList6.add("变更方式  增加 or 减少");
            headListFinal.add(headList6);
            List<String> headList7 = new ArrayList<>();
            headList7.add("调整数量");
            headListFinal.add(headList7);
            // 下拉框
            Map<Integer, List<String>> selectMap = new HashMap<>();
            List<String> sexList = new ArrayList<>();
            sexList.add("增加");
            sexList.add("减少");
            selectMap.put(4, sexList);

            String fileName = System.currentTimeMillis() + ".xlsx";
            // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
            ((ExcelWriterBuilder)EasyExcelFactory.write(fileName,ExportAcActivityConfigDetailsStoreQtyDto2.class)
                    .registerWriteHandler(new SelectSheetWriteHandler(selectMap))
                    .head(ExportAcActivityConfigDetailsStoreQtyDto2.class))
                    .sheet("sheet1")
                    .doWrite(list);

            File file = new File(fileName);
            String param = qiniuUtil.upload(file.getPath(), "复购计划库存导出文件"+System.currentTimeMillis() + ".xlsx");
            file.delete();
            log.info("导出复购计划门店库存key = {}", param);
            return param;
        }
        return null;
    }

    @Override
    public void upOrDown(UpOrDownReq requestData, String userId) {
        AcActivityConfig acActivityConfig = new AcActivityConfig();
        acActivityConfig.setId(requestData.getId());
        acActivityConfig.setStatus(requestData.getStatus());
        acActivityConfig.setUpdateTime(new Date());
        acActivityConfig.setUpdateBy(userId);
        acActivityConfigMapper.updateById(acActivityConfig);
    }

    @Override
    public void updateStoreTotalQtyById(List<AcActivityConfigDetailsStoreQtyResp> requestData) {
        // 调整总库存和剩余库存
        if(CollectionUtils.isEmpty(requestData)){
            return ;
        }

        template.execute(action->{
            for (AcActivityConfigDetailsStoreQtyResp requestDatum : requestData) {
                AcActivityConfigDetailsStoreQty acActivityConfigDetailsStoreQty = new AcActivityConfigDetailsStoreQty();
                acActivityConfigDetailsStoreQty.setQty(requestDatum.getQty());
                acActivityConfigDetailsStoreQty.setLeftQty(requestDatum.getQty());
                acActivityConfigDetailsStoreQty.setUpdateTime(new Date());
                acActivityConfigDetailsStoreQty.setId(requestDatum.getId());
                acActivityConfigDetailsStoreQtyMapper.updateById(acActivityConfigDetailsStoreQty);
            }
            return action;
        });
    }

    @Override
    public String importSaveQty(ImportUpdateOrSaveQtyReq importUpdateOrSaveQtyReq, String userId) {
        String acActivityConfigDetailsId = importUpdateOrSaveQtyReq.getAcActivityConfigDetailsId();
        // 导入库存
        List<Map<String,String>> importData  = new ArrayList<>();
        //返回数据
        String filePath = FileParseUtil.downLoadExcel(importUpdateOrSaveQtyReq.getUrl());
        EasyExcelUtil.read(filePath, new BaseNoModelDataAbstractListener() {

            @Override
            protected void saveData() {
                List<Map<Integer, String>> cachedDataList1 = this.cachedDataList;
                for (Map<Integer, String> integerStringMap : cachedDataList1) {
                    Map<String,String> result = new HashMap<>();
                    //库存id，
                    result.put("storeCode",integerStringMap.get(0));
                    result.put("storeName",integerStringMap.get(1));
                    result.put("qty",integerStringMap.get(2));
                    importData.add(result);
                }
            }
        });
        // 数据整合
        List<UpdateStoreQtyByIdReq> reqs= new ArrayList<>();
        List<AcActivityConfigDetailsStoreQty> insertList= new ArrayList<>();
        List<ExportAcActivityConfigDetailsStoreQtyDto> errorList =  new ArrayList<>();

        for (Map<String, String> importDatum : importData) {
            //
            try {
                if(StringUtils.isBlank(importDatum.get("storeCode"))){
                    errorList.add(buildErrorDto("门店编码未填写!",importDatum));
                    continue;
                }

                QueryWrapper<AcActivityConfigDetailsStoreQty> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("ac_activity_config_details_id",importUpdateOrSaveQtyReq.getAcActivityConfigDetailsId());
                queryWrapper.eq("is_del",0);
                queryWrapper.eq("store_code",importDatum.get("storeCode"));
                List<AcActivityConfigDetailsStoreQty> acActivityConfigDetailsStoreQtiesResult = acActivityConfigDetailsStoreQtyMapper.selectList(queryWrapper);
                // 修改
                if(CollectionUtils.isNotEmpty(acActivityConfigDetailsStoreQtiesResult)){
                    if(StringUtils.isBlank(importDatum.get("qty"))){
                        // 没有类型
                        errorList.add(buildErrorDto("门店库存为空，请重新填写!",importDatum));
                        continue;
                    }
                    if(!isNumber(importDatum.get("qty"))){
                        errorList.add(buildErrorDto("总库存或者剩余库存不是整数数字!",importDatum));
                        continue;
                    }

                    // 查询编码是否存在 如果存在则拿出来
                    UpdateStoreQtyByIdReq updateStoreQtyByIdReq = new UpdateStoreQtyByIdReq();
                    updateStoreQtyByIdReq.setId(acActivityConfigDetailsStoreQtiesResult.get(0).getId());
                    updateStoreQtyByIdReq.setAcActivityConfigId(importUpdateOrSaveQtyReq.getAcActivityConfigId());
                    updateStoreQtyByIdReq.setChangeNum(Integer.parseInt(importDatum.get("qty")));
                    reqs.add(updateStoreQtyByIdReq);

                }else{
                    // 验证门店编码是否存在   验证数字是否有
                    if(StringUtils.isBlank(importDatum.get("storeCode"))){
                        errorList.add(buildErrorDto("门店编码未填写!",importDatum));
                        continue;
                    }
                    if(StringUtils.isBlank(importDatum.get("qty"))){
                        errorList.add(buildErrorDto("总库存未填写!",importDatum));
                        continue;
                    }
                    if(StringUtils.isBlank(importDatum.get("storeName"))){
                        errorList.add(buildErrorDto("门店名称未填写!",importDatum));
                        continue;
                    }

                    List<String> storeCodes = new ArrayList<>();
                    storeCodes.add(importDatum.get("storeCode"));
                    List<CStore> cStores = cStoreMapper.selectListByCodes(storeCodes);
                    if(CollectionUtils.isEmpty(cStores)){
                        errorList.add(buildErrorDto("门店编码不正确!",importDatum));
                        continue;
                    }

                    if(!isNumber(importDatum.get("qty"))){
                        errorList.add(buildErrorDto("总库存或者剩余库存不是整数数字!",importDatum));
                        continue;
                    }

                    // 新增
                    AcActivityConfigDetailsStoreQty acActivityConfigDetailsStoreQty = new AcActivityConfigDetailsStoreQty();
                    acActivityConfigDetailsStoreQty.setStoreCode(importDatum.get("storeCode"));
                    acActivityConfigDetailsStoreQty.setLeftQty(Integer.parseInt(importDatum.get("qty")));
                    acActivityConfigDetailsStoreQty.setQty(Integer.parseInt(importDatum.get("qty")));
                    acActivityConfigDetailsStoreQty.setStoreName(cStores.get(0).getName());
                    acActivityConfigDetailsStoreQty.setIsDel(IsDeleteEnum.NORMAL.getCode());
                    acActivityConfigDetailsStoreQty.setCreateTime(new Date());
                    acActivityConfigDetailsStoreQty.setUpdateTime(new Date());
                    acActivityConfigDetailsStoreQty.setAcActivityConfigDetailsId(acActivityConfigDetailsId);
                    insertList.add(acActivityConfigDetailsStoreQty);
                }
            }catch (Exception e){
                log.info("处理导入库存数据报错 json = {} 错误信息 = ",JSONObject.toJSONString(importDatum),e);
                // 失败的需要处理
                continue;
            }
        }
        updateStoreQtyById(null,userId,insertList,reqs);

        // 上传七牛云 将地址发给前端
        if (CollectionUtils.isNotEmpty(errorList)) {
            List<List<String>> headListFinal = new ArrayList<>();
            List<String> headList2 = new ArrayList<>();
            headList2.add("门店编码");
            headListFinal.add(headList2);
            List<String> headList3 = new ArrayList<>();
            headList3.add("门店名称");
            headListFinal.add(headList3);
            List<String> headList4 = new ArrayList<>();
            headList4.add("门店库存");
            headListFinal.add(headList4);
            List<String> headList5 = new ArrayList<>();
            headList5.add("剩余库存");
            headListFinal.add(headList5);
            List<String> headList6 = new ArrayList<>();
            headList6.add("变更方式  增加 or 减少");
            headListFinal.add(headList6);
            List<String> headList7 = new ArrayList<>();
            headList7.add("调整数量");
            headListFinal.add(headList7);
            List<String> headList8 = new ArrayList<>();
            headList8.add("错误信息");
            headListFinal.add(headList8);
            // 下拉框
            Map<Integer, List<String>> selectMap = new HashMap<>();
            List<String> sexList = new ArrayList<>();
            sexList.add("增加");
            sexList.add("减少");
            selectMap.put(4, sexList);

            String fileName = System.currentTimeMillis() + ".xlsx";
            // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
            ((ExcelWriterBuilder)EasyExcelFactory.write(fileName,ExportAcActivityConfigDetailsStoreQtyDto.class)
                    .registerWriteHandler(new SelectSheetWriteHandler(selectMap))
                    .head(ExportAcActivityConfigDetailsStoreQtyDto.class))
                    .sheet("sheet1")
                    .doWrite(errorList);

            File file = new File(fileName);
            String param = qiniuUtil.upload(file.getPath(), "复购计划错误的导入门店库存信息"+System.currentTimeMillis() + ".xlsx");
            file.delete();
            log.info("错误门店导入信息 = {}", param);
            return param;
        }
        try {
            Files.deleteIfExists(Paths.get(filePath));
        }catch (Exception e){
            log.error("e= ",e);
        }
        return "";
    }

    @Override
    public void fixAoOrderReOrder(String aoOrderId) {
        AcActivityOrder acActivityOrder = acActivityOrderMapper.selectById(aoOrderId);
        if(acActivityOrder != null){
            createRetailOrder(acActivityOrder);
        }
    }

    private List<ExportAcActivityConfigDetailsStoreQtyDto> buildExportData(List<AcActivityConfigDetailsStoreQty> acActivityConfigDetailsStoreQtiesList) {
        List<ExportAcActivityConfigDetailsStoreQtyDto> result = new ArrayList<>();
        for (AcActivityConfigDetailsStoreQty acActivityConfigDetailsStoreQty : acActivityConfigDetailsStoreQtiesList) {
            ExportAcActivityConfigDetailsStoreQtyDto exportAcActivityConfigDetailsStoreQtyDto = new ExportAcActivityConfigDetailsStoreQtyDto();
            exportAcActivityConfigDetailsStoreQtyDto.setStoreCode(acActivityConfigDetailsStoreQty.getStoreCode());
            exportAcActivityConfigDetailsStoreQtyDto.setStoreName(acActivityConfigDetailsStoreQty.getStoreName());
            exportAcActivityConfigDetailsStoreQtyDto.setQty(acActivityConfigDetailsStoreQty.getQty().toString());
            exportAcActivityConfigDetailsStoreQtyDto.setLeftQty(acActivityConfigDetailsStoreQty.getLeftQty().toString());
            exportAcActivityConfigDetailsStoreQtyDto.setChangeNum("0");
            result.add(exportAcActivityConfigDetailsStoreQtyDto);
        }
        return result;
    }

    private FormatTimeCycle getFormatTimeCycle ( List<RepurchaseUseRuleCastDto> repurchaseUseRuleCastDtos){
        FormatTimeCycle formatTimeCycle = new FormatTimeCycle();
        // 获取周期

        String MMdd = DateUtils.formatDate(new Date(), "MMdd");
        int nowMmDd = Integer.parseInt(MMdd);

        String yyyy = DateUtils.formatDate(new Date(), "yyyy");
        int nowYear = Integer.parseInt(yyyy);
        // true 今年
        Boolean flag = true;

        int i = 1;
        int k = 100;

        for ( int j = 0 ; j < repurchaseUseRuleCastDtos.size() ; j++ ) {
            RepurchaseUseRuleCastDto repurchaseUseRuleCastDto = repurchaseUseRuleCastDtos.get(j);

            String startTime = repurchaseUseRuleCastDto.getStartTime();
            String endTime = repurchaseUseRuleCastDto.getEndTime();
            // 需要对比的是   当前和下一个
            // 只要是非最后一个  均走此
            // 如果是今年要走这个  明年就不用走了



            if(flag){
                repurchaseUseRuleCastDto.setHaveYearStartTime(Long.parseLong(nowYear+startTime));
                repurchaseUseRuleCastDto.setHaveYearEndTime(Long.parseLong(nowYear+endTime));
                repurchaseUseRuleCastDto.setSortNum(i);
                i++;
            }

            //false的时候 就是明年的
            if(!flag){

                repurchaseUseRuleCastDto.setHaveYearStartTime(Long.parseLong((nowYear) + startTime));
                repurchaseUseRuleCastDto.setHaveYearEndTime(Long.parseLong((nowYear) + endTime));
                repurchaseUseRuleCastDto.setSortNum(k);
            }

            if(j != repurchaseUseRuleCastDtos.size() - 1){
                // 对比第一个和下一个  这个满足 下一个开始下一年
                RepurchaseUseRuleCastDto repurchaseUseRuleCastDto1 = repurchaseUseRuleCastDtos.get(j + 1);
                if(Integer.parseInt(repurchaseUseRuleCastDto.getStartTime()) > Integer.parseInt(repurchaseUseRuleCastDto1.getStartTime())){
                    flag =false;
                    nowYear = nowYear + 1 ;
                }
            }

            k++;
        }
        // 排序 按照排序号

        repurchaseUseRuleCastDtos = repurchaseUseRuleCastDtos.stream().sorted(new Comparator<RepurchaseUseRuleCastDto>() {
            @Override
            public int compare(RepurchaseUseRuleCastDto o1, RepurchaseUseRuleCastDto o2) {
                return o1.getSortNum() - o2.getSortNum();
            }
        }).collect(Collectors.toList());
        RepurchaseUseRuleCastDto repurchaseUseRuleCastDto = repurchaseUseRuleCastDtos.get(0);
        RepurchaseUseRuleCastDto repurchaseUseRuleCastDto1 = repurchaseUseRuleCastDtos.get(repurchaseUseRuleCastDtos.size() - 1);

        formatTimeCycle.setRepurchaseUseRuleCastDtos(repurchaseUseRuleCastDtos);
        formatTimeCycle.setStartCycle(DateUtil.parseDate(repurchaseUseRuleCastDto.getHaveYearStartTime().toString(),"yyyyMMdd"));
        formatTimeCycle.setEndCycle(DateUtil.parseDate(repurchaseUseRuleCastDto1.getHaveYearEndTime().toString() +" 23:59:59","yyyyMMdd HH:mm:ss"));
        return formatTimeCycle;
    }


    //封装子单
    private List<AcRepurchasePlanDetail> buildRepurchaseList(AcActivityOrder acActivityOrder){
        //封装对象
        List<AcRepurchasePlanDetail> insertList = new ArrayList<>();
        // 生成计划
        // 通过信息获取配置信息
        // 筛选获取
        List<AcActivityConfigDetails> acActivityConfigDetails = acActivityConfigDetailsMapper.selectByActivityConfigIdAndWeid(acActivityOrder.getActivityConfigId(), acActivityOrder.getWeid());
        // 获取配置信息
        if(CollectionUtils.isEmpty(acActivityConfigDetails)){
            log.info("生成复购计划子单  未查询到配置信息  activityDetailId  = {}",acActivityOrder.getActivityConfDetailId());
            throw  new RuntimeException("生成复购计划子单  未查询到配置信息");
        }
        // 开始创建
        String useRule = acActivityConfigDetails.get(0).getUseRule();
        List<RepurchaseUseRuleCastDto> repurchaseUseRuleCastDtos   = formatUseRule(useRule, acActivityConfigDetails.get(0).getJoinTimes(), acActivityConfigDetails.get(0).getCycleType());
        repurchaseUseRuleCastDtos = getFormatTimeCycle(repurchaseUseRuleCastDtos).getRepurchaseUseRuleCastDtos();

        for (RepurchaseUseRuleCastDto repurchaseUseRuleCastDto : repurchaseUseRuleCastDtos) {

            String id = IdLeaf.getId(IdConstant.AC_REPURCHASE_PLAN_DETAIL);

            AcRepurchasePlanDetail acRepurchasePlanDetail = new AcRepurchasePlanDetail();
            acRepurchasePlanDetail.setId(id);
            acRepurchasePlanDetail.setCreateTime(new Date());
            acRepurchasePlanDetail.setUpdateTime(new Date());
            acRepurchasePlanDetail.setIsDel(IsDeleteEnum.NORMAL.getCode());
            acRepurchasePlanDetail.setAwardId(repurchaseUseRuleCastDto.getAwardId());
            acRepurchasePlanDetail.setActivityOrderId(acActivityOrder.getId());
            acRepurchasePlanDetail.setSendNumber(repurchaseUseRuleCastDto.getNumber());
            acRepurchasePlanDetail.setJoinTimes(acActivityConfigDetails.get(0).getJoinTimes());
            acRepurchasePlanDetail.setOutName(repurchaseUseRuleCastDto.getOutName());
            acRepurchasePlanDetail.setStartTime(repurchaseUseRuleCastDto.getHaveYearStartTime());
            acRepurchasePlanDetail.setEndTime(repurchaseUseRuleCastDto.getHaveYearEndTime());
            acRepurchasePlanDetail.setSortNum(repurchaseUseRuleCastDto.getSortNum());
            insertList.add(acRepurchasePlanDetail);
        }
        // 发放优惠券  排序
        insertList = insertList.stream().sorted(new Comparator<AcRepurchasePlanDetail>() {
            @Override
            public int compare(AcRepurchasePlanDetail o1, AcRepurchasePlanDetail o2) {
                return o1.getSortNum() - o2.getSortNum();
            }
        }).collect(Collectors.toList());
        // 发放优惠券
        AcRepurchasePlanDetail acRepurchasePlanDetail = insertList.get(0);
        String referOrder = IdConstant.AC_REPURCHASE_PLAN_DETAIL + acRepurchasePlanDetail.getId();
        acRepurchasePlanDetail.setVoucherNos(sendCoupon(acRepurchasePlanDetail.getAwardId().toString(),
                null,acRepurchasePlanDetail.getSendNumber().toString(),referOrder,1,acRepurchasePlanDetail.getId(),
                acActivityOrder.getUnionid(),acActivityOrder.getWeid(),acActivityOrder.getStoreCode()));

        // 插入数据库
        return insertList;
    }


    /**
     * @param awardIds     券号 多个
     * @param openId       公众号openid
     * @param numbers      发放数量 多个
     * @param referOrder   发放ordersn
     * @param reSendNumber 默认重试3次 ，  传递 0 和 1 为测试发放失败,传递 2正常发放  可重试一次
     * @param storeCode    门店id
     * @return
     */
    private String sendCoupon(String awardIds, String openId, String numbers, String referOrder, int reSendNumber,
                              String acRepurchasePlanDetailId, String unionid, String weid, String storeCode){

        List<String> voucherNosResult = new ArrayList<>();

        // 查询用户信息
        if(StringUtils.isBlank(openId)){
            MemberCardQueryContext req = new MemberCardQueryContext();
            req.setUnionId(unionid);
            req.setBrandId(weid);
            CustomerBaseResponse<MemberCardEntity> memberCard = iUserVipService.getMemberCard(req);
            if(memberCard != null && memberCard.getData() != null){
                if(StringUtils.isNotBlank(memberCard.getData().getOpenId())){
                    openId = memberCard.getData().getOpenId();
                }
            }
        }
        if(StringUtils.isBlank(openId)){
            return "";
        }
        // 需要拿到 公众号openid
        String referOrderRe = IdConstant.AC_REPURCHASE_PLAN_DETAIL + acRepurchasePlanDetailId;

        // 先查询是否已经发放过
        List<JicVoucherSendRecord> jicVoucherSendRecords = jicVoucherSendRecordMapper.selectByReferOrder(referOrderRe);
        if(CollectionUtils.isNotEmpty(jicVoucherSendRecords)){
            log.info("该用户已经发放过， 直接使用数据库数据  = {}",JSONObject.toJSONString(jicVoucherSendRecords));
            // 拼接 然后进行塞入
            List<String> collect = jicVoucherSendRecords.stream().filter(r -> StringUtils.isNotBlank(r.getVoucherNo())).map(r -> r.getVoucherNo()).collect(Collectors.toList());
            voucherNosResult.addAll(collect);
            return voucherNosResult.stream().collect(Collectors.joining(","));
        }

        JICVoucherSendReqEntity jicVoucherSendReqEntity = new JICVoucherSendReqEntity();
        jicVoucherSendReqEntity.setOpenId(openId);
        List<RulesList> rulesLists = new ArrayList<>();

        String[] split = awardIds.split(",");
        String[] split1 = numbers.split(",");
        for (int j = 0 ; j< split.length; j++) {
            String awardId = split[j];
            String number = split1[j];
            RulesList rules = new RulesList();
            rules.setAwardId(Long.parseLong(awardId));
            rules.setNum(Long.parseLong(number));
            rulesLists.add(rules);
        }

        jicVoucherSendReqEntity.setRulesList(rulesLists);
        jicVoucherSendReqEntity.setReferOrder(referOrderRe);
        JICVoucherSendRespEntity jicVoucherSendRespEntity = new JICVoucherSendRespEntity();
        // 为了测试
        if(reSendNumber <= 0){
            jicVoucherSendRespEntity.setCode(200);
            jicVoucherSendRespEntity.setData(new JICVoucherSendRespEntity.JICVoucherSendEntityData());
        }else{
            jicVoucherSendRespEntity = iVoucherService.sendVoucher(jicVoucherSendReqEntity);
        }
        if (jicVoucherSendRespEntity != null && jicVoucherSendRespEntity.getCode() == 200 && ObjectUtils.isNotEmpty(jicVoucherSendRespEntity.getData())) {
            String voucher = jicVoucherSendRespEntity.getData().getR_voucher();
            if (StringUtils.isBlank(voucher) || voucher.equals("[]")) {
                if("会员不存在".equals(jicVoucherSendRespEntity.getData().getRetmsg())){
                    return "";
                }
                log.error("发放复购计划优惠券  发券失败 msg = {}, e = {}", jicVoucherSendRespEntity.getData().getRetmsg(), jicVoucherSendRespEntity);
                // 补偿发放3次
                reSendNumber++;
                if(reSendNumber < 3){
                    sendCoupon(awardIds,openId,numbers,referOrder,reSendNumber,acRepurchasePlanDetailId,unionid,weid,storeCode);
                }
                return "";
            }

            List<Map> maps = JSONObject.parseArray(voucher, Map.class);
            StringBuffer voucherCode = new StringBuffer();

            for(int i =0 ; i< maps.size(); i++){
                if(i == maps.size() - 1){
                    voucherCode.append(maps.get(i).get("code").toString());
                }else{
                    voucherCode.append(maps.get(i).get("code").toString());
                    voucherCode.append(",");
                }
            }
            String[] split2 = voucherCode.toString().split(",");
            voucherNosResult.addAll(Arrays.asList(split2));
        }

        String collect = voucherNosResult.stream().collect(Collectors.joining(","));
        if(CollectionUtils.isNotEmpty(voucherNosResult)){
            // 如果默认第一次门店为null 则不处理
            // 将券的适用门店更改  c_vouchers_store    表
            // 1. 通过voucherNos 查询c_vouchers 获取id
            // 2. 通过c_vouhcers_id 查询 c_vouchers_store  获取id
            // 3. 如果为空数据，那么直接新增， 否则  通过id 更新ISACTIVE 为 N
            // 4. 新增数据 结束   需要拿到c_store 的id
            if(!StringUtils.isBlank(storeCode)){
                // 查询数据
                for (String voucherNo : voucherNosResult) {
                    Long cVoucherId = cVouchersMapper.selectByVouvherNo(voucherNo);
                    List<Long> cVouhcerStoreIds = cVoucherStoreMapper.selectByCVouhcerId(cVoucherId);
                    if(CollectionUtils.isNotEmpty(cVouhcerStoreIds)){
                        // 更新数据 批量更信
                        List<List<Long>> partition = Lists.partition(cVouhcerStoreIds, 500);
                        for (List<Long> longs : partition) {
                            cVoucherStoreMapper.updateIsActiveByIds(longs);
                        }
                    }
                    // 新增一个数据
                    List<String> storeCodes = new ArrayList<>();
                    storeCodes.add(storeCode);
                    List<CStore> cStores = cStoreMapper.selectListByCodes(storeCodes);
                    if(CollectionUtils.isNotEmpty(cStores)){
                        Long id = cStores.get(0).getId();
                        cVoucherStoreMapper.insertSelective(id,cVoucherId);
                    }
                }
            }
            return collect;
        }
        return "";
    }


    /**
     * @param acActivityConfigDetails 活动详情id
     * @param acActivityConfig
     * @param unionid
     * @param weid
     * @param payType                 支付类型  0 积分 1 组合
     * @param wxopenid
     * @param miniopenid
     * @param exchangePoint           积分支付时候付的积分
     * @param payPoint                组合支付付的积分
     * @param payAmount               组合支付付的金额
     * @param requestData
     */
    private AcActivityOrder createOrder(AcActivityConfigDetails acActivityConfigDetails,
                                        AcActivityConfig acActivityConfig, String unionid,
                                        String weid, Integer payType, String wxopenid,
                                        String miniopenid, BigDecimal exchangePoint,
                                        BigDecimal payPoint, BigDecimal payAmount, String tradeNo, PreSubmitOrderReq requestData) {

        MemberCardQueryContext req = new MemberCardQueryContext();
        req.setUnionId(unionid);
        req.setBrandId(weid);
        CustomerBaseResponse<MemberCardEntity> memberCard = iUserVipService.getMemberCard(req);
        String cardNo = "";
        if(memberCard != null && memberCard.isSuccess() && memberCard.getData() != null){
            cardNo = memberCard.getData().getCardNo();
        }



        //创建一个订单
        AcActivityOrder acActivityOrder = new AcActivityOrder();
        acActivityOrder.setId("AO_"+ DateUtils.formatDate(new Date(),"yyyyMMdd") + "00000"+ IdLeaf.getId(IdConstant.AC_ACTIVITY_ORDER));
        acActivityOrder.setCreateTime(new Date());
        acActivityOrder.setUpdateTime(new Date());
        acActivityOrder.setIsDel(IsDeleteEnum.NORMAL.getCode());
        acActivityOrder.setMiniopenid(miniopenid);
        acActivityOrder.setWxopenid(wxopenid);
        acActivityOrder.setPayType(payType);
        if(payType.equals(0)){
            acActivityOrder.setPayPoint(exchangePoint);
        }else{
            acActivityOrder.setPayPoint(payPoint);
            acActivityOrder.setPayAmount(payAmount);
        }
        acActivityOrder.setActivityConfDetailId(acActivityConfigDetails.getId());
        acActivityOrder.setActivityConfigId(acActivityConfigDetails.getAcActivityConfigId());
        acActivityOrder.setStatus(0);
        acActivityOrder.setTradeNo(tradeNo);
        acActivityOrder.setCardNo(cardNo);
        acActivityOrder.setUnionid(unionid);
        acActivityOrder.setWeid(weid);
        // 生成订阅时间  以分段时间处理
        String useRule = acActivityConfigDetails.getUseRule();
        List<RepurchaseUseRuleCastDto> repurchaseUseRuleCastDtos   = formatUseRule(useRule, acActivityConfigDetails.getJoinTimes(), acActivityConfigDetails.getCycleType());
        FormatTimeCycle formatTimeCycle = getFormatTimeCycle(repurchaseUseRuleCastDtos);
        acActivityOrder.setSubStartTime(formatTimeCycle.getStartCycle());
        acActivityOrder.setSubEndTime(formatTimeCycle.getEndCycle());

        acActivityOrder.setBackImg(acActivityConfigDetails.getBackImg());
        acActivityOrder.setRuleImg(acActivityConfig.getActivityRuleImg());
        acActivityOrder.setBrandTitle(acActivityConfigDetails.getBrandTitle());
        acActivityOrder.setActivityIcon(acActivityConfigDetails.getActivityIcon());
        // 仅有库存的才存储门店  没库存的也需要储存门店编码
        acActivityOrder.setStoreCode(requestData.getStoreCode());



        // 生成子单
        template.execute(action->{
            //回退库存
            changeStoreQty(acActivityConfigDetails.getId(),requestData.getStoreCode(),-1);
            acActivityOrderMapper.insert(acActivityOrder);
            return action;
        });
        return acActivityOrder;
    }

    

    private int addPoint(String unionid, BigDecimal point, String weid) {
        // 查询当前用户的vipId
        MemberCardQueryContext req = new MemberCardQueryContext();
        req.setUnionId(unionid);
        req.setBrandId(weid);
        CustomerBaseResponse<MemberCardEntity> memberCard = iUserVipService.getMemberCard(req);
        if(memberCard != null && memberCard.getData() != null){
            Long vipId = memberCard.getData().getId();
            // 准备扣减积分
            SubtractUserIntegralContext subtractUserIntegralContext = new SubtractUserIntegralContext();
            //iUserIntegralHttpApi.subtractUserIntegral().execute();

        }

        return 0;
    }

    private Map<String, Integer> queryVoucherByVoucherNos(List<AcRepurchasePlanDetail> list) {

        // 返回的是券号和
        Map<String,Integer> result  = new HashMap<>();

        // 根据券号查询信息
        List<String> voucherNos = new ArrayList<>();
        for (AcRepurchasePlanDetail acRepurchasePlanDetail : list) {
            boolean notBlank = StringUtils.isNotBlank(acRepurchasePlanDetail.getVoucherNos());
            if(notBlank){
                String[] split = acRepurchasePlanDetail.getVoucherNos().split(",");
                voucherNos.addAll(Arrays.asList(split));
            }
        }
        String vouchers = voucherNos.stream().collect(Collectors.joining(","));
        if(StringUtils.isBlank(vouchers)){
            log.error("queryVoucherByVoucherNos = {}",vouchers);
            return  new HashMap<>();
        }
        VoucherRuleDetailsContext voucherRuleDetailsContext = new VoucherRuleDetailsContext();
        voucherRuleDetailsContext.setVoucherNos(vouchers);
        // 券信息
        List<VoucherRuleDetailsEntity> voucherByVoucherNos = iVoucherService.getVoucherByVoucherNo(voucherRuleDetailsContext);
        // status 默认是Y  但是要判断   然后判断是否核销   然后判断是否过期
        for (VoucherRuleDetailsEntity voucherByVoucherNo : voucherByVoucherNos) {
            if("VOU5".equals(voucherByVoucherNo.getVoutype())){
                // 默认兑换券
                result.put(voucherByVoucherNo.getCode(),2);
                result.put(voucherByVoucherNo.getCode() + "isExpire",0);
            }else{
                // 判断是否快过期
                if(Integer.parseInt(voucherByVoucherNo.getValiddate()) - Integer.parseInt(DateUtils.formatDate(new Date(), "yyyyMMdd")) < 7
                        && Integer.parseInt(voucherByVoucherNo.getValiddate()) - Integer.parseInt(DateUtils.formatDate(new Date(), "yyyyMMdd")) >= 0){
                    result.put(voucherByVoucherNo.getCode() + "isExpire",1);
                }else{
                    result.put(voucherByVoucherNo.getCode() + "isExpire",0);
                }

                String isvalid = voucherByVoucherNo.getIsvalid();
                boolean b = Integer.parseInt(voucherByVoucherNo.getValiddate()) >= Integer.parseInt(DateUtils.formatDate(new Date(), "yyyyMMdd"));
                // 可用券
                if(isvalid.equals("Y") && b && voucherByVoucherNo.getIsverifyed().equals("N")){
                    result.put(voucherByVoucherNo.getCode(),1);
                }else if(voucherByVoucherNo.getIsverifyed().equals("Y")){
                    // 已用
                    result.put(voucherByVoucherNo.getCode(),3);
                }else if(!b){
                    // 过期
                    result.put(voucherByVoucherNo.getCode(),2);
                }
            }
        }
        return result;
    }




//    private void sendWxOpenMsgOrSmsMsg(){
//        // 获取当前时间  ， 获取符合规则条件的
//        // 查询 主表是已经支付的， 并且当前时间在周期内的， 并且 子单在周期内的， 并且是没有发过消息的
//        List<String> acRepurchaseIds = acRepurchasePlanDetailMapper.selectShouldSendMsgAcRepurchaseDetailId(new Date(),
//                Long.parseLong(DateUtils.formatDate(new Date(),"yyyyMMdd")),null);
//
//        for (String acRepurchaseId : acRepurchaseIds) {
//            try {
//                sendOpenMsgOrSms(acRepurchaseId);
//            }catch (Exception e){
//                log.info("发送短信失败 acRepurchaseId = {}",acRepurchaseId);
//            }
//        }
//    }

    private void sendOpenMsgOrSms(String acRepurchaseId) {
        List<String> acRepurchaseIds = acRepurchasePlanDetailMapper.selectShouldSendMsgAcRepurchaseDetailId(new Date(),
                Long.parseLong(DateUtils.formatDate(new Date(),"yyyyMMdd")),acRepurchaseId);
        if(CollectionUtils.isEmpty(acRepurchaseIds)){
            log.info("该用户信息不满足  该用户未设置提醒  or 已经发送过  acRepurchasePlanId = {}",acRepurchaseId);
            return ;
        }
        // 更新为已经发送

        // 查询基本数据
        AcRepurchasePlanDetail acRepurchasePlanDetail = acRepurchasePlanDetailMapper.selectById(acRepurchaseId);
        AcActivityOrder acActivityOrder = acActivityOrderMapper.selectById(acRepurchasePlanDetail.getActivityOrderId());
        // 查询当前是否关注了公众号  如果关注了公众号
        List<WxFans> wxFans = voucherVirtualReferConfigMapper.selectJicWxfansByUniondIdAndWeId(acActivityOrder.getUnionid(), acActivityOrder.getWeid());
        if(CollectionUtils.isNotEmpty(wxFans)){
            if(StringUtils.isBlank(wxFans.get(0).getOpenid())){
                log.info("查询到用户unionid weid 的openid为空  unionid = {} , weid = {} " , acActivityOrder.getUnionid(),acActivityOrder.getWeid());
                return ;
            }
            // 更新为发送公众号
            AcRepurchasePlanDetail updateAc = new AcRepurchasePlanDetail();
            updateAc.setUpdateTime(new Date());
            updateAc.setId(acRepurchasePlanDetail.getId());
            updateAc.setSendMsgFlag(1);
            acRepurchasePlanDetailMapper.updateById(updateAc);

            // 发送公众号
            // 发送公众号消息  公众号appid
            String appid = null;
            String miniappid = null;
            String brandName = "";
            String offTemplateId = "";
            String jumpUrl =  "";
            // 根据品牌映射成appid
            List<Map> brandConfigs = JSONObject.parseArray(commonConfigs, Map.class);
            for (Map brandConfig : brandConfigs) {
                if(brandConfig.get("weid").toString().equals(acActivityOrder.getWeid())){
                    // 公众号appid
                    appid = brandConfig.get("offAppId").toString();
                    miniappid = brandConfig.get("appid").toString();
                    offTemplateId  =  brandConfig.get("offRepurchaseTemplateId").toString();
                    jumpUrl = brandConfig.get("offRepurchaseTemplateUrl").toString();
                }
            }
            if(appid == null){
                log.info("未能匹配到正确的weid = {}",acActivityOrder.getWeid());
                return ;
            }

            List<SendWxOffMsgContext> sendWxOffMsgContexts = new ArrayList<>();
            Map<String,String> params = new HashMap<>();
            params.put("appId",miniappid);
            params.put("path",jumpUrl);
            params.put("keyword1",wxFans.get(0).getNickname());
            params.put("keyword2","您购买的权益券已到账，请前往卡券包进行查收~");

            SendWxOffMsgContext sendWxOffMsgContext = new SendWxOffMsgContext();
            SendWxOffMsgContext.WxTemplateMsgSendDTO build = SendWxOffMsgContext.build(wxFans.get(0).getOpenid(), offTemplateId, params);
            sendWxOffMsgContext.setWxTemplateMsgSendDTO(build);
            sendWxOffMsgContext.setAppid(appid);
            sendWxOffMsgContexts.add(sendWxOffMsgContext);
            messageService.sendWxOffMsg(sendWxOffMsgContexts);

        }else{
            //更新发送短信
            AcRepurchasePlanDetail updateAc = new AcRepurchasePlanDetail();
            updateAc.setUpdateTime(new Date());
            updateAc.setId(acRepurchasePlanDetail.getId());
            updateAc.setSendMsgFlag(2);
            acRepurchasePlanDetailMapper.updateById(updateAc);

            // 发送短信  开始发送短信
            MemberCardQueryContext req = new MemberCardQueryContext();
            req.setUnionId(acActivityOrder.getUnionid());
            req.setBrandId(acActivityOrder.getWeid());
            CustomerBaseResponse<MemberCardEntity> memberCard = iUserVipService.getMemberCard(req);
            if(memberCard != null && memberCard.isSuccess() && memberCard.getData() != null){
                // 判断手机号不为空
                if(StringUtils.isNotBlank(memberCard.getData().getTel())){
                    SmsSendContext sendContext = new SmsSendContext();
                    sendContext.setTel(memberCard.getData().getTel());
                    sendContext.setContent(sendSmsTemplate);
                    messageService.sendSms(sendContext);
                }else{
                    log.info("用户手机号为空 不发送 unionid = {}  weid= {}",acActivityOrder.getUnionid(),acActivityOrder.getWeid());
                }
            }
        }
    }
}
