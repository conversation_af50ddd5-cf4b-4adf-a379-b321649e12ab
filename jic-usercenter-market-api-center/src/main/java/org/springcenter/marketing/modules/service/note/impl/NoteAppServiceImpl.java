package org.springcenter.marketing.modules.service.note.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.jnby.common.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springcenter.marketing.api.dto.CustomerBaseResponse;
import org.springcenter.marketing.api.dto.miniapp.NoteBehaviorReq;
import org.springcenter.marketing.api.dto.miniapp.NoteListItemResp;
import org.springcenter.marketing.api.dto.miniapp.NoteListQueryReq;
import org.springcenter.marketing.modules.context.MemberListQueryContext;
import org.springcenter.marketing.modules.context.MemberNameAndHeadImgEntity;
import org.springcenter.marketing.modules.convert.NoteConvert;
import org.springcenter.marketing.modules.mapper.NoteBehaviorMapper;
import org.springcenter.marketing.modules.mapper.NoteMapper;
import org.springcenter.marketing.modules.mapper.ThemeActivityJoinRecordMapper;
import org.springcenter.marketing.modules.model.Note;
import org.springcenter.marketing.modules.service.IUserVipService;
import org.springcenter.marketing.modules.service.note.INoteAppService;
import org.springcenter.marketing.modules.util.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class NoteAppServiceImpl implements INoteAppService {

    @Autowired
    private NoteMapper noteMapper;
    @Autowired
    private NoteBehaviorMapper noteBehaviorMapper;
    @Autowired
    private ThemeActivityJoinRecordMapper themeActivityJoinRecordMapper;
    @Autowired
    private IUserVipService iUserVipService;
    @Autowired
    private NoteConvert noteConvert;
    @Autowired
    @Qualifier("marketingTransactionTemplate")
    private TransactionTemplate marketingTransactionTemplate;

    @Override
    public List<NoteListItemResp> listNotes(NoteListQueryReq req, Page page) {
        // 构建查询
        com.github.pagehelper.Page<Note> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        LambdaQueryWrapper<Note> qw = new LambdaQueryWrapper<>();
        qw.eq(Note::getIsDelete, 0)
                .eq(Note::getDraft, 0)
                .eq(Note::getOutId, req.getActivityId());
        Integer sort = Optional.ofNullable(req.getSort()).orElse(1);
        if (sort == 2) {
            qw.orderByDesc(Note::getLikeCount);
        } else {
            qw.orderByDesc(Note::getPublishTime);
        }
        List<Note> notes = noteMapper.selectList(qw);
        page.setCount(hPage.getTotal());
        page.setPages(hPage.getPages());
        if (CollectionUtils.isEmpty(notes)) {
            return new ArrayList<>();
        }

        // 准备会员头像昵称
        Map<String, MemberNameAndHeadImgEntity> memberMap = Collections.emptyMap();
        if (!notes.isEmpty()) {
            List<String> cardNos = notes.stream().map(Note::getCardNo).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
            if (!cardNos.isEmpty()) {
                MemberListQueryContext ctx = new MemberListQueryContext().buildCardNoList(cardNos);
                CustomerBaseResponse<List<MemberNameAndHeadImgEntity>> resp = iUserVipService.listMemberCardNameAndHeadImg(ctx);
                if (resp != null && resp.getData() != null) {
                    memberMap = resp.getData().stream().filter(Objects::nonNull).collect(Collectors.toMap(MemberNameAndHeadImgEntity::getCardNo, e -> e, (a, b) -> a));
                }
            }
        }
        final Map<String, MemberNameAndHeadImgEntity> memberInfoMap = memberMap;

        // 转换
        List<NoteListItemResp> list = notes.stream().map(n -> {
            NoteListItemResp item = noteConvert.toListItem(n);
            item.setContent(n.getContent());
            item.setPublishTime(n.getPublishTime() == null ? null : DateUtils.formatDate(n.getPublishTime(), "yyyy.MM.dd HH:mm"));
            item.setLikeCount(n.getLikeCount());
            item.setCommentCount(n.getCommentCount());
            item.setCardNo(n.getCardNo());
            // 是否当前人
            item.setIsCurrentUser(StringUtils.isNotBlank(req.getCardNo()) && req.getCardNo().equals(n.getCardNo()));
            // 头像昵称
            MemberNameAndHeadImgEntity info = memberInfoMap.get(n.getCardNo());
            if (info != null) {
                item.setUserName(info.getUserName());
                item.setHeadImgUrl(info.getHeadImgUrl());
            }
            return item;
        }).collect(Collectors.toList());

        return list;
    }
//
//    @Override
//    public Integer getJoinDays(NoteJoinDaysReq req) {
//        QueryWrapper<ThemeActivityJoinRecord> qw = new QueryWrapper<>();
//        qw.eq("is_delete", IsDeleteEnum.NORMAL.getCode());
//        if (req.getActivityId() != null) {
//            qw.eq("activity_id", req.getActivityId());
//        }
//        if (StringUtils.isNotBlank(req.getCardNo())) {
//            qw.eq("card_no", req.getCardNo());
//        }
//        // 计算打卡成功天数：按 join_date 的去重天数
//        List<ThemeActivityJoinRecord> list = themeActivityJoinRecordMapper.selectList(qw);
//        if (list == null || list.isEmpty()) {
//            return 0;
//        }
//        java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy-MM-dd");
//        return (int) list.stream().map(r -> r.getJoinDate() == null ? "" : sdf.format(r.getJoinDate())).filter(StringUtils::isNotBlank).distinct().count();
//    }
//
//    @Override
//    public Integer getMyDraftCount(NoteDraftCountReq req) {
//        QueryWrapper<Note> qw = new QueryWrapper<>();
//        qw.eq("is_delete", IsDeleteEnum.NORMAL.getCode());
//        if (req.getActivityId() != null) {
//            qw.eq("out_id", String.valueOf(req.getActivityId()));
//        }
//        if (StringUtils.isNotBlank(req.getCardNo())) {
//            qw.eq("card_no", req.getCardNo());
//        }
//        qw.eq("draft", 1);
//        return noteMapper.selectCount(qw);
//    }

    @Override
    public Boolean likeOrCancel(NoteBehaviorReq req) {
//        return marketingTransactionTemplate.execute(status -> {
//            Date now = new Date();
//            // 写行为表
//            NoteBehavior behavior = new NoteBehavior();
//            behavior.setAppKey(null);
//            behavior.setOutId(String.valueOf(req.getNoteId()));
//            behavior.setType(2);
//            behavior.setCancel(req.getOpt());
//            behavior.setCardNo(req.getCardNo());
//            behavior.setIsDelete(IsDeleteEnum.NORMAL.getCode());
//            behavior.setCreateTime(now);
//            behavior.setUpdateTime(now);
//            noteBehaviorMapper.insert(behavior);
//
//            // 更新计数
//            Note update = new Note();
//            update.setId(req.getNoteId());
//            // 简化处理：取消则-1，否则+1（防止负数）
//            Note db = noteMapper.selectById(req.getNoteId());
//            long like = Optional.ofNullable(db != null ? db.getLikeCount() : 0L).orElse(0L);
//            if (Boolean.TRUE.equals(req.getCancel())) {
//                like = Math.max(0, (int) (like - 1));
//            } else {
//                like = like + 1;
//            }
//            update.setLikeCount(like);
//            update.setUpdateTime(now);
//            noteMapper.updateById(update);
//            return true;
//        });
        return true;
    }

}

