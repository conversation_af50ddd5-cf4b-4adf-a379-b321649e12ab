package org.springcenter.marketing.modules.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Objects;
import com.google.common.base.Preconditions;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Lists;
import com.google.common.collect.Multimap;
import com.jnby.common.CommonRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.joda.time.DateTime;
import org.springcenter.marketing.api.dto.miniapp.GetSourcePosInfoDto;
import org.springcenter.marketing.common.BirthdayTimeEnum;
import org.springcenter.marketing.common.PositionEnum;
import org.springcenter.marketing.common.ShowTimeTypeEnum;
import org.springcenter.marketing.convert.PosTemplateConvert;
import org.springcenter.marketing.modules.context.*;
import org.springcenter.marketing.modules.entity.PosTemplate;
import org.springcenter.marketing.modules.entity.SourcePosWithConfigEntity;
import org.springcenter.marketing.modules.model.BSourcePosManager;
import org.springcenter.marketing.modules.model.BSourcePosTemplate;
import org.springcenter.marketing.modules.service.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@RefreshScope
public class BSourcePosCombineServiceImpl implements IBSourcePosCombineService {

    @Resource
    private IBSourcePosManagerService ibSourcePosManagerService;

    @Resource
    private IUserVipService iUserVipService;

    @Resource
    private IBSourcePosTemplateService ibSourcePosTemplateService;

    @Resource
    private IWebService iWebService;

    @Value("${people.close.flag}")
    private Boolean peopleCloseFlag;

    @Value("${people.default.id}")
    private String peopleDefaultId;

    @Override
    public List<SourcePosWithConfigEntity> sourceByPersonalise(CommonRequest<GetSourcePosInfoDto> request) {
        log.info("资源详情入参request:{} appId:{}", JSONObject.toJSONString(request),request.getMerchantId());
        GetSourcePosInfoDto getSourcePosInfoDto = request.getRequestData();
        String appId = Preconditions.checkNotNull(request.getMerchantId(), "appId不能为空");
        Set<Long> positionSet = getSourcePosInfoDto.getPositionSet();

        Multimap<String, BSourcePosTemplate> sourcePosTemplateMultimap = ArrayListMultimap.create();
        List<BSourcePosTemplate> bSourcePosTemplates = packageSourcePos(getSourcePosInfoDto, appId);
        bSourcePosTemplates.stream().forEach(e -> sourcePosTemplateMultimap.put(e.getPosManagerId(), e));

        List<SourcePosWithConfigEntity> sourcePosWithConfigEntityList = new ArrayList<>();
        List<BSourcePosManager> sourcePosManagerList = packageSourceManage(appId,positionSet);
        sourcePosManagerList.stream().forEach(e -> {
            SourcePosWithConfigEntity sourcePosWithConfigEntity = new SourcePosWithConfigEntity();
            List<BSourcePosTemplate> bSourcePosTemplateListTemp = (List<BSourcePosTemplate>) sourcePosTemplateMultimap.get(e.getId());

            if (CollectionUtils.isNotEmpty(bSourcePosTemplateListTemp)) {
                // 搜索词 特殊处理
                if(PositionEnum.SEARCH_TXT.getCode().equals(e.getPosition())){
                    sourcePosWithConfigEntity.setBSourcePosTemplates(buildPostTemplate(bSourcePosTemplateListTemp.stream()
                            .sorted(Comparator.comparing(BSourcePosTemplate::getSortNo, Comparator.naturalOrder()).thenComparing(BSourcePosTemplate::getUpdateTime,Comparator.reverseOrder()))
                            .collect(Collectors.toList())));
                }else{
                    sourcePosWithConfigEntity.setBSourcePosTemplates(buildPostTemplate(bSourcePosTemplateListTemp.stream()
                            .sorted(Comparator.comparing(BSourcePosTemplate::getSortNo, Comparator.reverseOrder()).thenComparing(BSourcePosTemplate::getCreateTime,Comparator.reverseOrder()))
                            .collect(Collectors.toList())));
                }

                sourcePosWithConfigEntity.setBSourcePosManager(PosTemplateConvert.INSTANCE.toPosManager(e));
                sourcePosWithConfigEntityList.add(sourcePosWithConfigEntity);
            }
        });
        return sourcePosWithConfigEntityList;
    }



    List<BSourcePosTemplate> packageSourcePos(GetSourcePosInfoDto getSourcePosInfoDto, String appId) {
        log.info("packageSourcePos start");
        String officialOpenId = getSourcePosInfoDto.getOfficialOpenId();
        String birthdayTimeStr = getSourcePosInfoDto.getBirthdayTime();
        String unionId = getSourcePosInfoDto.getUnionId();
        Set<Long> positionSet = getSourcePosInfoDto.getPositionSet();


        Date nowDate = new Date();
        List<BSourcePosTemplate> bSourcePosTemplates = ibSourcePosTemplateService.getSourceListByAppId(appId).stream()
                .filter(e -> e.getStartTime().before(nowDate) && e.getEndTime().after(nowDate)).collect(Collectors.toList());


        // 处理全部数据
        List<BSourcePosTemplate> allCommonBSourcePosTemplates = ibSourcePosTemplateService.getSourceListByAppId("0").stream()
                .filter(e -> e.getStartTime().before(nowDate) && e.getEndTime().after(nowDate)).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(allCommonBSourcePosTemplates)){
            bSourcePosTemplates.addAll(allCommonBSourcePosTemplates);
        }

        if (CollectionUtils.isNotEmpty(positionSet) && CollectionUtils.isNotEmpty(bSourcePosTemplates)) {
            bSourcePosTemplates = bSourcePosTemplates.stream().filter(e -> positionSet.contains(e.getPosition())).collect(Collectors.toList());
        }

        // 单个位置 搜索词
        if (CollectionUtils.isNotEmpty(positionSet) && positionSet.contains(PositionEnum.SEARCH_TXT.getCode())) {
            if (StringUtils.isNotEmpty(getSourcePosInfoDto.getSearchTxt())) {
                // 判断搜索词
                bSourcePosTemplates = bSourcePosTemplates.stream().filter(x -> {
                    if (PositionEnum.SEARCH_TXT.getCode().equals(x.getPosition())) {
                        try {
                            return Arrays.stream(x.getSearchTxt().split(",")).anyMatch(e -> getSourcePosInfoDto.getSearchTxt().contains(e));
                        } catch (Exception e) {
                            log.error("搜索词转换异常 sourPosTempId:{} e:{}", x.getId(), e);
                            return false;
                        }
                    } else {
                        return true;
                    }
                }).collect(Collectors.toList());
            } else {
                // 传入值 搜索词为空
                bSourcePosTemplates = bSourcePosTemplates.stream().filter(x -> {
                    return !PositionEnum.SEARCH_TXT.getCode().equals(x.getPosition());
                }).collect(Collectors.toList());
            }
        }

        Set<String> peoplePackageSet = new HashSet<>();
        if (StringUtils.isNotEmpty(unionId) && bSourcePosTemplates.stream().anyMatch(e -> StringUtils.isNotEmpty(e.getAdaptPeopleId()))) {
            List<String> realAdaptPeopleIds = getRealPeopleIdsByUnionId(bSourcePosTemplates, unionId);
            if (CollectionUtils.isNotEmpty(realAdaptPeopleIds)) {
                peoplePackageSet.addAll(realAdaptPeopleIds);
            }
        }
        // 开关开启 默认人群包id  unionId为空的情况
        if(peopleCloseFlag){
            log.info("开关打开，peopleDefaultId:{}",peopleDefaultId);
            peoplePackageSet.add(peopleDefaultId);
        }


        Date birthdayTime = null;
        Date beforeBirthdayTime = null;
        Date nowTime = new Date();

        if (StringUtils.isNotEmpty(birthdayTimeStr)) {
            try{
                birthdayTime = DateUtils.parseDate(birthdayTimeStr,  "yyyyMMdd" );
                beforeBirthdayTime = DateUtils.addMonths(birthdayTime,-1);
            }catch (Exception e){
                log.error("用户wid:{} 生日有误birthdayTime:{} message:{}",getSourcePosInfoDto.getWid(),birthdayTime,e.getMessage(),e);
                // throw new RuntimeException("用户生日数据有误");
            }
        }


        List<BSourcePosTemplate> bSourcePosTemplatesResult = new ArrayList<>();
        for (BSourcePosTemplate bSourcePosTemplate : bSourcePosTemplates) {
            // 用户无卡或用户卡无生日 过滤生日类型
            if (ShowTimeTypeEnum.BIRTHDAY.getCode().equals(bSourcePosTemplate.getShowTimeType()) && birthdayTime == null) {
                continue;
            }
            // 生日类型 不满足 生日条件
            if (ShowTimeTypeEnum.BIRTHDAY.getCode().equals(bSourcePosTemplate.getShowTimeType()) && birthdayTime != null) {
                // 0生日前一个月至生日月结束 1仅生日当月
                if (BirthdayTimeEnum.NOW_MONTH.getCode().equals(bSourcePosTemplate.getBirthdayTime()) && !isNowMonth(nowTime, birthdayTime)) {
                    continue;
                }
                if (BirthdayTimeEnum.BEFORE_TO_NOW_MONTH.getCode().equals(bSourcePosTemplate.getBirthdayTime()) && !isNowOrBeforeMonth(nowTime, birthdayTime, beforeBirthdayTime)) {
                    continue;
                }
            }

            if (PositionEnum.HEAD_BACKGROUND.getCode().equals(bSourcePosTemplate.getPosition())) {
                bSourcePosTemplatesResult.add(bSourcePosTemplate);
                continue;
            }
            if (StringUtils.isEmpty(bSourcePosTemplate.getAdaptPeopleId())) {
                bSourcePosTemplatesResult.add(bSourcePosTemplate);
                continue;
            }

            // 人群包 满足
            if (peoplePackageSet.contains(bSourcePosTemplate.getAdaptPeopleId())) {
                bSourcePosTemplatesResult.add(bSourcePosTemplate);
                continue;
            }

        }
        log.info("packageSourcePos end");
        return bSourcePosTemplatesResult;
    }


    List<BSourcePosManager> packageSourceManage(String appId, Set<Long> positionSet) {
        log.info("packageSourceManage start");
        // 当前小程序
        List<BSourcePosManager> sourcePosManagerList = ibSourcePosManagerService.posManagerList(appId);
        // 全部
        List<BSourcePosManager> allCommonSourcePosManagerList = ibSourcePosManagerService.posManagerList("0");
        if (CollectionUtils.isNotEmpty(allCommonSourcePosManagerList)) {
            sourcePosManagerList.addAll(allCommonSourcePosManagerList);
        }
        if (CollectionUtils.isNotEmpty(positionSet) && CollectionUtils.isNotEmpty(sourcePosManagerList)) {
            sourcePosManagerList = sourcePosManagerList.stream().filter(e -> positionSet.contains(e.getPosition())).collect(Collectors.toList());
        }
        log.info("packageSourceManage end");
        return sourcePosManagerList;
    }




    /**
     * 是否是当月
     *
     * @param nowTime
     * @param birthdayTime
     * @return
     */
    public Boolean isNowMonth(Date nowTime, Date birthdayTime) {
        DateTime nowTimeJoda = new DateTime(nowTime);
        DateTime birthdayTimeJoda = new DateTime(birthdayTime);
        //  log.info("现在月：{} 生日月：{}", nowTimeJoda.monthOfYear().getAsString() , birthdayTimeJoda.monthOfYear().getAsString());
        return  StringUtils.equals(nowTimeJoda.monthOfYear().getAsString(), birthdayTimeJoda.monthOfYear().getAsString());
    }


    /**
     * 是否是当月和上个月
     *
     * @param nowTime
     * @param birthdayTime
     * @param beforeBirthdayTime
     * @return
     */
    public Boolean isNowOrBeforeMonth(Date nowTime, Date birthdayTime, Date beforeBirthdayTime) {
        DateTime nowTimeJoda = new DateTime(nowTime);
        DateTime birthdayTimeJoda = new DateTime(birthdayTime);
        DateTime beforeBirthdayTimeJoda = new DateTime(beforeBirthdayTime);
//        log.info("现在月：{}  生日月：{} 生日前月:{}",
//                nowTimeJoda.monthOfYear().getAsString() ,
//                birthdayTimeJoda.monthOfYear().getAsString(),
//                beforeBirthdayTimeJoda.monthOfYear().getAsString());



        Boolean flag = StringUtils.equals(nowTimeJoda.monthOfYear().getAsString(), birthdayTimeJoda.monthOfYear().getAsString()) ||
                StringUtils.equals(nowTimeJoda.monthOfYear().getAsString(), beforeBirthdayTimeJoda.monthOfYear().getAsString());


        return flag;
    }


    /**
     * 取符合条件的 人群包

     * @param bSourcePosTemplates
     * @param officialOpenId
     * @return
     */
    List<String> getRealPeopleIds(List<BSourcePosTemplate> bSourcePosTemplates, String officialOpenId) {
        log.info("获取取符合条件的人群包");
        List<Long> adaptPeopleIds = bSourcePosTemplates.stream().filter(e -> StringUtils.isNotEmpty(e.getAdaptPeopleId()))
                .map(s -> Long.parseLong(s.getAdaptPeopleId())).collect(Collectors.toList());
        return iWebService.getNewPeopleInfo(NewPeoplePagContext.build(officialOpenId, adaptPeopleIds)).stream()
                .filter(e -> e.getHit()).map(s -> String.valueOf(s.getCrowdId())).collect(Collectors.toList());
    }


    /**
     * 通过unionId 获取人群包
     * @param bSourcePosTemplates
     * @param unionId
     * @return
     */
    List<String> getRealPeopleIdsByUnionId(List<BSourcePosTemplate> bSourcePosTemplates, String unionId) {
        log.info("获取取符合条件的人群包");
        List<Long> adaptPeopleIds = bSourcePosTemplates.stream().filter(e -> StringUtils.isNotEmpty(e.getAdaptPeopleId()))
                .map(s -> Long.parseLong(s.getAdaptPeopleId())).collect(Collectors.toList());

        if(peopleCloseFlag){
            log.info("开关打开，不请求人群包,peopleDefaultId:{}",peopleDefaultId);
            List<String> stringList = Lists.newArrayList();
            stringList.add(peopleDefaultId);
            return stringList;
        }

        NewPeoplePagContext newPeoplePagContext = new NewPeoplePagContext();
        newPeoplePagContext.setUnionId(unionId);
        newPeoplePagContext.setCrowdIds(adaptPeopleIds);
        return iWebService.getNewPeopleInfo(newPeoplePagContext).stream()
                .filter(e -> e.getHit()).map(s -> String.valueOf(s.getCrowdId())).collect(Collectors.toList());
    }


    List<PosTemplate> buildPostTemplate(List<BSourcePosTemplate> bSourcePosTemplates) {
        List<PosTemplate> posTemplates = new ArrayList<>();
        if (CollectionUtils.isEmpty(bSourcePosTemplates)) {
            return posTemplates;
        }

        for (int i = 0; i < bSourcePosTemplates.size(); i++) {
            posTemplates.add(PosTemplateConvert.INSTANCE.toPosTemplate(bSourcePosTemplates.get(i)));
        }
        return posTemplates;
    }

    //个人中心全局开关
    @Override
    public Boolean getPeopleCloseFlag(){
        return peopleCloseFlag;
    }

}
