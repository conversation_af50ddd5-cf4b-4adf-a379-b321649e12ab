<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.marketing.modules.bojunMapper.CVouchersMapper">

    <resultMap id="BaseResultMap" type="org.springcenter.marketing.modules.model.CVouchers">
            <id property="id" column="ID" jdbcType="DECIMAL"/>
            <result property="adClientId" column="AD_CLIENT_ID" jdbcType="DECIMAL"/>
            <result property="adOrgId" column="AD_ORG_ID" jdbcType="DECIMAL"/>
            <result property="vouchersNo" column="VOUCHERS_NO" jdbcType="VARCHAR"/>
            <result property="accountLimit" column="ACCOUNT_LIMIT" jdbcType="DECIMAL"/>
            <result property="isAllstore" column="IS_ALLSTORE" jdbcType="CHAR"/>
            <result property="isListLimit" column="IS_LIST_LIMIT" jdbcType="CHAR"/>
            <result property="isAccpayAfterdis" column="IS_ACCPAY_AFTERDIS" jdbcType="CHAR"/>
            <result property="isDelaccount" column="IS_DELACCOUNT" jdbcType="CHAR"/>
            <result property="isOnetime" column="IS_ONETIME" jdbcType="CHAR"/>
            <result property="vouDis" column="VOU_DIS" jdbcType="DECIMAL"/>
            <result property="isVerifyed" column="IS_VERIFYED" jdbcType="CHAR"/>
            <result property="amtAcount" column="AMT_ACOUNT" jdbcType="DECIMAL"/>
            <result property="vouCycle" column="VOU_CYCLE" jdbcType="DECIMAL"/>
            <result property="validDate" column="VALID_DATE" jdbcType="DECIMAL"/>
            <result property="vouType" column="VOU_TYPE" jdbcType="VARCHAR"/>
            <result property="isValid" column="IS_VALID" jdbcType="CHAR"/>
            <result property="ownerid" column="OWNERID" jdbcType="DECIMAL"/>
            <result property="modifierid" column="MODIFIERID" jdbcType="DECIMAL"/>
            <result property="creationdate" column="CREATIONDATE" jdbcType="TIMESTAMP"/>
            <result property="modifieddate" column="MODIFIEDDATE" jdbcType="TIMESTAMP"/>
            <result property="isactive" column="ISACTIVE" jdbcType="CHAR"/>
            <result property="accountLimitDue" column="ACCOUNT_LIMIT_DUE" jdbcType="DECIMAL"/>
            <result property="qtyLimit" column="QTY_LIMIT" jdbcType="DECIMAL"/>
            <result property="hrEmployeeId" column="HR_EMPLOYEE_ID" jdbcType="DECIMAL"/>
            <result property="verifyedStoreId" column="VERIFYED_STORE_ID" jdbcType="DECIMAL"/>
            <result property="verifyedUserId" column="VERIFYED_USER_ID" jdbcType="DECIMAL"/>
            <result property="cCustomerId" column="C_CUSTOMER_ID" jdbcType="DECIMAL"/>
            <result property="cDepartmentId" column="C_DEPARTMENT_ID" jdbcType="DECIMAL"/>
            <result property="department" column="DEPARTMENT" jdbcType="VARCHAR"/>
            <result property="verifyedTime" column="VERIFYED_TIME" jdbcType="TIMESTAMP"/>
            <result property="isshareVouchers" column="ISSHARE_VOUCHERS" jdbcType="CHAR"/>
            <result property="issharePaytype" column="ISSHARE_PAYTYPE" jdbcType="CHAR"/>
            <result property="isshareDoutype" column="ISSHARE_DOUTYPE" jdbcType="CHAR"/>
            <result property="isshowDiscount" column="ISSHOW_DISCOUNT" jdbcType="CHAR"/>
            <result property="isshowFeelsale" column="ISSHOW_FEELSALE" jdbcType="CHAR"/>
            <result property="storename" column="STORENAME" jdbcType="VARCHAR"/>
            <result property="identifyCode" column="IDENTIFY_CODE" jdbcType="VARCHAR"/>
            <result property="empNo" column="EMP_NO" jdbcType="VARCHAR"/>
            <result property="headvc1" column="HEADVC1" jdbcType="VARCHAR"/>
            <result property="headvc2" column="HEADVC2" jdbcType="VARCHAR"/>
            <result property="headvc3" column="HEADVC3" jdbcType="VARCHAR"/>
            <result property="isExceptdis" column="IS_EXCEPTDIS" jdbcType="CHAR"/>
            <result property="headvc4" column="HEADVC4" jdbcType="DECIMAL"/>
            <result property="headvc5" column="HEADVC5" jdbcType="VARCHAR"/>
            <result property="headvc6" column="HEADVC6" jdbcType="VARCHAR"/>
            <result property="headvc7" column="HEADVC7" jdbcType="VARCHAR"/>
            <result property="headvc8" column="HEADVC8" jdbcType="VARCHAR"/>
            <result property="cVipId" column="C_VIP_ID" jdbcType="DECIMAL"/>
            <result property="amtDiscount" column="AMT_DISCOUNT" jdbcType="DECIMAL"/>
            <result property="isNomoreqty" column="IS_NOMOREQTY" jdbcType="CHAR"/>
            <result property="mDim1Id" column="M_DIM1_ID" jdbcType="DECIMAL"/>
            <result property="isEntire" column="IS_ENTIRE" jdbcType="CHAR"/>
            <result property="amtNobef" column="AMT_NOBEF" jdbcType="DECIMAL"/>
            <result property="amtNoles" column="AMT_NOLES" jdbcType="DECIMAL"/>
            <result property="disNoles" column="DIS_NOLES" jdbcType="DECIMAL"/>
            <result property="disNoless" column="DIS_NOLESS" jdbcType="DECIMAL"/>
            <result property="startdate" column="STARTDATE" jdbcType="DECIMAL"/>
            <result property="ruleid" column="RULEID" jdbcType="DECIMAL"/>
            <result property="isMdamt" column="IS_MDAMT" jdbcType="CHAR"/>
            <result property="amtLimit" column="AMT_LIMIT" jdbcType="DECIMAL"/>
            <result property="isUseamt" column="IS_USEAMT" jdbcType="CHAR"/>
            <result property="vouamt" column="VOUAMT" jdbcType="DECIMAL"/>
            <result property="source" column="SOURCE" jdbcType="DECIMAL"/>
            <result property="isWx" column="IS_WX" jdbcType="CHAR"/>
            <result property="activityname" column="ACTIVITYNAME" jdbcType="VARCHAR"/>
            <result property="mRetailDocno" column="M_RETAIL_DOCNO" jdbcType="VARCHAR"/>
            <result property="isshowVoudiscount" column="ISSHOW_VOUDISCOUNT" jdbcType="CHAR"/>
            <result property="deltype" column="DELTYPE" jdbcType="CHAR"/>
            <result property="startDate" column="START_DATE" jdbcType="DECIMAL"/>
            <result property="mDim7Id" column="M_DIM7_ID" jdbcType="DECIMAL"/>
            <result property="mDim17Id" column="M_DIM17_ID" jdbcType="DECIMAL"/>
            <result property="vouName" column="VOU_NAME" jdbcType="VARCHAR"/>
            <result property="platform" column="PLATFORM" jdbcType="VARCHAR"/>
            <result property="details" column="DETAILS" jdbcType="VARCHAR"/>
            <result property="cCVipId" column="C_C_VIP_ID" jdbcType="DECIMAL"/>
            <result property="cPaywayId" column="C_PAYWAY_ID" jdbcType="DECIMAL"/>
            <result property="accountLimitoverlay" column="ACCOUNT_LIMITOVERLAY" jdbcType="CHAR"/>
            <result property="isListVipdis" column="IS_LIST_VIPDIS" jdbcType="CHAR"/>
            <result property="couponsWay" column="COUPONS_WAY" jdbcType="VARCHAR"/>
            <result property="isVipdisbeforevou" column="IS_VIPDISBEFOREVOU" jdbcType="CHAR"/>
            <result property="batchno" column="BATCHNO" jdbcType="VARCHAR"/>
            <result property="voudiscount" column="VOUDISCOUNT" jdbcType="CHAR"/>
            <result property="qtyLimitMin" column="QTY_LIMIT_MIN" jdbcType="DECIMAL"/>
            <result property="whetherSingle" column="WHETHER_SINGLE" jdbcType="VARCHAR"/>
            <result property="applicationStoreType" column="APPLICATION_STORE_TYPE" jdbcType="VARCHAR"/>
            <result property="applicationProductType" column="APPLICATION_PRODUCT_TYPE" jdbcType="CHAR"/>
            <result property="vouIsclaim" column="VOU_ISCLAIM" jdbcType="CHAR"/>
            <result property="vouConsumeclaim" column="VOU_CONSUMECLAIM" jdbcType="DECIMAL"/>
            <result property="vouDoublenum" column="VOU_DOUBLENUM" jdbcType="DECIMAL"/>
            <result property="thresholdIsactive" column="THRESHOLD_ISACTIVE" jdbcType="CHAR"/>
            <result property="isReachall" column="IS_REACHALL" jdbcType="CHAR"/>
            <result property="threshold" column="THRESHOLD" jdbcType="DECIMAL"/>
            <result property="dollarbond" column="DOLLARBOND" jdbcType="CHAR"/>
            <result property="isNeedlock" column="IS_NEEDLOCK" jdbcType="CHAR"/>
            <result property="lockStatus" column="LOCK_STATUS" jdbcType="CHAR"/>
            <result property="isSharejf" column="IS_SHAREJF" jdbcType="CHAR"/>
            <result property="isSuperposition" column="IS_SUPERPOSITION" jdbcType="CHAR"/>
            <result property="proMaxQty" column="PRO_MAX_QTY" jdbcType="DECIMAL"/>
            <result property="vouchersAndvipdis" column="VOUCHERS_ANDVIPDIS" jdbcType="VARCHAR"/>
            <result property="islimitOlddiscount" column="ISLIMIT_OLDDISCOUNT" jdbcType="CHAR"/>
            <result property="awarid" column="AWARID" jdbcType="DECIMAL"/>
            <result property="memo" column="memo" jdbcType="VARCHAR"/>
            <result property="weId" column="WEID" jdbcType="VARCHAR"/>
    </resultMap>
    <update id="updateValidDateById">
        update C_VOUCHERS set VALID_DATE = #{validDate} where VOUCHERS_NO = #{voucherNo}
    </update>
    <select id="selectByVouvherNo" resultType="java.lang.Long">

        select id from C_VOUCHERS where vouchers_no = #{voucherNo}

    </select>


</mapper>
