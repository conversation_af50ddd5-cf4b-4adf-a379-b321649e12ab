package org.springcenter.marketing.modules.repository;

import org.springcenter.marketing.modules.model.CStore;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 3/12/21 4:22 PM
 */
public interface ICStoreRepository {

    /**
     * 根据门店id批量查询
     * @param ids
     * @return
     */
    List<CStore> selectCStoreByIds(List<Long> ids);


    List<CStore> selectListByCodes(List<String> stringList);
}
