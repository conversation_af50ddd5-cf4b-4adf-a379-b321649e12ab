package org.springcenter.marketing.modules.bojunMapper;


import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface MProductMapper {

    public List<String> getProductIdsByGoodsFilter(@Param("sqlFilter") String sqlFitler);


    public Long sumByStoreIdAndProudctIds(@Param("storeId") String storeId , @Param("productIds") List<String> productIds);

}
