package org.springcenter.marketing.modules.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.jnby.common.ResponseResult;
import com.jnby.common.util.IdLeaf;
import org.killbill.bus.api.PersistentBus;
import org.springcenter.marketing.api.constant.IdConstant;
import org.springcenter.marketing.api.context.SendMiniSubMessageContext;
import org.springcenter.marketing.api.context.SendRealWashCouponContext;
import org.springcenter.marketing.api.dto.*;
import org.springcenter.marketing.api.entity.WashCouponPacketEntity;
import org.springcenter.marketing.api.enums.RightsTypeEnum;
import org.springcenter.marketing.api.enums.WashAppointmentEnum;
import org.springcenter.marketing.api.enums.WashAppointmentShowEnum;
import org.springcenter.marketing.modules.bojunMapper.CVouchersMapper;
import org.springcenter.marketing.modules.bojunMapper.CclientVipMapper;
import org.springcenter.marketing.modules.context.*;
import org.springcenter.marketing.modules.event.ActiveVoucherChangeVoucherReq;
import org.springcenter.marketing.modules.event.bus.SendWashCouponEventBus;
import org.springcenter.marketing.modules.mapper.*;
import org.springcenter.marketing.modules.model.*;
import org.springcenter.marketing.modules.repository.IRightsRepository;
import org.springcenter.marketing.modules.service.*;
import org.springcenter.marketing.modules.util.DateUtil;
import org.springcenter.marketing.modules.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springcenter.marketing.modules.wxMapper.*;
import org.springcenter.product.api.enums.IsDeleteEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.Month;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@RefreshScope
public class WashVoucherServiceImpl implements IWashVoucherService {

    @Autowired
    @Qualifier("marketingTransactionTemplate")
    private TransactionTemplate template;

    @Autowired
    private WashAppointmentMapper washAppointmentMapper;

    @Autowired
    private BRightsMapper bRightsMapper;

    @Autowired
    private ViewVoucherRuleConfigMapper viewVoucherRuleConfigMapper;

    @Autowired
    private IVoucherService iVoucherService;

    @Autowired
    private MessageService messageService;

    @Value("${wx.brand.commonConfigs}")
    private String commonConfigs;
    /**
     * 小程序模板id
     */
//    @Value("${sendmini.templateid}")
//    private String minitemplateId;
//
//    @Value("${jump.miniapp.url}")
//    private String jumpUrl;

    @Autowired
    private CardmainMapper cardmainMapper;

    @Autowired
    private VoucherVirtualBaseMapper voucherVirtualBaseMapper;

    @Autowired
    CclientVipMapper cclientVipMapper;

    @Autowired
    JicCardTypeMapper jicCardTypeMapper;

    @Autowired
    private BRightsCustomizeStoreMapper bRightsCustomizeStoreMapper;

    @Autowired
    private IUserVipService iUserVipService;

    @Autowired
    private CVouchersMapper cVouchersMapper;

    @Autowired
    private VoucherBaseMapper voucherBaseMapper;

    @Autowired
    private SendWashCouponEventBus sendWashCouponEventBus;

    @Autowired
    private WashUpdateTimeLogMapper washUpdateTimeLogMapper;

    @Autowired
    private RightsV3Service rightsV3Service;

    @Autowired
    private IRightsRepository rightsRepository;

    @Override
    public List<String> checkAndSendWashAppointment(CheckAndSendWashVoucherReq requestData,List<WashAppointment> notIn) {
        // 1. 校验用户今年有没有发过
        Date now = new Date();
        List<String> collect = notIn.stream().map(r -> r.getId()).collect(Collectors.toList());
        List<WashAppointment> list = washAppointmentMapper.selectByUnionidAndDate(requestData.getUnionid(),now,requestData.getFlag(),collect);
        if(CollectionUtils.isNotEmpty(list)){
            return new ArrayList<>();
        }

        GetRightsReq getRightsReq = new GetRightsReq();
        getRightsReq.setBrandId(requestData.getBrandId());
        getRightsReq.setRightsType(RightsTypeEnum.WASH_RIGHTS.getCode());
        getRightsReq.setCardLevel(requestData.getCardTypeId());
        List<RightsResp> rights = rightsRepository.getRightsByRedisOrDataBase(getRightsReq);

        // 筛选符合条件的 当前品牌卡绑定的门店
        if(StringUtils.isNotBlank(requestData.getWeid())){
            rights = dealFilterStore(rights,requestData.getUnionid(),requestData.getWeid());
        }

        if(CollectionUtils.isEmpty(rights)){
            return new ArrayList<>();
        }

        // 获取当前月份和最后一个月的差值
        LocalDate date = LocalDate.now();
        Month month = date.getMonth();
        int value = month.getValue();
        // 应该发有效的数量
        int diff = 12 - value +1;

        List<WashCouponPacketEntity> washCouponPacketEntities = JSONObject.parseArray(rights.get(0).getUseRule(), WashCouponPacketEntity.class);
        List<WashAppointment> insertList = new ArrayList<>();
        int size = washCouponPacketEntities.size();
        if(diff >= size){
            for (WashCouponPacketEntity washCouponPacketEntity : washCouponPacketEntities) {
                List<WashCouponPacketEntity.WashCoupon> washCoupons = washCouponPacketEntity.getList();
                for (WashCouponPacketEntity.WashCoupon washCoupon : washCoupons) {
                    // 如果有洗护券 才发
                    // 2023-03-23 洗护券在一个券包中 有且仅有一张
                    if (washCoupon.getType().equals(WashCouponPacketEntity.WASH_TYPE)) {
                        WashAppointment insertWashAppointment = new WashAppointment();
                        insertWashAppointment.setId(IdLeaf.getId(IdConstant.WASH_APPOINTMENT));
                        insertWashAppointment.setUnionid(requestData.getUnionid());
                        insertWashAppointment.setCreateTime(now);
                        insertWashAppointment.setUpdateTime(now);
                        insertWashAppointment.setIsDel(IsDeleteEnum.NORMAL.getCode());
                        insertWashAppointment.setCardTypeId(requestData.getCardTypeId());
                        insertWashAppointment.setStatus(WashAppointmentEnum.NOT_APPOINTMENT.getCode());
                        insertWashAppointment.setBRightsId(rights.get(0).getRightsId());
                        insertWashAppointment.setCouponPacketId(washCouponPacketEntity.getId());
                        insertWashAppointment.setCardLevelId(requestData.getCardLevelId());
                        insertWashAppointment.setMiniAppOpenId(requestData.getMiniAppOpenId());
                        insertWashAppointment.setCClientVipId(requestData.getCClientVipId());
                        insertWashAppointment.setOpenId(requestData.getOpenId());
                        insertList.add(insertWashAppointment);
                    }
                }
            }
        }else{

            for( int i = 0 ; i < diff; i ++){
                WashCouponPacketEntity washCouponPacketEntity = washCouponPacketEntities.get(i);
                List<WashCouponPacketEntity.WashCoupon> washCoupons = washCouponPacketEntity.getList();
                for (WashCouponPacketEntity.WashCoupon washCoupon : washCoupons) {
                    if(washCoupon.getType().equals(WashCouponPacketEntity.WASH_TYPE)){
                        WashAppointment insertWashAppointment = new WashAppointment();
                        insertWashAppointment.setId(IdLeaf.getId(IdConstant.WASH_APPOINTMENT));
                        insertWashAppointment.setUnionid(requestData.getUnionid());
                        insertWashAppointment.setCreateTime(now);
                        insertWashAppointment.setUpdateTime(now);
                        insertWashAppointment.setIsDel(IsDeleteEnum.NORMAL.getCode());
                        insertWashAppointment.setCardTypeId(requestData.getCardTypeId());
                        insertWashAppointment.setStatus(WashAppointmentEnum.NOT_APPOINTMENT.getCode());
                        insertWashAppointment.setBRightsId(rights.get(0).getRightsId());
                        insertWashAppointment.setCouponPacketId(washCouponPacketEntity.getId());
                        insertWashAppointment.setCardLevelId(requestData.getCardLevelId());
                        insertWashAppointment.setMiniAppOpenId(requestData.getMiniAppOpenId());
                        insertWashAppointment.setCClientVipId(requestData.getCClientVipId());
                        insertWashAppointment.setOpenId(requestData.getOpenId());
                        insertList.add(insertWashAppointment);
                    }
                }
            }
                //发失效的
            int dis = size - diff;
            for( int i = 0 ; i < dis; i ++){
                WashCouponPacketEntity washCouponPacketEntity = washCouponPacketEntities.get(i);
                List<WashCouponPacketEntity.WashCoupon> washCoupons = washCouponPacketEntity.getList();
                for (WashCouponPacketEntity.WashCoupon washCoupon : washCoupons) {
                    if(washCoupon.getType().equals(WashCouponPacketEntity.WASH_TYPE)){
                        WashAppointment insertWashAppointment = new WashAppointment();
                        insertWashAppointment.setId(IdLeaf.getId(IdConstant.WASH_APPOINTMENT));
                        insertWashAppointment.setUnionid(requestData.getUnionid());
                        insertWashAppointment.setCreateTime(now);
                        insertWashAppointment.setUpdateTime(now);
                        insertWashAppointment.setIsDel(IsDeleteEnum.NORMAL.getCode());
                        insertWashAppointment.setCardTypeId(requestData.getCardTypeId());
                        insertWashAppointment.setStatus(WashAppointmentEnum.DIS_APPOINTMENT.getCode());
                        insertWashAppointment.setBRightsId(rights.get(0).getRightsId());
                        insertWashAppointment.setCouponPacketId(washCouponPacketEntity.getId());
                        insertWashAppointment.setCardLevelId(requestData.getCardLevelId());
                        insertWashAppointment.setMiniAppOpenId(requestData.getMiniAppOpenId());
                        insertWashAppointment.setCClientVipId(requestData.getCClientVipId());
                        insertWashAppointment.setOpenId(requestData.getOpenId());
                        insertList.add(insertWashAppointment);
                    }
                }
            }
        }


        // 2. 进行发放预约记录  获取当前的这个用户的应该看到的权益信息
        template.execute(action->{
            if(CollectionUtils.isNotEmpty(insertList)){
                insertList.forEach(e->washAppointmentMapper.insertSelective(e));
            }
            return action;
        });
        return insertList.stream().map(r->r.getId()).collect(Collectors.toList());
    }

    private List<RightsResp> dealFilterStore(List<RightsResp> rights,  String unionid , String weid) {
        //适用商家类型   1    直营可用        2  经销可用      3  均可用      4 自定义商家
        MemberQueryContext memberQueryContext = new MemberQueryContext();
        memberQueryContext.setUnionId(unionid);
        memberQueryContext.setStatus("Y");
        memberQueryContext.setBrandId(weid);
        CustomerBaseResponse<List<MemberCardEntity>> memberCardList = iUserVipService.getMemberCardList(memberQueryContext);

        List<RightsResp> result = new ArrayList<>();
        // 品牌id  获取当前用户这张卡的绑定的门店
        if(CollectionUtils.isEmpty(memberCardList.getData())){
            return result;
        }
        MemberCardEntity memberCardEntity1 = memberCardList.getData().get(0);
        if(memberCardEntity1.getCustomerId() == null || memberCardEntity1.getCustomerId() == -1){
            // 经销 非176为经销
            memberCardEntity1.setCustomerId(1776);
        }

        List<RightsResp> rightsResps = rightsV3Service.filterSuitRightsResp(rights, memberCardEntity1.getCustomerId(),
                memberCardEntity1.getStoreId(), unionid, weid);
        return rightsResps;
    }

    @Override
    public List<UserAllWashCouponResp> getUserAllWashCoupon(UserAllWashCouponReq requestData) {
        // 1. 获取当前用户所有的预约洗护券数据
        Date now = new Date();
        List<WashAppointment> list = washAppointmentMapper.selectByUnionidAndDate(requestData.getUnionid(),now,null,null);
        if(CollectionUtils.isNotEmpty(list)){
            // 筛选出来已经发送了凭证券的   去第三方接口查询
            List<WashAppointment> alreadySendVoucher = list.stream().filter(r -> r.getStatus().equals(WashAppointmentEnum.APPOINTMENT_SEND.getCode())).collect(Collectors.toList());
            List<WashAppointment> notSendVoucher = list.stream().filter(r -> !r.getStatus().equals(WashAppointmentEnum.APPOINTMENT_SEND.getCode())).collect(Collectors.toList());
            // 没发送凭证券的转换为前端使用的对象
            List<UserAllWashCouponResp> notSendVoucherCouponResp = coverFrontUseData(notSendVoucher);
            // 第三方接口查询信息
            List<UserAllWashCouponResp> result = findVouchers(alreadySendVoucher);
            //进行排序
            result = sortList(result,notSendVoucherCouponResp);
            return result;
        }
        return new ArrayList<>();
    }

    @Override
    public ResponseResult checkCanUpdateAppointment(UpdateOrAddAppointmentDateReq requestData) {
        // 1. 验证用户数据是否存在，
        WashAppointment washAppointment = washAppointmentMapper.selectByPrimaryKey(requestData.getId());
        if(washAppointment == null){
            return ResponseResult.error(-1,"该id未查询到预约记录!");
        }
        // 2. 验证用户数据状态是否正确
        if(washAppointment.getStatus().equals(WashAppointmentEnum.NOT_APPOINTMENT.getCode()) ){
            return ResponseResult.error(-1,"状态不正确 不允许修改预约时间!");
        }
        if(!washAppointment.getActiveTime().equals(requestData.getAppointmentDate())){
            // 3. 验证修改次数是否足够
            if(washAppointment.getLeaveUpdateTimes() - 1 < 0){
                return ResponseResult.error(-1,"修改次数已用完，无法再修改!");
            }
        }

        // 4. 验证今年的券只能预约今年
        if(!DateUtils.formatDate(washAppointment.getCreateTime(),"yyyy").equals(DateUtils.formatDate(new Date(),"yyyy"))){
            return ResponseResult.error(-1,"本年的券只能预约本年!");
        }

        return ResponseResult.success();
    }

    @Override
    public ResponseResult checkAddAppointment(List<UpdateOrAddAppointmentDateReq> requestData) {
        Set<String> hashSet = new HashSet<>();
        for (UpdateOrAddAppointmentDateReq requestDatum : requestData) {
            hashSet.add(requestDatum.getAppointmentDate());
        }
        if(requestData.size() != hashSet.size()){
            return ResponseResult.error(-1,"每张预约券只能在一个月份进行预约，请修改!");
        }
        //
        for (UpdateOrAddAppointmentDateReq requestDatum : requestData) {
            WashAppointment washAppointment = washAppointmentMapper.selectByPrimaryKey(requestDatum.getId());
            if(!washAppointment.getStatus().equals(WashAppointmentEnum.NOT_APPOINTMENT.getCode())){
                return ResponseResult.error(-1,"当前有一张券已经不在未预约状态，不能进行全部预约时间操作!");
            }

            // 4. 验证今年的券只能预约今年
            if(!DateUtils.formatDate(washAppointment.getCreateTime(),"yyyy").equals(DateUtils.formatDate(new Date(),"yyyy"))){
                return ResponseResult.error(-1,"本年的券只能预约本年!");
            }
        }

        return ResponseResult.success();
    }

    @Override
    public void updateAppointmentDate(List<UpdateOrAddAppointmentDateReq> requestData) {
        template.execute(action->{
            requestData.stream().forEach(e-> {
                if(e.getIsUpdateStatus() == 0){
                    WashAppointment washAppointment = washAppointmentMapper.selectByPrimaryKey(e.getId());
                    WashAppointment update = new WashAppointment();
                    //预约为当月
                    if(e.getAppointmentDate().equals(DateUtil.formatToStr(new Date(),"yyyyMM"))){
                        update.setStatus(WashAppointmentEnum.APPOINTMENT_SEND.getCode());
                        // 调用接口发送券
                        sendWashCoupon(washAppointment);
                    }else{
                        update.setStatus(WashAppointmentEnum.APPOINTMENT.getCode());
                    }
                    update.setId(e.getId());
                    update.setActiveTime(e.getAppointmentDate());
                    update.setUpdateTime(new Date());
                    washAppointmentMapper.updateByPrimaryKeySelective(update);

                    WashUpdateTimeLog washUpdateTimeLog = new WashUpdateTimeLog();
                    washUpdateTimeLog.setId(IdLeaf.getId(IdConstant.WASH_UPDATE_TIME_LOG));
                    washUpdateTimeLog.setWashId(e.getId());
                    washUpdateTimeLog.setCreateTime(new Date());
                    washUpdateTimeLog.setUpdateTime(new Date());
                    washUpdateTimeLogMapper.insertSelective(washUpdateTimeLog);
                }else{
                    WashAppointment washAppointment = washAppointmentMapper.selectByPrimaryKey(e.getId());
                    if(!washAppointment.getActiveTime().equals(e.getAppointmentDate())){
                        WashAppointment update = new WashAppointment();
                        update.setId(e.getId());
                        update.setActiveTime(e.getAppointmentDate());
                        update.setUpdateTime(new Date());
                        //如果不同 变更为已经预约
                        update.setStatus(WashAppointmentEnum.APPOINTMENT.getCode());
                        update.setVoucherNo("");
                        int i = washAppointmentMapper.updateActiveTimeByIdAndStatus(update);

                        WashUpdateTimeLog washUpdateTimeLog = new WashUpdateTimeLog();
                        washUpdateTimeLog.setId(IdLeaf.getId(IdConstant.WASH_UPDATE_TIME_LOG));
                        washUpdateTimeLog.setWashId(e.getId());
                        washUpdateTimeLog.setCreateTime(new Date());
                        washUpdateTimeLog.setUpdateTime(new Date());
                        washUpdateTimeLogMapper.insertSelective(washUpdateTimeLog);

                        if(i == 0){
                            throw  new RuntimeException("非正确状态 更改失败!");
                        }

                        if(washAppointment.getStatus().equals(WashAppointmentEnum.APPOINTMENT_SEND.getCode())){
                            // 失效优惠券
                            loseEffectiveCoupon(washAppointment);
                        }
                        String yyyyMM = DateUtils.formatDate(new Date(), "yyyyMM");
                        // 如果当前时间是预约的是当月  那么在发一次 更新一下状态
                        if(yyyyMM.equals(e.getAppointmentDate())){
                            // 调用接口发送券
                            sendWashCoupon(washAppointment);
                            WashAppointment up = new WashAppointment();
                            up.setId(e.getId());
                            up.setStatus(WashAppointmentEnum.APPOINTMENT_SEND.getCode());
                            washAppointmentMapper.updateByPrimaryKeySelective(up);
                        }
                    }
                }
            });
            return action;
        });
    }



    @Override
    public void upOrDownCardLevel(UpOrDownCardLevelReq requestData) {
        // 1. 查询出来老的数据， 看看是否已经激活过，如果已经激活过，那么不管， 未激活 的 直接软删除   删除之后 （新发的券关联软删的这个id）
        if(CollectionUtils.isEmpty(requestData.getCClientVipIds()) && CollectionUtils.isEmpty(requestData.getCardNos())){
            return ;
        }
        List<List<String>> partition = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(requestData.getCClientVipIds())){
            partition = Lists.partition(requestData.getCClientVipIds(), 90);
        }else if(CollectionUtils.isNotEmpty(requestData.getCardNos())){
            partition = Lists.partition(requestData.getCardNos(), 90);
        }
        for (List<String> longs : partition) {
            List<CclientVip> cclientVips = new ArrayList<>();
            if(CollectionUtils.isNotEmpty(requestData.getCClientVipIds())){
                cclientVips = cclientVipMapper.selectByPrimaryKeys(longs);
            }else if(CollectionUtils.isNotEmpty(requestData.getCardNos())){
                cclientVips = cclientVipMapper.selectByCardNos(longs);
            }

            for (CclientVip cclientVip : cclientVips) {

                // 今年的数据
                List<WashAppointment> list =  washAppointmentMapper.selectByClientVipIdAndDate(cclientVip.getId(), new Date());
                if(CollectionUtils.isEmpty(list)){
                    // 空数据不进行处理
                    continue;
                }
                // 如果升级的卡和当前卡等级一致  不管了
                if(list.get(list.size()-1).getCardTypeId().equals(cclientVip.getcViptypeId().intValue())){
                    continue;
                }

                //  老数据
                List<WashAppointment> oldWashAppointment = list;
                // 筛选已预约已发送的数据
                List<WashAppointment> alreadySendCoupon = oldWashAppointment.stream().filter(r -> r.getStatus().equals(WashAppointmentEnum.APPOINTMENT_SEND.getCode())).collect(Collectors.toList());
                // 查询这些已经预约已经发送的，返回未激活的，这些需要删除
                List<WashAppointment> notActiveShouldUpdate = checkAlreadySendCouponIsActive(alreadySendCoupon);
                // 获取非已预约已发送的数据
                List<WashAppointment> notSendCoupon = oldWashAppointment.stream().filter(r -> !r.getStatus().equals(WashAppointmentEnum.APPOINTMENT_SEND.getCode())).collect(Collectors.toList());
                notSendCoupon = notSendCoupon.stream().filter(r -> !r.getStatus().equals(WashAppointmentEnum.DIS_APPOINTMENT.getCode())).collect(Collectors.toList());
                // 删除这些数据

                notSendCoupon.addAll(notActiveShouldUpdate);
                List<WashAppointment> finalNotSendCoupon = notSendCoupon;
                template.execute(action->{
                    //删除
                    List<WashAppointment> notIn = new ArrayList<>();

                    for (WashAppointment washAppointment : finalNotSendCoupon) {
                        WashAppointment update = new WashAppointment();
                        update.setId(washAppointment.getId());
                        // 如果当前用户身上有凭证券  并且是已经失效了， 那么就不管了
                        WashAppointment old = washAppointmentMapper.selectByPrimaryKey(washAppointment.getId());
                        if(old != null && StringUtils.isNotBlank(old.getVoucherNo()) && !old.getVoucherNo().contains(",")){
                            List<WashAppointment> params = new ArrayList<>();
                            params.add(old);
                            List<VoucherRuleDetailsEntity> vouchersFromApi = findVouchersFromApi(params);
                            if(vouchersFromApi.get(0).getIsverifyed().equals("N") && Integer.parseInt(vouchersFromApi.get(0).getValiddate())
                                    < Integer.parseInt(DateUtils.formatDate(new Date(),"yyyyMMdd"))){
                                //如果是已经失效的， 那么就不管了
                                notIn.add(washAppointment);
                                continue;
                            }
                        }
                        update.setIsDel(IsDeleteEnum.IS_DELETED.getCode());
                        washAppointmentMapper.updateByPrimaryKeySelective(update);
                    }

                    // 发送新的数据  已经预约的 随便找一张塞里面就行
                    CheckAndSendWashVoucherReq checkAndSendWashVoucherReq = new CheckAndSendWashVoucherReq();
                    checkAndSendWashVoucherReq.setBrandId(BrandConfigData.CARD_CODES);
                    checkAndSendWashVoucherReq.setCardTypeId(cclientVip.getcViptypeId().intValue());
                    checkAndSendWashVoucherReq.setUnionid(list.get(0).getUnionid());
                    // 查询卡级别
                    JicCardTypeR byId = jicCardTypeMapper.getById(cclientVip.getcViptypeId());
                    checkAndSendWashVoucherReq.setCardLevelId(byId.getLevelId().intValue());
                    checkAndSendWashVoucherReq.setMiniAppOpenId(list.get(0).getMiniAppOpenId());
                    checkAndSendWashVoucherReq.setCClientVipId(cclientVip.getId());
                    checkAndSendWashVoucherReq.setOpenId(list.get(0).getOpenId());
                    checkAndSendWashVoucherReq.setFlag(1L);

                    // 验证升级还是降级 old
                    JicCardTypeR oldC = jicCardTypeMapper.getById(list.get(list.size() - 1).getCardTypeId().longValue());
                    JicCardTypeR newC = jicCardTypeMapper.getById(cclientVip.getcViptypeId());
                    if(newC != null && newC.getWeid() != null){
                        checkAndSendWashVoucherReq.setWeid(String.valueOf(newC.getWeid()));
                    }
                    //  升级
                    List<String> ids = new ArrayList<>();
                    if(oldC.getLevelId() < newC.getLevelId()){
                        ids = checkAndSendWashAppointment(checkAndSendWashVoucherReq,notIn);
                    }else{
                        // 降级重置之前的权益
                        for (WashAppointment washAppointment : list) {
                            // 只处理老的
                            if(oldC.getLevelId().equals(washAppointment.getCardLevelId().longValue())){
                                WashAppointment updateData = new WashAppointment();
                                updateData.setId(washAppointment.getId());
                                updateData.setIsDel(IsDeleteEnum.IS_DELETED.getCode());
                                washAppointmentMapper.updateByPrimaryKeySelective(updateData);


                                if(StringUtils.isNotBlank(washAppointment.getSourceId())  &&  (StringUtils.isBlank(washAppointment.getVoucherNo()) || !washAppointment.getVoucherNo().contains(","))){
                                    // 如果voucherNo不为空   那么取到 voucherNo 去判断是否失效了，如果已经失效了 ，则不进行更改， 如果未失效 则更改

                                    WashAppointment old = washAppointmentMapper.selectByPrimaryKey(washAppointment.getSourceId());
                                    if(old != null && StringUtils.isNotBlank(old.getVoucherNo()) && !old.getVoucherNo().contains(",")){
                                        List<WashAppointment> params = new ArrayList<>();
                                        params.add(old);
                                        List<VoucherRuleDetailsEntity> vouchersFromApi = findVouchersFromApi(params);
                                        if(vouchersFromApi.get(0).getIsverifyed().equals("N") && Integer.parseInt(vouchersFromApi.get(0).getValiddate())
                                                < Integer.parseInt(DateUtils.formatDate(new Date(),"yyyyMMdd"))){
                                            //如果是已经失效的， 那么就不管了
                                            continue;
                                        }
                                    }

                                    WashAppointment update = new WashAppointment();
                                    update.setStatus(0);
                                    update.setId(washAppointment.getSourceId());
                                    update.setActiveTime("");
                                    update.setIsDel(IsDeleteEnum.NORMAL.getCode());
                                    washAppointmentMapper.updateByPrimaryKeySelective(update);
                                }
                            }
                        }
                    }

                    // 更新一下数据 已经预约的时间塞一下
                    List<WashAppointment> alreadyAppointment = finalNotSendCoupon.stream().filter(r -> !r.getStatus().equals(WashAppointmentEnum.NOT_APPOINTMENT)).collect(Collectors.toList());
                    // 未预约的
                    List<WashAppointment> notApp = finalNotSendCoupon.stream().filter(r -> r.getStatus().equals(WashAppointmentEnum.NOT_APPOINTMENT)).collect(Collectors.toList());
                    int size = alreadyAppointment.size();
                    //
                    int i = 0;
                    for (String id : ids) {
                        WashAppointment washAppointment = washAppointmentMapper.selectByPrimaryKey(id);
                        if(i < size){
                            WashAppointment update = new WashAppointment();
                            update.setId(washAppointment.getId());
                            update.setActiveTime(alreadyAppointment.get(i).getActiveTime());
                            update.setStatus(alreadyAppointment.get(i).getStatus());
                            update.setOldCardTypeId(alreadyAppointment.get(i).getCardTypeId());
                            update.setSourceId(alreadyAppointment.get(i).getId());
                            update.setOldCardLevelId(alreadyAppointment.get(i).getCardLevelId());
                            if(StringUtils.isNotBlank(alreadyAppointment.get(i).getActiveTime())){
                                if(alreadyAppointment.get(i).getActiveTime().equals(DateUtil.formatToStr(new Date(),"yyyyMM"))){
                                    sendWashCoupon(washAppointment);
                                }else{
                                    if(oldC.getLevelId() < newC.getLevelId()){
                                        // 升级  则带着， 降级则不带
                                        update.setActiveTime(alreadyAppointment.get(i).getActiveTime());
                                        update.setStatus(alreadyAppointment.get(i).getStatus());
                                    }else{
                                        update.setActiveTime("");
                                        update.setStatus(WashAppointmentEnum.NOT_APPOINTMENT.getCode());
                                    }
                                }
                            }
                            washAppointmentMapper.updateByPrimaryKeySelective(update);
                        }else{
                            if(i  < notApp.size()){
                                WashAppointment update = new WashAppointment();
                                update.setId(washAppointment.getId());
                                update.setOldCardTypeId(notApp.get(i).getCardTypeId());
                                update.setSourceId(notApp.get(i).getId());
                                update.setOldCardLevelId(notApp.get(i).getCardLevelId());
                                washAppointmentMapper.updateByPrimaryKeySelective(update);
                            }
                        }
                        i++;
                    }

                    return action;
                });

            }
        }
    }


    @Override
    public void sendCouponJob() {
        com.github.pagehelper.Page<Object> hPageTotal = PageHelper.startPage(1, 100);
        // 查询预约时间为当月的，今年的券 状态为已经预约
        washAppointmentMapper.selectSendCouponJob();
        PageInfo<WashAppointment> pageInfoTotal = new PageInfo(hPageTotal);
        long total = pageInfoTotal.getTotal();

        long pageTotal = 0;
        int pageSize = 100;
        if(total % pageSize == 0){
            pageTotal = total / pageSize;
        }else{
            pageTotal = total / pageSize + 1;
        }

        for(int i = 1 ; i <= pageTotal; i++){
            // 分页查询
            com.github.pagehelper.Page<Object> hPage = PageHelper.startPage(1, 100);
            // 查询预约时间为当月的，今年的券 状态为已经预约
            washAppointmentMapper.selectSendCouponJob();
            PageInfo<WashAppointment> pageInfo = new PageInfo(hPage);
            List<WashAppointment> result = pageInfo.getList();
            for (WashAppointment washAppointment : result) {
                try {
                    template.execute(action->{
                        sendWashCoupon(washAppointment);
                        if(StringUtils.isNotBlank(washAppointment.getVoucherNo())){
                            WashAppointment update = new WashAppointment();
                            update.setId(washAppointment.getId());
                            update.setStatus(WashAppointmentEnum.APPOINTMENT_SEND.getCode());
                            washAppointmentMapper.updateByPrimaryKeySelective(update);
                        }
                        return action;
                    });
                }catch (Exception e){
                    log.error("sendCouponJob =   page = {} ,Data = {}",e, i,JSONObject.toJSONString(result));
                    continue;
                }
            }
        }
    }

    @Override
    public void updateIsShow(List<String> washAppointmentIds) {
        template.execute(action ->{
            for (String washAppointmentId : washAppointmentIds) {
                WashAppointment washAppointment = new WashAppointment();
                washAppointment.setId(washAppointmentId);
                washAppointment.setIsShow(WashAppointmentShowEnum.ALREADY_SHOW.getCode());
                washAppointmentMapper.updateByPrimaryKeySelective(washAppointment);
            }
            return action;
        });

    }

    @Override
    public int activeVoucherChangeVoucherNos(List<ActiveVoucherChangeVoucherNosReq> listParams) {

        try {
            ActiveVoucherChangeVoucherReq activeVoucherChangeVoucherReq = new ActiveVoucherChangeVoucherReq(listParams,
                    System.currentTimeMillis(),
                    System.currentTimeMillis(),
                    UUID.randomUUID()
            );
            sendWashCouponEventBus.post(activeVoucherChangeVoucherReq);
        } catch (PersistentBus.EventBusException e) {
            throw new RuntimeException(e);
        }
        return 1;
    }

    private List<String> sendCouponByWashCoupon(WashAppointment washAppointment) {
        List<String> result = new ArrayList<>();
        BRights bRights = bRightsMapper.selectByPrimaryKey(washAppointment.getBRightsId());
        List<WashCouponPacketEntity> washCouponPacketEntities = JSONObject.parseArray(bRights.getUseRule(), WashCouponPacketEntity.class);
        for (WashCouponPacketEntity washCouponPacketEntity : washCouponPacketEntities) {
            if(washAppointment.getCouponPacketId().equals(washCouponPacketEntity.getId())){
                List<WashCouponPacketEntity.WashCoupon> list = washCouponPacketEntity.getList();
                for (WashCouponPacketEntity.WashCoupon washCoupon : list) {
                    if(washCoupon.getType() != WashCouponPacketEntity.WASH_TYPE){
                        String awardId = washCoupon.getCouponId();
                        Integer num = washCoupon.getNum();
                        // 调用第三方接口发送优惠券
                        JICVoucherSendReqEntity jicVoucherSendReqEntity = new JICVoucherSendReqEntity();
                        jicVoucherSendReqEntity.setOpenId(washAppointment.getUnionid());
                        List<RulesList> rulesLists = new ArrayList<>();
                        RulesList rules = new RulesList();
                        rules.setAwardId(Long.parseLong(awardId));
                        rules.setNum(num.longValue());
                        rulesLists.add(rules);
                        jicVoucherSendReqEntity.setRulesList(rulesLists);
                        JICVoucherSendRespEntity jicVoucherSendRespEntity = iVoucherService.sendVoucher(jicVoucherSendReqEntity);
                        if (jicVoucherSendRespEntity != null && jicVoucherSendRespEntity.getCode() == 200 && ObjectUtils.isNotEmpty(jicVoucherSendRespEntity.getData())) {
                            String voucher = jicVoucherSendRespEntity.getData().getR_voucher();
                            if (StringUtils.isBlank(voucher)) {
                                log.error("发券失败 msg = {}, e = {}", jicVoucherSendRespEntity.getData().getRetmsg(), jicVoucherSendRespEntity);
                                return null;
                            }
                            List<Map> maps = JSONObject.parseArray(voucher, Map.class);
                            String voucherCode= "";
                            for (Map map : maps) {
                                voucherCode = map.get("code").toString();
                                result.add(voucherCode);
                            }
                        }
                    }
                }
            }
        }
        return result;
    }

    private String sendRealWashCoupon(WashAppointment washAppointment,ActiveVoucherChangeVoucherNosReq activeVoucherChangeVoucherNosReq) {
        //调用微商城接口发送  凭证券
        BRights bRights = bRightsMapper.selectByPrimaryKey(washAppointment.getBRightsId());
        List<WashCouponPacketEntity> washCouponPacketEntities = JSONObject.parseArray(bRights.getUseRule(), WashCouponPacketEntity.class);
        for (WashCouponPacketEntity washCouponPacketEntity : washCouponPacketEntities) {
            if(washAppointment.getCouponPacketId().equals(washCouponPacketEntity.getId())){
                List<WashCouponPacketEntity.WashCoupon> list = washCouponPacketEntity.getList();
                for (WashCouponPacketEntity.WashCoupon washCoupon : list) {
                    if(washCoupon.getType() == WashCouponPacketEntity.WASH_TYPE){
                        List<Cardmain> cardmain = cardmainMapper.selectByUnionId(washAppointment.getUnionid());
                        List<Cardmain> collect = cardmain.stream().filter(c -> StringUtils.isNotBlank(c.getTel())).collect(Collectors.toList());
                        // 洗衣券
                        String washCouponId = washCoupon.getWashCouponId();
                        // 拼接参数  获取手机号
                        SendRealWashCouponContext sendRealWashCouponContext = new SendRealWashCouponContext();
                        sendRealWashCouponContext.setCardNo(activeVoucherChangeVoucherNosReq.getCardNo());
                        sendRealWashCouponContext.setExchangeStore(activeVoucherChangeVoucherNosReq.getExchangeStore());
                        sendRealWashCouponContext.setVouId(washCouponId);
                        sendRealWashCouponContext.setNum("1");
                        sendRealWashCouponContext.setMobile(collect.get(0).getTel());
                        sendRealWashCouponContext.setRelationVoucherNo(washAppointment.getVoucherNo());
                        List<String> result = iVoucherService.sendRealWashCoupon(sendRealWashCouponContext);
                        if(CollectionUtils.isNotEmpty(result) ){
                            return result.get(0);
                        }
                    }
                }
            }
        }
        return null;
    }

    @Override
    public void notActiveSendMessageJob() {
        String yyyyMM = DateUtils.formatDate(new Date(), "yyyyMM");

        // 1. 扫描表 已经预约 已经发送的， 然后找到 voucherNo 里面只有一张券的,  分页查询， 未删除的， 然后定时任务查询这个券有没有激活，没激活 则直接放消息
        long count = washAppointmentMapper.selectUnSendMessageNotActiveCount(yyyyMM);
        long pageTotal = 0;
        int pageSize = 100;
        if(count % pageSize == 0){
            pageTotal = count / pageSize;
        }else{
            pageTotal = count / pageSize + 1;
        }

        for(int i = 1 ; i <= pageTotal; i++){
            try {
                // 分页查询
                com.github.pagehelper.Page<Object> hPage = PageHelper.startPage(1, pageSize);
                List<WashAppointment> list= washAppointmentMapper.selectUnSendMessageNotActive(yyyyMM);
                PageInfo<WashAppointment> pageInfo = new PageInfo(hPage);
                List<WashAppointment> result = pageInfo.getList();
                List<VoucherRuleDetailsEntity> vouchersFromApi = findVouchersFromApi(result);
                for (VoucherRuleDetailsEntity voucherRuleDetailsEntity : vouchersFromApi) {
                    if(voucherRuleDetailsEntity.getIsverifyed().equals("N")
                            && Integer.parseInt(voucherRuleDetailsEntity.getValiddate()) >= Integer.parseInt(DateUtils.formatDate(new Date(),"yyyyMMdd"))
                            &&
                            (Integer.parseInt(voucherRuleDetailsEntity.getValidmindate().equals("")? "99999999":voucherRuleDetailsEntity.getValidmindate()) <= Integer.parseInt(DateUtils.formatDate(new Date(),"yyyyMMdd"))
                                    || (Integer.parseInt(voucherRuleDetailsEntity.getInsertdate().substring(0,10).replaceAll("-",""))) <= Integer.parseInt(DateUtils.formatDate(new Date(),"yyyyMMdd")))
                    ){
                        // 应该发送消息  然后更新
                        List<WashAppointment> collect = result.stream().filter(e -> e.getVoucherNo().contains(voucherRuleDetailsEntity.getCode())).collect(Collectors.toList());
                        if(CollectionUtils.isNotEmpty(collect)){
                            WashAppointment washAppointment = collect.get(0);
                            Boolean isSend = notActiveSendMessage(voucherRuleDetailsEntity,washAppointment);
                            if(isSend){
                                WashAppointment update = new WashAppointment();
                                update.setId(washAppointment.getId());
                                update.setSendBeginMessage(1);
                                washAppointmentMapper.updateByPrimaryKeySelective(update);
                            }
                        }
                    }
                }
            }catch (Exception e){
                log.error("notActiveSendMessageJob = ",e);
                continue;
            }
        }
    }

    @Override
    public void notActiveSendMessageEndJob() {
        String yyyyMM = DateUtils.formatDate(new Date(), "yyyyMM");

        // 1. 扫描表 已经预约 已经发送的， 然后找到 voucherNo 里面只有一张券的,  分页查询， 未删除的， 然后定时任务查询这个券有没有激活，没激活 则直接放消息
        long count = washAppointmentMapper.selectUnSendMessageNotActiveEndCount(yyyyMM);
        long pageTotal = 0;
        int pageSize = 100;
        if(count % pageSize == 0){
            pageTotal = count / pageSize;
        }else{
            pageTotal = count / pageSize + 1;
        }

        for(int i = 1 ; i <= pageTotal; i++){
            try {
                // 分页查询
                com.github.pagehelper.Page<Object> hPage = PageHelper.startPage(1, pageSize);
                List<WashAppointment> list= washAppointmentMapper.selectUnSendMessageNotActiveEnd(yyyyMM);
                PageInfo<WashAppointment> pageInfo = new PageInfo(hPage);
                List<WashAppointment> result = pageInfo.getList();
                List<VoucherRuleDetailsEntity> vouchersFromApi = findVouchersFromApi(result);
                for (VoucherRuleDetailsEntity voucherRuleDetailsEntity : vouchersFromApi) {
                    if(voucherRuleDetailsEntity.getIsverifyed().equals("N")
                            && Integer.parseInt(voucherRuleDetailsEntity.getValiddate()) >= Integer.parseInt(DateUtils.formatDate(new Date(),"yyyyMMdd"))
                            &&
                            (Integer.parseInt(voucherRuleDetailsEntity.getValidmindate().equals("")? "99999999":voucherRuleDetailsEntity.getValidmindate()) <= Integer.parseInt(DateUtils.formatDate(new Date(),"yyyyMMdd"))
                            ||
                            (Integer.parseInt(voucherRuleDetailsEntity.getInsertdate().substring(0,10).replaceAll("-",""))) <= Integer.parseInt(DateUtils.formatDate(new Date(),"yyyyMMdd")))
                    ){
                        // 应该发送消息  然后更新
                        List<WashAppointment> collect = result.stream().filter(e -> e.getVoucherNo().contains(voucherRuleDetailsEntity.getCode())).collect(Collectors.toList());
                        if(CollectionUtils.isNotEmpty(collect)){
                            WashAppointment washAppointment = collect.get(0);
                            Boolean isSend = notActiveSendMessageEnd(voucherRuleDetailsEntity,washAppointment);
                            if(isSend){
                                WashAppointment update = new WashAppointment();
                                update.setId(washAppointment.getId());
                                update.setSendEndMessage(1);
                                washAppointmentMapper.updateByPrimaryKeySelective(update);
                            }
                        }
                    }
                }
            }catch (Exception e){
                log.error("notActiveSendMessageEndJob = ",e);
                continue;
            }
        }
    }

    @Override
    public UserWashImgDetailsResp getUserWashImgDetials(CheckAndSendWashVoucherReq requestData) {
        UserWashImgDetailsResp result = new UserWashImgDetailsResp();
        GetRightsReq getRightsReq = new GetRightsReq();
        getRightsReq.setBrandId(requestData.getBrandId());
        getRightsReq.setRightsType(RightsTypeEnum.WASH_RIGHTS.getCode());
        getRightsReq.setCardLevel(requestData.getCardTypeId());
        List<RightsResp> rights = rightsRepository.getRightsByRedisOrDataBase(getRightsReq);
        if(CollectionUtils.isEmpty(rights)){
            return result;
        }
        //
//        rights = dealFilterStore(rights, requestData);
//        if(CollectionUtils.isEmpty(rights)){
//            return result;
//        }

        List<BigDecimal> list = new ArrayList<>();
        BigDecimal totalAmount = BigDecimal.ZERO;

        // 获取优惠券金额
        String useRule = rights.get(0).getUseRule();
        List<WashCouponPacketEntity> washCouponPacketEntities = JSONObject.parseArray(useRule, WashCouponPacketEntity.class);
        for (WashCouponPacketEntity washCouponPacketEntity : washCouponPacketEntities) {
            BigDecimal couponAmount  = getCouponAmountFromApi(rights.get(0).getUseRule(),washCouponPacketEntity.getId());
            list.add(couponAmount);
            totalAmount = totalAmount.add(couponAmount);
        }
        result.setAmountList(list);
        result.setTotalAmount(totalAmount);
        result.setNumber(list.size());
        return result;
    }

    @Override
    public List<String> getBeforeUserAllWashCoupon(BeforeUserAllWashCouponReq requestData) {
        List<String> list = new ArrayList<>();
        for (String id : requestData.getIds()) {
            WashAppointment washAppointment = washAppointmentMapper.selectByPrimaryKey(id);
            BRights bRights = bRightsMapper.selectByPrimaryKey(washAppointment.getBRightsId());
            // 获取洗护券的金额
            BigDecimal couponAmount  = getCouponAmountFromApi(bRights.getUseRule(),washAppointment.getCouponPacketId());
            list.add(couponAmount.toString());
        }
        return list;
    }

    @Override
    public List<Integer> getUpOrDownNumber(String washAppointId) {
        List<Integer> result = new ArrayList<>();

        WashAppointment washAppointment = washAppointmentMapper.selectByPrimaryKey(washAppointId);
        String sourceId = washAppointment.getSourceId();
        if(StringUtils.isBlank(sourceId)){
            result.add(0);
            result.add(0);
            return result;
        }
        WashAppointment old = washAppointmentMapper.selectByPrimaryKey(washAppointment.getSourceId());
        // 获取老的  获取新的
        BRights bRights = bRightsMapper.selectByPrimaryKey(old.getBRightsId());
        List<WashCouponPacketEntity> washCouponPacketEntities = JSONObject.parseArray(bRights.getUseRule(), WashCouponPacketEntity.class);
        result.add(washCouponPacketEntities.size());

        BRights newBrights = bRightsMapper.selectByPrimaryKey(washAppointment.getBRightsId());
        List<WashCouponPacketEntity> washCouponPacketEntitiesNew = JSONObject.parseArray(newBrights.getUseRule(), WashCouponPacketEntity.class);
        result.add(washCouponPacketEntitiesNew.size());
        return result;
    }

    @Override
    public int checkNotSendWashCouponFixJob() {
        com.github.pagehelper.Page<Object> hPageTotal = PageHelper.startPage(1, 100);
        // 查询预约时间为当月的，今年的券 状态为已经预约
        washAppointmentMapper.selectBySign();
        PageInfo<WashAppointment> pageInfoTotal = new PageInfo(hPageTotal);
        long total = pageInfoTotal.getTotal();

        long pageTotal = 0;
        int pageSize = 100;
        if(total % pageSize == 0){
            pageTotal = total / pageSize;
        }else{
            pageTotal = total / pageSize + 1;
        }

        for(int i = 1 ; i <= pageTotal; i++){
            com.github.pagehelper.Page<Object> hPageTotal2 = PageHelper.startPage(1, 100);
            // 查询预约时间为当月的，今年的券 状态为已经预约
            washAppointmentMapper.selectBySign();
            PageInfo<WashAppointment> pageInfoTotal2 = new PageInfo(hPageTotal2);
            List<WashAppointment> list = pageInfoTotal2.getList();
            for (WashAppointment washAppointment : list) {
                if(washAppointment.getVoucherNo().split(",").length <= 1){
                    // 进行查询
                    List<VoucherVirtualBase> voucherVirtualBases = voucherVirtualBaseMapper.selectByExmemo(washAppointment.getVoucherNo());
                    if(CollectionUtils.isNotEmpty(voucherVirtualBases)){
                        String linkno = voucherVirtualBases.get(0).getLinkno();
                        List<String> vouchers = new ArrayList<>();
                        vouchers.add(washAppointment.getVoucherNo());
                        vouchers.add(linkno);

                        String collect = vouchers.stream().collect(Collectors.joining(","));
                        WashAppointment update = new WashAppointment();
                        update.setId(washAppointment.getId());
                        update.setVoucherNo(collect);
                        template.execute(action->{
                            washAppointmentMapper.updateByPrimaryKeySelective(update);
                            return action;
                        });
                    }
                }

                // 查询  补发优惠券
                WashAppointment newWash = washAppointmentMapper.selectByPrimaryKey(washAppointment.getId());
                if(newWash.getVoucherNo().split(",").length <= 2){
                    try {
                        // 查询是否发送了优惠券  如果发送了优惠券 则查询
                        List<String> awardIds = new ArrayList<>();
                        BRights bRights = bRightsMapper.selectByPrimaryKey(washAppointment.getBRightsId());
                        List<WashCouponPacketEntity> washCouponPacketEntities = JSONObject.parseArray(bRights.getUseRule(), WashCouponPacketEntity.class);
                        for (WashCouponPacketEntity washCouponPacketEntity : washCouponPacketEntities) {
                            if (washAppointment.getCouponPacketId().equals(washCouponPacketEntity.getId())) {
                                List<WashCouponPacketEntity.WashCoupon> washCoupons = washCouponPacketEntity.getList();
                                for (WashCouponPacketEntity.WashCoupon washCoupon : washCoupons) {
                                    if (washCoupon.getType() != WashCouponPacketEntity.WASH_TYPE) {
                                        String awardId = washCoupon.getCouponId();
                                        awardIds.add(awardId);
                                    }
                                }
                            }
                        }
                        if( StringUtils.isBlank(newWash.getUnionid()) || CollectionUtils.isEmpty(awardIds)){
                            // 下一条
                            continue;
                        }
                        List<VoucherBase> voucherBases = voucherBaseMapper.selectByUnionIdAndAwardIds(newWash.getUnionid(),awardIds);
                        if(CollectionUtils.isNotEmpty(voucherBases)){
                            String[] split = newWash.getVoucherNo().split(",");
                            List<String> strings = Lists.newArrayList(split);
                            for (VoucherBase voucherBase : voucherBases) {
                                strings.add(voucherBase.getLinksource());
                            }
                            String collect = strings.stream().collect(Collectors.joining(","));
                            WashAppointment update = new WashAppointment();
                            update.setId(newWash.getId());
                            update.setVoucherNo(collect);
                            update.setSign("2");
                            template.execute(action->{
                                washAppointmentMapper.updateByPrimaryKeySelective(update);
                                return action;
                            });

                        }else{
                            List<String> washCouponBinding = sendCouponByWashCoupon(newWash);
                            if(CollectionUtils.isNotEmpty(washCouponBinding)){
                                String[] split = newWash.getVoucherNo().split(",");
                                List<String> strings = Lists.newArrayList(split);
                                strings.addAll(washCouponBinding);
                                String collect = strings.stream().collect(Collectors.joining(","));
                                WashAppointment update = new WashAppointment();
                                update.setId(newWash.getId());
                                update.setVoucherNo(collect);
                                update.setSign("2");
                                template.execute(action->{
                                    washAppointmentMapper.updateByPrimaryKeySelective(update);
                                    return action;
                                });
                            }
                        }
                    }catch (Exception e){
                        log.info("checkNotSendWashCouponFixJob = ",e);
                        continue;
                    }
                }
            }
        }
        return 0;
    }

    @Override
    public void activeVoucherChangeVoucherNosNotify(List<ActiveVoucherChangeVoucherNosReq> info) {
        for (ActiveVoucherChangeVoucherNosReq requestData : info) {
            List<WashAppointment> list = new ArrayList<>();
            if(StringUtils.isNotBlank(requestData.getWashVoucherId())){
                WashAppointment washAppointment  = washAppointmentMapper.selectByPrimaryKey(requestData.getWashVoucherId());
            }else{
                List<String> voucherNos = requestData.getVoucherNos();
                if(CollectionUtils.isNotEmpty(voucherNos)){
                    for (String voucherNo : voucherNos) {
                        WashAppointment res = washAppointmentMapper.selectByVoucherNo(voucherNo);
                        if(res != null){
                            list.add(res);
                        }
                    }
                }
            }

            if(CollectionUtils.isNotEmpty(list)){
                for (WashAppointment appointment : list) {
                    List<String> vouchers = new ArrayList<>();
                    String voucherNo = appointment.getVoucherNo();

                    if(StringUtils.isNotBlank(voucherNo)){
                        String[] split = voucherNo.split(",");
                        if(split.length > 1){
                            // 大于1 则代表已经处理过
                            continue;
                        }
                        vouchers.addAll(Arrays.asList(split));
                    }
                    // sendRealWashCoupon 发送真正的洗护券
                    try {
                        String voucherReal = sendRealWashCoupon(appointment,requestData);
                        if(StringUtils.isNotBlank(voucherReal)){
                            vouchers.add(voucherReal);
                        }
                    }catch (Exception e){
                        log.error("发送真正洗护券异常 request ={} ,Appointment={}",
                                JSONObject.toJSONString(requestData),JSONObject.toJSONString(appointment),e);
                        WashAppointment update = new WashAppointment();
                        update.setId(appointment.getId());
                        update.setSign("1");
                        template.execute(action->{
                            washAppointmentMapper.updateByPrimaryKeySelective(update);
                            return action;
                        });
                        continue;
                    }

                    try {
                        // 发放绑定的优惠券
                        List<String> washCouponBinding = sendCouponByWashCoupon(appointment);
                        if(CollectionUtils.isNotEmpty(washCouponBinding)){
                            vouchers.addAll(washCouponBinding);
                        }
                    }catch (Exception e ){
                        log.error("发送绑定的优惠券异常 request ={} ,Appointment={}",
                                JSONObject.toJSONString(requestData),JSONObject.toJSONString(appointment),e);
                        continue;
                    }

                    String collect = vouchers.stream().collect(Collectors.joining(","));
                    WashAppointment update = new WashAppointment();
                    update.setId(appointment.getId());
                    update.setVoucherNo(collect);
                    template.execute(action->{
                        washAppointmentMapper.updateByPrimaryKeySelective(update);
                        return action;
                    });
                }
            }
        }

    }

    @Override
    public void expireWashVoucherByMonth() {
        log.info("定时任务根据月份失效匹配不上的优惠券 expireWashVoucherByMonth 开始");

        com.github.pagehelper.Page<Object> hPageTotal = PageHelper.startPage(1, 100);
        // 查询预约时间为当月的，今年的券 状态为已经预约
        washAppointmentMapper.selectShouldExpireWashGroupByUnionid();
        PageInfo<String> pageInfoTotal = new PageInfo(hPageTotal);
        long total = pageInfoTotal.getTotal();

        long pageTotal = 0;
        int pageSize = 100;
        if(total % pageSize == 0){
            pageTotal = total / pageSize;
        }else{
            pageTotal = total / pageSize + 1;
        }

        LocalDate date = LocalDate.now();
        Month month = date.getMonth();
        int value = month.getValue();

        // 可以剩余的数量
        int diff = 12 - value + 1;

        for(int i = 1 ; i <= pageTotal; i++){
            // 分页查询
            com.github.pagehelper.Page<Object> hPage = PageHelper.startPage(i, 100);
            // 查询预约时间为当月的，今年的券 状态为已经预约
            washAppointmentMapper.selectShouldExpireWashGroupByUnionid();
            PageInfo<String> pageInfo = new PageInfo(hPage);
            List<String> result = pageInfo.getList();
            for (String unionid : result) {
                // 根据unionid查询用户剩余可用的洗护预约券
                List<WashAppointment> washAppointments = washAppointmentMapper.selectShouldExpireByUnionid(unionid);
                int size = washAppointments.size();
                // 需要失效一些张
                try {
                    if(size > diff){
                        int shouldExpireSize = size - diff;
                        for(int j = 0 ; j < shouldExpireSize ; j++){
                            WashAppointment update = new WashAppointment();
                            update.setStatus(WashAppointmentEnum.DIS_APPOINTMENT.getCode());
                            update.setId(washAppointments.get(j).getId());
                            washAppointmentMapper.updateByPrimaryKeySelective(update);
                        }
                    }
                }catch (Exception e){
                    log.error("expireWashVoucherByMonth  =   page = {} ,Data = {}",e, i,JSONObject.toJSONString(result));
                    continue;
                }
            }
        }

        log.info("定时任务根据月份失效匹配不上的优惠券 expireWashVoucherByMonth 结束");
    }

    @Override
    public List<String> pointsExchangeCoupon(PointsExchangeCouponReq requestData, ArrayList<Object> objects) {

        GetRightsReq getRightsReq = new GetRightsReq();
        getRightsReq.setBrandId(requestData.getBrandId());
        getRightsReq.setRightsType(RightsTypeEnum.WASH_RIGHTS.getCode());
        getRightsReq.setCardLevel(requestData.getCardTypeId());
        List<RightsResp> rights = rightsRepository.getRightsByRedisOrDataBase(getRightsReq);

        // 筛选符合条件的 当前品牌卡绑定的门店
        if(StringUtils.isNotBlank(requestData.getWeid())){
            rights = dealFilterStore(rights,requestData.getUnionid(),requestData.getWeid());
        }

        if(CollectionUtils.isEmpty(rights)){
            return new ArrayList<>();
        }

        List<WashCouponPacketEntity> washCouponPacketEntities = JSONObject.parseArray(rights.get(0).getUseRule(), WashCouponPacketEntity.class);
        List<WashAppointment> insertList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(washCouponPacketEntities)){
            for (WashCouponPacketEntity washCouponPacketEntity : washCouponPacketEntities) {
                List<WashCouponPacketEntity.WashCoupon> washCoupons = washCouponPacketEntity.getList();
                if(CollectionUtils.isNotEmpty(washCoupons)){
                    WashCouponPacketEntity.WashCoupon washCoupon = washCoupons.get(0);

                    // 查询voucherBase表  查询到券号    根据券号查询 是否已经存在  已经存在的不处理   不存在的插入
                    List<String> awardIds = new ArrayList<>();
                    awardIds.add(washCoupon.getVoucherId());
                    List<VoucherBase> voucherBases = voucherBaseMapper.selectByUnionIdAndAwardIdsAll(requestData.getUnionid(), awardIds);
                    if(CollectionUtils.isNotEmpty(voucherBases)){
                        for (VoucherBase voucherBase : voucherBases) {
                            String linksource = voucherBase.getLinksource();
                            WashAppointment washAppointment = washAppointmentMapper.selectByVoucherNoLike(linksource);
                            if(washAppointment != null){
                                continue;
                            }

                            if (washCoupon.getType().equals(WashCouponPacketEntity.WASH_TYPE)){
                                WashAppointment insertWashAppointment = new WashAppointment();
                                insertWashAppointment.setId(IdLeaf.getId(IdConstant.WASH_APPOINTMENT));
                                insertWashAppointment.setUnionid(requestData.getUnionid());
                                insertWashAppointment.setCreateTime(new Date());
                                insertWashAppointment.setUpdateTime(new Date());
                                insertWashAppointment.setIsDel(IsDeleteEnum.NORMAL.getCode());
                                insertWashAppointment.setCardTypeId(requestData.getCardTypeId());
                                insertWashAppointment.setStatus(WashAppointmentEnum.APPOINTMENT_SEND.getCode());
                                insertWashAppointment.setBRightsId(rights.get(0).getRightsId());
                                insertWashAppointment.setCouponPacketId(washCouponPacketEntity.getId());
                                insertWashAppointment.setMiniAppOpenId(requestData.getMiniAppOpenId());
                                insertWashAppointment.setCClientVipId(requestData.getCClientVipId());
                                insertWashAppointment.setOpenId(requestData.getOpenId());
                                insertWashAppointment.setVoucherNo(linksource);
                                insertWashAppointment.setActiveTime(DateUtil.formatToStr(new Date(),"yyyyMM"));
                                insertList.add(insertWashAppointment);
                            }
                        }
                    }
                }
            }
        }
        // 2. 进行发放预约记录  获取当前的这个用户的应该看到的权益信息
        template.execute(action->{
            if(CollectionUtils.isNotEmpty(insertList)){
                insertList.forEach(e->washAppointmentMapper.insertSelective(e));
            }
            return action;
        });
        return insertList.stream().map(r->r.getId()).collect(Collectors.toList());
    }

    @Override
    public void washSendMsgFinishJob() {
        // 查询模版是否开启







    }

    private Boolean notActiveSendMessageEnd(VoucherRuleDetailsEntity voucherRuleDetailsEntity, WashAppointment washAppointment) {
        String appid = null;
        String brandName = "";
        String templateId = "";
        String jumpUrl = "";
        // 根据品牌映射成appid
        List<Map> brandConfigs = JSONObject.parseArray(commonConfigs, Map.class);
        for (Map brandConfig : brandConfigs) {
            if(brandConfig.get("weid").toString().equals(voucherRuleDetailsEntity.getWeid()+"")){
                appid = brandConfig.get("appid").toString();
                brandName = brandConfig.get("brandName").toString();
                templateId = brandConfig.get("templateEndId").toString();
                jumpUrl = brandConfig.get("jumpEndUrl").toString();
            }
        }
        if(appid == null){
            log.info("notActiveSendMessageEnd 根据weid未映射到appid");
            return false;
        }

        //
        SendMiniSubMessageContext sendMiniSubMessageContext = new SendMiniSubMessageContext();
        sendMiniSubMessageContext.setToUser(washAppointment.getMiniAppOpenId());
        sendMiniSubMessageContext.setTemplateId(templateId);
        List<SendMiniSubMessageContext.SendMniSubMessageSon> list = new ArrayList<>();
        SendMiniSubMessageContext.SendMniSubMessageSon sendMniSubMessageSon = new SendMiniSubMessageContext.SendMniSubMessageSon();
        LocalDate lastDayOfMonth = LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());
        LocalDate firstDayOfMonth = LocalDate.now().with(TemporalAdjusters.firstDayOfMonth());
        Month month = firstDayOfMonth.getMonth();
        sendMniSubMessageSon.setName("thing4");
//        String title = "您有一张洗衣券，【" + month.getValue() + "." + firstDayOfMonth.getDayOfMonth() + "-" + month.getValue() + "." + lastDayOfMonth.getDayOfMonth() + "】至门店激活后可用,若无法及时到店请前往小程序修改预约时间";
        String title = "洗衣券即将过期，请前往小程序修改预约时间";
        sendMniSubMessageSon.setValue(title);
        list.add(sendMniSubMessageSon);
        SendMiniSubMessageContext.SendMniSubMessageSon mniSubMessageSon = new SendMiniSubMessageContext.SendMniSubMessageSon();
        mniSubMessageSon.setName("thing7");
        mniSubMessageSon.setValue("点击查看详细信息");
        list.add(mniSubMessageSon);
        sendMiniSubMessageContext.setData(list);
        sendMiniSubMessageContext.setPage(jumpUrl);
        boolean b = messageService.sendMiniSubMessage(appid, sendMiniSubMessageContext);

        //

        return b;

    }

    private Boolean notActiveSendMessage(VoucherRuleDetailsEntity voucherRuleDetailsEntity, WashAppointment washAppointment) {
        String appid = null;
        String brandName = "";
        String templateId = "";
        String jumpUrl = "";
        // 根据品牌映射成appid
        List<Map> brandConfigs = JSONObject.parseArray(commonConfigs, Map.class);
        for (Map brandConfig : brandConfigs) {
            if(brandConfig.get("weid").toString().equals(voucherRuleDetailsEntity.getWeid()+"")){
                appid = brandConfig.get("appid").toString();
                brandName = brandConfig.get("brandName").toString();
                templateId = brandConfig.get("templateStartId").toString();
                jumpUrl = brandConfig.get("jumpStartUrl").toString();
            }
        }
        if(appid == null){
            log.info("notActiveSendMessage 根据weid未映射到appid");
            return false;
        }

        //
        SendMiniSubMessageContext sendMiniSubMessageContext = new SendMiniSubMessageContext();
        sendMiniSubMessageContext.setToUser(washAppointment.getMiniAppOpenId());
        sendMiniSubMessageContext.setTemplateId(templateId);
        List<SendMiniSubMessageContext.SendMniSubMessageSon> list = new ArrayList<>();
        SendMiniSubMessageContext.SendMniSubMessageSon sendMniSubMessageSon = new SendMiniSubMessageContext.SendMniSubMessageSon();
        LocalDate lastDayOfMonth = LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());
        LocalDate firstDayOfMonth = LocalDate.now().with(TemporalAdjusters.firstDayOfMonth());
        Month month = firstDayOfMonth.getMonth();
        sendMniSubMessageSon.setName("thing4");
//        String title = "您有一张洗衣券，【" + month.getValue() + "." + firstDayOfMonth.getDayOfMonth() + "-" + month.getValue() + "." + lastDayOfMonth.getDayOfMonth() + "】至门店激活后可用";
        String title = "您的洗衣券"+month.getValue()+"月至门店激活后可用";
        sendMniSubMessageSon.setValue(title);
        list.add(sendMniSubMessageSon);
        SendMiniSubMessageContext.SendMniSubMessageSon mniSubMessageSon = new SendMiniSubMessageContext.SendMniSubMessageSon();
        mniSubMessageSon.setName("thing7");
        mniSubMessageSon.setValue("点击查看详细信息");
        list.add(mniSubMessageSon);
        sendMiniSubMessageContext.setData(list);
        sendMiniSubMessageContext.setPage(jumpUrl);
        boolean b = messageService.sendMiniSubMessage(appid, sendMiniSubMessageContext);

//        //
//        List<SendWxOffMsgContext> sendWxOffMsgContexts = new ArrayList<>();
//        Map<String,String> params = new HashMap<>();
//        params.put("appId",appid);
//        params.put("path",jumpUrl);
//        params.put("first",title);
//        params.put("remark","点击查看详细信息");
//
//        SendWxOffMsgContext sendWxOffMsgContext = new SendWxOffMsgContext();
//        SendWxOffMsgContext.WxTemplateMsgSendDTO build = SendWxOffMsgContext.build(voucherRuleDetailsEntity.getOpenid(), wxOfficeAccountTemplateId, params);
//        sendWxOffMsgContext.setWxTemplateMsgSendDTO(build);
//        sendWxOffMsgContext.setAppid(appid);
//        sendWxOffMsgContext.setUnionId(washAppointment.getUnionid());
//        sendWxOffMsgContexts.add(sendWxOffMsgContext);
//        messageService.sendWxOffMsg(sendWxOffMsgContexts);

        return b;
    }



    private List<UserAllWashCouponResp> sortList(List<UserAllWashCouponResp> result, List<UserAllWashCouponResp> notSendVoucherCouponResp) {
        result.addAll(notSendVoucherCouponResp);
        return result.stream().sorted(new Comparator<UserAllWashCouponResp>() {
            @Override
            public int compare(UserAllWashCouponResp o1, UserAllWashCouponResp o2) {
                if (o2.getStatus() - o1.getStatus() == 0) {
                    // 判断按照从小到大的顺序
                    if(o2.getStatus() == 4){
                        return Integer.parseInt(o1.getActiveTime()) - Integer.parseInt(o2.getActiveTime());
                    }else{
                        return Integer.parseInt(o1.getCouponPacketId()) - Integer.parseInt(o2.getCouponPacketId());
                    }
                } else {
                    return o2.getStatus() - o1.getStatus();
                }
            }
        }).collect(Collectors.toList());
    }

    /**
     * 后台对象转换成前台可使用的对象  仅针对于box这边的 未发送凭证券的预约
     * @param notSendVoucher
     * @return
     */
    private List<UserAllWashCouponResp> coverFrontUseData(List<WashAppointment> notSendVoucher) {
        List<UserAllWashCouponResp> result = new ArrayList<>();
        // 排序 由小到大
        notSendVoucher.stream().sorted(new Comparator<WashAppointment>() {
            @Override
            public int compare(WashAppointment o1, WashAppointment o2) {
                return o1.getStatus()-o2.getStatus();
            }
        });

        for (WashAppointment washAppointment : notSendVoucher) {
            UserAllWashCouponResp userAllWashCouponResp = new UserAllWashCouponResp();

            BRights bRights = bRightsMapper.selectByPrimaryKey(washAppointment.getBRightsId());
            // 获取洗护券的金额
            BigDecimal couponAmount  = getCouponAmountFromApi(bRights.getUseRule(),washAppointment.getCouponPacketId());

            userAllWashCouponResp.setName(bRights.getOutName());
            userAllWashCouponResp.setCouponAmount(couponAmount.toString());
            userAllWashCouponResp.setMemo("激活后90日内可用");
            if(washAppointment.getStatus().equals(WashAppointmentEnum.NOT_APPOINTMENT.getCode())){
                userAllWashCouponResp.setStatus(5);
            }else if(washAppointment.getStatus().equals(WashAppointmentEnum.APPOINTMENT.getCode())){
                userAllWashCouponResp.setStatus(4);
            }else if(washAppointment.getStatus().equals(WashAppointmentEnum.DIS_APPOINTMENT.getCode())){
                userAllWashCouponResp.setStatus(0);
            }
            userAllWashCouponResp.setUnionid(washAppointment.getUnionid());
            userAllWashCouponResp.setLeaveUpdateTimes(washAppointment.getLeaveUpdateTimes());
            userAllWashCouponResp.setActiveTime(washAppointment.getActiveTime());
            userAllWashCouponResp.setCouponPacketId(washAppointment.getCouponPacketId());
            userAllWashCouponResp.setCreateTime(washAppointment.getCreateTime());
            userAllWashCouponResp.setId(washAppointment.getId());
            userAllWashCouponResp.setCardLevelId(washAppointment.getCardLevelId());
            userAllWashCouponResp.setCardTypeId(washAppointment.getCardTypeId());
            userAllWashCouponResp.setIsShow(washAppointment.getIsShow());
            userAllWashCouponResp.setOldCardLevelId(washAppointment.getOldCardLevelId());
            userAllWashCouponResp.setOldCardTypeId(washAppointment.getOldCardTypeId());
            userAllWashCouponResp.setSourceId(washAppointment.getSourceId());
            userAllWashCouponResp.setVoucherNo(washAppointment.getVoucherNo());
            result.add(userAllWashCouponResp);
        }
        return result;
    }

    private BigDecimal getCouponAmountFromApi(String useRule, String couponPacketId) {
        List<WashCouponPacketEntity> washCouponPacketEntities = JSONObject.parseArray(useRule, WashCouponPacketEntity.class);
        for (WashCouponPacketEntity washCouponPacketEntity : washCouponPacketEntities) {
            if(couponPacketId.equals(washCouponPacketEntity.getId())){
                List<WashCouponPacketEntity.WashCoupon> list = washCouponPacketEntity.getList();
                for (WashCouponPacketEntity.WashCoupon washCoupon : list) {
                    if(washCoupon.getType() == WashCouponPacketEntity.WASH_TYPE){
                        String washCouponId = washCoupon.getWashCouponId();
                        String awardId = washCoupon.getVoucherId();
                        String outWashCouponId = washCoupon.getOutWashCouponId();
                        // 调用第三方接口获取数据
                        ViewVoucherRuleConfig viewVoucherRuleConfig = viewVoucherRuleConfigMapper.selectByPrimaryId(awardId);
                        BigDecimal amtAcount = viewVoucherRuleConfig.getAmtAcount();
                        return amtAcount;
                    }
                }
            }
        }
        throw  new RuntimeException("程序有误！");
    }




    private List<UserAllWashCouponResp> findVouchers(List<WashAppointment> alreadySendVoucher) {
        List<UserAllWashCouponResp> result = new ArrayList<>();
        // 获取已经发送的金额和东西
        //TODO  获取券包的信息  获取凭证券和洗护券
        List<VoucherRuleDetailsEntity> vouchersFromApi = findVouchersFromApi(alreadySendVoucher);

        for (WashAppointment washAppointment : alreadySendVoucher) {
            UserAllWashCouponResp userAllWashCouponResp = new UserAllWashCouponResp();
            String voucherNo = washAppointment.getVoucherNo();
            if(StringUtils.isNotBlank(voucherNo)){
                List<UserAllWashCouponResp.UserCouponResp> userCouponResps = new ArrayList<>();
                // 这是小雪的接口 是查询不到洗护券的  洗护券需要单独查询
                for (VoucherRuleDetailsEntity voucherRuleDetailsEntity : vouchersFromApi) {
                    if(voucherNo.contains(voucherRuleDetailsEntity.getCode())){
                        if(voucherNo.split(",").length == 1){
                            // 只有一个数据 那么证明只有凭证券
                            if(voucherNo.split(",")[0].equals(voucherRuleDetailsEntity.getCode())){
                                //凭证券 获取凭证券金额
                                // 获取洗护券的金额
                                BRights bRights = bRightsMapper.selectByPrimaryKey(washAppointment.getBRightsId());
                                BigDecimal couponAmount  = getCouponAmountFromApi(bRights.getUseRule(),washAppointment.getCouponPacketId());
                                userAllWashCouponResp.setCouponAmount(couponAmount+"");
                                // 如果是
                                String isverifyed = voucherRuleDetailsEntity.getIsverifyed();
                                if(isverifyed.equals("N") && Integer.parseInt(voucherRuleDetailsEntity.getValiddate()) < Integer.parseInt(DateUtils.formatDate(new Date(),"yyyyMMdd"))){
                                    userAllWashCouponResp.setStatus(2);
                                }else if(isverifyed.equals("N")
                                        && Integer.parseInt(voucherRuleDetailsEntity.getValiddate()) >= Integer.parseInt(DateUtils.formatDate(new Date(),"yyyyMMdd"))
                                        &&
                                        (Integer.parseInt(voucherRuleDetailsEntity.getValidmindate().equals("")? "99999999":voucherRuleDetailsEntity.getValidmindate()) <= Integer.parseInt(DateUtils.formatDate(new Date(),"yyyyMMdd"))
                                                ||
                                        (Integer.parseInt(voucherRuleDetailsEntity.getInsertdate().substring(0,10).replaceAll("-",""))) <= Integer.parseInt(DateUtils.formatDate(new Date(),"yyyyMMdd"))
                                        )
                                ){
                                    userAllWashCouponResp.setStatus(6);
                                }

                                // 获取当前月当天 和当月最后一天  差值几天
//                                LocalDate lastDayOfMonth = LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());
//                                LocalDate now = LocalDate.now();
//                                int diffDay = lastDayOfMonth.getDayOfMonth() - now.getDayOfMonth();
                                //
//                                String mm = DateUtils.formatDate(new Date(), "MM");
//                                int value = now.getMonth().getValue();
//                                userAllWashCouponResp.setRemark(value+"月剩余"+diffDay+"天，请尽快到门店激活，过期失效");
                                userAllWashCouponResp.setRemark("");
                                userAllWashCouponResp.setMemo("激活后90日内可用");
                            }
                        }else if(voucherNo.split(",").length == 2){
                            // 两位不做操作
                        }else{
                            if(!voucherNo.split(",")[0].equals(voucherRuleDetailsEntity.getCode())){
                                //优惠券
                                UserAllWashCouponResp.UserCouponResp userCouponResp = new UserAllWashCouponResp.UserCouponResp();
                                userCouponResp.setName(voucherRuleDetailsEntity.getVouname());
                                userCouponResp.setNum(1);
                                userCouponResps.add(userCouponResp);
                            }
                        }
                    }
                }

                // 处理单独洗护券  获取到洗护券
                if(voucherNo.split(",").length > 1){
                    // 洗护券券码
                    String washCouponVoucherNo = voucherNo.split(",")[1];
                    // 查询
                    VoucherVirtualBase voucherVirtualBase = voucherVirtualBaseMapper.selectByLinkNo(washCouponVoucherNo);
                    if(voucherVirtualBase != null){
                        //洗护券
                        userAllWashCouponResp.setCouponAmount(voucherVirtualBase.getAmt()+"");
                        // 如果是
                        String status = voucherVirtualBase.getStatus();
                        // 使用状态
                        String consumestate = voucherVirtualBase.getConsumestate();
                        if(status.equals("Y") &&
                                voucherVirtualBase.getValiddate().intValue() < Integer.parseInt(DateUtils.formatDate(new Date(),"yyyyMMdd"))){
                            userAllWashCouponResp.setStatus(1);
                        }else if(status.equals("Y") && consumestate.equals("N")
                                && voucherVirtualBase.getValiddate().intValue() >= Integer.parseInt(DateUtils.formatDate(new Date(),"yyyyMMdd"))
                        ){
                            userAllWashCouponResp.setStatus(7);
                        }else if(consumestate.equals("Y")){
                            userAllWashCouponResp.setStatus(3);
                        }
                        userAllWashCouponResp.setMemo("激活后90日内可用");
                        Date validmindate = voucherVirtualBase.getInsertdate();
                        BigDecimal validdate = voucherVirtualBase.getValiddate();
                        if(ObjectUtils.isNotEmpty(validmindate) && ObjectUtils.isNotEmpty(validdate)){
                            String startDate = DateUtils.formatDate(validmindate, "yyyy.MM.dd");
                            String endDate = DateUtils.formatDate(DateUtils.parseDate(validdate+"", "yyyyMMdd"), "yyyy.MM.dd");
                            userAllWashCouponResp.setMemo(startDate+"-"+endDate);
                        }

                        StringBuffer jumpUrl = new StringBuffer();

                        // 设置jumoUrl 跳转url
                        List<Cardmain> cardmains = cardmainMapper.selectByOpenId(voucherVirtualBase.getOpenid());
                        if(CollectionUtils.isNotEmpty(cardmains)){
                            String remark = cardmains.get(0).getRemark();
                            List<WashCouponJumpUrlDto> washCouponJumpUrlDtos = new ArrayList<>();
                            WashCouponJumpUrlDto washCouponJumpUrlDto = new WashCouponJumpUrlDto();
                            washCouponJumpUrlDto.setCoupon_code(voucherVirtualBase.getLinkno());
                            washCouponJumpUrlDto.setCoupon_id(voucherVirtualBase.getOutVoucherId());
                            washCouponJumpUrlDtos.add(washCouponJumpUrlDto);
                            jumpUrl.append("pagesA/pages/coupon/coupon?tpc=oABaUXppm0Xrqf&appid=wx6cbb0e084c69041d&groupCardNo=");
                            jumpUrl.append(remark);
                            jumpUrl.append("&coupon=");
                            jumpUrl.append(JSONObject.toJSONString(washCouponJumpUrlDtos));

                            userAllWashCouponResp.setJumpUrl(jumpUrl.toString());
                        }
                    }
                }
                userAllWashCouponResp.setList(userCouponResps);
            }


            BRights bRights = bRightsMapper.selectByPrimaryKey(washAppointment.getBRightsId());
            userAllWashCouponResp.setName(bRights.getOutName());
            //

            userAllWashCouponResp.setName(bRights.getOutName());
            userAllWashCouponResp.setUnionid(washAppointment.getUnionid());
            userAllWashCouponResp.setLeaveUpdateTimes(washAppointment.getLeaveUpdateTimes());
            userAllWashCouponResp.setActiveTime(washAppointment.getActiveTime());
            userAllWashCouponResp.setCouponPacketId(washAppointment.getCouponPacketId());
            userAllWashCouponResp.setId(washAppointment.getId());
            userAllWashCouponResp.setCreateTime(washAppointment.getCreateTime());
            userAllWashCouponResp.setCardLevelId(washAppointment.getCardLevelId());
            userAllWashCouponResp.setCardTypeId(washAppointment.getCardTypeId());
            userAllWashCouponResp.setIsShow(washAppointment.getIsShow());
            userAllWashCouponResp.setOldCardLevelId(washAppointment.getOldCardLevelId());
            userAllWashCouponResp.setOldCardTypeId(washAppointment.getOldCardTypeId());
            userAllWashCouponResp.setSourceId(washAppointment.getSourceId());
            userAllWashCouponResp.setVoucherNo(washAppointment.getVoucherNo());
            result.add(userAllWashCouponResp);
        }
        return result;
    }



    private WashAppointment loseEffectiveCoupon(WashAppointment washAppointment) {
        // 根据预约券id 失效凭证券信息
        String voucherNos = washAppointment.getVoucherNo();
        if(StringUtils.isNotBlank(voucherNos)){
            if(voucherNos.split(",").length > 1){
                // 相当于已经发送了真正的洗护券  不需要操作了
            }else{
                String voucherNo = voucherNos.split(",")[0];
                VoucherRuleDetailsContext voucherRuleDetailsContext = new VoucherRuleDetailsContext();
                voucherRuleDetailsContext.setVoucherNos(voucherNo);
                List<VoucherRuleDetailsEntity> voucherByVoucherNos = iVoucherService.getVoucherByVoucherNo(voucherRuleDetailsContext);
                if(CollectionUtils.isNotEmpty(voucherByVoucherNos)){
                    // 未核销  进行失效
                    if(voucherByVoucherNos.get(0).getIsverifyed().equals("N")){
                        List<String> voucherList = new ArrayList<>();
                        voucherList.add(voucherNo);
                        VoucherInvalidContext voucherInvalidContext = new VoucherInvalidContext();
                        voucherInvalidContext.setVoucherNos(voucherList);
                        Boolean invalid = iVoucherService.invalid(voucherInvalidContext);
                        if(!invalid){
                            log.info("失效失败!" +JSONObject.toJSONString(washAppointment));
                        }
                        return washAppointment;
                    }
                }
            }
        }
        return null;
    }

    private List<VoucherRuleDetailsEntity> findVouchersFromApi(List<WashAppointment> washAppointments) {
        List<String> voucherNos = new ArrayList<>();
        // 根据 预约券id查询凭证券和优惠券信息
        for (WashAppointment washAppointment : washAppointments) {
            boolean notBlank = StringUtils.isNotBlank(washAppointment.getVoucherNo());
            if(notBlank){
                String[] split = washAppointment.getVoucherNo().split(",");
                voucherNos.addAll(Arrays.asList(split));
            }
        }
        String vouchers = voucherNos.stream().collect(Collectors.joining(","));
        if(StringUtils.isBlank(vouchers)){
            log.error("findVouchersFromApi = {}",vouchers);
            return  new ArrayList<>();
        }
        VoucherRuleDetailsContext voucherRuleDetailsContext = new VoucherRuleDetailsContext();
        voucherRuleDetailsContext.setVoucherNos(vouchers);
        List<VoucherRuleDetailsEntity> voucherByVoucherNos = iVoucherService.getVoucherByVoucherNo(voucherRuleDetailsContext);
        return voucherByVoucherNos;
    }

    private List<WashAppointment> checkAlreadySendCouponIsActive(List<WashAppointment> alreadySendCoupon) {
        List<WashAppointment> result = new ArrayList<>();
        // 查询这些券 如果是未激活的 直接进行失效
        //TODO  并且失效这些未核销的
        for (WashAppointment washAppointment : alreadySendCoupon) {
            // 查询当前的用户的洗护券
            WashAppointment washAppointment1 = loseEffectiveCoupon(washAppointment);
            //如果是空的话  这个券 已经激活了
            if(washAppointment1 != null){
                result.add(washAppointment1);
            }
        }
        return result;
    }


    /**
     * 发送凭证券
     * @param washAppointment
     * @param
     */
    private void sendWashCoupon(WashAppointment washAppointment) {
        //调用微商城接口发送  凭证券
        BRights bRights = bRightsMapper.selectByPrimaryKey(washAppointment.getBRightsId());
        List<WashCouponPacketEntity> washCouponPacketEntities = JSONObject.parseArray(bRights.getUseRule(), WashCouponPacketEntity.class);
        for (WashCouponPacketEntity washCouponPacketEntity : washCouponPacketEntities) {
            if(washAppointment.getCouponPacketId().equals(washCouponPacketEntity.getId())){
                List<WashCouponPacketEntity.WashCoupon> list = washCouponPacketEntity.getList();
                for (WashCouponPacketEntity.WashCoupon washCoupon : list) {
                    if(washCoupon.getType() == WashCouponPacketEntity.WASH_TYPE){
                        String awardId = washCoupon.getVoucherId();
                        String ruleId = washCoupon.getRuleId();
                        // 调用第三方接口发送优惠券
                        JICVoucherSendReqEntity jicVoucherSendReqEntity = new JICVoucherSendReqEntity();
                        jicVoucherSendReqEntity.setOpenId(washAppointment.getOpenId());
                        List<RulesList> rulesLists = new ArrayList<>();
                        RulesList rules = new RulesList();
                        rules.setAwardId(Long.parseLong(awardId));
                        rules.setNum(1L);
                        rulesLists.add(rules);
                        jicVoucherSendReqEntity.setRulesList(rulesLists);
                        JICVoucherSendRespEntity jicVoucherSendRespEntity = iVoucherService.sendVoucher(jicVoucherSendReqEntity);
                        if (jicVoucherSendRespEntity != null && jicVoucherSendRespEntity.getCode() == 200 && ObjectUtils.isNotEmpty(jicVoucherSendRespEntity.getData())) {
                            String voucher = jicVoucherSendRespEntity.getData().getR_voucher();
                            if (StringUtils.isBlank(voucher)) {
                                log.error("发券失败 msg = {}, e = {}", jicVoucherSendRespEntity.getData().getRetmsg(), jicVoucherSendRespEntity);
                                return ;
                            }
                            List<Map> maps = JSONObject.parseArray(voucher, Map.class);
                            String voucherCode= "";
                            for (Map map : maps) {
                                voucherCode = map.get("code").toString();
                            }
                            String finalVoucherCode = voucherCode;
                            // 生成日期
                            LocalDate lastDayOfMonth = LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());
                            int value = lastDayOfMonth.getMonth().getValue();
                            String monthValue = "";
                            if(value >=10){
                                monthValue = value+"";
                            }else{
                                monthValue = "0"+value;
                            }
                            String lastDayOfMonthStr = lastDayOfMonth.getYear() + "" + monthValue + "" + lastDayOfMonth.getDayOfMonth();

                            template.execute(action->{
                                //更新数据
                                cVouchersMapper.updateValidDateById(finalVoucherCode,Integer.parseInt(lastDayOfMonthStr));
                                voucherBaseMapper.updateValidDateByVoucherNo(finalVoucherCode,Integer.parseInt(lastDayOfMonthStr));

                                WashAppointment update = new WashAppointment();
                                update.setVoucherNo(finalVoucherCode);
                                update.setId(washAppointment.getId());
                                update.setUpdateTime(new Date());
                                washAppointmentMapper.updateByPrimaryKeySelective(update);
                                washAppointment.setVoucherNo(finalVoucherCode);
                                return action;
                            });
                        }
                    }
                }
            }
        }
    }

}
