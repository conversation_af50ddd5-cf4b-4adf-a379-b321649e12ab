package org.springcenter.marketing.modules.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * @TableName send_award_log
 */
@TableName(value ="send_award_log")
@Data
public class SendAwardLog implements Serializable {
    /**
     * 主键
     */
    @TableId
    private Integer id;

    /**
     * unionid
     */
    @TableField(value = "unionid")
    private String unionid;

    /**
     * 卡号
     */
    @TableField(value = "cardno")
    private String cardno;

    /**
     * 
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 0 正常  1 删除
     */
    @TableField(value = "is_del")
    private Integer isDel;

    /**
     * 类型  0  内部券   1  微信券  2  积分  3 内部商品  4 外部商品
     */
    @TableField(value = "award_type")
    private Integer awardType;

    /**
     * 奖励名称
     */
    @TableField(value = "award_name")
    private String awardName;

    /**
     * 奖励编码  券则为券awardId  微信为微信券id  积分为积分数量   内部商品为 productCode   外部商品不定
     */
    @TableField(value = "award_code")
    private String awardCode;

    /**
     * 发放个数
     */
    @TableField(value = "award_num")
    private Integer awardNum;

    /**
     * 奖励id
     */
    @TableField(value = "award_config_id")
    private Integer awardConfigId;

    /**
     * 唯一发放单号
     */
    @TableField(value = "ref_no")
    private String refNo;

    /**
     * 0  未发放  1 已发放
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 存储的发放信息 ，  内部券为券号  微信券id  积分数量  快递号
     */
    @TableField(value = "common_info")
    private String commonInfo;

}