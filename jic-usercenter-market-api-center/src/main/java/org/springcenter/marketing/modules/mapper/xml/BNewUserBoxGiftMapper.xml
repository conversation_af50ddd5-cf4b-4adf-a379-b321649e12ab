<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.marketing.modules.mapper.BNewUserBoxGiftMapper">
  <resultMap id="BaseResultMap" type="org.springcenter.marketing.modules.model.BNewUserBoxGift">
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="RIGHTS_TYPE" jdbcType="DECIMAL" property="rightsType" />
    <result column="UNIONID" jdbcType="VARCHAR" property="unionid" />
    <result column="STATUS" jdbcType="DECIMAL" property="status" />
    <result column="SEND_NUM" jdbcType="DECIMAL" property="sendNum" />
    <result column="SKU" jdbcType="VARCHAR" property="sku" />
    <result column="BOX_GIFT_NAME" jdbcType="VARCHAR" property="boxGiftName" />
    <result column="PRICE" jdbcType="DECIMAL" property="price" />
    <result column="OUT_NO" jdbcType="VARCHAR" property="outNo" />
    <result column="B_RIGHTS_ID" jdbcType="VARCHAR" property="bRightsId" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="IS_DEL" jdbcType="DECIMAL" property="isDel" />
    <result column="SEND_STATUS" jdbcType="DECIMAL" property="sendStatus" />
    <result column="START_TIME" jdbcType="TIMESTAMP" property="startTime" />
    <result column="END_TIME" jdbcType="TIMESTAMP" property="endTime" />
    <result column="CONSUME_OUT_NO" jdbcType="VARCHAR" property="consumeOutNo" />

    <result column="CONSUME_PARAMS" jdbcType="VARCHAR" property="consumeParams" />
    <result column="EB_ORDER" jdbcType="VARCHAR" property="ebOrder" />

    <result column="BOX_SN" jdbcType="VARCHAR" property="boxSn" />
    <result column="CUS_PHONE" jdbcType="VARCHAR" property="cusPhone" />
    <result column="LOGISTICS_STATUS" jdbcType="DECIMAL" property="logisticsStatus" />

    <result column="EXPRESS_NO" jdbcType="DECIMAL" property="expressNo" />
    <result column="SEND_BOX_GIFT_TIME" jdbcType="DECIMAL" property="sendBoxGiftTime" />

    <result column="USE_SEND_TYPE" jdbcType="DECIMAL" property="useSendType" />

    <result column="ARC_BRAND_ID" jdbcType="DECIMAL" property="arcBrandId" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, RIGHTS_TYPE, UNIONID, STATUS, SEND_NUM, SKU, BOX_GIFT_NAME, PRICE, OUT_NO,
    B_RIGHTS_ID, CREATE_TIME, UPDATE_TIME, IS_DEL, SEND_STATUS, START_TIME, END_TIME,CONSUME_OUT_NO,CONSUME_PARAMS
        ,EB_ORDER,BOX_SN,CUS_PHONE,LOGISTICS_STATUS,EXPRESS_NO,SEND_BOX_GIFT_TIME,USE_SEND_TYPE,ARC_BRAND_ID
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from b_new_user_box_gift
    where ID = #{id,jdbcType=VARCHAR}
  </select>

  <select id="selectAvialableUserRightsBoxGiftByUnionId"
          resultMap="BaseResultMap">

    select
    burcc.ID, burcc.RIGHTS_TYPE, burcc.UNIONID, burcc.STATUS, burcc.SEND_NUM, burcc.SKU, burcc.BOX_GIFT_NAME, burcc.PRICE, burcc.OUT_NO,
    burcc.B_RIGHTS_ID, burcc.CREATE_TIME, burcc.UPDATE_TIME, burcc.IS_DEL, burcc.SEND_STATUS, burcc.START_TIME, burcc.END_TIME,burcc.CONSUME_OUT_NO,burcc.CONSUME_PARAMS,burcc.EB_ORDER,
    burcc.BOX_SN,burcc.CUS_PHONE,burcc.LOGISTICS_STATUS,burcc.EXPRESS_NO,burcc.SEND_BOX_GIFT_TIME,burcc.USE_SEND_TYPE,burcc.ARC_BRAND_ID
    from b_new_user_box_gift burcc left join b_rights br on burcc.B_RIGHTS_ID = br.ID
    inner join b_new_user_member_card bumc on bumc.out_no = burcc.out_no
    where  burcc.IS_DEL = 0
    and burcc.UNIONID = #{unionid}
    and br.rights_type = #{rightType} and br.B_MEMBER_CARD_ID = #{bMemberCardId}
    and burcc.send_status = 0 and burcc.STATUS = 1
    <if test="now != null">
      and #{now} >= burcc.start_time and burcc.end_time >= #{now}
    </if>
    <if test="outNo != null">
      and burcc.OUT_NO = #{outNo}
    </if>
    order by burcc.id
  </select>
  <select id="selectUserRightsBoxGiftByUnionId"
          resultMap="BaseResultMap">

    select
    burcc.ID, burcc.RIGHTS_TYPE, burcc.UNIONID, burcc.STATUS, burcc.SEND_NUM, burcc.SKU, burcc.BOX_GIFT_NAME, burcc.PRICE, burcc.OUT_NO,
    burcc.B_RIGHTS_ID, burcc.CREATE_TIME, burcc.UPDATE_TIME, burcc.IS_DEL, burcc.SEND_STATUS, burcc.START_TIME, burcc.END_TIME,burcc.CONSUME_OUT_NO,burcc.CONSUME_PARAMS,burcc.EB_ORDER,
    burcc.BOX_SN,burcc.CUS_PHONE,burcc.LOGISTICS_STATUS,burcc.EXPRESS_NO,burcc.SEND_BOX_GIFT_TIME,burcc.USE_SEND_TYPE,burcc.ARC_BRAND_ID
    from b_new_user_box_gift burcc left join b_rights br on burcc.B_RIGHTS_ID = br.ID
    inner join b_new_user_member_card bumc on bumc.out_no = burcc.out_no
    where  burcc.IS_DEL = 0
    and burcc.UNIONID = #{unionid}
    and br.rights_type = #{rightType} and br.B_MEMBER_CARD_ID = #{bMemberCardId}
    <if test="now != null">
      and #{now} >= burcc.start_time and burcc.end_time >= #{now}
    </if>
    <if test="outNo != null">
      and burcc.OUT_NO = #{outNo}
    </if>
    order by burcc.id
  </select>
  <select id="selectByConsumeOutNos" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"></include> from b_new_user_box_gift
    where CONSUME_OUT_NO in
    <foreach collection="consumeOutNos" item="item" close=")" open="(" separator=",">
      #{item}
    </foreach>
  </select>
  <select id="selectUnSuccessSendOrSendNotUpdate"
          resultMap="BaseResultMap">

    select <include refid="Base_Column_List"></include> from b_new_user_box_gift
    where STATUS = 4 and IS_DEL = 0 and CONSUME_OUT_NO is not null and SEND_STATUS = 1

  </select>
  <select id="syncLogisticsStatus" resultMap="BaseResultMap">
    SELECT <include refid="Base_Column_List"></include> FROM b_new_user_box_gift
    WHERE LOGISTICS_STATUS = 1
    AND UPDATE_TIME > DATE_SUB(NOW(), INTERVAL 30 DAY)
    AND STATUS = 4
    AND EXPRESS_NO IS NULL
    <if test="id != null and id != '' ">
      AND id = #{id}
    </if>
  </select>

  <select id="getBoxGiftList" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"></include> from b_new_user_box_gift
    <where>
            and is_del  = 0
            and STATUS = 4
        <if test="cusPhone != null and cusPhone != ''">
            and cus_phone = #{cusPhone}
        </if>
      <if test="boxSn != null and boxSn != ''">
        and BOX_SN = #{boxSn}
      </if>
      <if test="logisticsStatus != null   and logisticsStatus ==  0 ">
        and EB_ORDER is null
      </if>
      <if test="logisticsStatus != null   and logisticsStatus !=  0 ">
        and EB_ORDER is not null and LOGISTICS_STATUS = #{logisticsStatus}
      </if>
      <if test="startTime != null ">
        and  CREATE_TIME >= #{startTime}
      </if>
      <if test="endTime != null ">
        and    #{endTime} >= CREATE_TIME
      </if>
        order by CREATE_TIME desc
    </where>

  </select>
  <select id="selectByBoxSns" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"></include> from b_new_user_box_gift
    where BOX_SN in
    <foreach collection="boxSns" item="item" close=")" open="(" separator=",">
      #{item}
    </foreach>
    and status != 0
  </select>
  <select id="selectByOutNo" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"></include> from b_new_user_box_gift
    where out_no  = #{outNo}
  </select>
  <select id="selectByConsumeOutNosAndStatus"
          resultMap="BaseResultMap">
    select <include refid="Base_Column_List"></include> from b_new_user_box_gift
    where CONSUME_OUT_NO in
    <foreach collection="consumeOutNos" item="item" close=")" open="(" separator=",">
      #{item}
    </foreach>
    and status  != 0
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from b_new_user_box_gift
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="org.springcenter.marketing.modules.model.BNewUserBoxGift">
    insert into b_new_user_box_gift (ID, RIGHTS_TYPE, UNIONID,
      STATUS, SEND_NUM, SKU,
      BOX_GIFT_NAME, PRICE, OUT_NO, 
      B_RIGHTS_ID, CREATE_TIME, UPDATE_TIME, 
      IS_DEL, SEND_STATUS, START_TIME, 
      END_TIME)
    values (#{id,jdbcType=VARCHAR}, #{rightsType,jdbcType=DECIMAL}, #{unionid,jdbcType=VARCHAR}, 
      #{status,jdbcType=DECIMAL}, #{sendNum,jdbcType=DECIMAL}, #{sku,jdbcType=VARCHAR}, 
      #{boxGiftName,jdbcType=VARCHAR}, #{price,jdbcType=DECIMAL}, #{outNo,jdbcType=VARCHAR}, 
      #{bRightsId,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{isDel,jdbcType=DECIMAL}, #{sendStatus,jdbcType=DECIMAL}, #{startTime,jdbcType=TIMESTAMP}, 
      #{endTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="org.springcenter.marketing.modules.model.BNewUserBoxGift">
    insert into b_new_user_box_gift
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="rightsType != null">
        RIGHTS_TYPE,
      </if>
      <if test="unionid != null">
        UNIONID,
      </if>
      <if test="status != null">
        STATUS,
      </if>
      <if test="sendNum != null">
        SEND_NUM,
      </if>
      <if test="sku != null">
        SKU,
      </if>
      <if test="boxGiftName != null">
        BOX_GIFT_NAME,
      </if>
      <if test="price != null">
        PRICE,
      </if>
      <if test="outNo != null">
        OUT_NO,
      </if>
      <if test="bRightsId != null">
        B_RIGHTS_ID,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="isDel != null">
        IS_DEL,
      </if>
      <if test="sendStatus != null">
        SEND_STATUS,
      </if>
      <if test="startTime != null">
        START_TIME,
      </if>
      <if test="endTime != null">
        END_TIME,
      </if>

      <if test="consumeOutNo != null">
        CONSUME_OUT_NO,
      </if>

      <if test="consumeParams != null">
        CONSUME_PARAMS,
      </if>

      <if test="ebOrder != null">
        EB_ORDER,
      </if>

      <if test="boxSn != null">
        BOX_SN,
      </if>
      <if test="cusPhone != null">
        CUS_PHONE,
      </if>
      <if test="logisticsStatus != null">
        LOGISTICS_STATUS,
      </if>

      <if test="expressNo != null">
        EXPRESS_NO,
      </if>

      <if test="sendBoxGiftTime != null">
        SEND_BOX_GIFT_TIME,
      </if>
      <if test="useSendType != null">
        USE_SEND_TYPE,
      </if>
      <if test="arcBrandId != null">
        ARC_BRAND_ID,
      </if>

    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="rightsType != null">
        #{rightsType,jdbcType=DECIMAL},
      </if>
      <if test="unionid != null">
        #{unionid,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=DECIMAL},
      </if>
      <if test="sendNum != null">
        #{sendNum,jdbcType=DECIMAL},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="boxGiftName != null">
        #{boxGiftName,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="outNo != null">
        #{outNo,jdbcType=VARCHAR},
      </if>
      <if test="bRightsId != null">
        #{bRightsId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDel != null">
        #{isDel,jdbcType=DECIMAL},
      </if>
      <if test="sendStatus != null">
        #{sendStatus,jdbcType=DECIMAL},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>

      <if test="consumeOutNo != null">
        #{consumeOutNo},
      </if>
      <if test="consumeParams != null">
        #{consumeParams},
      </if>

      <if test="ebOrder != null">
        #{ebOrder},
      </if>

      <if test="boxSn != null">
        #{boxSn},
      </if>
      <if test="cusPhone != null">
        #{cusPhone},
      </if>
      <if test="logisticsStatus != null">
        #{logisticsStatus},
      </if>

      <if test="expressNo != null">
        #{expressNo},
      </if>

      <if test="sendBoxGiftTime != null">
        #{sendBoxGiftTime},
      </if>
      <if test="useSendType != null">
        #{useSendType},
      </if>
      <if test="arcBrandId != null">
        #{arcBrandId},
      </if>

    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="org.springcenter.marketing.modules.model.BNewUserBoxGift">
    update b_new_user_box_gift
    <set>
      <if test="rightsType != null">
        RIGHTS_TYPE = #{rightsType,jdbcType=DECIMAL},
      </if>
      <if test="unionid != null">
        UNIONID = #{unionid,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        STATUS = #{status,jdbcType=DECIMAL},
      </if>
      <if test="sendNum != null">
        SEND_NUM = #{sendNum,jdbcType=DECIMAL},
      </if>
      <if test="sku != null">
        SKU = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="boxGiftName != null">
        BOX_GIFT_NAME = #{boxGiftName,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        PRICE = #{price,jdbcType=DECIMAL},
      </if>
      <if test="outNo != null">
        OUT_NO = #{outNo,jdbcType=VARCHAR},
      </if>
      <if test="bRightsId != null">
        B_RIGHTS_ID = #{bRightsId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDel != null">
        IS_DEL = #{isDel,jdbcType=DECIMAL},
      </if>
      <if test="sendStatus != null">
        SEND_STATUS = #{sendStatus,jdbcType=DECIMAL},
      </if>
      <if test="startTime != null">
        START_TIME = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        END_TIME = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="consumeOutNo != null">
        CONSUME_OUT_NO = #{consumeOutNo},
      </if>
      <if test="consumeParams != null">
        CONSUME_PARAMS = #{consumeParams},
      </if>

      <if test="ebOrder != null">
        EB_ORDER = #{ebOrder},
      </if>

      <if test="boxSn != null">
        BOX_SN = #{boxSn},
      </if>
      <if test="cusPhone != null">
        CUS_PHONE = #{cusPhone},
      </if>
      <if test="logisticsStatus != null">
        LOGISTICS_STATUS = #{logisticsStatus},
      </if>
      <if test="expressNo != null">
        EXPRESS_NO = #{expressNo},
      </if>

      <if test="sendBoxGiftTime != null">
        SEND_BOX_GIFT_TIME = #{sendBoxGiftTime},
      </if>
      <if test="useSendType != null">
        USE_SEND_TYPE = #{useSendType},
      </if>

      <if test="arcBrandId != null">
        ARC_BRAND_ID = #{arcBrandId},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="org.springcenter.marketing.modules.model.BNewUserBoxGift">
    update B_NEW_USER_BOX_GIFT
    set RIGHTS_TYPE = #{rightsType,jdbcType=DECIMAL},
      UNIONID = #{unionid,jdbcType=VARCHAR},
      STATUS = #{status,jdbcType=DECIMAL},
      SEND_NUM = #{sendNum,jdbcType=DECIMAL},
      SKU = #{sku,jdbcType=VARCHAR},
      BOX_GIFT_NAME = #{boxGiftName,jdbcType=VARCHAR},
      PRICE = #{price,jdbcType=DECIMAL},
      OUT_NO = #{outNo,jdbcType=VARCHAR},
      B_RIGHTS_ID = #{bRightsId,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      IS_DEL = #{isDel,jdbcType=DECIMAL},
      SEND_STATUS = #{sendStatus,jdbcType=DECIMAL},
      START_TIME = #{startTime,jdbcType=TIMESTAMP},
      END_TIME = #{endTime,jdbcType=TIMESTAMP}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateEbNumIsNull" parameterType="org.springcenter.marketing.modules.model.BNewUserBoxGift">
    update     b_new_user_box_gift set EB_ORDER = null where id = #{id}
  </update>
    <update id="updateBySku">
      update  b_new_user_box_gift set SKU = #{newSku} , BOX_GIFT_NAME = #{newName} where is_del = 0 and status = 1 and sku = #{originSku} and SEND_NUM = #{sendNum} and arc_brand_id = #{arcBrandId}

    </update>
</mapper>