<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.marketing.modules.bojunMapper.FaVipintegralFtpMapper">


    <select id="sumIntegralSixMonth" resultType="java.math.BigDecimal">
        SELECT sum(integral_up) FROM "FA_VIPINTEGRAL_FTP" where C_VIP_ID =#{clientVipId}
                                                            and CREATIONDATE >= sysdate - 180
                                                            and ISACTIVE = 'Y'
                                                            and C_VIPINTEGRAL_REASON_ID not in (3,22)
                                                            and description not like '由VIP退货返还最近一次升级积分%'
    </select>
</mapper>