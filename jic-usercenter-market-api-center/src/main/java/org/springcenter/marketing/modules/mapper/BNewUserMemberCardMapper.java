package org.springcenter.marketing.modules.mapper;


import org.apache.ibatis.annotations.Param;
import org.springcenter.marketing.modules.model.BNewUserMemberCard;

import java.util.Date;
import java.util.List;

public interface BNewUserMemberCardMapper {

    int updateUserStatus(String id);

    int deleteByPrimaryKey(String id);

    int insert(BNewUserMemberCard record);

    int insertSelective(BNewUserMemberCard record);

    BNewUserMemberCard selectByPrimaryKey(String id);

    List<BNewUserMemberCard> selectListSelective(BNewUserMemberCard record);

    int updateByPrimaryKeySelective(BNewUserMemberCard record);

    int updateByPrimaryKey(BNewUserMemberCard record);

    //查询前一天有效的还剩有box服务次数的用户数据

    //修改推送状态为1，已推送
    int updateForSubscription(String userId);

    //通过userId查询开卡的开始和过期时间
    List<BNewUserMemberCard> queryCardByUserId(String userId);

    //初始化推送状态为0
    int updateForFormer(@Param("scheduleIds")List<String> scheduleIds);


    List<BNewUserMemberCard> selectValidListSelective(@Param("nowTime")String nowTime,@Param("unionid")String unionid);


    List<BNewUserMemberCard> selectAvailableMemberCardByUserId(@Param("unionid") String unionid,
                                                               @Param("bMemberCardId") String bMemberCardId,
                                                               @Param("nowTime") Date nowTime);

    void batchUpdateStatutsByDate(@Param("now") Date date);


    List<BNewUserMemberCard> findShouldUpdateStatusByDate(@Param("now") Date date);

    List<BNewUserMemberCard> selectAvailableMemberCardByUserIdAndCardType(@Param("unionid") String unionid,
                                                                       @Param("applicationParty") Integer applicationParty,
                                                                       @Param("nowTime") Date nowTime);

    List<BNewUserMemberCard> selectAllMemberCardByUserIdAndCardType(@Param("unionid") String unionid,
                                                                          @Param("applicationParty") Integer applicationParty,
                                                                          @Param("nowTime") Date nowTime);

    List<BNewUserMemberCard> selectByUnionidAndCardId(@Param("unionid") String unionid, @Param("bMemberCardId") String bMemberCardId);

    BNewUserMemberCard selectBySubId(@Param("subId") String subId);

    List<BNewUserMemberCard> selectBySubIds(@Param("subIds") List<String> subIds);

    List<BNewUserMemberCard> selectByOutNo(@Param("outNo") String outNo);
}
