package org.springcenter.marketing.modules.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 问卷模版
 * @TableName QUES_QUESTION_TEMPLATE
 */
@TableName("ques_question_template")
@Data
public class QuesQuestionTemplate implements Serializable {
    /**
     * 主键id
     */
    @TableField("id")
    private String id;

    /**
     * 场景id
     */
    @TableField("QUES_SCENE_ID")
    private String quesSceneId;

    /**
     * 页面标题
     */
    @TableField("PAGE_TITLE")
    private String pageTitle;

    /**
     * 备注
     */
    @TableField("REMARK")
    private String remark;

    /**
     * 场景数据 （举例 券类的 优惠券awardId）
     */
    @TableField("SCENE_PARAMS")
    private String sceneParams;

    /**
     * 形式  0 页面  1 组件
     */
    @TableField("TYPE")
    private Integer type;

    /**
     * 标题
     */
    @TableField("TITLE")
    private String title;

    /**
     * 副标题
     */
    @TableField("SUB_TITLE")
    private String subTitle;

    /**
     * 组件页面  0 生日权益领取页  1 积分兑换成功页
     */
    @TableField("ASSEMBLY_PAGE")
    private Integer assemblyPage;

    /**
     * 配置信息  频率设置和奖励设置
     */
    @TableField("ASSEMBLY_JSON")
    private String assemblyJson;

    /**
     * 0 不自动  1自动
     */
    @TableField("AUTO_EJECT")
    private Integer autoEject;

    /**
     * 0 禁用  1启用
     */
    @TableField("STATUS")
    private Integer status;

    /**
     * 跳转类型 0 小程序 1 h5
     */
    @TableField("JUMP_TYPE")
    private Integer jumpType;

    /**
     * appid   跳转小程序必填 
     */
    @TableField("APP_ID")
    private String appId;

    /**
     * 小程序路径  跳转小程序必填
     */
    @TableField("PAGE_PATH")
    private String pagePath;

    /**
     * 跳转h5路径  跳转h5时候必填
     */
    @TableField("H5_URL")
    private String h5Url;

    /**
     * 结束按钮文案
     */
    @TableField("END_BUTTON")
    private String endButton;

    /**
     * 创建时间
     */
    @TableField("CREATE_TIME")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("UPDATE_TIME")
    private Date updateTime;

    /**
     * 0 未删除  1 已删除
     */
    @TableField("IS_DEL")
    private Integer isDel;

    /**
     * 背景图或者 入口图
     */
    @TableField("IMG_URL")
    private String imgUrl;


    @TableField("CREATE_BY")
    private String createBy;

    @TableField("SCENE_PARAMS_TYPE")
    private Integer sceneParamsType;

    @TableField("OVER_CONTENT")
    private String overContent;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}