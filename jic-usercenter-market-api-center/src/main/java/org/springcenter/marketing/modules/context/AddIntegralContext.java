package org.springcenter.marketing.modules.context;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date:2023/3/1 9:37
 */
@Data
public class AddIntegralContext {

    @ApiModelProperty(value = "活动id")
    private Integer activityId;

    @ApiModelProperty(value = "品牌Id")
    private String brandId;

    @ApiModelProperty(value = "积分值")
    private Integer integral;

    @ApiModelProperty(value = "操作原因")
    private String reason;

    @ApiModelProperty(value = "微信开放平台唯一标识")
    private String serialNo;

    @ApiModelProperty(value = "操作来源")
    private String source;

    @ApiModelProperty(value = "实时 1,非实时 2")
    private Integer sync;

    @ApiModelProperty(value = "渠道:微商城 1,线下 2")
    private Integer type;

    @ApiModelProperty(value = "微信开放平台唯一标识")
    private String unionId;

    @ApiModelProperty(value = "会员id")
    private String vipId;

    @ApiModelProperty(value = "活动门店,示例值(5DA22112-(5DA221)上海港汇恒隆广场(JNBY))")
    private String storeName;
}
