package org.springcenter.marketing.modules.convert;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.springcenter.marketing.api.dto.miniapp.NoteListItemResp;
import org.springcenter.marketing.modules.model.Note;

@Mapper(config = ConvertConfig.class)
public interface NoteConvert {

    @Mappings({
            @Mapping(target = "userName", ignore = true),
            @Mapping(target = "headImgUrl", ignore = true),
            @Mapping(target = "showMore", expression = "java(note.getContent() != null && note.getContent().length() > 90)"),
            @Mapping(target = "publishTime", ignore = true),
            @Mapping(target = "top", expression = "java(note.getTop() != null && note.getTop() == 1)"),
            @Mapping(target = "isCurrentUser", ignore = true),
            @Mapping(target = "noteId", source = "id"),
            @Mapping(target = "auditStatus", source = "auditStatus"),
            @Mapping(target = "auditRefuseMsg", source = "auditRefuseMsg")
    })
    NoteListItemResp toListItem(Note note);
}

