package org.springcenter.marketing.modules.factory;

import com.alibaba.fastjson.JSONObject;
import com.jnby.common.util.IdLeaf;
import org.springcenter.marketing.api.dto.UserClickTaskReq;
import org.springcenter.marketing.config.PropertiesValueConfig;
import org.springcenter.marketing.config.exception.MarketException;
import org.springcenter.marketing.api.dto.TaskTemplateReq;
import org.springcenter.marketing.api.dto.UpdateTaskTemplateReq;
import org.springcenter.marketing.api.dto.UserTaskListReq;
import org.springcenter.marketing.api.enums.TaskTemplateLimitationTypeEnum;
import org.springcenter.marketing.api.enums.TaskTemplateRewardTypeEnum;
import org.springcenter.marketing.api.enums.TaskTemplateStatusEnum;
import org.springcenter.marketing.api.enums.TaskTemplateTypeEnum;
import org.springcenter.marketing.modules.context.AddCouponContext;
import org.springcenter.marketing.modules.context.AddIntegralContext;
import org.springcenter.marketing.modules.model.*;
import org.springcenter.marketing.modules.repository.ITaskRepository;
import org.springcenter.marketing.modules.util.DateUtil;
import org.springcenter.product.api.enums.IsDeleteEnum;
import com.jnbyframework.boot.api.ISysBaseAPI;
import com.jnbyframework.boot.common.system.vo.LoginUser;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date:2023/2/9 13:44
 */
public class TaskFactory {

    private static final Integer ZERO = 0;

    public static TaskTemplateOperator buildTaskOperator(Integer type, String operatorName, String id, String taskTempId) {
        TaskTemplateOperator operator = new TaskTemplateOperator();
        operator.setOperatorName(operatorName);
        operator.setOptType(type);
        operator.setCreateTime(new Date());
        operator.setUpdateTime(new Date());
        operator.setIsDeleted(IsDeleteEnum.NORMAL.getCode());
        operator.setId(id);
        operator.setTaskTempId(taskTempId);
        return operator;
    }

    /*public static TaskTemplateLimitation buildLimitation(Integer type, String id, TaskTemplateReq.PopulationObject object, String taskTemId) {
        TaskTemplateLimitation taskTemplateLimitation = new TaskTemplateLimitation();
        taskTemplateLimitation.setId(id);
        taskTemplateLimitation.setTaskTempId(taskTemId);
        taskTemplateLimitation.setUpdateTime(new Date());
        taskTemplateLimitation.setCreateTime(new Date());
        taskTemplateLimitation.setValue(object.getId());
        taskTemplateLimitation.setValueName(object.getName());
        taskTemplateLimitation.setType(type);
        return taskTemplateLimitation;
    }*/

    public static TaskTemplateLog buildTaskTemplateLog(TaskTemplate taskTemplate, ISysBaseAPI sysBaseAPI, Logger log,
                                                       PropertiesValueConfig propertiesValueConfig) {
        LoginUser currentLoginUserInfo = null;
        try {
            log.info("sysBaseAPI 获取数据{}", sysBaseAPI);
            log.info("sysBaseAPI.getCurrentLoginUserInfo 获取数据{}", sysBaseAPI.getCurrentLoginUserInfo());
            currentLoginUserInfo = sysBaseAPI == null ? null : sysBaseAPI.getCurrentLoginUserInfo();
        } catch (Exception e) {
            log.error("SignInTaskHandle拿不到用户信息");
        }
        TaskTemplateLog taskTemplateLog = new TaskTemplateLog();
        BeanUtils.copyProperties(taskTemplate, taskTemplateLog);
        taskTemplateLog.setOperator(currentLoginUserInfo == null ? "系统" : currentLoginUserInfo.getRealname());
        taskTemplateLog.setId(IdLeaf.getDateId(propertiesValueConfig.getTaskTemplateTag()));
        taskTemplateLog.setTaskTemplateId(taskTemplate.getId());
        taskTemplateLog.setCreateTime(new Date());
        taskTemplateLog.setUpdateTime(new Date());
        return taskTemplateLog;
    }

    public static TaskTemplate buildTaskTemplate(TaskTemplateReq req, ITaskRepository taskRepository, String version,
                                                 PropertiesValueConfig propertiesValueConfig, String weId) {
        TaskTemplate taskTemplate = new TaskTemplate();
        BeanUtils.copyProperties(req, taskTemplate);
        String taskNo = taskRepository.getTaskTemplateNo();
        taskTemplate.setTaskNo(taskNo);
        taskTemplate.setCreateTime(new Date());
        taskTemplate.setUpdateTime(new Date());
        taskTemplate.setVersion(version);
        taskTemplate.setId(IdLeaf.getDateId(propertiesValueConfig.getTaskTemplateTag()));
        taskTemplate.setWeid(weId);
        return taskTemplate;
    }

    /*public static List<TaskTemplateLimitation> buildTaskTemplatePopulation(TaskContext text,
                                                                            List<TaskTemplateLimitation> insertPeopleInfo,
                                                                            TaskTemplate taskTemplate,
                                                                            TaskTemplateLog taskTemplateLog,
                                                                            PropertiesValueConfig propertiesValueConfig) {
        Boolean isDesignatedPopulation = taskTemplate.getIsDesignatedPopulation() == 1;
        if (isDesignatedPopulation) {
            // 品牌卡等级信息
            if (CollectionUtils.isNotEmpty(text.getCardLevels())) {
                text.getCardLevels().forEach(v -> {
                    TaskTemplateLimitation taskTemplateLimitation = TaskFactory.buildLimitation(TaskTemplateLimitationTypeEnum.CARD_LEVELS.getCode(),
                            IdLeaf.getDateId(propertiesValueConfig.getTaskTemplateTag()), v, taskTemplate.getId());
                    insertPeopleInfo.add(taskTemplateLimitation);
                    taskTemplateLog.setCardLevels(JSONObject.toJSONString(text.getCardLevels()));
                });
            }

            //人群包信息
            if (CollectionUtils.isNotEmpty(text.getPeoplePag())) {
                text.getPeoplePag().forEach(v -> {
                    TaskTemplateLimitation taskTemplateLimitation = TaskFactory.buildLimitation(TaskTemplateLimitationTypeEnum.PEOPLE_PAR.getCode(),
                            IdLeaf.getDateId(propertiesValueConfig.getTaskTemplateTag()), v, taskTemplate.getId());
                    insertPeopleInfo.add(taskTemplateLimitation);
                    taskTemplateLog.setPeoplePag(JSONObject.toJSONString(text.getPeoplePag()));
                });
            }

            //门店包信息
            if (text.getStorePag() != null) {
                TaskTemplateLimitation taskTemplateLimitation = TaskFactory.buildLimitation(TaskTemplateLimitationTypeEnum.STORE_PAG.getCode(),
                        IdLeaf.getDateId(propertiesValueConfig.getTaskTemplateTag()), text.getStorePag(), taskTemplate.getId());
                insertPeopleInfo.add(taskTemplateLimitation);
                taskTemplateLog.setStorePag(JSONObject.toJSONString(text.getStorePag()));
            }

        }
        return insertPeopleInfo;
    }*/

    public static TaskPartDetails buildTaskPartDetails(TaskInfo v, UserTaskListReq requestData,
                                                       PropertiesValueConfig propertiesValueConfig, String taskDeNo) {
        TaskPartDetails taskPartDetails = new TaskPartDetails();
        taskPartDetails.setUnionid(requestData.getUnionId());
        taskPartDetails.setVipLevel(requestData.getVipLevel());
        taskPartDetails.setCreateTime(new Date());
        taskPartDetails.setUpdateTime(new Date());
        taskPartDetails.setNickname(requestData.getNickName());
        taskPartDetails.setPhone(requestData.getPhone());
        taskPartDetails.setWeid(requestData.getWeId());
        taskPartDetails.setId(IdLeaf.getDateId(propertiesValueConfig.getTaskTemplateTag()));
        taskPartDetails.setTaskInfoId(v.getId());
        taskPartDetails.setTaskTempId(v.getTaskTempId());
        taskPartDetails.setTaskTempType(v.getTaskType());
        taskPartDetails.setTaskItemNo(taskDeNo);
        taskPartDetails.setInCycleEndTime(v.getInCycleEndTime());
        taskPartDetails.setInCycleStartTime(v.getInCycleStartTime());
        taskPartDetails.setFinishStatus(ZERO);
        taskPartDetails.setVersion(v.getVersion());
        // 判断任务状态 用户任务的状态 0未领取任务 1已领取任务 2统计中[此状态只有消费才有，是已完成但是不能领取] 3待领取奖励 4已领取奖励 5用户任务状态结束
        if (Objects.equals(v.getTaskType(), TaskTemplateTypeEnum.COST_TASK.getCode())) {
            if (Objects.equals(v.getIsDrawType(), 0)) {
                taskPartDetails.setTaskStatus(1);
            } else {
                taskPartDetails.setTaskStatus(0);
            }
        } else {
            taskPartDetails.setTaskStatus(1);
        }

        // 判断当前任务是否要领取任务 如果是的 改时间则是领取任务时间
        if (v.getInCycleStartTime() != null && v.getInCycleEndTime() != null && Objects.equals(v.getIsDrawType(), 0)) {
            taskPartDetails.setTaskStatusStartTime(v.getInCycleStartTime());
            taskPartDetails.setTaskStatusEndTime(v.getInCycleEndTime());
        }
        /*else if (v.getInCycleStartTime() != null && v.getInCycleEndTime() != null && Objects.equals(v.getIsDrawType(), 1)) {
            taskPartDetails.setTaskStatusStartTime(v.getInCycleStartTime());
            Date date = DateUtil.addDate(v.getInCycleStartTime(), v.getDrawDays());
            taskPartDetails.setTaskStatusEndTime(date);
        }*/

        return taskPartDetails;
    }

    public static void checkCommonParams(TaskTemplateReq req) {
        // 检验链接是否填充
        if (StringUtils.isBlank(req.getTaskLink())) {
            throw new MarketException("任务跳转链接不能为空");
        }

        //校验共同参数
        if (req.getIsDesignatedPopulation() == null) {
            throw new MarketException("是否指定人群不能为空");
        }

        if (Objects.equals(req.getIsDesignatedPopulation(), 1) && req.getPeopleCrowdId() == null) {
            throw new RuntimeException("指定人群后选的人群组件id不能为空");
        }

        if (Objects.equals(req.getIsDesignatedPopulation(), 0)) {
            req.setPeopleCrowdId(null);
        }

        if (StringUtils.isBlank(req.getTaskAliasName())) {
            throw new MarketException("任务别名不能为空");
        }

        if (CollectionUtils.isEmpty(req.getWeIds())) {
            throw new MarketException("品牌不能为空");
        }
        if (req.getStartTime() == null) {
            throw new MarketException("开始时间不能为空");
        }
        if (req.getEndTime() == null) {
            throw new MarketException("结束时间不能为空");
        }
        if (req.getEndTime().equals(new Date()) || req.getEndTime().before(new Date())) {
            throw new MarketException("结束时间不能小于等于当前时间");
        }
        if (req.getEndTime().before(req.getStartTime()) || req.getStartTime().equals(req.getEndTime())) {
            throw new MarketException("开始时间不能小于结束时间且不能相等");
        }
        if (req.getExecutionMode() == null) {
            throw new MarketException("执行方式不能为空");
        }

        if (req.getExecutionMode() == 0) {
            if (req.getExecutionCycle() == null) {
                throw new MarketException("执行周期不能为空");
            }

            if ((req.getExecutionCycle() == 1 || req.getExecutionCycle() == 2) && StringUtils.isBlank(req.getExecutionCycleAttr())) {
                throw new MarketException("实际执行不能为空");
            }

            if (StringUtils.isBlank(req.getExecutionStartTime())) {
                throw new MarketException("执行生效开始时间不能为空");
            }

            if (StringUtils.isBlank(req.getExecutionEndTime())) {
                throw new MarketException("执行生效结束时间不能为空");
            }
        }

    }

    public static void checkCommonUpdateParams(UpdateTaskTemplateReq req) {
        // 检验链接是否填充
        if (StringUtils.isBlank(req.getTaskLink())) {
            throw new MarketException("任务跳转链接不能为空");
        }

        //校验共同参数
        if (req.getIsDesignatedPopulation() == null) {
            throw new MarketException("是否指定人群不能为空");
        }

        if (Objects.equals(req.getIsDesignatedPopulation(), 1) && req.getPeopleCrowdId() == null) {
            throw new RuntimeException("指定人群后选的人群组件id不能为空");
        }

        if (Objects.equals(req.getIsDesignatedPopulation(), 0)) {
            req.setPeopleCrowdId(null);
        }

        if (StringUtils.isBlank(req.getTaskAliasName())) {
            throw new MarketException("任务别名不能为空");
        }

        if (req.getStartTime() == null) {
            throw new MarketException("开始时间不能为空");
        }
        if (req.getEndTime() == null) {
            throw new MarketException("结束时间不能为空");
        }
        if (req.getEndTime().equals(new Date()) || req.getEndTime().before(new Date())) {
            throw new MarketException("结束时间不能小于等于当前时间");
        }
        if (req.getEndTime().before(req.getStartTime()) || req.getStartTime().equals(req.getEndTime())) {
            throw new MarketException("开始时间不能小于结束时间且不能相等");
        }

        if (req.getStatus() != null && Objects.equals(req.getStatus(), TaskTemplateStatusEnum.TAKE_OFF_THE_SHELF.getCode())) {
            req.setEndTime(new Date());
        } else if (req.getStatus() != null && Objects.equals(req.getStatus(), TaskTemplateStatusEnum.PUT_ON_SHELVES.getCode())) {
            req.setStartTime(new Date());
        }

        if (req.getExecutionMode() == null) {
            throw new MarketException("执行方式不能为空");
        }

        if (req.getExecutionMode() == 0) {
            if (req.getExecutionCycle() == null) {
                throw new MarketException("执行周期不能为空");
            }

            if ((req.getExecutionCycle() == 1 || req.getExecutionCycle() == 2) && StringUtils.isBlank(req.getExecutionCycleAttr())) {
                throw new MarketException("实际执行不能为空");
            }

            if (StringUtils.isBlank(req.getExecutionStartTime())) {
                throw new MarketException("执行生效开始时间不能为空");
            }

            if (StringUtils.isBlank(req.getExecutionEndTime())) {
                throw new MarketException("执行生效结束时间不能为空");
            }
        }

    }

    public static void buildTaskOutInfo(TaskTemplate taskTemplate, List<TaskTemplateOutInfo> outInfos,
                                        PropertiesValueConfig propertiesValueConfig, TaskTemplateReq req,
                                        TaskTemplateLog taskTemplateLog) {
        TaskTemplateOutInfo info = new TaskTemplateOutInfo();
        BeanUtils.copyProperties(req, info);
        info.setId(IdLeaf.getDateId(propertiesValueConfig.getTaskTemplateTag()));
        info.setTaskTempId(taskTemplate.getId());
        info.setUpdateTime(new Date());
        info.setCreateTime(new Date());
        outInfos.add(info);
        taskTemplateLog.setButtonName(info.getButtonName());
        taskTemplateLog.setTaskName(info.getTaskName());
        taskTemplateLog.setTaskLink(info.getTaskLink());
        taskTemplateLog.setTaskDescription(info.getTaskDescription());
        taskTemplateLog.setIcon(info.getIcon());
    }

    public static void buildTaskExecutionInfo(TaskTemplate taskTemplate, List<TaskTemplateExecution> executions,
                                              PropertiesValueConfig propertiesValueConfig,  TaskTemplateReq req,
                                              TaskTemplateLog taskTemplateLog) {
        TaskTemplateExecution info = new TaskTemplateExecution();
        BeanUtils.copyProperties(req, info);
        info.setId(IdLeaf.getDateId(propertiesValueConfig.getTaskTemplateTag()));
        info.setTaskTempId(taskTemplate.getId());
        info.setUpdateTime(new Date());
        info.setCreateTime(new Date());
        executions.add(info);
        taskTemplateLog.setExecutionStartTime(info.getExecutionStartTime());
        taskTemplateLog.setExecutionEndTime(info.getExecutionEndTime());
        taskTemplateLog.setExecutionCycle(info.getExecutionCycle());
        taskTemplateLog.setExecutionCycleAttr(info.getExecutionCycleAttr());
    }

    public static void buildTaskRewardInfo(TaskTemplate taskTemplate, List<TaskTemplateRewardInfo> rewardInfos,
                                           PropertiesValueConfig propertiesValueConfig, TaskTemplateReq req) {

        if (req.getRequiredTimes() != null && req.getRequiredTimes() > 0) {
            TaskTemplateRewardInfo info = new TaskTemplateRewardInfo();
            info.setId(IdLeaf.getDateId(propertiesValueConfig.getTaskTemplateTag()));
            info.setUpdateTime(new Date());
            info.setCreateTime(new Date());
            info.setTaskTempId(taskTemplate.getId());
            info.setType(TaskTemplateRewardTypeEnum.REWORD_THRESHOLD.getCode());
            info.setValue(Objects.toString(req.getRequiredTimes()));
            info.setDrawType(req.getDrawType());
            rewardInfos.add(info);
        }
        if (req.getGainPoints() != null && req.getGainPoints() > 0) {
            TaskTemplateRewardInfo info = new TaskTemplateRewardInfo();
            info.setId(IdLeaf.getDateId(propertiesValueConfig.getTaskTemplateTag()));
            info.setUpdateTime(new Date());
            info.setCreateTime(new Date());
            info.setTaskTempId(taskTemplate.getId());
            info.setType(TaskTemplateRewardTypeEnum.BONUS_POINTS.getCode());
            info.setValue(Objects.toString(req.getGainPoints()));
            info.setDrawType(req.getDrawType());
            rewardInfos.add(info);
        }

        if (CollectionUtils.isNotEmpty(req.getAwardInfos())) {
            req.getAwardInfos().forEach(v -> {
                TaskTemplateRewardInfo info = new TaskTemplateRewardInfo();
                info.setId(IdLeaf.getDateId(propertiesValueConfig.getTaskTemplateTag()));
                info.setUpdateTime(new Date());
                info.setCreateTime(new Date());
                info.setTaskTempId(taskTemplate.getId());
                info.setType(TaskTemplateRewardTypeEnum.COUPON.getCode());
                info.setValue(Objects.toString(v.getAwardId()));
                info.setNum(v.getNum());
                info.setDrawType(req.getDrawType());
                rewardInfos.add(info);
            });
        }

        // 库存
        if (req.getStock() != null && req.getStock() > 0) {
            TaskTemplateRewardInfo info = new TaskTemplateRewardInfo();
            info.setId(IdLeaf.getDateId(propertiesValueConfig.getTaskTemplateTag()));
            info.setUpdateTime(new Date());
            info.setCreateTime(new Date());
            info.setTaskTempId(taskTemplate.getId());
            info.setType(TaskTemplateRewardTypeEnum.STOCK.getCode());
            info.setValue(Objects.toString(req.getStock()));
            info.setDrawType(req.getDrawType());
            rewardInfos.add(info);
        }

        // 领取奖励时间
        if (req.getRewardIssueTime() != null && req.getRewardIssueTime() > 0) {
            TaskTemplateRewardInfo info = new TaskTemplateRewardInfo();
            info.setId(IdLeaf.getDateId(propertiesValueConfig.getTaskTemplateTag()));
            info.setUpdateTime(new Date());
            info.setCreateTime(new Date());
            info.setTaskTempId(taskTemplate.getId());
            info.setType(TaskTemplateRewardTypeEnum.ISSUE_AWARD.getCode());
            info.setValue(Objects.toString(req.getRewardIssueTime()));
            info.setDrawType(req.getDrawType());
            rewardInfos.add(info);
        }

        // 奖励时效
        if (req.getRewardClaimableTime() != null && req.getRewardClaimableTime() > 0) {
            TaskTemplateRewardInfo info = new TaskTemplateRewardInfo();
            info.setId(IdLeaf.getDateId(propertiesValueConfig.getTaskTemplateTag()));
            info.setUpdateTime(new Date());
            info.setCreateTime(new Date());
            info.setTaskTempId(taskTemplate.getId());
            info.setType(TaskTemplateRewardTypeEnum.CLAIMABLE_AWARD.getCode());
            info.setValue(Objects.toString(req.getRewardClaimableTime()));
            info.setDrawType(req.getDrawType());
            rewardInfos.add(info);
        }

    }



    public static AddIntegralContext buildAddIntegralContext(TaskTemplateRewardInfo taskTemplateRewardInfo,
                                                             TaskPartDetails taskPartDetails) {
        AddIntegralContext addIntegralContext = new AddIntegralContext();
        addIntegralContext.setIntegral(Integer.valueOf(taskTemplateRewardInfo.getValue()));
        // 实时
        addIntegralContext.setSync(1);
        addIntegralContext.setUnionId(taskPartDetails.getUnionid());
        // 微商城
        addIntegralContext.setType(1);
        addIntegralContext.setBrandId(taskPartDetails.getWeid());
        // todo 会员id
        // addIntegralContext.setVipId();
        addIntegralContext.setSource(TaskTemplateTypeEnum.getTaskTemplateEnum(taskPartDetails.getTaskTempType()) == null
                ? "" : TaskTemplateTypeEnum.getTaskTemplateEnum(taskPartDetails.getTaskTempType()).getDesc());
        return addIntegralContext;
    }

    public static AddCouponContext AddCouponContext(List<TaskTemplateRewardInfo> couponRewardInfos,
                                                    String unionId,
                                                    TaskPartDetails taskPartDetails) {
        AddCouponContext addCouponContext = new AddCouponContext();
        addCouponContext.setUnionid(unionId);
        addCouponContext.setWeid(taskPartDetails.getWeid());
        List<TaskTemplateReq.AwardInfo> awardInfos = new ArrayList<>();
        couponRewardInfos.forEach(v -> {
            TaskTemplateReq.AwardInfo awardInfo = new TaskTemplateReq.AwardInfo();
            awardInfo.setNum(v.getNum() == null ? 1 : v.getNum());
            awardInfo.setAwardId(Long.valueOf(v.getValue()));
            awardInfos.add(awardInfo);
        });
        addCouponContext.setAwardInfos(awardInfos);
        return addCouponContext;
    }

    public static void buildTaskPopInfo(TaskTemplatePopInfo popInfo, TaskTemplate taskTemplate, TaskTemplateReq req,
                                        PropertiesValueConfig propertiesValueConfig) {
        BeanUtils.copyProperties(req, popInfo);
        popInfo.setId(IdLeaf.getDateId(propertiesValueConfig.getTaskTemplateTag()));
        popInfo.setTaskTemplateId(taskTemplate.getId());
        popInfo.setCreateTime(new Date());
        popInfo.setUpdateTime(new Date());
        popInfo.setIsDeleted(0);

    }
}
