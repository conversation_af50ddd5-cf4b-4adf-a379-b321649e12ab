<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.marketing.modules.bojunMapper.HrEmployeeMapper">
  <resultMap id="BaseResultMap" type="org.springcenter.marketing.modules.model.HrEmployee">
    <id column="ID" jdbcType="DECIMAL" property="id" />
    <result column="AD_CLIENT_ID" jdbcType="DECIMAL" property="adClientId" />
    <result column="AD_ORG_ID" jdbcType="DECIMAL" property="adOrgId" />
    <result column="NO" jdbcType="VARCHAR" property="no" />
    <result column="NAME" jdbcType="VARCHAR" property="name" />
    <result column="PIC" jdbcType="VARCHAR" property="pic" />
    <result column="C_DEPARTMENT_ID" jdbcType="DECIMAL" property="cDepartmentId" />
    <result column="HR_ROLE_ID" jdbcType="DECIMAL" property="hrRoleId" />
    <result column="HR_TITLE_ID" jdbcType="DECIMAL" property="hrTitleId" />
    <result column="HR_POST_ID" jdbcType="DECIMAL" property="hrPostId" />
    <result column="INCUMBENCY_STS" jdbcType="CHAR" property="incumbencySts" />
    <result column="WORK_FORM" jdbcType="CHAR" property="workForm" />
    <result column="BEGIN_DATE" jdbcType="DECIMAL" property="beginDate" />
    <result column="END_DATE" jdbcType="DECIMAL" property="endDate" />
    <result column="CONTRACT_START" jdbcType="DECIMAL" property="contractStart" />
    <result column="CONTRACT_END" jdbcType="DECIMAL" property="contractEnd" />
    <result column="FULL_MEMBER_DATE" jdbcType="DECIMAL" property="fullMemberDate" />
    <result column="RETIRE_DATE" jdbcType="DECIMAL" property="retireDate" />
    <result column="WORK_LENGTH" jdbcType="VARCHAR" property="workLength" />
    <result column="EMPLOYEE_FN" jdbcType="VARCHAR" property="employeeFn" />
    <result column="BANK_NAME" jdbcType="VARCHAR" property="bankName" />
    <result column="BANK_ACCOUNT" jdbcType="VARCHAR" property="bankAccount" />
    <result column="DESCRIPTION" jdbcType="VARCHAR" property="description" />
    <result column="SEX" jdbcType="CHAR" property="sex" />
    <result column="C_REGION_ID" jdbcType="DECIMAL" property="cRegionId" />
    <result column="NATION" jdbcType="VARCHAR" property="nation" />
    <result column="MARRIAGE" jdbcType="CHAR" property="marriage" />
    <result column="POLITY" jdbcType="VARCHAR" property="polity" />
    <result column="BIRTHDATE" jdbcType="DECIMAL" property="birthdate" />
    <result column="ID_CARD" jdbcType="VARCHAR" property="idCard" />
    <result column="HUKOU" jdbcType="VARCHAR" property="hukou" />
    <result column="DOMICILE_ADD" jdbcType="VARCHAR" property="domicileAdd" />
    <result column="ADDRESS" jdbcType="VARCHAR" property="address" />
    <result column="POSTALCODE" jdbcType="VARCHAR" property="postalcode" />
    <result column="PHONE" jdbcType="VARCHAR" property="phone" />
    <result column="HANDSET" jdbcType="VARCHAR" property="handset" />
    <result column="EMAIL" jdbcType="VARCHAR" property="email" />
    <result column="EDUCATION" jdbcType="CHAR" property="education" />
    <result column="HR_SPECIALITY_ID" jdbcType="DECIMAL" property="hrSpecialityId" />
    <result column="SCHOOL" jdbcType="VARCHAR" property="school" />
    <result column="GRADUATE_DATE" jdbcType="DECIMAL" property="graduateDate" />
    <result column="C_LANGUAGE1_ID" jdbcType="DECIMAL" property="cLanguage1Id" />
    <result column="LEVEL1" jdbcType="CHAR" property="level1" />
    <result column="C_LANGUAGE2_ID" jdbcType="DECIMAL" property="cLanguage2Id" />
    <result column="LEVEL2" jdbcType="CHAR" property="level2" />
    <result column="COMPUTER_LEVEL" jdbcType="CHAR" property="computerLevel" />
    <result column="BODY_STS" jdbcType="VARCHAR" property="bodySts" />
    <result column="HEIGHT" jdbcType="VARCHAR" property="height" />
    <result column="WEIGHT" jdbcType="VARCHAR" property="weight" />
    <result column="EYESIGHT" jdbcType="VARCHAR" property="eyesight" />
    <result column="BLOOD_TYPE" jdbcType="VARCHAR" property="bloodType" />
    <result column="BIO" jdbcType="VARCHAR" property="bio" />
    <result column="ACCESSORIES" jdbcType="VARCHAR" property="accessories" />
    <result column="OWNERID" jdbcType="DECIMAL" property="ownerid" />
    <result column="MODIFIERID" jdbcType="DECIMAL" property="modifierid" />
    <result column="CREATIONDATE" jdbcType="TIMESTAMP" property="creationdate" />
    <result column="MODIFIEDDATE" jdbcType="TIMESTAMP" property="modifieddate" />
    <result column="ISACTIVE" jdbcType="CHAR" property="isactive" />
    <result column="USER_ID" jdbcType="DECIMAL" property="userId" />
    <result column="CONTRACTURL" jdbcType="VARCHAR" property="contracturl" />
    <result column="C_STORE_ID" jdbcType="DECIMAL" property="cStoreId" />
    <result column="ISSALER" jdbcType="CHAR" property="issaler" />
    <result column="ISOPR" jdbcType="CHAR" property="isopr" />
    <result column="C_CUSTOMER_ID" jdbcType="DECIMAL" property="cCustomerId" />
    <result column="C_CUSTOMERUP_ID" jdbcType="DECIMAL" property="cCustomerupId" />
    <result column="ISSTORER" jdbcType="CHAR" property="isstorer" />
    <result column="BASE_WAGE" jdbcType="DECIMAL" property="baseWage" />
    <result column="DISCOUNTLIMIT" jdbcType="DECIMAL" property="discountlimit" />
    <result column="BIGAREAMNG_ID" jdbcType="DECIMAL" property="bigareamngId" />
    <result column="EMPTYPE" jdbcType="VARCHAR" property="emptype" />
    <result column="PDMTYPE" jdbcType="VARCHAR" property="pdmtype" />
    <result column="TIMECARDNO" jdbcType="VARCHAR" property="timecardno" />
    <result column="C_SALESTORE_ID" jdbcType="DECIMAL" property="cSalestoreId" />
    <result column="HR_PSN_ID" jdbcType="DECIMAL" property="hrPsnId" />
    <result column="IS_CONPEO" jdbcType="CHAR" property="isConpeo" />
    <result column="IS_CAN_EBSOOUT" jdbcType="CHAR" property="isCanEbsoout" />
    <result column="YG_ID" jdbcType="DECIMAL" property="ygId" />
    <result column="IS_PUBLICSHOPPERS" jdbcType="CHAR" property="isPublicshoppers" />
    <result column="CHECKPERSON" jdbcType="CHAR" property="checkperson" />
    <result column="CHECKCODE" jdbcType="VARCHAR" property="checkcode" />
    <result column="EHR_ID" jdbcType="DECIMAL" property="ehrId" />
    <result column="EHR_ROLEID" jdbcType="DECIMAL" property="ehrRoleid" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, AD_CLIENT_ID, AD_ORG_ID, NO, NAME, PIC, C_DEPARTMENT_ID, HR_ROLE_ID, HR_TITLE_ID, 
    HR_POST_ID, INCUMBENCY_STS, WORK_FORM, BEGIN_DATE, END_DATE, CONTRACT_START, CONTRACT_END, 
    FULL_MEMBER_DATE, RETIRE_DATE, WORK_LENGTH, EMPLOYEE_FN, BANK_NAME, BANK_ACCOUNT, 
    DESCRIPTION, SEX, C_REGION_ID, NATION, MARRIAGE, POLITY, BIRTHDATE, ID_CARD, HUKOU, 
    DOMICILE_ADD, ADDRESS, POSTALCODE, PHONE, HANDSET, EMAIL, EDUCATION, HR_SPECIALITY_ID, 
    SCHOOL, GRADUATE_DATE, C_LANGUAGE1_ID, LEVEL1, C_LANGUAGE2_ID, LEVEL2, COMPUTER_LEVEL, 
    BODY_STS, HEIGHT, WEIGHT, EYESIGHT, BLOOD_TYPE, BIO, ACCESSORIES, OWNERID, MODIFIERID, 
    CREATIONDATE, MODIFIEDDATE, ISACTIVE, USER_ID, CONTRACTURL, C_STORE_ID, ISSALER, 
    ISOPR, C_CUSTOMER_ID, C_CUSTOMERUP_ID, ISSTORER, BASE_WAGE, DISCOUNTLIMIT, BIGAREAMNG_ID, 
    EMPTYPE, PDMTYPE, TIMECARDNO, C_SALESTORE_ID, HR_PSN_ID, IS_CONPEO, IS_CAN_EBSOOUT, 
    YG_ID, IS_PUBLICSHOPPERS, CHECKPERSON, CHECKCODE, EHR_ID, EHR_ROLEID
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from HR_EMPLOYEE
    where ID = #{id,jdbcType=DECIMAL}
  </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from HR_EMPLOYEE
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="org.springcenter.marketing.modules.model.HrEmployee">
    insert into HR_EMPLOYEE (ID, AD_CLIENT_ID, AD_ORG_ID, 
      NO, NAME, PIC, C_DEPARTMENT_ID, 
      HR_ROLE_ID, HR_TITLE_ID, HR_POST_ID, 
      INCUMBENCY_STS, WORK_FORM, BEGIN_DATE, 
      END_DATE, CONTRACT_START, CONTRACT_END, 
      FULL_MEMBER_DATE, RETIRE_DATE, WORK_LENGTH, 
      EMPLOYEE_FN, BANK_NAME, BANK_ACCOUNT, 
      DESCRIPTION, SEX, C_REGION_ID, 
      NATION, MARRIAGE, POLITY, 
      BIRTHDATE, ID_CARD, HUKOU, 
      DOMICILE_ADD, ADDRESS, POSTALCODE, 
      PHONE, HANDSET, EMAIL, 
      EDUCATION, HR_SPECIALITY_ID, SCHOOL, 
      GRADUATE_DATE, C_LANGUAGE1_ID, LEVEL1, 
      C_LANGUAGE2_ID, LEVEL2, COMPUTER_LEVEL, 
      BODY_STS, HEIGHT, WEIGHT, 
      EYESIGHT, BLOOD_TYPE, BIO, 
      ACCESSORIES, OWNERID, MODIFIERID, 
      CREATIONDATE, MODIFIEDDATE, ISACTIVE, 
      USER_ID, CONTRACTURL, C_STORE_ID, 
      ISSALER, ISOPR, C_CUSTOMER_ID, 
      C_CUSTOMERUP_ID, ISSTORER, BASE_WAGE, 
      DISCOUNTLIMIT, BIGAREAMNG_ID, EMPTYPE, 
      PDMTYPE, TIMECARDNO, C_SALESTORE_ID, 
      HR_PSN_ID, IS_CONPEO, IS_CAN_EBSOOUT, 
      YG_ID, IS_PUBLICSHOPPERS, CHECKPERSON, 
      CHECKCODE, EHR_ID, EHR_ROLEID
      )
    values (#{id,jdbcType=DECIMAL}, #{adClientId,jdbcType=DECIMAL}, #{adOrgId,jdbcType=DECIMAL}, 
      #{no,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{pic,jdbcType=VARCHAR}, #{cDepartmentId,jdbcType=DECIMAL}, 
      #{hrRoleId,jdbcType=DECIMAL}, #{hrTitleId,jdbcType=DECIMAL}, #{hrPostId,jdbcType=DECIMAL}, 
      #{incumbencySts,jdbcType=CHAR}, #{workForm,jdbcType=CHAR}, #{beginDate,jdbcType=DECIMAL}, 
      #{endDate,jdbcType=DECIMAL}, #{contractStart,jdbcType=DECIMAL}, #{contractEnd,jdbcType=DECIMAL}, 
      #{fullMemberDate,jdbcType=DECIMAL}, #{retireDate,jdbcType=DECIMAL}, #{workLength,jdbcType=VARCHAR}, 
      #{employeeFn,jdbcType=VARCHAR}, #{bankName,jdbcType=VARCHAR}, #{bankAccount,jdbcType=VARCHAR}, 
      #{description,jdbcType=VARCHAR}, #{sex,jdbcType=CHAR}, #{cRegionId,jdbcType=DECIMAL}, 
      #{nation,jdbcType=VARCHAR}, #{marriage,jdbcType=CHAR}, #{polity,jdbcType=VARCHAR}, 
      #{birthdate,jdbcType=DECIMAL}, #{idCard,jdbcType=VARCHAR}, #{hukou,jdbcType=VARCHAR}, 
      #{domicileAdd,jdbcType=VARCHAR}, #{address,jdbcType=VARCHAR}, #{postalcode,jdbcType=VARCHAR}, 
      #{phone,jdbcType=VARCHAR}, #{handset,jdbcType=VARCHAR}, #{email,jdbcType=VARCHAR}, 
      #{education,jdbcType=CHAR}, #{hrSpecialityId,jdbcType=DECIMAL}, #{school,jdbcType=VARCHAR}, 
      #{graduateDate,jdbcType=DECIMAL}, #{cLanguage1Id,jdbcType=DECIMAL}, #{level1,jdbcType=CHAR}, 
      #{cLanguage2Id,jdbcType=DECIMAL}, #{level2,jdbcType=CHAR}, #{computerLevel,jdbcType=CHAR}, 
      #{bodySts,jdbcType=VARCHAR}, #{height,jdbcType=VARCHAR}, #{weight,jdbcType=VARCHAR}, 
      #{eyesight,jdbcType=VARCHAR}, #{bloodType,jdbcType=VARCHAR}, #{bio,jdbcType=VARCHAR}, 
      #{accessories,jdbcType=VARCHAR}, #{ownerid,jdbcType=DECIMAL}, #{modifierid,jdbcType=DECIMAL}, 
      #{creationdate,jdbcType=TIMESTAMP}, #{modifieddate,jdbcType=TIMESTAMP}, #{isactive,jdbcType=CHAR}, 
      #{userId,jdbcType=DECIMAL}, #{contracturl,jdbcType=VARCHAR}, #{cStoreId,jdbcType=DECIMAL}, 
      #{issaler,jdbcType=CHAR}, #{isopr,jdbcType=CHAR}, #{cCustomerId,jdbcType=DECIMAL}, 
      #{cCustomerupId,jdbcType=DECIMAL}, #{isstorer,jdbcType=CHAR}, #{baseWage,jdbcType=DECIMAL}, 
      #{discountlimit,jdbcType=DECIMAL}, #{bigareamngId,jdbcType=DECIMAL}, #{emptype,jdbcType=VARCHAR}, 
      #{pdmtype,jdbcType=VARCHAR}, #{timecardno,jdbcType=VARCHAR}, #{cSalestoreId,jdbcType=DECIMAL}, 
      #{hrPsnId,jdbcType=DECIMAL}, #{isConpeo,jdbcType=CHAR}, #{isCanEbsoout,jdbcType=CHAR}, 
      #{ygId,jdbcType=DECIMAL}, #{isPublicshoppers,jdbcType=CHAR}, #{checkperson,jdbcType=CHAR}, 
      #{checkcode,jdbcType=VARCHAR}, #{ehrId,jdbcType=DECIMAL}, #{ehrRoleid,jdbcType=DECIMAL}
      )
  </insert>
  <insert id="insertSelective" parameterType="org.springcenter.marketing.modules.model.HrEmployee">
    insert into HR_EMPLOYEE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="adClientId != null">
        AD_CLIENT_ID,
      </if>
      <if test="adOrgId != null">
        AD_ORG_ID,
      </if>
      <if test="no != null">
        NO,
      </if>
      <if test="name != null">
        NAME,
      </if>
      <if test="pic != null">
        PIC,
      </if>
      <if test="cDepartmentId != null">
        C_DEPARTMENT_ID,
      </if>
      <if test="hrRoleId != null">
        HR_ROLE_ID,
      </if>
      <if test="hrTitleId != null">
        HR_TITLE_ID,
      </if>
      <if test="hrPostId != null">
        HR_POST_ID,
      </if>
      <if test="incumbencySts != null">
        INCUMBENCY_STS,
      </if>
      <if test="workForm != null">
        WORK_FORM,
      </if>
      <if test="beginDate != null">
        BEGIN_DATE,
      </if>
      <if test="endDate != null">
        END_DATE,
      </if>
      <if test="contractStart != null">
        CONTRACT_START,
      </if>
      <if test="contractEnd != null">
        CONTRACT_END,
      </if>
      <if test="fullMemberDate != null">
        FULL_MEMBER_DATE,
      </if>
      <if test="retireDate != null">
        RETIRE_DATE,
      </if>
      <if test="workLength != null">
        WORK_LENGTH,
      </if>
      <if test="employeeFn != null">
        EMPLOYEE_FN,
      </if>
      <if test="bankName != null">
        BANK_NAME,
      </if>
      <if test="bankAccount != null">
        BANK_ACCOUNT,
      </if>
      <if test="description != null">
        DESCRIPTION,
      </if>
      <if test="sex != null">
        SEX,
      </if>
      <if test="cRegionId != null">
        C_REGION_ID,
      </if>
      <if test="nation != null">
        NATION,
      </if>
      <if test="marriage != null">
        MARRIAGE,
      </if>
      <if test="polity != null">
        POLITY,
      </if>
      <if test="birthdate != null">
        BIRTHDATE,
      </if>
      <if test="idCard != null">
        ID_CARD,
      </if>
      <if test="hukou != null">
        HUKOU,
      </if>
      <if test="domicileAdd != null">
        DOMICILE_ADD,
      </if>
      <if test="address != null">
        ADDRESS,
      </if>
      <if test="postalcode != null">
        POSTALCODE,
      </if>
      <if test="phone != null">
        PHONE,
      </if>
      <if test="handset != null">
        HANDSET,
      </if>
      <if test="email != null">
        EMAIL,
      </if>
      <if test="education != null">
        EDUCATION,
      </if>
      <if test="hrSpecialityId != null">
        HR_SPECIALITY_ID,
      </if>
      <if test="school != null">
        SCHOOL,
      </if>
      <if test="graduateDate != null">
        GRADUATE_DATE,
      </if>
      <if test="cLanguage1Id != null">
        C_LANGUAGE1_ID,
      </if>
      <if test="level1 != null">
        LEVEL1,
      </if>
      <if test="cLanguage2Id != null">
        C_LANGUAGE2_ID,
      </if>
      <if test="level2 != null">
        LEVEL2,
      </if>
      <if test="computerLevel != null">
        COMPUTER_LEVEL,
      </if>
      <if test="bodySts != null">
        BODY_STS,
      </if>
      <if test="height != null">
        HEIGHT,
      </if>
      <if test="weight != null">
        WEIGHT,
      </if>
      <if test="eyesight != null">
        EYESIGHT,
      </if>
      <if test="bloodType != null">
        BLOOD_TYPE,
      </if>
      <if test="bio != null">
        BIO,
      </if>
      <if test="accessories != null">
        ACCESSORIES,
      </if>
      <if test="ownerid != null">
        OWNERID,
      </if>
      <if test="modifierid != null">
        MODIFIERID,
      </if>
      <if test="creationdate != null">
        CREATIONDATE,
      </if>
      <if test="modifieddate != null">
        MODIFIEDDATE,
      </if>
      <if test="isactive != null">
        ISACTIVE,
      </if>
      <if test="userId != null">
        USER_ID,
      </if>
      <if test="contracturl != null">
        CONTRACTURL,
      </if>
      <if test="cStoreId != null">
        C_STORE_ID,
      </if>
      <if test="issaler != null">
        ISSALER,
      </if>
      <if test="isopr != null">
        ISOPR,
      </if>
      <if test="cCustomerId != null">
        C_CUSTOMER_ID,
      </if>
      <if test="cCustomerupId != null">
        C_CUSTOMERUP_ID,
      </if>
      <if test="isstorer != null">
        ISSTORER,
      </if>
      <if test="baseWage != null">
        BASE_WAGE,
      </if>
      <if test="discountlimit != null">
        DISCOUNTLIMIT,
      </if>
      <if test="bigareamngId != null">
        BIGAREAMNG_ID,
      </if>
      <if test="emptype != null">
        EMPTYPE,
      </if>
      <if test="pdmtype != null">
        PDMTYPE,
      </if>
      <if test="timecardno != null">
        TIMECARDNO,
      </if>
      <if test="cSalestoreId != null">
        C_SALESTORE_ID,
      </if>
      <if test="hrPsnId != null">
        HR_PSN_ID,
      </if>
      <if test="isConpeo != null">
        IS_CONPEO,
      </if>
      <if test="isCanEbsoout != null">
        IS_CAN_EBSOOUT,
      </if>
      <if test="ygId != null">
        YG_ID,
      </if>
      <if test="isPublicshoppers != null">
        IS_PUBLICSHOPPERS,
      </if>
      <if test="checkperson != null">
        CHECKPERSON,
      </if>
      <if test="checkcode != null">
        CHECKCODE,
      </if>
      <if test="ehrId != null">
        EHR_ID,
      </if>
      <if test="ehrRoleid != null">
        EHR_ROLEID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="adClientId != null">
        #{adClientId,jdbcType=DECIMAL},
      </if>
      <if test="adOrgId != null">
        #{adOrgId,jdbcType=DECIMAL},
      </if>
      <if test="no != null">
        #{no,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="pic != null">
        #{pic,jdbcType=VARCHAR},
      </if>
      <if test="cDepartmentId != null">
        #{cDepartmentId,jdbcType=DECIMAL},
      </if>
      <if test="hrRoleId != null">
        #{hrRoleId,jdbcType=DECIMAL},
      </if>
      <if test="hrTitleId != null">
        #{hrTitleId,jdbcType=DECIMAL},
      </if>
      <if test="hrPostId != null">
        #{hrPostId,jdbcType=DECIMAL},
      </if>
      <if test="incumbencySts != null">
        #{incumbencySts,jdbcType=CHAR},
      </if>
      <if test="workForm != null">
        #{workForm,jdbcType=CHAR},
      </if>
      <if test="beginDate != null">
        #{beginDate,jdbcType=DECIMAL},
      </if>
      <if test="endDate != null">
        #{endDate,jdbcType=DECIMAL},
      </if>
      <if test="contractStart != null">
        #{contractStart,jdbcType=DECIMAL},
      </if>
      <if test="contractEnd != null">
        #{contractEnd,jdbcType=DECIMAL},
      </if>
      <if test="fullMemberDate != null">
        #{fullMemberDate,jdbcType=DECIMAL},
      </if>
      <if test="retireDate != null">
        #{retireDate,jdbcType=DECIMAL},
      </if>
      <if test="workLength != null">
        #{workLength,jdbcType=VARCHAR},
      </if>
      <if test="employeeFn != null">
        #{employeeFn,jdbcType=VARCHAR},
      </if>
      <if test="bankName != null">
        #{bankName,jdbcType=VARCHAR},
      </if>
      <if test="bankAccount != null">
        #{bankAccount,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="sex != null">
        #{sex,jdbcType=CHAR},
      </if>
      <if test="cRegionId != null">
        #{cRegionId,jdbcType=DECIMAL},
      </if>
      <if test="nation != null">
        #{nation,jdbcType=VARCHAR},
      </if>
      <if test="marriage != null">
        #{marriage,jdbcType=CHAR},
      </if>
      <if test="polity != null">
        #{polity,jdbcType=VARCHAR},
      </if>
      <if test="birthdate != null">
        #{birthdate,jdbcType=DECIMAL},
      </if>
      <if test="idCard != null">
        #{idCard,jdbcType=VARCHAR},
      </if>
      <if test="hukou != null">
        #{hukou,jdbcType=VARCHAR},
      </if>
      <if test="domicileAdd != null">
        #{domicileAdd,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="postalcode != null">
        #{postalcode,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="handset != null">
        #{handset,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        #{email,jdbcType=VARCHAR},
      </if>
      <if test="education != null">
        #{education,jdbcType=CHAR},
      </if>
      <if test="hrSpecialityId != null">
        #{hrSpecialityId,jdbcType=DECIMAL},
      </if>
      <if test="school != null">
        #{school,jdbcType=VARCHAR},
      </if>
      <if test="graduateDate != null">
        #{graduateDate,jdbcType=DECIMAL},
      </if>
      <if test="cLanguage1Id != null">
        #{cLanguage1Id,jdbcType=DECIMAL},
      </if>
      <if test="level1 != null">
        #{level1,jdbcType=CHAR},
      </if>
      <if test="cLanguage2Id != null">
        #{cLanguage2Id,jdbcType=DECIMAL},
      </if>
      <if test="level2 != null">
        #{level2,jdbcType=CHAR},
      </if>
      <if test="computerLevel != null">
        #{computerLevel,jdbcType=CHAR},
      </if>
      <if test="bodySts != null">
        #{bodySts,jdbcType=VARCHAR},
      </if>
      <if test="height != null">
        #{height,jdbcType=VARCHAR},
      </if>
      <if test="weight != null">
        #{weight,jdbcType=VARCHAR},
      </if>
      <if test="eyesight != null">
        #{eyesight,jdbcType=VARCHAR},
      </if>
      <if test="bloodType != null">
        #{bloodType,jdbcType=VARCHAR},
      </if>
      <if test="bio != null">
        #{bio,jdbcType=VARCHAR},
      </if>
      <if test="accessories != null">
        #{accessories,jdbcType=VARCHAR},
      </if>
      <if test="ownerid != null">
        #{ownerid,jdbcType=DECIMAL},
      </if>
      <if test="modifierid != null">
        #{modifierid,jdbcType=DECIMAL},
      </if>
      <if test="creationdate != null">
        #{creationdate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifieddate != null">
        #{modifieddate,jdbcType=TIMESTAMP},
      </if>
      <if test="isactive != null">
        #{isactive,jdbcType=CHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=DECIMAL},
      </if>
      <if test="contracturl != null">
        #{contracturl,jdbcType=VARCHAR},
      </if>
      <if test="cStoreId != null">
        #{cStoreId,jdbcType=DECIMAL},
      </if>
      <if test="issaler != null">
        #{issaler,jdbcType=CHAR},
      </if>
      <if test="isopr != null">
        #{isopr,jdbcType=CHAR},
      </if>
      <if test="cCustomerId != null">
        #{cCustomerId,jdbcType=DECIMAL},
      </if>
      <if test="cCustomerupId != null">
        #{cCustomerupId,jdbcType=DECIMAL},
      </if>
      <if test="isstorer != null">
        #{isstorer,jdbcType=CHAR},
      </if>
      <if test="baseWage != null">
        #{baseWage,jdbcType=DECIMAL},
      </if>
      <if test="discountlimit != null">
        #{discountlimit,jdbcType=DECIMAL},
      </if>
      <if test="bigareamngId != null">
        #{bigareamngId,jdbcType=DECIMAL},
      </if>
      <if test="emptype != null">
        #{emptype,jdbcType=VARCHAR},
      </if>
      <if test="pdmtype != null">
        #{pdmtype,jdbcType=VARCHAR},
      </if>
      <if test="timecardno != null">
        #{timecardno,jdbcType=VARCHAR},
      </if>
      <if test="cSalestoreId != null">
        #{cSalestoreId,jdbcType=DECIMAL},
      </if>
      <if test="hrPsnId != null">
        #{hrPsnId,jdbcType=DECIMAL},
      </if>
      <if test="isConpeo != null">
        #{isConpeo,jdbcType=CHAR},
      </if>
      <if test="isCanEbsoout != null">
        #{isCanEbsoout,jdbcType=CHAR},
      </if>
      <if test="ygId != null">
        #{ygId,jdbcType=DECIMAL},
      </if>
      <if test="isPublicshoppers != null">
        #{isPublicshoppers,jdbcType=CHAR},
      </if>
      <if test="checkperson != null">
        #{checkperson,jdbcType=CHAR},
      </if>
      <if test="checkcode != null">
        #{checkcode,jdbcType=VARCHAR},
      </if>
      <if test="ehrId != null">
        #{ehrId,jdbcType=DECIMAL},
      </if>
      <if test="ehrRoleid != null">
        #{ehrRoleid,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="org.springcenter.marketing.modules.model.HrEmployee">
    update HR_EMPLOYEE
    <set>
      <if test="adClientId != null">
        AD_CLIENT_ID = #{adClientId,jdbcType=DECIMAL},
      </if>
      <if test="adOrgId != null">
        AD_ORG_ID = #{adOrgId,jdbcType=DECIMAL},
      </if>
      <if test="no != null">
        NO = #{no,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        NAME = #{name,jdbcType=VARCHAR},
      </if>
      <if test="pic != null">
        PIC = #{pic,jdbcType=VARCHAR},
      </if>
      <if test="cDepartmentId != null">
        C_DEPARTMENT_ID = #{cDepartmentId,jdbcType=DECIMAL},
      </if>
      <if test="hrRoleId != null">
        HR_ROLE_ID = #{hrRoleId,jdbcType=DECIMAL},
      </if>
      <if test="hrTitleId != null">
        HR_TITLE_ID = #{hrTitleId,jdbcType=DECIMAL},
      </if>
      <if test="hrPostId != null">
        HR_POST_ID = #{hrPostId,jdbcType=DECIMAL},
      </if>
      <if test="incumbencySts != null">
        INCUMBENCY_STS = #{incumbencySts,jdbcType=CHAR},
      </if>
      <if test="workForm != null">
        WORK_FORM = #{workForm,jdbcType=CHAR},
      </if>
      <if test="beginDate != null">
        BEGIN_DATE = #{beginDate,jdbcType=DECIMAL},
      </if>
      <if test="endDate != null">
        END_DATE = #{endDate,jdbcType=DECIMAL},
      </if>
      <if test="contractStart != null">
        CONTRACT_START = #{contractStart,jdbcType=DECIMAL},
      </if>
      <if test="contractEnd != null">
        CONTRACT_END = #{contractEnd,jdbcType=DECIMAL},
      </if>
      <if test="fullMemberDate != null">
        FULL_MEMBER_DATE = #{fullMemberDate,jdbcType=DECIMAL},
      </if>
      <if test="retireDate != null">
        RETIRE_DATE = #{retireDate,jdbcType=DECIMAL},
      </if>
      <if test="workLength != null">
        WORK_LENGTH = #{workLength,jdbcType=VARCHAR},
      </if>
      <if test="employeeFn != null">
        EMPLOYEE_FN = #{employeeFn,jdbcType=VARCHAR},
      </if>
      <if test="bankName != null">
        BANK_NAME = #{bankName,jdbcType=VARCHAR},
      </if>
      <if test="bankAccount != null">
        BANK_ACCOUNT = #{bankAccount,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        DESCRIPTION = #{description,jdbcType=VARCHAR},
      </if>
      <if test="sex != null">
        SEX = #{sex,jdbcType=CHAR},
      </if>
      <if test="cRegionId != null">
        C_REGION_ID = #{cRegionId,jdbcType=DECIMAL},
      </if>
      <if test="nation != null">
        NATION = #{nation,jdbcType=VARCHAR},
      </if>
      <if test="marriage != null">
        MARRIAGE = #{marriage,jdbcType=CHAR},
      </if>
      <if test="polity != null">
        POLITY = #{polity,jdbcType=VARCHAR},
      </if>
      <if test="birthdate != null">
        BIRTHDATE = #{birthdate,jdbcType=DECIMAL},
      </if>
      <if test="idCard != null">
        ID_CARD = #{idCard,jdbcType=VARCHAR},
      </if>
      <if test="hukou != null">
        HUKOU = #{hukou,jdbcType=VARCHAR},
      </if>
      <if test="domicileAdd != null">
        DOMICILE_ADD = #{domicileAdd,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        ADDRESS = #{address,jdbcType=VARCHAR},
      </if>
      <if test="postalcode != null">
        POSTALCODE = #{postalcode,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        PHONE = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="handset != null">
        HANDSET = #{handset,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        EMAIL = #{email,jdbcType=VARCHAR},
      </if>
      <if test="education != null">
        EDUCATION = #{education,jdbcType=CHAR},
      </if>
      <if test="hrSpecialityId != null">
        HR_SPECIALITY_ID = #{hrSpecialityId,jdbcType=DECIMAL},
      </if>
      <if test="school != null">
        SCHOOL = #{school,jdbcType=VARCHAR},
      </if>
      <if test="graduateDate != null">
        GRADUATE_DATE = #{graduateDate,jdbcType=DECIMAL},
      </if>
      <if test="cLanguage1Id != null">
        C_LANGUAGE1_ID = #{cLanguage1Id,jdbcType=DECIMAL},
      </if>
      <if test="level1 != null">
        LEVEL1 = #{level1,jdbcType=CHAR},
      </if>
      <if test="cLanguage2Id != null">
        C_LANGUAGE2_ID = #{cLanguage2Id,jdbcType=DECIMAL},
      </if>
      <if test="level2 != null">
        LEVEL2 = #{level2,jdbcType=CHAR},
      </if>
      <if test="computerLevel != null">
        COMPUTER_LEVEL = #{computerLevel,jdbcType=CHAR},
      </if>
      <if test="bodySts != null">
        BODY_STS = #{bodySts,jdbcType=VARCHAR},
      </if>
      <if test="height != null">
        HEIGHT = #{height,jdbcType=VARCHAR},
      </if>
      <if test="weight != null">
        WEIGHT = #{weight,jdbcType=VARCHAR},
      </if>
      <if test="eyesight != null">
        EYESIGHT = #{eyesight,jdbcType=VARCHAR},
      </if>
      <if test="bloodType != null">
        BLOOD_TYPE = #{bloodType,jdbcType=VARCHAR},
      </if>
      <if test="bio != null">
        BIO = #{bio,jdbcType=VARCHAR},
      </if>
      <if test="accessories != null">
        ACCESSORIES = #{accessories,jdbcType=VARCHAR},
      </if>
      <if test="ownerid != null">
        OWNERID = #{ownerid,jdbcType=DECIMAL},
      </if>
      <if test="modifierid != null">
        MODIFIERID = #{modifierid,jdbcType=DECIMAL},
      </if>
      <if test="creationdate != null">
        CREATIONDATE = #{creationdate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifieddate != null">
        MODIFIEDDATE = #{modifieddate,jdbcType=TIMESTAMP},
      </if>
      <if test="isactive != null">
        ISACTIVE = #{isactive,jdbcType=CHAR},
      </if>
      <if test="userId != null">
        USER_ID = #{userId,jdbcType=DECIMAL},
      </if>
      <if test="contracturl != null">
        CONTRACTURL = #{contracturl,jdbcType=VARCHAR},
      </if>
      <if test="cStoreId != null">
        C_STORE_ID = #{cStoreId,jdbcType=DECIMAL},
      </if>
      <if test="issaler != null">
        ISSALER = #{issaler,jdbcType=CHAR},
      </if>
      <if test="isopr != null">
        ISOPR = #{isopr,jdbcType=CHAR},
      </if>
      <if test="cCustomerId != null">
        C_CUSTOMER_ID = #{cCustomerId,jdbcType=DECIMAL},
      </if>
      <if test="cCustomerupId != null">
        C_CUSTOMERUP_ID = #{cCustomerupId,jdbcType=DECIMAL},
      </if>
      <if test="isstorer != null">
        ISSTORER = #{isstorer,jdbcType=CHAR},
      </if>
      <if test="baseWage != null">
        BASE_WAGE = #{baseWage,jdbcType=DECIMAL},
      </if>
      <if test="discountlimit != null">
        DISCOUNTLIMIT = #{discountlimit,jdbcType=DECIMAL},
      </if>
      <if test="bigareamngId != null">
        BIGAREAMNG_ID = #{bigareamngId,jdbcType=DECIMAL},
      </if>
      <if test="emptype != null">
        EMPTYPE = #{emptype,jdbcType=VARCHAR},
      </if>
      <if test="pdmtype != null">
        PDMTYPE = #{pdmtype,jdbcType=VARCHAR},
      </if>
      <if test="timecardno != null">
        TIMECARDNO = #{timecardno,jdbcType=VARCHAR},
      </if>
      <if test="cSalestoreId != null">
        C_SALESTORE_ID = #{cSalestoreId,jdbcType=DECIMAL},
      </if>
      <if test="hrPsnId != null">
        HR_PSN_ID = #{hrPsnId,jdbcType=DECIMAL},
      </if>
      <if test="isConpeo != null">
        IS_CONPEO = #{isConpeo,jdbcType=CHAR},
      </if>
      <if test="isCanEbsoout != null">
        IS_CAN_EBSOOUT = #{isCanEbsoout,jdbcType=CHAR},
      </if>
      <if test="ygId != null">
        YG_ID = #{ygId,jdbcType=DECIMAL},
      </if>
      <if test="isPublicshoppers != null">
        IS_PUBLICSHOPPERS = #{isPublicshoppers,jdbcType=CHAR},
      </if>
      <if test="checkperson != null">
        CHECKPERSON = #{checkperson,jdbcType=CHAR},
      </if>
      <if test="checkcode != null">
        CHECKCODE = #{checkcode,jdbcType=VARCHAR},
      </if>
      <if test="ehrId != null">
        EHR_ID = #{ehrId,jdbcType=DECIMAL},
      </if>
      <if test="ehrRoleid != null">
        EHR_ROLEID = #{ehrRoleid,jdbcType=DECIMAL},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="org.springcenter.marketing.modules.model.HrEmployee">
    update HR_EMPLOYEE
    set AD_CLIENT_ID = #{adClientId,jdbcType=DECIMAL},
      AD_ORG_ID = #{adOrgId,jdbcType=DECIMAL},
      NO = #{no,jdbcType=VARCHAR},
      NAME = #{name,jdbcType=VARCHAR},
      PIC = #{pic,jdbcType=VARCHAR},
      C_DEPARTMENT_ID = #{cDepartmentId,jdbcType=DECIMAL},
      HR_ROLE_ID = #{hrRoleId,jdbcType=DECIMAL},
      HR_TITLE_ID = #{hrTitleId,jdbcType=DECIMAL},
      HR_POST_ID = #{hrPostId,jdbcType=DECIMAL},
      INCUMBENCY_STS = #{incumbencySts,jdbcType=CHAR},
      WORK_FORM = #{workForm,jdbcType=CHAR},
      BEGIN_DATE = #{beginDate,jdbcType=DECIMAL},
      END_DATE = #{endDate,jdbcType=DECIMAL},
      CONTRACT_START = #{contractStart,jdbcType=DECIMAL},
      CONTRACT_END = #{contractEnd,jdbcType=DECIMAL},
      FULL_MEMBER_DATE = #{fullMemberDate,jdbcType=DECIMAL},
      RETIRE_DATE = #{retireDate,jdbcType=DECIMAL},
      WORK_LENGTH = #{workLength,jdbcType=VARCHAR},
      EMPLOYEE_FN = #{employeeFn,jdbcType=VARCHAR},
      BANK_NAME = #{bankName,jdbcType=VARCHAR},
      BANK_ACCOUNT = #{bankAccount,jdbcType=VARCHAR},
      DESCRIPTION = #{description,jdbcType=VARCHAR},
      SEX = #{sex,jdbcType=CHAR},
      C_REGION_ID = #{cRegionId,jdbcType=DECIMAL},
      NATION = #{nation,jdbcType=VARCHAR},
      MARRIAGE = #{marriage,jdbcType=CHAR},
      POLITY = #{polity,jdbcType=VARCHAR},
      BIRTHDATE = #{birthdate,jdbcType=DECIMAL},
      ID_CARD = #{idCard,jdbcType=VARCHAR},
      HUKOU = #{hukou,jdbcType=VARCHAR},
      DOMICILE_ADD = #{domicileAdd,jdbcType=VARCHAR},
      ADDRESS = #{address,jdbcType=VARCHAR},
      POSTALCODE = #{postalcode,jdbcType=VARCHAR},
      PHONE = #{phone,jdbcType=VARCHAR},
      HANDSET = #{handset,jdbcType=VARCHAR},
      EMAIL = #{email,jdbcType=VARCHAR},
      EDUCATION = #{education,jdbcType=CHAR},
      HR_SPECIALITY_ID = #{hrSpecialityId,jdbcType=DECIMAL},
      SCHOOL = #{school,jdbcType=VARCHAR},
      GRADUATE_DATE = #{graduateDate,jdbcType=DECIMAL},
      C_LANGUAGE1_ID = #{cLanguage1Id,jdbcType=DECIMAL},
      LEVEL1 = #{level1,jdbcType=CHAR},
      C_LANGUAGE2_ID = #{cLanguage2Id,jdbcType=DECIMAL},
      LEVEL2 = #{level2,jdbcType=CHAR},
      COMPUTER_LEVEL = #{computerLevel,jdbcType=CHAR},
      BODY_STS = #{bodySts,jdbcType=VARCHAR},
      HEIGHT = #{height,jdbcType=VARCHAR},
      WEIGHT = #{weight,jdbcType=VARCHAR},
      EYESIGHT = #{eyesight,jdbcType=VARCHAR},
      BLOOD_TYPE = #{bloodType,jdbcType=VARCHAR},
      BIO = #{bio,jdbcType=VARCHAR},
      ACCESSORIES = #{accessories,jdbcType=VARCHAR},
      OWNERID = #{ownerid,jdbcType=DECIMAL},
      MODIFIERID = #{modifierid,jdbcType=DECIMAL},
      CREATIONDATE = #{creationdate,jdbcType=TIMESTAMP},
      MODIFIEDDATE = #{modifieddate,jdbcType=TIMESTAMP},
      ISACTIVE = #{isactive,jdbcType=CHAR},
      USER_ID = #{userId,jdbcType=DECIMAL},
      CONTRACTURL = #{contracturl,jdbcType=VARCHAR},
      C_STORE_ID = #{cStoreId,jdbcType=DECIMAL},
      ISSALER = #{issaler,jdbcType=CHAR},
      ISOPR = #{isopr,jdbcType=CHAR},
      C_CUSTOMER_ID = #{cCustomerId,jdbcType=DECIMAL},
      C_CUSTOMERUP_ID = #{cCustomerupId,jdbcType=DECIMAL},
      ISSTORER = #{isstorer,jdbcType=CHAR},
      BASE_WAGE = #{baseWage,jdbcType=DECIMAL},
      DISCOUNTLIMIT = #{discountlimit,jdbcType=DECIMAL},
      BIGAREAMNG_ID = #{bigareamngId,jdbcType=DECIMAL},
      EMPTYPE = #{emptype,jdbcType=VARCHAR},
      PDMTYPE = #{pdmtype,jdbcType=VARCHAR},
      TIMECARDNO = #{timecardno,jdbcType=VARCHAR},
      C_SALESTORE_ID = #{cSalestoreId,jdbcType=DECIMAL},
      HR_PSN_ID = #{hrPsnId,jdbcType=DECIMAL},
      IS_CONPEO = #{isConpeo,jdbcType=CHAR},
      IS_CAN_EBSOOUT = #{isCanEbsoout,jdbcType=CHAR},
      YG_ID = #{ygId,jdbcType=DECIMAL},
      IS_PUBLICSHOPPERS = #{isPublicshoppers,jdbcType=CHAR},
      CHECKPERSON = #{checkperson,jdbcType=CHAR},
      CHECKCODE = #{checkcode,jdbcType=VARCHAR},
      EHR_ID = #{ehrId,jdbcType=DECIMAL},
      EHR_ROLEID = #{ehrRoleid,jdbcType=DECIMAL}
    where ID = #{id,jdbcType=DECIMAL}
  </update>


  <select id="selectListByIds" resultMap="BaseResultMap">
    select
    ID, AD_CLIENT_ID, AD_ORG_ID, NO, NAME, PIC, C_DEPARTMENT_ID, HR_ROLE_ID, HR_TITLE_ID,
    HR_POST_ID, INCUMBENCY_STS, WORK_FORM, BEGIN_DATE, END_DATE, CONTRACT_START, CONTRACT_END,
    FULL_MEMBER_DATE, RETIRE_DATE, WORK_LENGTH, EMPLOYEE_FN, BANK_NAME, BANK_ACCOUNT,
    DESCRIPTION, SEX, C_REGION_ID, NATION, MARRIAGE, POLITY, BIRTHDATE, ID_CARD, HUKOU,
    DOMICILE_ADD, ADDRESS, POSTALCODE, HANDSET AS PHONE, EMAIL, EDUCATION, HR_SPECIALITY_ID,
    SCHOOL, GRADUATE_DATE, C_LANGUAGE1_ID, LEVEL1, C_LANGUAGE2_ID, LEVEL2, COMPUTER_LEVEL,
    BODY_STS, HEIGHT, WEIGHT, EYESIGHT, BLOOD_TYPE, BIO, ACCESSORIES, OWNERID, MODIFIERID,
    CREATIONDATE, MODIFIEDDATE, ISACTIVE, USER_ID, CONTRACTURL, C_STORE_ID, ISSALER,
    ISOPR, C_CUSTOMER_ID, C_CUSTOMERUP_ID, ISSTORER, BASE_WAGE, DISCOUNTLIMIT, BIGAREAMNG_ID,
    EMPTYPE, PDMTYPE, TIMECARDNO, C_SALESTORE_ID, HR_PSN_ID, IS_CONPEO, IS_CAN_EBSOOUT,
    YG_ID, IS_PUBLICSHOPPERS, CHECKPERSON, CHECKCODE, EHR_ID, EHR_ROLEID
    from HR_EMPLOYEE
    where ID in
    <foreach collection="ids" item="id" open="(" close=")" separator=",">
      #{id}
    </foreach>
    AND ISACTIVE = 'Y'
  </select>







  <select id="activeIds" resultType="java.lang.Long">
    select
    ID
    from HR_EMPLOYEE
    where ID in
    <foreach collection="ids" item="id" open="(" close=")" separator=",">
      #{id}
    </foreach>
    and isactive = 'Y'
  </select>

  <select id="selectListByPhone" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from HR_EMPLOYEE
    where
    handset = #{phone}
    and isactive = 'Y'
  </select>


  <select id="selectListByStoreIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from HR_EMPLOYEE
    where
     isactive = 'Y'
    <if test="phone != null and  phone != ''">
      and handset = #{phone}
    </if>
    and c_store_id in
    <foreach collection="ids" item="id" open="(" close=")" separator=",">
      #{id}
    </foreach>
  </select>

</mapper>