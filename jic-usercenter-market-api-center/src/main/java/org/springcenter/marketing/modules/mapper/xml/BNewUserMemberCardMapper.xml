<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.marketing.modules.mapper.BNewUserMemberCardMapper">
  <resultMap id="BaseResultMap" type="org.springcenter.marketing.modules.model.BNewUserMemberCard">
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="UNIONID" jdbcType="VARCHAR" property="unionid" />
    <result column="B_MEMBER_CARD_ID" jdbcType="VARCHAR" property="bMemberCardId" />
    <result column="START_TIME" jdbcType="TIMESTAMP" property="startTime" />
    <result column="END_TIME" jdbcType="TIMESTAMP" property="endTime" />
    <result column="STATUS" jdbcType="DECIMAL" property="status" />
    <result column="IS_DEL" jdbcType="DECIMAL" property="isDel" />
    <result column="CARD_TYPE" jdbcType="DECIMAL" property="cardType" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="PULL_STATUS" jdbcType="TIMESTAMP" property="pullStatus" />
    <result column="OUT_NO" jdbcType="VARCHAR" property="outNo" />
    <result column="SUBSCRIBE_ID" jdbcType="VARCHAR" property="subscribeId" />
    <result column="APPLICABLE_PARTY" jdbcType="DECIMAL" property="applicationParty" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, unionid, B_MEMBER_CARD_ID,CARD_TYPE, START_TIME, END_TIME, STATUS, IS_DEL, CREATE_TIME,
    UPDATE_TIME,OUT_NO,SUBSCRIBE_ID,APPLICABLE_PARTY
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from b_new_user_member_card 
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <select id="selectListSelective" parameterType="org.springcenter.marketing.modules.model.BNewUserMemberCard" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from b_new_user_member_card
    where 1=1
    <if test="unionid != null">
      and unionid = #{unionid,jdbcType=VARCHAR}
    </if>
    <if test="bMemberCardId != null">
      and B_MEMBER_CARD_ID = #{bMemberCardId,jdbcType=VARCHAR}
    </if>
    <if test="startTime != null">
      and START_TIME = #{startTime,jdbcType=TIMESTAMP}
    </if>
    <if test="endTime != null">
      and END_TIME = #{endTime,jdbcType=TIMESTAMP}
    </if>
    <if test="status != null">
      and STATUS = #{status,jdbcType=DECIMAL}
    </if>
    <if test="isDel != null">
      and IS_DEL = #{isDel,jdbcType=DECIMAL}
    </if>
    <if test="cardType != null">
      and CARD_TYPE = #{cardType,jdbcType=DECIMAL}
    </if>
    <if test="outNo != null">
      and OUT_NO = #{outNo,jdbcType=VARCHAR}
    </if>
    <if test="subscribeId != null">
      and SUBSCRIBE_ID = #{subscribeId,jdbcType=VARCHAR}
    </if>
    order by create_time desc
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from b_new_user_member_card
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="org.springcenter.marketing.modules.model.BNewUserMemberCard">
    insert into b_new_user_member_card (ID, unionid, B_MEMBER_CARD_ID,
      START_TIME, END_TIME, STATUS,
      IS_DEL, CREATE_TIME, UPDATE_TIME
      )
    values (#{id,jdbcType=VARCHAR}, #{unionid}, #{bMemberCardId,jdbcType=VARCHAR},
      #{startTime,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP}, #{status,jdbcType=DECIMAL},
      #{isDel,jdbcType=DECIMAL}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="org.springcenter.marketing.modules.model.BNewUserMemberCard">
    insert into b_new_user_member_card
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="unionid != null">
        unionid,
      </if>
      <if test="bMemberCardId != null">
        B_MEMBER_CARD_ID,
      </if>
      <if test="startTime != null">
        START_TIME,
      </if>
      <if test="endTime != null">
        END_TIME,
      </if>
      <if test="cardType != null">
        CARD_TYPE,
      </if>
      <if test="status != null">
        STATUS,
      </if>
      <if test="isDel != null">
        IS_DEL,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="outNo != null">
         OUT_NO,
      </if>
      <if test="subscribeId != null">
         SUBSCRIBE_ID,
      </if>
      <if test="applicationParty != null">
        APPLICABLE_PARTY,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="unionid != null">
        #{unionid},
      </if>
      <if test="bMemberCardId != null">
        #{bMemberCardId,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cardType != null">
        #{cardType,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        #{status,jdbcType=DECIMAL},
      </if>
      <if test="isDel != null">
        #{isDel,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="outNo != null">
         #{outNo,jdbcType=VARCHAR},
      </if>
      <if test="subscribeId != null">
        #{subscribeId},
      </if>
      <if test="applicationParty != null">
        #{applicationParty},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="org.springcenter.marketing.modules.model.BNewUserMemberCard">
    update b_new_user_member_card
    <set>
      <if test="unionid != null">
        unionid = #{unionid},
      </if>
      <if test="bMemberCardId != null">
        B_MEMBER_CARD_ID = #{bMemberCardId,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        START_TIME = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        END_TIME = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        STATUS = #{status,jdbcType=DECIMAL},
      </if>
      <if test="cardType != null">
        CARD_TYPE = #{cardType,jdbcType=DECIMAL},
      </if>
      <if test="isDel != null">
        IS_DEL = #{isDel,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="outNo != null">
         OUT_NO = #{outNo,jdbcType=VARCHAR},
      </if>
      <if test="subscribeId != null">
         SUBSCRIBE_ID = #{subscribeId,jdbcType=VARCHAR},
      </if>
      <if test="applicationParty != null">
        APPLICABLE_PARTY = #{applicationParty},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="org.springcenter.marketing.modules.model.BNewUserMemberCard">
    update b_new_user_member_card
    set unionid = #{unionid},
      B_MEMBER_CARD_ID = #{bMemberCardId,jdbcType=VARCHAR},
      START_TIME = #{startTime,jdbcType=TIMESTAMP},
      END_TIME = #{endTime,jdbcType=TIMESTAMP},
      STATUS = #{status,jdbcType=DECIMAL},
      IS_DEL = #{isDel,jdbcType=DECIMAL},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
    where ID = #{id,jdbcType=VARCHAR}
  </update>

  <update id="updateForSubscription" parameterType="java.lang.String">

     UPDATE b_new_user_member_card SET PULL_STATUS=1 WHERE unionid = #{unionid}

  </update>


  <update id="updateUserStatus" parameterType="java.lang.String">

    UPDATE b_new_user_member_card SET PULL_STATUS=1

    where ID = #{id,jdbcType=VARCHAR}

  </update>


  <update id="updateForFormer" parameterType="java.util.List">

    update b_new_user_member_card set PULL_STATUS=0
    where ID in
    <foreach collection="scheduleIds" index="index" item="item" separator="," open="(" close=")">
      #{item}
    </foreach>

  </update>
  <select id="findShouldUpdateStatusByDate" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from b_new_user_member_card
    where STATUS = 1 and IS_DEL = 0 and #{now} > END_TIME
  </select>


    <update id="batchUpdateStatutsByDate">
     update b_new_user_member_card set status = 3 where STATUS = 1 and IS_DEL = 0 and #{now} > END_TIME
    </update>



  <select id="queryCardByUserId" resultMap="BaseResultMap">

    select
    <include refid="Base_Column_List" />
    from b_new_user_member_card
    where unionid = #{unionid} and STATUS = 1
    order by CREATE_TIME desc

  </select>








  <select id="selectValidListSelective"  resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from b_new_user_member_card
    where
     <if test="unionid != null and unionid != '' ">
       unionid = #{unionid,jdbcType=VARCHAR} and
     </if>
        STATUS = 1
      and IS_DEL = 0
    AND END_TIME > STR_TO_DATE(#{nowTime,jdbcType=TIMESTAMP}, '%Y-%m-%d %H:%i:%s')
    and APPLICABLE_PARTY  != 1
      order by card_type
  </select>




    <select id="selectAvailableMemberCardByUserId" resultMap="BaseResultMap">
      select <include refid="Base_Column_List"></include> from b_new_user_member_card where unionid = #{unionid}
      <if test="bMemberCardId != null">
        and B_MEMBER_CARD_ID = #{bMemberCardId}
      </if>
      and STATUS = 1
      and IS_DEL = 0
      <if test="nowTime != null">
        and END_TIME > #{nowTime}
      </if>
    </select>
    <select id="selectAvailableMemberCardByUserIdAndCardType" resultMap="BaseResultMap">
      select     <include refid="Base_Column_List" /> from b_new_user_member_card where UNIONID = #{unionid}
      <if test="applicationParty != null">
        and APPLICABLE_PARTY = #{applicationParty}
      </if>

      and IS_DEL = 0
      <if test="nowTime != null">
        and END_TIME > #{nowTime}
        and STATUS = 1
      </if>

    </select>


  <select id="selectAllMemberCardByUserIdAndCardType" resultMap="BaseResultMap">
    select     <include refid="Base_Column_List" /> from b_new_user_member_card where UNIONID = #{unionid}
    <if test="applicationParty != null">
      and APPLICABLE_PARTY = #{applicationParty}
    </if>
    and IS_DEL = 0
    <if test="nowTime != null">
      and END_TIME > #{nowTime}
    </if>

  </select>

  <select id="selectByUnionidAndCardId"
          resultMap="BaseResultMap">

    select
    <include refid="Base_Column_List" />
    from b_new_user_member_card
    where
    <if test="unionid != null and unionid != '' ">
      unionid = #{unionid,jdbcType=VARCHAR}
    </if>
    <if test="bMemberCardId != null and bMemberCardId != '' ">
      and B_MEMBER_CARD_ID = #{bMemberCardId}
    </if>
    and IS_DEL = 0
    order by card_type
  </select>
    <select id="selectBySubId" resultMap="BaseResultMap">
      select <include refid="Base_Column_List"></include> from b_new_user_member_card
    where SUBSCRIBE_ID = #{subId}
    </select>
    <select id="selectBySubIds" resultMap="BaseResultMap">
      select <include refid="Base_Column_List"></include> from b_new_user_member_card
      where SUBSCRIBE_ID  in
      <foreach collection="subIds" separator="," open="(" close=")" item="item">
        #{item}
      </foreach>
    </select>
  <select id="selectByOutNo" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"></include> from b_new_user_member_card
    where OUT_NO   = #{outNo}
  </select>


</mapper>
