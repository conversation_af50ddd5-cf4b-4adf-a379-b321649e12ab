<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.marketing.modules.mapper.BRightsMapper">
  <resultMap id="BaseResultMap" type="org.springcenter.marketing.modules.model.BRights">
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="NAME" jdbcType="VARCHAR" property="name" />
    <result column="OUT_NAME" jdbcType="VARCHAR" property="outName" />
    <result column="LABEL" jdbcType="VARCHAR" property="label" />
    <result column="ICON" jdbcType="VARCHAR" property="icon" />
    <result column="CONTENT" jdbcType="VARCHAR" property="content" />
    <result column="ACTION_URL" jdbcType="VARCHAR" property="actionUrl" />
    <result column="B_MEMBER_CARD_ID" jdbcType="VARCHAR" property="bMemberCardId" />
    <result column="RIGHTS_TYPE" jdbcType="DECIMAL" property="rightsType" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="STATUS" jdbcType="DECIMAL" property="status" />
    <result column="IS_DEL" jdbcType="DECIMAL" property="isDel" />
    <result column="ACTION_NAME" jdbcType="VARCHAR" property="actionName" />
    <result column="DETAIL_IMGS" jdbcType="VARCHAR" property="detailImgs"/>
    <result column="SORTED" jdbcType="DECIMAL" property="sorted" />
    <result column="EFFECTIVE_TIME_NUM" jdbcType="DECIMAL" property="effectiveTimeNum" />
    <result column="SUITABLE_STORE_TYPE" jdbcType="DECIMAL" property="suitableStoreType" />
    <result column="BIRTH_GIF" jdbcType="VARCHAR" property="birthGif" />
    <result column="IS_ONCE_FOR_ALL" jdbcType="DECIMAL" property="isOnceForAll" />
    <result column="CYCLE_TYPE" jdbcType="DECIMAL" property="cycleType" />
    <result column="UPGRADE_CONTENT" jdbcType="VARCHAR" property="upgradeContent" />
    <result column="ACTION_TYPE" jdbcType="DECIMAL" property="actionType" />
    <result column="RIGHTS_VALUE" jdbcType="DECIMAL" property="rightsValue" />
    <result column="RIGHTS_IMG" jdbcType="VARCHAR" property="rightsImg" />
    <result column="BUTTON_NAME" jdbcType="VARCHAR" property="buttonName" />

    <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy" />
    <result column="EXTEND_JSON" jdbcType="VARCHAR" property="extendJson" />

    <result column="SUIT_CUS_TYPE" jdbcType="DECIMAL" property="suitCusType" />
    <result column="people_crowd_ids" jdbcType="VARCHAR" property="peopleCrowdIds" />

  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="org.springcenter.marketing.modules.model.BRights">
    <result column="USE_RULE" jdbcType="VARCHAR" property="useRule" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, NAME, OUT_NAME, LABEL, ICON, CONTENT, ACTION_URL, B_MEMBER_CARD_ID, RIGHTS_TYPE, CREATE_TIME,
    UPDATE_TIME, STATUS, IS_DEL, ACTION_NAME,SORTED,DETAIL_IMGS,EFFECTIVE_TIME_NUM,SUITABLE_STORE_TYPE,BIRTH_GIF,
        CYCLE_TYPE,IS_ONCE_FOR_ALL,UPGRADE_CONTENT,RIGHTS_VALUE,RIGHTS_IMG,ACTION_TYPE,BUTTON_NAME,UPDATE_BY,EXTEND_JSON,SUIT_CUS_TYPE,people_crowd_ids
  </sql>
  <sql id="Blob_Column_List">
    USE_RULE
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from b_rights 
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <select id="selectListBySelective" parameterType="org.springcenter.marketing.modules.model.BRights" resultMap="ResultMapWithBLOBs">
    select
    b.ID, NAME, b.OUT_NAME, b.LABEL, b.ICON, b.CONTENT, b.ACTION_URL, b.RIGHTS_TYPE, b.CREATE_TIME,
    b.UPDATE_TIME, b.STATUS, b.IS_DEL, b.ACTION_NAME,b.SORTED,b.DETAIL_IMGS,b.EFFECTIVE_TIME_NUM,b.SUITABLE_STORE_TYPE,b.BIRTH_GIF,
    b.CYCLE_TYPE,b.IS_ONCE_FOR_ALL,b.UPGRADE_CONTENT,b.RIGHTS_VALUE,b.RIGHTS_IMG,b.ACTION_TYPE,   b.USE_RULE,b.UPDATE_BY,b.EXTEND_JSON,b.SUIT_CUS_TYPE,b.people_crowd_ids

    <if test="bMemberCardId != null">
    ,r.MEMBER_CARD_ID  as B_MEMBER_CARD_ID
    </if>
    from  b_rights b
    <if test="bMemberCardId != null">
      left join rights_member_card_relation r on r.RIGHTS_ID = b.id
    </if>

    where 1=1
    <if test="name != null">
      and b.NAME like '%${name}%' or LABEL like '%${name}%'
    </if>
    <if test="bMemberCardId != null">
      and r.MEMBER_CARD_ID = #{bMemberCardId,jdbcType=VARCHAR}
    </if>
    <if test="rightsType != null">
      and b.RIGHTS_TYPE = #{rightsType,jdbcType=DECIMAL}
    </if>
    <if test="status != null">
      and b.STATUS = #{status,jdbcType=DECIMAL}
    </if>
    <if test="isDel != null">
      and b.IS_DEL = #{isDel,jdbcType=DECIMAL}
    </if>
    <if test="bMemberCardId != null">
      and r.is_del  = #{isDel,jdbcType=DECIMAL}
    </if>
    <if test="id != null">
      and b.ID = #{id}
    </if>
    <if test="suitableStoreType != null">
      and b.SUITABLE_STORE_TYPE = #{suitableStoreType}
    </if>
    order by b.CREATE_TIME desc
  </select>
    <select id="getRights" resultType="org.springcenter.marketing.api.dto.RightsResp">

 SELECT bmc.id  rightsPackageId,
               br.id  rightsId,
               br.effective_time_num effectiveTimeNum,
               br.suitable_store_type suitableStoreType,
               br.use_rule useRule,
               br.SUIT_CUS_TYPE suitCusType,
               br.people_crowd_ids as peopleCrowdIds
      FROM b_member_card bmc
          left join rights_member_card_relation r on bmc.id = r.MEMBER_CARD_ID
          left join b_rights br on  r.RIGHTS_ID = br.id
     WHERE bmc.APPLICABLE_PARTY = 2
       AND bmc.status = 1
       AND br.status = 1
       AND br.is_del = 0
       AND bmc.is_del = 0
       and r.is_del = 0
       AND br.rights_type = #{rightsType}
       AND bmc.BRAND_ID = #{brandId,jdbcType=VARCHAR}
       AND bmc.CARD_LEVEL = #{cardLevel}
    </select>


  <select id="getRightsByBrandIds" resultType="org.springcenter.marketing.api.dto.RightsResp">

    SELECT bmc.id  rightsPackageId,
           br.id  rightsId,
           br.effective_time_num effectiveTimeNum,
           br.suitable_store_type suitableStoreType,
           br.use_rule useRule,
           br.SUIT_CUS_TYPE suitCusType,
           bmc.CARD_LEVEL as cardLevel,
           br.people_crowd_ids as peopleCrowdIds
    FROM b_member_card bmc
           left join rights_member_card_relation r on bmc.id = r.MEMBER_CARD_ID
           left join b_rights br on  r.RIGHTS_ID = br.id
    WHERE bmc.APPLICABLE_PARTY = 2
      AND bmc.status = 1
      AND br.status = 1
      and bmc.status = 1
      and r.is_del = 0
      AND br.rights_type = #{rightsType}
      AND bmc.BRAND_ID  in
          <foreach collection="brandIds" open="(" close=")" item="item" separator=",">
            #{item}
          </foreach>
  </select>


  <select id="getRightsByIds" resultType="org.springcenter.marketing.api.dto.RightsResp">

    SELECT bmc.id  rightsPackageId,
           br.id  rightsId,
           br.effective_time_num effectiveTimeNum,
           br.suitable_store_type suitableStoreType,
           br.use_rule useRule,
           br.SUIT_CUS_TYPE suitCusType,
          br.people_crowd_ids as peopleCrowdIds
    FROM b_member_card bmc
           left join rights_member_card_relation r on bmc.id = r.MEMBER_CARD_ID
           left join b_rights br on  r.RIGHTS_ID = br.id
    WHERE bmc.APPLICABLE_PARTY = 2
      AND bmc.status = 1
      AND br.status = 1
      and bmc.status = 1
      and r.is_del = 0
    and br.ID  in
    <foreach collection="ids" separator="," close=")" open="(" item="item">
      #{item}
    </foreach>
  </select>

  <select id="selectByMemberCardIds" resultMap="ResultMapWithBLOBs">
    select
    b.ID, NAME, b.OUT_NAME, b.LABEL, b.ICON, b.CONTENT, b.ACTION_URL,  b.RIGHTS_TYPE, b.CREATE_TIME,
    b.UPDATE_TIME, b.STATUS, b.IS_DEL, b.ACTION_NAME,b.SORTED,b.DETAIL_IMGS,b.EFFECTIVE_TIME_NUM,b.SUITABLE_STORE_TYPE,b.BIRTH_GIF,
    b.CYCLE_TYPE,b.IS_ONCE_FOR_ALL,b.UPGRADE_CONTENT,b.RIGHTS_VALUE,b.RIGHTS_IMG,b.ACTION_TYPE,
    b.USE_RULE,r.MEMBER_CARD_ID  as B_MEMBER_CARD_ID,b.UPDATE_BY,b.EXTEND_JSON,b.BUTTON_NAME,b.SUIT_CUS_TYPE,b.people_crowd_ids
    from rights_member_card_relation r
    left join b_rights b on r.RIGHTS_ID = b.id
    where r.MEMBER_CARD_ID  in
    <foreach collection="memberCardIds" item="item" open="(" close=")" separator=",">
      #{item}
    </foreach>
    AND b.status = 1 and b.is_del = 0 and r.IS_DEL = 0
    order by b.sorted ASC,b.create_time desc
  </select>
  <select id="selectRightsBySeletive" resultMap="ResultMapWithBLOBs">

    select
    b.ID
    from b_rights b
      <if test=" bMemberCardId != null">
        left join rights_member_card_relation r on r.RIGHTS_ID = b.id
      </if>
    where 1=1 and b.IS_DEL = 0
    <if test="name != null and name != ''">
      and (UPPER(b.NAME) like '%${name}%' or b.LABEL like '%${name}%' or b.OUT_NAME like '%${name}%')
    </if>
    <if test="bMemberCardId != null">
      and r.MEMBER_CARD_ID = #{bMemberCardId,jdbcType=VARCHAR}
      and r.is_del  = 0
    </if>
    <if test="rightsType != null">
      and b.RIGHTS_TYPE = #{rightsType,jdbcType=DECIMAL}
    </if>
    <if test="status != null">
      and b.STATUS = #{status,jdbcType=DECIMAL}
    </if>
    <if test="isDel != null">
      and b.IS_DEL = #{isDel,jdbcType=DECIMAL}
    </if>
    <if test="id != null">
      and b.ID = #{id}
    </if>
    <if test="suitableStoreType != null">
      and b.SUITABLE_STORE_TYPE = #{suitableStoreType}
    </if>

    <if test="usePeopleCrowd == 1 ">
      and b.people_crowd_ids is not null
    </if>

    <if test="usePeopleCrowd  == 0 ">
      and b.people_crowd_ids is null
    </if>
    order by b.update_time desc
  </select>
  <select id="selectByPrimaryKeys" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from b_rights
    where ID  in
    <foreach collection="ids" separator="," close=")" open="(" item="item">
      #{item}
    </foreach>
    order by UPDATE_TIME desc
  </select>
  <select id="selectByPrimaryKeysOrderByCreateTime" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from b_rights
    where ID  in
    <foreach collection="ids" separator="," close=")" open="(" item="item">
      #{item}
    </foreach>
    order by CREATE_TIME desc
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from b_rights
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="org.springcenter.marketing.modules.model.BRights">
    insert into b_rights (ID, NAME, LABEL,
      ICON, CONTENT, ACTION_URL,
      B_MEMBER_CARD_ID, RIGHTS_TYPE, CREATE_TIME,
      UPDATE_TIME, STATUS, IS_DEL,
      ACTION_NAME, USE_RULE)
    values (#{id,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{label,jdbcType=VARCHAR},
      #{icon,jdbcType=VARCHAR}, #{content,jdbcType=VARCHAR}, #{actionUrl,jdbcType=VARCHAR},
      #{bMemberCardId,jdbcType=VARCHAR}, #{rightsType,jdbcType=DECIMAL}, #{createTime,jdbcType=TIMESTAMP},
      #{updateTime,jdbcType=TIMESTAMP}, #{status,jdbcType=DECIMAL}, #{isDel,jdbcType=DECIMAL},
      #{actionName,jdbcType=VARCHAR}, #{useRule,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="org.springcenter.marketing.modules.model.BRights">
    insert into b_rights
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="name != null">
        NAME,
      </if>
      <if test="outName != null">
        OUT_NAME,
      </if>
      <if test="label != null">
        LABEL,
      </if>
      <if test="icon != null">
        ICON,
      </if>
      <if test="content != null">
        CONTENT,
      </if>
      <if test="actionUrl != null">
        ACTION_URL,
      </if>
      <if test="bMemberCardId != null">
        B_MEMBER_CARD_ID,
      </if>
      <if test="rightsType != null">
        RIGHTS_TYPE,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="status != null">
        STATUS,
      </if>
      <if test="isDel != null">
        IS_DEL,
      </if>
      <if test="actionName != null">
        ACTION_NAME,
      </if>
      <if test="useRule != null">
        USE_RULE,
      </if>
      <if test="sorted != null">
        SORTED,
      </if>
      <if test="detailImgs !=null">
        DETAIL_IMGS,
      </if>
      <if test="effectiveTimeNum !=null">
        EFFECTIVE_TIME_NUM,
      </if>
      <if test="suitableStoreType !=null">
        SUITABLE_STORE_TYPE,
      </if>

      <if test="birthGif !=null">
        BIRTH_GIF,
      </if>

      <if test="cycleType !=null">
        CYCLE_TYPE,
      </if>
      <if test="isOnceForAll !=null">
        IS_ONCE_FOR_ALL,
      </if>
      <if test="upgradeContent !=null">
        UPGRADE_CONTENT,
      </if>
      <if test="actionType !=null">
        ACTION_TYPE,
      </if>
      <if test="rightsValue !=null">
        RIGHTS_VALUE,
      </if>
      <if test="rightsImg !=null">
        RIGHTS_IMG,
      </if>
      <if test="buttonName !=null">
        BUTTON_NAME,
      </if>
      <if test="updateBy !=null">
        UPDATE_BY,
      </if>
      <if test="extendJson !=null">
        EXTEND_JSON,
      </if>
      <if test="suitCusType !=null">
        SUIT_CUS_TYPE,
      </if>
      <if test="peopleCrowdIds !=null">
        people_crowd_ids,
      </if>

    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="outName != null">
        #{outName,jdbcType=VARCHAR},
      </if>
      <if test="label != null">
        #{label,jdbcType=VARCHAR},
      </if>
      <if test="icon != null">
        #{icon,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        #{content,jdbcType=VARCHAR},
      </if>
      <if test="actionUrl != null">
        #{actionUrl,jdbcType=VARCHAR},
      </if>
      <if test="bMemberCardId != null">
        #{bMemberCardId,jdbcType=VARCHAR},
      </if>
      <if test="rightsType != null">
        #{rightsType,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=DECIMAL},
      </if>
      <if test="isDel != null">
        #{isDel,jdbcType=DECIMAL},
      </if>
      <if test="actionName != null">
        #{actionName,jdbcType=VARCHAR},
      </if>
      <if test="useRule != null">
        #{useRule,jdbcType=VARCHAR},
      </if>
      <if test="sorted != null">
        #{sorted,jdbcType=VARCHAR},
      </if>
      <if test="detailImgs != null">
        #{detailImgs,jdbcType=VARCHAR},
      </if>
      <if test="effectiveTimeNum !=null">
        #{effectiveTimeNum,jdbcType=DECIMAL},
      </if>
      <if test="suitableStoreType !=null">
        #{suitableStoreType,jdbcType=DECIMAL},
      </if>
      <if test="birthGif !=null">
        #{birthGif},
      </if>
      <if test="cycleType !=null">
        #{cycleType},
      </if>
      <if test="isOnceForAll !=null">
        #{isOnceForAll},
      </if>
      <if test="upgradeContent !=null">
        #{upgradeContent},
      </if>

      <if test="actionType !=null">
        #{actionType},
      </if>
      <if test="rightsValue !=null">
        #{rightsValue},
      </if>
      <if test="rightsImg !=null">
        #{rightsImg},
      </if>
      <if test="buttonName !=null">
        #{buttonName},
      </if>
      <if test="updateBy !=null">
        #{updateBy},
      </if>
      <if test="extendJson !=null">
        #{extendJson},
      </if>
      <if test="suitCusType !=null">
        #{suitCusType},
      </if>
      <if test="peopleCrowdIds !=null">
        #{peopleCrowdIds},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="org.springcenter.marketing.modules.model.BRights">
    update b_rights
    <set>
      <if test="name != null">
        NAME = #{name,jdbcType=VARCHAR},
      </if>
      <if test="outName != null">
        OUT_NAME = #{outName,jdbcType=VARCHAR},
      </if>
      <if test="label != null">
        LABEL = #{label,jdbcType=VARCHAR},
      </if>
      <if test="icon != null">
        ICON = #{icon,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        CONTENT = #{content,jdbcType=VARCHAR},
      </if>
      <if test="actionUrl != null">
        ACTION_URL = #{actionUrl,jdbcType=VARCHAR},
      </if>
      <if test="bMemberCardId != null">
        B_MEMBER_CARD_ID = #{bMemberCardId,jdbcType=VARCHAR},
      </if>
      <if test="rightsType != null">
        RIGHTS_TYPE = #{rightsType,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        STATUS = #{status,jdbcType=DECIMAL},
      </if>
      <if test="isDel != null">
        IS_DEL = #{isDel,jdbcType=DECIMAL},
      </if>
      <if test="actionName != null">
        ACTION_NAME = #{actionName,jdbcType=VARCHAR},
      </if>
      <if test="useRule != null">
        USE_RULE = #{useRule,jdbcType=VARCHAR},
      </if>
      <if test="sorted != null">
        SORTED = #{sorted,jdbcType=DECIMAL},
      </if>
      <if test="detailImgs != null">
        DETAIL_IMGS = #{detailImgs,jdbcType=VARCHAR},
      </if>
      <if test="effectiveTimeNum !=null">
        EFFECTIVE_TIME_NUM = #{effectiveTimeNum,jdbcType=DECIMAL},
      </if>
      <if test="suitableStoreType !=null">
        SUITABLE_STORE_TYPE = #{suitableStoreType,jdbcType=DECIMAL},
      </if>
      <if test="birthGif !=null">
        BIRTH_GIF = #{birthGif},
      </if>
      <if test="cycleType !=null">
        CYCLE_TYPE =  #{cycleType},
      </if>
      <if test="isOnceForAll !=null">
        IS_ONCE_FOR_ALL = #{isOnceForAll},
      </if>
      <if test="upgradeContent !=null">
        UPGRADE_CONTENT =  #{upgradeContent},
      </if>
      <if test="actionType !=null">
        action_type =  #{actionType},
      </if>

      <if test="rightsValue == null">
        RIGHTS_VALUE =  null,
      </if>
      <if test="rightsValue !=null">
        RIGHTS_VALUE =  #{rightsValue},
      </if>
      <if test="rightsImg !=null">
        RIGHTS_IMG =  #{rightsImg},
      </if>
      <if test="buttonName !=null">
        BUTTON_NAME = #{buttonName},
      </if>
      <if test="updateBy !=null">
        UPDATE_BY = #{updateBy},
      </if>
      <if test="extendJson !=null">
        EXTEND_JSON = #{extendJson},
      </if>
      <if test="suitCusType !=null">
        SUIT_CUS_TYPE = #{suitCusType},
      </if>
      <if test="peopleCrowdIds !=null and peopleCrowdIds != '' ">
        people_crowd_ids = #{peopleCrowdIds},
      </if>
      <if test="peopleCrowdIds !=null and peopleCrowdIds== '' ">
        people_crowd_ids = null,
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="org.springcenter.marketing.modules.model.BRights">
    update b_rights
    set NAME = #{name,jdbcType=VARCHAR},
      LABEL = #{label,jdbcType=VARCHAR},
      ICON = #{icon,jdbcType=VARCHAR},
      CONTENT = #{content,jdbcType=VARCHAR},
      ACTION_URL = #{actionUrl,jdbcType=VARCHAR},
      B_MEMBER_CARD_ID = #{bMemberCardId,jdbcType=VARCHAR},
      RIGHTS_TYPE = #{rightsType,jdbcType=DECIMAL},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      STATUS = #{status,jdbcType=DECIMAL},
      IS_DEL = #{isDel,jdbcType=DECIMAL},
      ACTION_NAME = #{actionName,jdbcType=VARCHAR},
      USE_RULE = #{useRule,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="org.springcenter.marketing.modules.model.BRights">
    update b_rights
    set NAME = #{name,jdbcType=VARCHAR},
      LABEL = #{label,jdbcType=VARCHAR},
      ICON = #{icon,jdbcType=VARCHAR},
      CONTENT = #{content,jdbcType=VARCHAR},
      ACTION_URL = #{actionUrl,jdbcType=VARCHAR},
      B_MEMBER_CARD_ID = #{bMemberCardId,jdbcType=VARCHAR},
      RIGHTS_TYPE = #{rightsType,jdbcType=DECIMAL},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      STATUS = #{status,jdbcType=DECIMAL},
      IS_DEL = #{isDel,jdbcType=DECIMAL},
      ACTION_NAME = #{actionName,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
</mapper>
