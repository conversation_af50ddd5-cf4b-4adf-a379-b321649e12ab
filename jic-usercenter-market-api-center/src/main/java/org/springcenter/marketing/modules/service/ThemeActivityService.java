package org.springcenter.marketing.modules.service;

import com.jnby.common.Page;
import org.springcenter.marketing.api.dto.admin.*;
import org.springcenter.marketing.api.dto.miniapp.ActivityAwardListResp;

import java.util.List;

public interface ThemeActivityService {
    /**
     * 新增或者更新或者删除奖励
     * @param requestData
     * @return
     */
    List<Integer> saveOrUpdateListAward(SaveOrUpdateListAwardReq requestData);

    /**
     * 根据ids查询奖励信息
     * @param requestData
     * @return
     */
    List<SaveOrUpdateListAwardReq.ThemeActivityDaysRuleAwardReq> findAwardByIds(List<Integer> requestData);

    void saveOrUpdateActivity(ThemeActivityReq requestData);

    /**
     * 列表信息
     * @param requestData
     * @param page
     * @return
     */
    List<ThemeActivityReq> activityList(ActivityListReq requestData, Page page);

    /**
     * 删除活动
     * @param requestData
     */
    void deleteActivity(Integer requestData);

    /**
     * 根据id变更status状态
     * @param requestData
     */
    void updateStatusById(UpdateStatusById requestData);

    /**
     * 用户奖励列表
     * @param requestData
     * @param page
     * @return
     */
    List<UserAwardListResp> userAwardList(UserAwardListReq requestData, Page page);

    void sendCouponOrPoint(SendCouponOrPointDto sendCouponOrPointDto);

    /**
     * 实物发货
     * @param requestData
     */
    boolean sendMaterialById(SendMaterialById requestData);

    String exportUserAwardList(UserAwardListReq requestData);

    /**
     * 导入excel
     * @param requestData
     * @return
     */
    String importSendMaterialUserAward(String requestData);

    /**
     * 生成奖励job  活动结束后立即生成
     * 并且需要发送公众号消息
     */
    void genUserAwardJob();

    void updateMaterialAddress(SendMaterialById requestData);

    ShowSendPointButtonAndSendCouponButton showSendPointButtonAndSendCouponButton(SendCouponOrPointDto requestData);

    /**
     * c端查询活动奖励信息  按照规则返回
     * @param requestData
     * @return
     */
    List<ActivityAwardListResp> getActivityAwardList(String requestData);
}
