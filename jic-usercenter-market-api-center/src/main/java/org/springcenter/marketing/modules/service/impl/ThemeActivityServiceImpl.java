package org.springcenter.marketing.modules.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jnby.common.CommonRequest;
import com.jnby.common.Page;
import com.jnby.common.ResponseResult;
import com.jnby.common.util.QiniuUtil;
import com.jnby.common.util.excel.BaseNoModelDataAbstractListener;
import com.jnby.common.util.excel.EasyExcelUtil;
import com.jnby.common.util.excel.IWriteDataExcel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springcenter.marketing.api.constant.IdConstant;
import org.springcenter.marketing.api.context.SendWxOffMsgContext;
import org.springcenter.marketing.api.dto.CustomerBaseResponse;
import org.springcenter.marketing.api.dto.admin.*;
import org.springcenter.marketing.api.dto.miniapp.ActivityAwardListResp;
import org.springcenter.marketing.modules.context.*;
import org.springcenter.marketing.modules.mapper.*;
import org.springcenter.marketing.modules.model.*;
import org.springcenter.marketing.modules.service.*;
import org.springcenter.marketing.modules.util.DateUtils;
import org.springcenter.marketing.modules.util.FileParseUtil;
import org.springcenter.marketing.modules.wxMapper.CardmainMapper;
import org.springcenter.marketing.modules.wxMapper.JicVoucherSendRecordMapper;
import org.springcenter.paycenter.api.IPayCenterApi;
import org.springcenter.paycenter.api.req.SendCouponReq;
import org.springcenter.product.api.enums.IsDeleteEnum;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ThemeActivityServiceImpl implements ThemeActivityService {

    @Autowired
    private AwardConfigMapper awardConfigMapper;

    @Autowired
    private ThemeActivityMapper themeActivityMapper;

    @Autowired
    private ThemeActivityDaysRuleMapper themeActivityDaysRuleMapper;

    @Autowired
    private SendAwardLogMapper sendAwardLogMapper;

    @Autowired
    private IUserVipService iUserVipService;

    @Autowired
    private JicVoucherSendRecordMapper jicVoucherSendRecordMapper;

    @Autowired
    private IVoucherService iVoucherService;

    private String merchid;

    @Autowired
    private IPayCenterApi iPayCenterApi;

    @Value("${wx.brand.commonConfigs}")
    private String commonConfigs;

    @Autowired
    private ThemeActivityUserAwardMapper themeActivityUserAwardMapper;

    @Autowired
    private IAddIntegerService iAddIntegerService;

    @Autowired
    private QiniuUtil qiniuUtil;

    @Autowired
    private MessageService messageService;

    @Autowired
    private CardmainMapper cardmainMapper;

    @Override
    public List<Integer> saveOrUpdateListAward(SaveOrUpdateListAwardReq requestData) {
        List<Integer> ids = new ArrayList<>();

        if(requestData == null){
            return new ArrayList<>();
        }
        // 处理数据
        List<Integer> deleteIds = requestData.getDeleteIds();
        if(CollectionUtils.isNotEmpty(deleteIds)){
            // 处理数据  删除
            QueryWrapper<AwardConfig> queryWrapper  = new QueryWrapper<>();
            queryWrapper.in("id",deleteIds);
            AwardConfig awardConfig = new AwardConfig();
            awardConfig.setIsDel(1);
            awardConfigMapper.update(awardConfig,queryWrapper);
        }
        // 更新或者插入
        if(CollectionUtils.isNotEmpty(requestData.getSaveOrUpdateList())){
            List<SaveOrUpdateListAwardReq.ThemeActivityDaysRuleAwardReq> saveOrUpdateList = requestData.getSaveOrUpdateList();
            List<SaveOrUpdateListAwardReq.ThemeActivityDaysRuleAwardReq> updateList = saveOrUpdateList.stream().filter(r -> r.getId() != null).collect(Collectors.toList());
            List<SaveOrUpdateListAwardReq.ThemeActivityDaysRuleAwardReq> saveList = saveOrUpdateList.stream().filter(r -> r.getId() == null).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(updateList)){
                for (SaveOrUpdateListAwardReq.ThemeActivityDaysRuleAwardReq themeActivityDaysRuleAwardReq : saveList) {
                    ids.add(themeActivityDaysRuleAwardReq.getId());
                    AwardConfig awardConfig = new AwardConfig();
                    BeanUtils.copyProperties(themeActivityDaysRuleAwardReq, awardConfig);
                    awardConfig.setUpdateTime(new Date());
                    awardConfigMapper.updateById(awardConfig);
                }
            }
            if(CollectionUtils.isNotEmpty(saveList)){
                for (SaveOrUpdateListAwardReq.ThemeActivityDaysRuleAwardReq themeActivityDaysRuleAwardReq : saveList) {
                    AwardConfig awardConfig = new AwardConfig();
                    BeanUtils.copyProperties(themeActivityDaysRuleAwardReq, awardConfig);
                    awardConfig.setUpdateTime(new Date());
                    awardConfig.setCreateTime(new Date());
                    awardConfig.setIsDel(IsDeleteEnum.NORMAL.getCode());
                    awardConfigMapper.insert(awardConfig);
                    ids.add(awardConfig.getId());
                }
            }
        }
        return ids;
    }

    @Override
    public List<SaveOrUpdateListAwardReq.ThemeActivityDaysRuleAwardReq> findAwardByIds(List<Integer> ruleAwardIds) {
        QueryWrapper<AwardConfig> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("id",ruleAwardIds);
        queryWrapper.eq("is_del",0);
        List<AwardConfig> awardConfigs = awardConfigMapper.selectList(queryWrapper);
        return JSONObject.parseArray(JSONObject.toJSONString(awardConfigs), SaveOrUpdateListAwardReq.ThemeActivityDaysRuleAwardReq.class);
    }

    @Override
    public void saveOrUpdateActivity(ThemeActivityReq themeActivityReq) {
        // 处理信息
        ThemeActivity themeActivity = new ThemeActivity();
        BeanUtils.copyProperties(themeActivityReq,themeActivity);
        if(themeActivity.getId() != null){
            themeActivityMapper.updateById(themeActivity);
        }else{
            themeActivityMapper.insert(themeActivity);
        }
        // 活动主键id
        Integer id = themeActivity.getId();
        // 需要删除的
        List<Integer> shouldDeleteIds = themeActivityReq.getShouldDeleteIds();
        if(CollectionUtils.isNotEmpty(shouldDeleteIds)){
            QueryWrapper<ThemeActivityDaysRule> queryWrapper = new QueryWrapper<>();
            queryWrapper.in("id",shouldDeleteIds);
            queryWrapper.eq("is_del",IsDeleteEnum.NORMAL.getCode());
            ThemeActivityDaysRule themeActivityDaysRule = new ThemeActivityDaysRule();
            themeActivityDaysRule.setIsDel(IsDeleteEnum.IS_DELETED.getCode());
            themeActivityDaysRuleMapper.update(themeActivityDaysRule,queryWrapper);
        }
        // 需要新增或者修改的
        List<ThemeActivityReq.ThemeActivityDaysRuleDto> list = themeActivityReq.getList();
        if(CollectionUtils.isNotEmpty(list)){
            // 如果有id  则是修改   如果没有id  则是新增  按照顺序处理
            int i = 1;
            for (ThemeActivityReq.ThemeActivityDaysRuleDto themeActivityDaysRuleDto : list) {
                ThemeActivityDaysRule themeActivityDaysRule = new ThemeActivityDaysRule();
                BeanUtils.copyProperties(themeActivityDaysRuleDto,themeActivityDaysRule);
                Integer daysRuleId = themeActivityDaysRule.getId();
                themeActivityDaysRule.setSortNum(i);
                themeActivityDaysRule.setUpdateTime(new Date());
                themeActivityDaysRule.setThemeActivityId(id);
                if(daysRuleId != null){
                    // 更新
                    themeActivityDaysRuleMapper.updateById(themeActivityDaysRule);
                }else{
                    // 新增
                    themeActivityDaysRule.setCreateTime(new Date());
                    themeActivityDaysRuleMapper.insert(themeActivityDaysRule);
                }
                i++;
            }

        }
    }

    @Override
    public List<ThemeActivityReq> activityList(ActivityListReq requestData, Page page) {
        com.github.pagehelper.Page<ThemeActivity> hPageTotal = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        QueryWrapper<ThemeActivity> queryWrapper = new QueryWrapper<>();
        if(requestData.getId() != null){
            queryWrapper.eq("id",requestData.getId());
        }
        queryWrapper.like("theme_name",requestData.getActivityName()+"%");
        queryWrapper.eq("is_del",IsDeleteEnum.NORMAL.getCode());
        // 进行中
        if(requestData.getStatus().equals(0)){
            queryWrapper.eq("status",0);
            queryWrapper.ge("activity_start_time",new Date());
            queryWrapper.lt("activity_end_time",new Date());
        }else if(requestData.getStatus().equals(1)){
            queryWrapper.eq("status",1);
        }else if(requestData.getStatus().equals(2)){
            queryWrapper.eq("status",0);
            queryWrapper.lt("activity_start_time",new Date());
        }else{
            queryWrapper.eq("status",0);
            queryWrapper.gt("activity_end_time",new Date());
        }

        List<ThemeActivity> shouldUpdateStatusByDateTotal = themeActivityMapper.selectList(queryWrapper);
        //一个数据 那么额外处理
        PageInfo<ThemeActivityReq> pageInfoTotal = new PageInfo(hPageTotal);
        String jsonString = JSONObject.toJSONString(shouldUpdateStatusByDateTotal);
        List<ThemeActivityReq> themeActivityReqs = JSONObject.parseArray(jsonString, ThemeActivityReq.class);
        if(CollectionUtils.isNotEmpty(themeActivityReqs) && themeActivityReqs.size() == 1){
            QueryWrapper<ThemeActivityDaysRule> queryWrapper2 = new QueryWrapper<>();
            queryWrapper2.eq("is_del",IsDeleteEnum.NORMAL.getCode());
            queryWrapper2.eq("theme_activity_id",themeActivityReqs.get(0).getId());
            List<ThemeActivityDaysRule> themeActivityDaysRules = themeActivityDaysRuleMapper.selectList(queryWrapper2);
            if(CollectionUtils.isNotEmpty(themeActivityDaysRules)){
                themeActivityReqs.get(0).setList(JSONObject.parseArray(JSONObject.toJSONString(themeActivityDaysRules), ThemeActivityReq.ThemeActivityDaysRuleDto.class));
            }
        }
        pageInfoTotal.setList(themeActivityReqs);
        page.setCount(hPageTotal.getTotal());
        page.setPages(hPageTotal.getPages());
        return pageInfoTotal.getList();
    }

    @Override
    public void deleteActivity(Integer requestData) {
        if(requestData == null){
            return ;
        }
        ThemeActivity themeActivity = new ThemeActivity();
        themeActivity.setIsDel(IsDeleteEnum.IS_DELETED.getCode());
        themeActivity.setId(requestData);
        themeActivityMapper.updateById(themeActivity);

    }

    @Override
    public void updateStatusById(UpdateStatusById requestData) {
        if(requestData.getId() == null){
            return ;
        }
        ThemeActivity themeActivity = new ThemeActivity();
        themeActivity.setStatus(requestData.getStatus());
        themeActivity.setId(requestData.getId());
        themeActivityMapper.updateById(themeActivity);
    }

    @Override
    public List<UserAwardListResp> userAwardList(UserAwardListReq requestData, Page page) {

        com.github.pagehelper.Page<ThemeActivityUserAward> hPageTotal = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        QueryWrapper<ThemeActivityUserAward> queryWrapper = new QueryWrapper<>();
        if(StringUtils.isNotBlank(requestData.getPhone())){
            queryWrapper.eq("phone",requestData.getPhone());
        }
        if(StringUtils.isNotBlank(requestData.getCardno())){
            queryWrapper.eq("phone",requestData.getCardno());
        }
        if(StringUtils.isNotBlank(requestData.getAwardName())){
            queryWrapper.eq("award_name",requestData.getAwardName());
        }
        if(requestData.getAwardType() != null){
            queryWrapper.eq("award_type",requestData.getAwardType());
        }
        queryWrapper.eq("is_del",IsDeleteEnum.NORMAL.getCode());
        queryWrapper.eq("theme_activity_id",requestData.getThemeActivityId());
        themeActivityUserAwardMapper.selectList(queryWrapper);
        //一个数据 那么额外处理
        PageInfo<ThemeActivityUserAward> pageInfoTotal = new PageInfo(hPageTotal);
        List<ThemeActivityUserAward> list = pageInfoTotal.getList();
        page.setCount(hPageTotal.getTotal());
        page.setPages(hPageTotal.getPages());
        String jsonString = JSONObject.toJSONString(list);
        return JSONObject.parseArray(jsonString,UserAwardListResp.class);
    }

    @Override
    public void sendCouponOrPoint(SendCouponOrPointDto sendCouponOrPointDto) {
        // 判断x天后才可以发奖
        ThemeActivity themeActivity = themeActivityMapper.selectById(sendCouponOrPointDto.getThemeActivityId());
        if(themeActivity.getActivityEndSendAwardDay() != null){
            // 判断当前时间是否 大于结束时间x天
            Date activityEndTime = themeActivity.getActivityEndTime();
            Date date = DateUtils.addDays(activityEndTime, themeActivity.getActivityEndSendAwardDay());
            if(new Date().getTime() < (date.getTime())){
                throw new RuntimeException("活动结束后"+themeActivity.getActivityEndSendAwardDay()+"天才可以进行发奖!");
            }
        }

        QueryWrapper<ThemeActivityUserAward> queryWrapper = new QueryWrapper<>();
        if(sendCouponOrPointDto.getId() !=null){
            queryWrapper.eq("id",sendCouponOrPointDto.getId());
        }
        queryWrapper.eq("theme_activity_id",sendCouponOrPointDto.getId());
        queryWrapper.eq("is_del",IsDeleteEnum.NORMAL.getCode());
        queryWrapper.eq("status",0);
        if(sendCouponOrPointDto.getSendAwardType() == 0){
            List<String> awardTypes = new ArrayList<>();
            awardTypes.add("0");
            awardTypes.add("1");
            queryWrapper.in("award_type",awardTypes);
        }else{
            List<String> awardTypes = new ArrayList<>();
            awardTypes.add("2");
            queryWrapper.in("award_type",awardTypes);
        }
        List<ThemeActivityUserAward> themeActivityUserAwards = themeActivityUserAwardMapper.selectList(queryWrapper);
        if(CollectionUtils.isEmpty(themeActivityUserAwards)){
            return ;
        }

        for (ThemeActivityUserAward themeActivityUserAward : themeActivityUserAwards) {
            List<Integer> list = new ArrayList<>();
            list.add(themeActivityUserAward.getAwardConfigId());
            Integer id = themeActivityUserAward.getId();
            String refNo = IdConstant.THEME_ACTIVITY_USER_AWARD + id;
            List<SendAwardDto> sendAwardDtos = sendAwardById(themeActivityUserAward.getUnionid(), themeActivityUserAward.getCardno(), refNo, list);
            if(CollectionUtils.isNotEmpty(sendAwardDtos)){
                // 发放奖励看看是否成功  如果成功  则直接结束
                if(sendAwardDtos.get(0).getIsSendSuccess()){
                    // 发放成功
                    ThemeActivityUserAward update  = new ThemeActivityUserAward();
                    update.setId(id);
                    update.setStatus(1);
                    update.setUpdateTime(new Date());
                    update.setCommonInfo(sendAwardDtos.get(0).getCommonInfo());
                    themeActivityUserAwardMapper.updateById(update);
                }
            }
        }
    }

    @Override
    public boolean sendMaterialById(SendMaterialById requestData) {
        QueryWrapper<ThemeActivityUserAward> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id",requestData.getId());
        queryWrapper.eq("is_del",IsDeleteEnum.NORMAL.getCode());
        queryWrapper.eq("status",0);
        List<ThemeActivityUserAward> themeActivityUserAwards = themeActivityUserAwardMapper.selectList(queryWrapper);
        if(CollectionUtils.isEmpty(themeActivityUserAwards)){
            return false;
        }
        // 更新信息
        for (ThemeActivityUserAward themeActivityUserAward : themeActivityUserAwards) {
            ThemeActivityUserAward update = new ThemeActivityUserAward();
            if(StringUtils.isNotBlank(requestData.getProvinces())){
                update.setProvinces(requestData.getProvinces());
            }
            if(StringUtils.isNotBlank(requestData.getCity())){
                update.setCity(requestData.getCity());
            }
            if(StringUtils.isNotBlank(requestData.getDistricts())){
                update.setDistricts(requestData.getDistricts());
            }
            if(StringUtils.isNotBlank(requestData.getAddress())){
                update.setAddress(requestData.getAddress());
            }
            update.setCommonInfo(requestData.getExpressNo());
            update.setId(themeActivityUserAward.getId());
            update.setUpdateTime(new Date());
            update.setStatus(1);
            int i = themeActivityUserAwardMapper.updateById(update);
            return true;
        }
        return false;
    }

    @Override
    public String exportUserAwardList(UserAwardListReq requestData) {
        // 导出excel
        // 序号   unionid  手机号  cardno  打卡天数  最后打卡时间  奖品名称   省市区  详细地址  物流单号  发放情况
        QueryWrapper<ThemeActivityUserAward> queryWrapper = new QueryWrapper<>();
        if(StringUtils.isNotBlank(requestData.getPhone())){
            queryWrapper.eq("phone",requestData.getPhone());
        }
        if(StringUtils.isNotBlank(requestData.getCardno())){
            queryWrapper.eq("phone",requestData.getCardno());
        }
        if(StringUtils.isNotBlank(requestData.getAwardName())){
            queryWrapper.eq("award_name",requestData.getAwardName());
        }
        if(requestData.getAwardType() != null){
            queryWrapper.eq("award_type",requestData.getAwardType());
        }
        queryWrapper.eq("is_del",IsDeleteEnum.NORMAL.getCode());
        queryWrapper.eq("theme_activity_id",requestData.getThemeActivityId());
        List<ThemeActivityUserAward> themeActivityUserAwards = themeActivityUserAwardMapper.selectList(queryWrapper);
        if(CollectionUtils.isEmpty(themeActivityUserAwards)){
            return "";
        }
        // 导出excel
        List<ExportExcelUserAwardDto> excelUserAwardDtos = new ArrayList<>();


        for (ThemeActivityUserAward userAwardDto : themeActivityUserAwards) {
            ExportExcelUserAwardDto exportShareBookDto = new ExportExcelUserAwardDto();
            BeanUtils.copyProperties(userAwardDto,exportShareBookDto);
            if(userAwardDto.getStatus() == 0){
                exportShareBookDto.setStatus("未发放");
            }else if(userAwardDto.getStatus() ==1){
                exportShareBookDto.setStatus("已发放");
            }
            // 设置时间
            exportShareBookDto.setSignInDays(userAwardDto.getSignInDays()+"");
            exportShareBookDto.setLastSignInTime(DateUtils.formatDate(userAwardDto.getLastSignInTime(),"yyyy-MM-dd HH:mm:ss"));
            excelUserAwardDtos.add(exportShareBookDto);
        }

        //配置列明
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(excelUserAwardDtos)) {
            String fileName = System.currentTimeMillis() + ".xlsx";
            EasyExcelUtil.write(fileName, ExportExcelUserAwardDto.class, new IWriteDataExcel<ExportExcelUserAwardDto>() {
                @Override
                public List<ExportExcelUserAwardDto> getData() {
                    return excelUserAwardDtos;
                }
            });
            File file = new File(fileName);
            String param = qiniuUtil.upload(file.getPath(), "活动奖励情况信息"+System.currentTimeMillis() + ".xlsx");
            file.delete();
            log.info("redis的key{}", param);
            return param;
        }
        return null;
    }

    @Override
    public String importSendMaterialUserAward(String url) {
        List<ExportExcelUserAwardDto> resp = new ArrayList<>();

        // 导入库存
        //库存id，门店编码、门店名称、门店库存、剩余库存、库存调整方式（下拉：增加、减少），调整库存
        List<ExportExcelUserAwardDto> importData  = new ArrayList<>();
        //返回数据
        String filePath = FileParseUtil.downLoadExcel(url);
        EasyExcelUtil.read(filePath, new BaseNoModelDataAbstractListener() {
            @Override
            protected void saveData() {
                List<Map<Integer, String>> cachedDataList1 = this.cachedDataList;
                for (Map<Integer, String> integerStringMap : cachedDataList1) {
                    ExportExcelUserAwardDto exportExcelUserAwardDto = new ExportExcelUserAwardDto();
                    exportExcelUserAwardDto.setId(integerStringMap.get(0));
                    exportExcelUserAwardDto.setUnionid(integerStringMap.get(1));
                    exportExcelUserAwardDto.setPhone(integerStringMap.get(2));
                    exportExcelUserAwardDto.setCardno(integerStringMap.get(3));
                    exportExcelUserAwardDto.setSignInDays(integerStringMap.get(4));
                    exportExcelUserAwardDto.setLastSignInTime(integerStringMap.get(5));
                    exportExcelUserAwardDto.setAwardName(integerStringMap.get(6));
                    exportExcelUserAwardDto.setProvinces(integerStringMap.get(7));
                    exportExcelUserAwardDto.setCity(integerStringMap.get(8));
                    exportExcelUserAwardDto.setDistricts(integerStringMap.get(9));
                    exportExcelUserAwardDto.setAddress(integerStringMap.get(10));
                    exportExcelUserAwardDto.setCommonInfo(integerStringMap.get(11));
                    exportExcelUserAwardDto.setStatus(integerStringMap.get(12));
                    importData.add(exportExcelUserAwardDto);
                }
            }
        });

        // 进行处理
        for (ExportExcelUserAwardDto importDatum : importData) {
            // 处理数据
            SendMaterialById sendMaterialById = new SendMaterialById();
            BeanUtils.copyProperties(importDatum,sendMaterialById);
            sendMaterialById.setId(Integer.parseInt(importDatum.getId()));
            sendMaterialById.setExpressNo(importDatum.getCommonInfo());
            boolean b = sendMaterialById(sendMaterialById);
            // 处理失败  已经处理过了
            if(!b){
                importDatum.setMsg("该订单已经处理过，不允许再次处理");
                resp.add(importDatum);
            }
        }

        // 上传七牛云 将地址发给前端

        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(resp)) {
            String fileName = System.currentTimeMillis() + ".xlsx";
            EasyExcelUtil.write(fileName, ExportExcelUserAwardDto.class, new IWriteDataExcel<ExportExcelUserAwardDto>() {
                @Override
                public List<ExportExcelUserAwardDto> getData() {
                    return resp;
                }
            });
            File file = new File(fileName);
            String param = qiniuUtil.upload(file.getPath(), "错误的导入物流单号信息"+System.currentTimeMillis() + ".xlsx");
            file.delete();
            log.info("redis的key{}", param);
            return param;
        }
        return null;
    }

    @Override
    public void genUserAwardJob() {
        // 查询未删除的 活动已经结束的   并且是进行中的  并且是未生成奖励的
        QueryWrapper<ThemeActivity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_del",IsDeleteEnum.NORMAL.getCode());
        queryWrapper.eq("status",0);
        queryWrapper.gt("activity_end_time",new Date());
        queryWrapper.eq("is_gen_award",0);
        List<ThemeActivity> genAwardList = themeActivityMapper.selectList(queryWrapper);
        if(CollectionUtils.isEmpty(genAwardList)){
            log.info("无需要生成奖励的活动信息");
            return ;
        }
        // 开始处理生成
        for (ThemeActivity themeActivity : genAwardList) {
            // 查询到活动中的用户以及用户打卡次数
            List<ThemeActivityUserAward> user = getThemeUserInfo(themeActivity.getId());

            QueryWrapper<ThemeActivityDaysRule> queryWrapper2 = new QueryWrapper<>();
            queryWrapper2.eq("is_del",IsDeleteEnum.NORMAL.getCode());
            queryWrapper2.eq("theme_activity_id",themeActivity.getId());
            queryWrapper2.orderByAsc("sort_num");
            List<ThemeActivityDaysRule> themeActivityDaysRules = themeActivityDaysRuleMapper.selectList(queryWrapper2);
            // 最终数据
            List<ThemeActivityUserAward> userAwards = new ArrayList<>();
            // 开始处理数据  查看用户在哪档奖励
            for (ThemeActivityUserAward themeActivityUserAward : user) {
                //签到天数   >=  大于等于
                Integer signInDays = themeActivityUserAward.getSignInDays();

                for (ThemeActivityDaysRule themeActivityDaysRule : themeActivityDaysRules) {
                    // 大于等于  则直接结束
                    if(signInDays>= themeActivityDaysRule.getSignInDays()){
                        String awardConfigIds = themeActivityDaysRule.getAwardConfigIds();
                        String[] split = awardConfigIds.split(",");
                        List<String> list = Arrays.asList(split);
                        for (String awardIdInfo : list) {
                            ThemeActivityUserAward themeActivityUserAwardInfo = new ThemeActivityUserAward();
                            BeanUtils.copyProperties(themeActivityUserAward,themeActivityUserAwardInfo);
                            // 最后在处理 奖励的其他信息
                            themeActivityUserAwardInfo.setAwardConfigId(Integer.parseInt(awardIdInfo));
                            themeActivityUserAwardInfo.setThemeActivityId(themeActivity.getId());
                            themeActivityUserAwardInfo.setThemeRuleId(themeActivityDaysRule.getId());
                            userAwards.add(themeActivityUserAwardInfo);
                        }
                        break;
                    }
                }
            }
            // 开始处理最终数据
            if(CollectionUtils.isNotEmpty(userAwards)){
                // 插入一条  发一条公众号消息
                Set<Integer> collect = userAwards.stream().map(r -> r.getAwardConfigId()).collect(Collectors.toSet());
                // 查询奖励信息

                QueryWrapper<AwardConfig> queryWrapper1 = new QueryWrapper<>();
                queryWrapper1.in("id",collect);
                List<AwardConfig> awardConfigs = awardConfigMapper.selectList(queryWrapper1);
                // 填充数据
                Map<Integer, List<AwardConfig>> groupById = awardConfigs.stream().collect(Collectors.groupingBy(r -> r.getId()));
                for (ThemeActivityUserAward userAward : userAwards) {
                    userAward.setIsDel(0);
                    userAward.setCreateTime(new Date());
                    userAward.setUpdateTime(new Date());
                    userAward.setAwardCode(groupById.get(userAward.getAwardConfigId()).get(0).getAwardCode());
                    userAward.setAwardNum(groupById.get(userAward.getAwardConfigId()).get(0).getAwardNum());
                    userAward.setAwardType(groupById.get(userAward.getAwardConfigId()).get(0).getAwardType());
                    userAward.setAwardName(groupById.get(userAward.getAwardConfigId()).get(0).getAwardName());
                    // 插入数据
                    themeActivityUserAwardMapper.insert(userAward);
                    // 发送公众号消息
                    try {
                        sendOpenWxMsg(userAward.getCardno(),userAward.getThemeActivityId(),userAward.getAwardName());
                    }catch (Exception e){
                        log.info("发送公众号消息失败 cardno = {}",userAward.getCardno());
                    }
                }
            }
        }
    }

    @Override
    public void updateMaterialAddress(SendMaterialById requestData) {
        QueryWrapper<ThemeActivityUserAward> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id",requestData.getId());
        queryWrapper.eq("is_del",IsDeleteEnum.NORMAL.getCode());
        queryWrapper.eq("status",0);
        List<ThemeActivityUserAward> themeActivityUserAwards = themeActivityUserAwardMapper.selectList(queryWrapper);
        if(CollectionUtils.isEmpty(themeActivityUserAwards)){
            return ;
        }
        // 更新信息
        for (ThemeActivityUserAward themeActivityUserAward : themeActivityUserAwards) {
            ThemeActivityUserAward update = new ThemeActivityUserAward();
            if(StringUtils.isNotBlank(requestData.getProvinces())){
                update.setProvinces(requestData.getProvinces());
            }
            if(StringUtils.isNotBlank(requestData.getCity())){
                update.setCity(requestData.getCity());
            }
            if(StringUtils.isNotBlank(requestData.getDistricts())){
                update.setDistricts(requestData.getDistricts());
            }
            if(StringUtils.isNotBlank(requestData.getAddress())){
                update.setAddress(requestData.getAddress());
            }
            if(StringUtils.isNotBlank(requestData.getRealName())){
                update.setRealName(requestData.getRealName());
            }
            if(StringUtils.isNotBlank(requestData.getSendMaterialPhone())){
                update.setSendMaterialPhone(requestData.getSendMaterialPhone());
            }
            update.setId(themeActivityUserAward.getId());
            update.setUpdateTime(new Date());
            int i = themeActivityUserAwardMapper.updateById(update);
        }
    }

    @Override
    public ShowSendPointButtonAndSendCouponButton showSendPointButtonAndSendCouponButton(SendCouponOrPointDto sendCouponOrPointDto) {
        ShowSendPointButtonAndSendCouponButton showSendPointButtonAndSendCouponButton = new ShowSendPointButtonAndSendCouponButton();

        QueryWrapper<ThemeActivityUserAward> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("theme_activity_id",sendCouponOrPointDto.getId());
        queryWrapper.eq("is_del",IsDeleteEnum.NORMAL.getCode());
        queryWrapper.eq("status",0);
        List<String> awardTypes = new ArrayList<>();
        awardTypes.add("0");
        awardTypes.add("1");
        queryWrapper.in("award_type",awardTypes);

        List<ThemeActivityUserAward> themeActivityUserAwards = themeActivityUserAwardMapper.selectList(queryWrapper);
        if(CollectionUtils.isNotEmpty(themeActivityUserAwards)){
            showSendPointButtonAndSendCouponButton.setShowSendCoupon(true);
        }
        awardTypes.clear();
        queryWrapper.clear();


        awardTypes.add("2");
        queryWrapper.in("award_type",awardTypes);
        queryWrapper.eq("theme_activity_id",sendCouponOrPointDto.getId());
        queryWrapper.eq("is_del",IsDeleteEnum.NORMAL.getCode());
        queryWrapper.eq("status",0);
        List<ThemeActivityUserAward> themeActivityUserAwards2 = themeActivityUserAwardMapper.selectList(queryWrapper);
        if(CollectionUtils.isNotEmpty(themeActivityUserAwards2)){
            showSendPointButtonAndSendCouponButton.setShowSendPoint(true);
        }
        return showSendPointButtonAndSendCouponButton;
    }

    @Override
    public List<ActivityAwardListResp> getActivityAwardList(String themeActivityId) {

        List<ActivityAwardListResp> resps = new ArrayList<>();


        // 查询到数据
        ThemeActivity themeActivity = themeActivityMapper.selectById(themeActivityId);
        //查询 规则数据
        QueryWrapper<ThemeActivityDaysRule> queryWrapper2 = new QueryWrapper<>();
        queryWrapper2.eq("is_del",IsDeleteEnum.NORMAL.getCode());
        queryWrapper2.eq("theme_activity_id",themeActivity.getId());
        queryWrapper2.orderByAsc("sort_num");
        List<ThemeActivityDaysRule> themeActivityDaysRules = themeActivityDaysRuleMapper.selectList(queryWrapper2);

        // 查询活动中所有用户奖励信息
        QueryWrapper<ThemeActivityUserAward> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("theme_activity_id",themeActivity.getId());
        queryWrapper.eq("is_del",IsDeleteEnum.NORMAL.getCode());
        List<ThemeActivityUserAward> themeActivityUserAwards2 = themeActivityUserAwardMapper.selectList(queryWrapper);
        // 根据规则分组
        Map<Integer, List<ThemeActivityUserAward>> collect = themeActivityUserAwards2.stream().collect(Collectors.groupingBy(r -> r.getThemeRuleId()));
        // 查询用户昵称 批量 TODO


        // 查询用户奖励信息
        for (ThemeActivityDaysRule themeActivityDaysRule : themeActivityDaysRules) {
            // title 是从奖励信息里面获取
            ActivityAwardListResp activityAwardListResp = new ActivityAwardListResp();
            // 处理数据
            List<ThemeActivityUserAward> themeActivityUserAwards = collect.get(themeActivityDaysRule.getId());
            if(CollectionUtils.isNotEmpty(themeActivityUserAwards)){
                List<ActivityAwardListResp.AwardData> list = new ArrayList<>();
                // 排序 根据 打卡天数排序
                themeActivityUserAwards.sort(new Comparator<ThemeActivityUserAward>() {
                    @Override
                    public int compare(ThemeActivityUserAward o1, ThemeActivityUserAward o2) {
                        return o2.getSignInDays() - o1.getSignInDays();
                    }
                });
                List<String> collect1 = themeActivityUserAwards.stream().map(r -> r.getCardno()).collect(Collectors.toList());
                // 批量查询 cardno -> nickName
                List<Cardmain> cardmains = cardmainMapper.selectByCardNos(collect1);
                Map<String, List<Cardmain>> collect2 = cardmains.stream().collect(Collectors.groupingBy(r -> r.getCardno()));

                for (ThemeActivityUserAward themeActivityUserAward : themeActivityUserAwards) {
                    ActivityAwardListResp.AwardData awardData = new ActivityAwardListResp.AwardData();
                    awardData.setThemeActivityAwardId(themeActivityUserAward.getId()+"");
                    List<Cardmain> cardmains1 = collect2.get(themeActivityUserAward.getCardno());
                    if(CollectionUtils.isNotEmpty(cardmains1)){
                        awardData.setNickName(cardmains1.get(0).getNickname());
                    }
                    awardData.setSignInDays(themeActivityUserAward.getSignInDays()+"");
                    list.add(awardData);
                }
                activityAwardListResp.setList(list);
                activityAwardListResp.setTitle(themeActivityDaysRule.getAwardTitle());
                activityAwardListResp.setSubTitle(themeActivityDaysRule.getAwardSubtitle());
            }
            resps.add(activityAwardListResp);
        }
        return resps;
    }

    private void sendOpenWxMsg(String cardNo, Integer themeActivityId, String awardName) {

        String vipName = "";
        String weid = null;
        String openId = null;
        if(weid == null){
            MemberCardQueryContext req = new MemberCardQueryContext();
            req.setCardNo(cardNo);
            CustomerBaseResponse<MemberCardEntity> memberCard = iUserVipService.getMemberCard(req);
            if(memberCard != null && memberCard.getData() != null){
                if(StringUtils.isNotBlank(memberCard.getData().getOpenId())){
                    vipName = memberCard.getData().getNickName();
                    weid = memberCard.getData().getBrandId();
                    openId = memberCard.getData().getOpenId();
                }
            }
        }

        // 发送公众号消息  公众号appid
        String appid = null;
        String miniappid = null;
        String brandName = "";
        String offTemplateId = "";
        String jumpUrl =  "";
        // 根据品牌映射成appid
        List<Map> brandConfigs = JSONObject.parseArray(commonConfigs, Map.class);
        for (Map brandConfig : brandConfigs) {
            if(brandConfig.get("weid").toString().equals(weid+"")){
                // 公众号appid
                appid = brandConfig.get("offAppId").toString();
                miniappid = brandConfig.get("appid").toString();
                offTemplateId  =  brandConfig.get("offLessThemeActivityTemp").toString();
                jumpUrl = brandConfig.get("offLessThemeActivityUrl").toString();
            }
        }
        if(appid == null){
            log.info("未能匹配到正确的weid = {}",weid);
            return ;
        }

        List<SendWxOffMsgContext> sendWxOffMsgContexts = new ArrayList<>();
        Map<String,String> params = new HashMap<>();
        params.put("appId",miniappid);
        params.put("path",jumpUrl+"themeActivityId="+themeActivityId);
        params.put("first",awardName);      // 商品名称
        params.put("keyword1",vipName);  // 用户昵称

        SendWxOffMsgContext sendWxOffMsgContext = new SendWxOffMsgContext();
        SendWxOffMsgContext.WxTemplateMsgSendDTO build = SendWxOffMsgContext.build(openId, offTemplateId, params);
        sendWxOffMsgContext.setWxTemplateMsgSendDTO(build);
        sendWxOffMsgContext.setAppid(appid);
//        sendWxOffMsgContext.setUnionId(openId);
        sendWxOffMsgContexts.add(sendWxOffMsgContext);
        messageService.sendWxOffMsg(sendWxOffMsgContexts);
    }

    private List<ThemeActivityUserAward> getThemeUserInfo(Integer themeActivityId) {
        //TODO

        return new ArrayList<>();
    }


    /**
     * 通过unionid和cardno 进行发放奖励    awardConfigIds为需要发放的奖励信息
     * @param unionid
     * @param cardNo
     * @param awardConfigIds
     * @return
     */
    public List<SendAwardDto> sendAwardById(String unionid ,
                                            String cardNo,
                                            String refNo,
                                            List<Integer> awardConfigIds){
        log.info("开始发放奖励 unionid = {} cardNo = {}  refNo = {}  awardIds = {} " ,unionid,cardNo,refNo,JSONObject.toJSONString(awardConfigIds));

        List<SendAwardDto> sendAwardDtos = new ArrayList<>();

        // 查询信息
        QueryWrapper<AwardConfig> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("id",awardConfigIds);
        List<AwardConfig> awardConfigs = awardConfigMapper.selectList(queryWrapper);
        if(CollectionUtils.isEmpty(awardConfigs)){
            return null;
        }
        // 准备进行发放

        for (AwardConfig awardConfig : awardConfigs) {
            //  0  内部券   1  微信券  2  积分  3 内部商品  4 外部商品
            Integer awardType = awardConfig.getAwardType();
            // 插入记录
            SendAwardDto sendAwardDto =  checkAndInsertSendLog(awardConfig,unionid,cardNo,refNo);
            // 如果是已经成功， 那么就是已经发过了，直接返回即可
            if(sendAwardDto.getIsSendSuccess()){
                sendAwardDtos.add(sendAwardDto);
                continue;
            }
            // 内部券
            try {
                if(awardType.equals(0)){
                    sendInnerCoupon(cardNo,awardConfig,sendAwardDto);
                }
            }catch (Exception e){
                log.info("发放内部券报错!",e);
            }

            // 微信券
            try {
                if(awardType.equals(1)){
                    sendWeChatCoupon(cardNo,awardConfig,sendAwardDto);
                }
            }catch (Exception e){
                log.info("发放微信券报错!",e);
            }

            // 积分
            try {
                if(awardType.equals(2)){
                    sendPoint(unionid,cardNo,awardConfig,sendAwardDto);
                }
            }catch (Exception e){
                log.info("发放积分报错!",e);
            }

            if(awardType.equals(3) || awardType.equals(4)){
                // 直接成功
                sendAwardDto.setIsSendSuccess(true);
                sendAwardDto.setCommonInfo("实物直接成功");
            }

            sendAwardDtos.add(sendAwardDto);

            if(sendAwardDto.getIsSendSuccess()){
                // 更新发放记录
                String sendAwardLogId = sendAwardDto.getSendAwardLogId();
                SendAwardLog sendAwardLog = new SendAwardLog();
                sendAwardLog.setStatus(1);
                sendAwardLog.setId(Integer.parseInt(sendAwardLogId));
                sendAwardLogMapper.updateById(sendAwardLog);
            }
        }
        return sendAwardDtos;
    }

    private void sendPoint(String unionid,String cardNo,
                           AwardConfig awardConfig, SendAwardDto sendAwardDto) {
        String storeName = "";
        Long vipId = null;
        if(vipId == null){
            MemberCardQueryContext req = new MemberCardQueryContext();
            req.setCardNo(cardNo);
            CustomerBaseResponse<MemberCardEntity> memberCard = iUserVipService.getMemberCard(req);
            if(memberCard != null && memberCard.getData() != null){
                if(StringUtils.isNotBlank(memberCard.getData().getOpenId())){
                    storeName = memberCard.getData().getStoreName();
                    vipId = memberCard.getData().getId();
                }
            }
        }

        // 发放积分
        // 封装接口
        AddIntegralContext addIntegralContext = new AddIntegralContext();
        addIntegralContext.setVipId(vipId+"");
        addIntegralContext.setIntegral(Integer.parseInt(awardConfig.getAwardCode()));
        addIntegralContext.setReason("留言板发放积分");
        addIntegralContext.setSerialNo(IdConstant.SEND_AWARD_LOG+ sendAwardDto.getSendAwardLogId());
        addIntegralContext.setUnionId(unionid);
        addIntegralContext.setStoreName(storeName);
        AddIntegralEntity addIntegralEntity = iAddIntegerService.addIntegral(addIntegralContext);
        if(addIntegralEntity != null && addIntegralEntity.getResult().equals("Y")){
            // 加积分成功
            sendAwardDto.setIsSendSuccess(true);
            sendAwardDto.setCommonInfo("留言板发放积分成功");
        }else{
            sendAwardDto.setIsSendSuccess(false);
            sendAwardDto.setCommonInfo("留言板发放积分失败");
        }
    }

    private void sendWeChatCoupon(String cardNo, AwardConfig awardConfig, SendAwardDto sendAwardDto) {
        // 映射到appid

        Integer awardNum = awardConfig.getAwardNum();

        String openId = "";
        String appid = "";
        if(StringUtils.isBlank(openId)){
            MemberCardQueryContext req = new MemberCardQueryContext();
            req.setCardNo(cardNo);
            CustomerBaseResponse<MemberCardEntity> memberCard = iUserVipService.getMemberCard(req);
            if(memberCard != null && memberCard.getData() != null){
                if(StringUtils.isNotBlank(memberCard.getData().getOpenId())){
                    openId = memberCard.getData().getOpenId();

                    List<Map> brandConfigs = JSONObject.parseArray(commonConfigs, Map.class);
                    for (Map brandConfig : brandConfigs) {
                        if(brandConfig.get("weid").toString().equals(memberCard.getData().getBrandId())){
                            appid = brandConfig.get("appid").toString();
                        }
                    }
                }
            }
        }
        if(StringUtils.isBlank(openId) || StringUtils.isBlank(appid)){
            sendAwardDto.setCommonInfo("openid或者appid无值，不可以发放");
            return;
        }
        String voucherNos = "";
        List<String> vouchers = new ArrayList<>();

        for (int i = 0 ; i < awardNum;i++) {
            CommonRequest<SendCouponReq> request = new CommonRequest<>();
            SendCouponReq weChatSendCouponReq = new SendCouponReq();
            weChatSendCouponReq.setStockId(String.valueOf(awardConfig.getAwardCode()));
            weChatSendCouponReq.setOpenId(openId);
            weChatSendCouponReq.setAppId(appid);
            //组合id
            String outReuestNo = sendAwardDto.getSendAwardLogId() + awardConfig.getAwardCode() + i;
            weChatSendCouponReq.setOutRequestNo(outReuestNo);
            // merchid 固定的 ， 配置在nacos上
            weChatSendCouponReq.setType(1);
            weChatSendCouponReq.setStockCreatorMchid(merchid);
            weChatSendCouponReq.setMchId(merchid);
            request.setRequestData(weChatSendCouponReq);
            log.info("发放微信代金券 sendCoupon = {}",JSONObject.toJSONString(request));
            ResponseResult<String> stringResponseResult = iPayCenterApi.sendCoupon(request);
            log.info("发放微信代金券 sendCoupon = {}",JSONObject.toJSONString(stringResponseResult));
            if(stringResponseResult != null && stringResponseResult.getData() != null){
                vouchers.add(stringResponseResult.getData());
            }else{
                log.info("发放微信代金券失败! = {}", JSONObject.toJSONString(stringResponseResult));
                continue;
            }
        }

        if(CollectionUtils.isNotEmpty(vouchers)){
            voucherNos = vouchers.stream().collect(Collectors.joining(","));
            sendAwardDto.setIsSendSuccess(true);
            sendAwardDto.setCommonInfo(voucherNos);
        }else{
            sendAwardDto.setIsSendSuccess(false);
            sendAwardDto.setCommonInfo(voucherNos);
        }
    }

    private SendAwardDto sendInnerCoupon( String cardNo,AwardConfig awardConfig, SendAwardDto sendAwardDto) {

        // 发放内部券  发券信息
        String awardCode = awardConfig.getAwardCode();
        Integer awardNum = awardConfig.getAwardNum();
        String openId = "";
        if(StringUtils.isBlank(openId)){
            MemberCardQueryContext req = new MemberCardQueryContext();
            req.setCardNo(cardNo);
            CustomerBaseResponse<MemberCardEntity> memberCard = iUserVipService.getMemberCard(req);
            if(memberCard != null && memberCard.getData() != null){
                if(StringUtils.isNotBlank(memberCard.getData().getOpenId())){
                    openId = memberCard.getData().getOpenId();
                }
            }
        }
        if(StringUtils.isBlank(openId)){
            sendAwardDto.setCommonInfo("openid无值，不可以发放");
            return sendAwardDto;
        }
        // 开始查询是否已经发送过
        // 需要拿到 公众号openid
        String referOrderRe = IdConstant.SEND_AWARD_LOG + sendAwardDto.getSendAwardLogId();

        // 先查询是否已经发放过
        List<JicVoucherSendRecord> jicVoucherSendRecords = jicVoucherSendRecordMapper.selectByReferOrder(referOrderRe);
        if(org.apache.commons.collections.CollectionUtils.isNotEmpty(jicVoucherSendRecords)){
            log.info("该用户已经发放过， 直接使用数据库数据  = {}",JSONObject.toJSONString(jicVoucherSendRecords));
            // 拼接 然后进行塞入
            List<String> collect = jicVoucherSendRecords.stream().filter(r -> StringUtils.isNotBlank(r.getVoucherNo())).map(r -> r.getVoucherNo()).collect(Collectors.toList());
            String collect1 = collect.stream().collect(Collectors.joining(","));
            sendAwardDto.setIsSendSuccess(true);
            sendAwardDto.setCommonInfo(collect1);
            return sendAwardDto;
        }

        JICVoucherSendReqEntity jicVoucherSendReqEntity = new JICVoucherSendReqEntity();
        jicVoucherSendReqEntity.setOpenId(openId);
        List<RulesList> rulesLists = new ArrayList<>();
        RulesList rules = new RulesList();
        rules.setAwardId(Long.parseLong(awardCode));
        rules.setNum(awardNum.longValue());
        rulesLists.add(rules);

        jicVoucherSendReqEntity.setRulesList(rulesLists);
        jicVoucherSendReqEntity.setReferOrder(referOrderRe);
        JICVoucherSendRespEntity jicVoucherSendRespEntity = new JICVoucherSendRespEntity();
        // 为了测试
        jicVoucherSendRespEntity = iVoucherService.sendVoucher(jicVoucherSendReqEntity);
        if (jicVoucherSendRespEntity != null && jicVoucherSendRespEntity.getCode() == 200 && ObjectUtils.isNotEmpty(jicVoucherSendRespEntity.getData())) {
            String voucher = jicVoucherSendRespEntity.getData().getR_voucher();
            if (StringUtils.isBlank(voucher) || voucher.equals("[]")) {
                if("会员不存在".equals(jicVoucherSendRespEntity.getData().getRetmsg())){
                    sendAwardDto.setCommonInfo("会员不存在");
                    sendAwardDto.setIsSendSuccess(false);
                    return sendAwardDto;
                }
                log.error("通用发放券接口  发券失败 msg = {}, e = {}", jicVoucherSendRespEntity.getData().getRetmsg(), jicVoucherSendRespEntity);
                // 补偿发放3次
                sendAwardDto.setCommonInfo("通用发放券接口发券失败");
                sendAwardDto.setIsSendSuccess(false);
                return sendAwardDto;
            }

            List<Map> maps = JSONObject.parseArray(voucher, Map.class);
            StringBuffer voucherCode = new StringBuffer();

            for(int i =0 ; i< maps.size(); i++){
                if(i == maps.size() - 1){
                    voucherCode.append(maps.get(i).get("code").toString());
                }else{
                    voucherCode.append(maps.get(i).get("code").toString());
                    voucherCode.append(",");
                }
            }
            String[] split2 = voucherCode.toString().split(",");
            List<String> list = Arrays.asList(split2);
            String collect1 = list.stream().collect(Collectors.joining(","));
            sendAwardDto.setIsSendSuccess(true);
            sendAwardDto.setCommonInfo(collect1);
            return sendAwardDto;
        }
        sendAwardDto.setIsSendSuccess(false);
        sendAwardDto.setCommonInfo("发券失败!");
        return sendAwardDto;
    }



    private SendAwardDto checkAndInsertSendLog(AwardConfig awardConfig, String unionid, String cardNo, String refNo) {
        SendAwardDto sendAwardDto = new SendAwardDto();
        sendAwardDto.setAwardConfigId(awardConfig.getId());
        sendAwardDto.setRefNo(refNo);

        // 根据refno查询 是否已经发过了
        QueryWrapper<SendAwardLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_del",IsDeleteEnum.NORMAL.getCode());
        queryWrapper.eq("ref_no",refNo);
        queryWrapper.eq("award_config_id",awardConfig.getId());
        List<SendAwardLog> sendAwardLogs = sendAwardLogMapper.selectList(queryWrapper);
        if(CollectionUtils.isNotEmpty(sendAwardLogs)){
            Integer status = sendAwardLogs.get(0).getStatus();
            if(status.equals(1)){
                // 已经发过了， 直接返回 发送成功
                sendAwardDto.setIsSendSuccess(true);
                sendAwardDto.setAwardConfigId(awardConfig.getId());
                sendAwardDto.setCommonInfo(sendAwardLogs.get(0).getCommonInfo());
                sendAwardDto.setSendAwardLogId(sendAwardLogs.get(0).getId() +"");
                return sendAwardDto;
            }
            // 未发放  不处理
        }else{
            // 插入数据
            SendAwardLog sendAwardLog = new SendAwardLog();
            sendAwardLog.setAwardCode(awardConfig.getAwardCode());
            sendAwardLog.setAwardType(awardConfig.getAwardType());
            sendAwardLog.setAwardName(awardConfig.getAwardName());
            sendAwardLog.setAwardNum(awardConfig.getAwardNum());
            sendAwardLog.setAwardConfigId(awardConfig.getId());
            sendAwardLog.setCardno(cardNo);
            sendAwardLog.setUnionid(unionid);
            sendAwardLog.setRefNo(refNo);
            sendAwardLog.setStatus(0);
            sendAwardLog.setCreateTime(new Date());
            sendAwardLog.setUpdateTime(new Date());
            sendAwardLogMapper.insert(sendAwardLog);
            sendAwardDto.setSendAwardLogId(sendAwardLogs.get(0).getId() +"");
        }
        return sendAwardDto;
    }
}
