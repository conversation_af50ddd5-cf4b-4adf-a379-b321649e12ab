<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.marketing.modules.mapper.AcActivityOrderMapper">

    <resultMap id="BaseResultMap" type="org.springcenter.marketing.modules.model.AcActivityOrder">
            <id property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="unionid" column="UNIONID" jdbcType="VARCHAR"/>
            <result property="weid" column="WEID" jdbcType="VARCHAR"/>
            <result property="wxopenid" column="WXOPENID" jdbcType="VARCHAR"/>
            <result property="miniopenid" column="MINIOPENID" jdbcType="VARCHAR"/>
            <result property="activityConfigId" column="ACTIVITY_CONFIG_ID" jdbcType="VARCHAR"/>
            <result property="payType" column="PAY_TYPE" jdbcType="DECIMAL"/>
            <result property="payPoint" column="PAY_POINT" jdbcType="DECIMAL"/>
            <result property="payAmount" column="PAY_AMOUNT" jdbcType="DECIMAL"/>
            <result property="tradeNo" column="TRADE_NO" jdbcType="VARCHAR"/>
            <result property="status" column="STATUS" jdbcType="DECIMAL"/>
            <result property="activityConfigName" column="ACTIVITY_CONFIG_NAME" jdbcType="VARCHAR"/>
            <result property="subStartTime" column="SUB_START_TIME"/>
            <result property="subEndTime" column="SUB_END_TIME"/>
            <result property="isDel" column="IS_DEL" jdbcType="DECIMAL"/>
            <result property="createTime" column="CREATE_TIME"/>
            <result property="updateTime" column="UPDATE_TIME"/>
        <result property="activityConfDetailId" column="ACTIVITY_CONF_DETAIL_ID" jdbcType="VARCHAR"/>
        <result property="cardNo" column="CARD_NO" jdbcType="VARCHAR"/>
        <result property="payTime" column="PAY_TIME"/>
        <result property="isCanSendAlert" column="IS_CAN_SEND_ALERT" jdbcType="DECIMAL"/>
        <result property="ebOrder" column="EB_ORDER" jdbcType="VARCHAR"/>
        <result property="backImg" column="BACK_IMG" jdbcType="VARCHAR"/>
        <result property="ruleImg" column="RULE_IMG" jdbcType="VARCHAR"/>
        <result property="activityIcon" column="ACTIVITY_ICON" jdbcType="VARCHAR"/>
        <result property="brandTitle" column="BRAND_TITLE" jdbcType="VARCHAR"/>
        <result property="storeCode" column="store_code" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,UNIONID,WEID,
        WXOPENID,MINIOPENID,ACTIVITY_CONFIG_ID,
        PAY_TYPE,PAY_POINT,PAY_AMOUNT,
        TRADE_NO,STATUS,
        ACTIVITY_CONFIG_NAME,SUB_START_TIME,SUB_END_TIME,
        IS_DEL,CREATE_TIME,UPDATE_TIME,ACTIVITY_CONF_DETAIL_ID,CARD_NO,PAY_TIME,IS_CAN_SEND_ALERT,EB_ORDER,
        BACK_IMG,RULE_IMG,ACTIVITY_ICON,BRAND_TITLE,store_code
    </sql>
    <select id="selectShouldCancelActivityOrder"
            resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include>  from  ac_activity_order where
        status  = 0 and TRADE_NO is null and is_del = 0 and
        TIMESTAMPDIFF(MINUTE, create_time, NOW()) > 10
    </select>
    <delete id="cancelActivityOrder">
        update ac_activity_order set status = -1 where status  = 0 and TRADE_NO is null and is_del = 0 and
         TIMESTAMPDIFF(MINUTE, create_time, NOW()) > 10
    </delete>
    <select id="selectEfftiveOrderByUnionid"
            resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include> from ac_activity_order
        where is_del = 0 and unionid = #{unionid} and status  in( 1 , 2 )
        order by CREATE_TIME desc

    </select>

    <select id="selectEfftiveStatusOneOrderByUnionid"
            resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include> from ac_activity_order
        where is_del = 0 and unionid = #{unionid} and status  in( 1 )
        order by CREATE_TIME desc
    </select>

    <select id="selectEfftiveOrderByUnionidAndWeidAndDate" resultMap="BaseResultMap">
        select  <include refid="Base_Column_List"></include>
        from ac_activity_order
        where IS_DEL = 0 and STATUS = 1  and
        #{now} between SUB_START_TIME and SUB_END_TIME
        and UNIONID = #{unionid} and weid = #{weid}
    </select>


    <select id="selectAlreadyPayOrderList"
            resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include> from ac_activity_order
        where is_del = 0 and status  in (1,2,3)
        <if test="cardNo != null and cardNo != '' ">
            and card_no = #{cardNo}
        </if>
        <if test="status  != null ">
            and status = #{status}
        </if>
        <if test="weid != null and weid != '' ">
            and WEID = #{weid}
        </if>
        <if test="payType != null ">
            and PAY_TYPE = #{payType}
        </if>
        <if test="startTime != null ">
            and PAY_TIME >= #{startTime}
        </if>
        <if test="endTime != null">
            and #{endTime}  >= PAY_TIME
        </if>
        <if test="activityConfigId != null and activityConfigId != '' ">
            and ACTIVITY_CONFIG_ID = #{activityConfigId}
        </if>
        order by CREATE_TIME desc
    </select>


</mapper>
