package org.springcenter.marketing.modules.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.marketing.api.dto.TaskTemplateDetailsResp;
import org.springcenter.marketing.modules.model.TaskPartDetails;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【TASK_PART_DETAILS(参与任务明细)】的数据库操作Mapper
* @createDate 2023-02-13 20:02:41
* @Entity generator.domain.TaskPartDetails
*/
@Mapper
public interface TaskPartDetailsMapper extends BaseMapper<TaskPartDetails> {

    List<TaskTemplateDetailsResp> queryTaskTemplateDetailsByParam(@Param("taskId") String id, @Param("itemId")String itemId,
                                                                  @Param("phone") String phone, @Param("startTime") Date startTime,
                                                                  @Param("endTime") Date endTime, @Param("nickName") String nickName);

    void updateTaskTemplatePartDetailByCycle(@Param("taskId")String id);

    void batchUpdate(@Param("list") List<TaskPartDetails> updates);

    void expireUserTask();

    List<TaskPartDetails> selectDuringDateTaskList(@Param("unionId") String unionId, @Param("weId") String weId,
                                                  @Param("start") Date start, @Param("end") Date end);

    List<TaskPartDetails> selectAllNewerTaskList(@Param("unionId") String unionId, @Param("weId") String weId);

    @MapKey("taskInfoId")
    Map<String, Integer> selectCostTaskCount();


    List<TaskPartDetails> selectSwitchStatusTask(@Param("taskStatus") Integer taskStatus);
}
