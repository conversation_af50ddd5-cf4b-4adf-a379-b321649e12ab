package org.springcenter.marketing.modules.bojunMapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.marketing.modules.model.EbOrderso;

import java.util.List;


/**
 * @Author: lwz
 * @Date: 2022-02-10 10:45:59
 * @Description: Mapper
 */
public interface EbOrdersoMapper extends BaseMapper<EbOrderso> {

    /**
     * 获取内淘宝订单
     * @param id
     * @return
     */
    EbOrderso getEbSoOrder(Long id);

    List<EbOrderso> selectByOmsEbonum(@Param("ebNum") String omsEbonum);
}
