<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.marketing.modules.mapper.ThemeActivityUserAwardMapper">

    <resultMap id="BaseResultMap" type="org.springcenter.marketing.modules.model.ThemeActivityUserAward">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="phone" column="phone" jdbcType="VARCHAR"/>
            <result property="cardno" column="cardno" jdbcType="VARCHAR"/>
            <result property="lastSignInTime" column="last_sign_in_time" jdbcType="TIMESTAMP"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="isDel" column="is_del" jdbcType="INTEGER"/>
            <result property="signInDays" column="sign_in_days" jdbcType="INTEGER"/>
            <result property="awardConfigId" column="award_config_id" jdbcType="INTEGER"/>
            <result property="awardType" column="award_type" jdbcType="INTEGER"/>
            <result property="awardName" column="award_name" jdbcType="VARCHAR"/>
            <result property="awardCode" column="award_code" jdbcType="VARCHAR"/>
            <result property="awardNum" column="award_num" jdbcType="INTEGER"/>
            <result property="status" column="status" jdbcType="INTEGER"/>
            <result property="commonInfo" column="common_info" jdbcType="VARCHAR"/>

            <result property="provinces" column="provinces" jdbcType="VARCHAR"/>
            <result property="city" column="city" jdbcType="VARCHAR"/>
            <result property="districts" column="districts" jdbcType="VARCHAR"/>
            <result property="address" column="address" jdbcType="VARCHAR"/>
            <result property="themeActivityId" column="theme_activity_id" jdbcType="VARCHAR"/>
            <result property="unionid" column="unionid" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,phone,cardno,
        last_sign_in_time,create_time,update_time,
        is_del,sign_in_days,award_config_id,
        award_type,award_name,award_code,
        award_num,status,common_info,provinces,city,districts,address,theme_activity_id,unionid
    </sql>
</mapper>
