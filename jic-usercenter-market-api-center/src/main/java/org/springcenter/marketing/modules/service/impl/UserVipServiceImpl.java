package org.springcenter.marketing.modules.service.impl;

import org.springcenter.marketing.api.dto.CustomerBaseResponse;
import org.springcenter.marketing.api.dto.MemberIntegralDto;
import org.springcenter.marketing.modules.context.*;
import org.springcenter.marketing.modules.service.IUserVipService;
import org.springcenter.marketing.modules.webapi.ICustomerVipHttpApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import retrofit2.Response;
import retrofit2.http.Body;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;


@Service
@Slf4j
public class UserVipServiceImpl implements IUserVipService {

    @Value("${jic.express.url}")
    private String jicExpressUrl;

//    @Resource
//    private RetrofitBean retrofitBean;

    @Resource
    private ICustomerVipHttpApi iCustomerVipHttpApi;


    @Override
    public CustomerBaseResponse<List<MemberCardEntity>> getMemberCardList(MemberQueryContext memberQueryContext) {
        // 获取API
        // ICustomerVipHttpApi iCustomerVipHttpApi = retrofitBean.getApi(jicExpressUrl, ICustomerVipHttpApi.class);

        try {
            Response<CustomerBaseResponse<List<MemberCardEntity>>> result = iCustomerVipHttpApi.getMemberCardList(memberQueryContext).execute();
            if (result.isSuccessful()) {
                return result.body();
            }
            throw new RuntimeException("获取外部客户服务用户会员卡列表信息异常" + result);
        } catch (Exception e) {
            log.error("获取外部客户服务用户会员卡列表信息异常 params:{}  e:{} message:{}", memberQueryContext, e, e.getMessage());
            throw new RuntimeException("获取外部客户服务用户会员卡列表信息异常");
        }

    }

    @Override
    public CustomerBaseResponse<List<MemberNameAndHeadImgEntity>> listMemberCardNameAndHeadImg(MemberListQueryContext context) {
        try {
            Response<CustomerBaseResponse<List<MemberListCardEntity>>> result = iCustomerVipHttpApi.listMemberCard(context).execute();
            if (!result.isSuccessful() || result.body() == null) {
                throw new RuntimeException("获取外部客户服务用户会员卡列表信息异常" + result);
            }
            CustomerBaseResponse<List<MemberListCardEntity>> body = result.body();
            List<MemberListCardEntity> raw = body.getData();
            List<MemberNameAndHeadImgEntity> data;
            if (raw == null) {
                data = Collections.emptyList();
            } else {
                data = new java.util.ArrayList<>(raw.size());
                for (MemberListCardEntity e : raw) {
                    if (e == null) continue;
                    MemberNameAndHeadImgEntity item = new MemberNameAndHeadImgEntity();
                    if (e.getMemberChannelVo() != null) {
                        item.setUserName(e.getMemberChannelVo().getUserName());
                        item.setHeadImgUrl(e.getMemberChannelVo().getHeadImgUrl());
                    }
                    if (e.getMemberShipVo() != null) {
                        item.setCardNo(e.getMemberShipVo().getCardNo());
                    }
                    data.add(item);
                }
            }
            CustomerBaseResponse<List<MemberNameAndHeadImgEntity>> resp = new CustomerBaseResponse<>();
            resp.setCode(body.getCode());
            resp.setSuccess(body.isSuccess());
            resp.setMsg(body.getMsg());
            resp.setData(data);
            return resp;
        } catch (Exception e) {
            log.error("获取外部客户服务用户会员卡头像昵称信息异常 params:{}  e:{} message:{}", context, e, e.getMessage());
            throw new RuntimeException("获取外部客户服务用户会员卡头像昵称信息异常");
        }

    }

    @Override
    public CustomerBaseResponse<MemberCardEntity> getMemberCard(MemberCardQueryContext memberCardQueryContext) {
        // 获取API
        //  ICustomerVipHttpApi iCustomerVipHttpApi = retrofitBean.getApi(jicExpressUrl, ICustomerVipHttpApi.class);
        try {
            Response<CustomerBaseResponse<MemberCardEntity>> result = iCustomerVipHttpApi.getMemberCard(memberCardQueryContext).execute();

            if (result.isSuccessful()) {
                return result.body();
            }
            throw new RuntimeException("获取外部客户服务用户会员卡信息异常" + result);
        } catch (Exception e) {
            log.error("获取外部客户服务用户会员卡信息异常 params:{}  e:{} message:{}", memberCardQueryContext, e, e.getMessage());
            throw new RuntimeException("获取外部客户服务用户会员卡信息异常");
        }


    }


    @Override
    public CustomerBaseResponse<MemberCardEntity> getMemberCardWid(MemberCardQueryContext memberCardQueryContext) {
        // 获取API
        //  ICustomerVipHttpApi iCustomerVipHttpApi = retrofitBean.getApi(jicExpressUrl, ICustomerVipHttpApi.class);
        try {
            Response<CustomerBaseResponse<MemberCardEntity>> result = iCustomerVipHttpApi.getMemberCardWid(memberCardQueryContext).execute();

            if (result.isSuccessful()) {
                return result.body();
            }
            throw new RuntimeException("获取外部客户服务用户会员卡信息异常wid" + result);
        } catch (Exception e) {
            log.error("获取外部客户服务用户会员卡信息异常wid params:{}  e:{} message:{}", memberCardQueryContext, e, e.getMessage());
            throw new RuntimeException("获取外部客户服务用户会员卡信息异常wid");
        }


    }


    @Override
    public MemberBaseInfoEntity getUserBaseInfo(MemberBaseInfoQueryContext memberBaseInfoQueryContext) {
        try {
            Response<CustomerBaseResponse<MemberBaseInfoEntity>> response = iCustomerVipHttpApi.getUserBaseInfo(memberBaseInfoQueryContext).execute();
            if (response.isSuccessful()) {
                return response.body().getData();
            }
            throw new RuntimeException("获取用户基础信息异常" + response);
        } catch (Exception e) {
            log.error("获取用户基础信息异常 params:{}  e:{} message:{}", memberBaseInfoQueryContext, e, e.getMessage());
            throw new RuntimeException("获取用户基础信息信息异常");
        }
    }

    @Override
    public CustomerBaseResponse<MemberIntegralDto> getMemberIntegral(MemberCardQueryContext req) {
        try {
            Response<CustomerBaseResponse<MemberIntegralDto>> response = iCustomerVipHttpApi.getMemberIntegral(req).execute();
            if (response.isSuccessful()) {
                return response.body();
            }
            throw new RuntimeException("获取用户集团积分异常" + response);
        } catch (Exception e) {
            log.error("获取用户集团积分异常 params:{}  e:{} message:{}", req, e, e.getMessage());
            throw new RuntimeException("获取用户集团积分异常");
        }
    }



    @Override
    public CustomerBaseResponse<List<KidBirthListEntity>> getKidBirthList(KidBirthListQueryContext req) {
        try {
            Response<CustomerBaseResponse<List<KidBirthListEntity>>> response = iCustomerVipHttpApi.getKidBirthList(req.getCardNo()).execute();
            if (response.isSuccessful()) {
                return response.body();
            }
            throw new RuntimeException("获取当前用户是否填写过生日异常" + response);
        } catch (Exception e) {
            log.error("获取当前用户是否填写过生日异常 params:{}  e:{} message:{}", req, e, e.getMessage());
            throw new RuntimeException("获取当前用户是否填写过生日");
        }
    }


    @Override
    public CustomerBaseResponse<Boolean> updateCustomer(MemberUpdateInfoEntity req) {
        try {
            Response<CustomerBaseResponse<Boolean>> response = iCustomerVipHttpApi.updateCustomer(req).execute();
            if (response.isSuccessful()) {
                return response.body();
            }
            throw new RuntimeException("更新用户基本信息异常" + response);
        } catch (Exception e) {
            log.error("更新用户基本信息异常 params:{}  e:{} message:{}", req, e, e.getMessage());
            throw new RuntimeException("更新用户基本信息异常");
        }
    }
}
