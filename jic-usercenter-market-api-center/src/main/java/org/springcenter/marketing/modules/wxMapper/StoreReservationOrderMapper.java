package org.springcenter.marketing.modules.wxMapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.marketing.modules.model.StoreReservationOrder;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【STORE_RESERVATION_ORDER】的数据库操作Mapper
* @createDate 2024-09-12 11:16:38
* @Entity generator.domain.StoreReservationOrder
*/
public interface StoreReservationOrderMapper{

    List<StoreReservationOrder> selectByCardNo(@Param("cardNo") String cardNo);

    List<Long> selectProductIdByOrderId(@Param("orderId") Long orderId);

    List<String> selectByStoreId(@Param("storeId") String storeId);
}




