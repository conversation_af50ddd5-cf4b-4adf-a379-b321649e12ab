<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.marketing.modules.mapper.ThemeActivityDaysRuleMapper">

    <resultMap id="BaseResultMap" type="org.springcenter.marketing.modules.model.ThemeActivityDaysRule">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="themeActivityId" column="theme_activity_id" jdbcType="INTEGER"/>
            <result property="signInDays" column="sign_in_days" jdbcType="INTEGER"/>
            <result property="sortNum" column="sort_num" jdbcType="INTEGER"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="isDel" column="is_del" jdbcType="INTEGER"/>
            <result property="awardConfigIds" column="theme_activity_days_rule_award_ids" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,theme_activity_id,sign_in_days,
        sort_num,create_time,update_time,
        is_del,award_title,award_subtitle,
        theme_activity_days_rule_award_ids
    </sql>
</mapper>
