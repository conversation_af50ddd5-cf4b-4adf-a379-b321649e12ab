package org.springcenter.marketing.modules.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 留言行为
 */
@Data
@TableName("note_behavior")
public class NoteBehavior {
    @ApiModelProperty(value = "自增主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    @ApiModelProperty(value = "业务ID")
    @TableField("biz_id")
    private String bizId;
    @ApiModelProperty("外部应用key")
    @TableField("app_key")
    private String appKey;
    @ApiModelProperty("外部ID：留言ID或者其他业务ID")
    @TableField("out_id")
    private String outId;
    @ApiModelProperty("行为类型 1=评论、2=点赞、3=转发")
    @TableField("type")
    private Integer type;
    @ApiModelProperty("是否取消：0=否, 1=是")
    @TableField("cancel")
    private Integer cancel;
    @ApiModelProperty("用户unionId")
    @TableField("union_id")
    private String unionId;
    @ApiModelProperty("品牌卡号")
    @TableField("card_no")
    private String cardNo;
    @ApiModelProperty("是否删除")
    @TableField("is_delete")
    private Integer isDelete;
    @ApiModelProperty("创建时间")
    @TableField("create_time")
    private Date createTime;
    @ApiModelProperty("更新时间")
    @TableField("update_time")
    private Date updateTime;
}

