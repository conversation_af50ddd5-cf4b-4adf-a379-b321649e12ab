package org.springcenter.marketing.modules.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 留言
 */
@Data
@TableName("note")
public class Note {
    @ApiModelProperty(value = "自增主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    @ApiModelProperty(value = "业务ID")
    @TableField("biz_id")
    private String bizId;
    @ApiModelProperty("外部应用key")
    @TableField("app_key")
    private String appKey;
    @ApiModelProperty("外部ID")
    @TableField("out_id")
    private String outId;
    @ApiModelProperty("用户unionId")
    @TableField("union_id")
    private String unionId;
    @ApiModelProperty("品牌卡号")
    @TableField("card_no")
    private String cardNo;
    @ApiModelProperty("留言内容")
    @TableField("content")
    private String content;
    @ApiModelProperty("发布时间")
    @TableField("publish_time")
    private Date publishTime;
    @ApiModelProperty("置顶：0=不置顶,1=置顶")
    @TableField("top")
    private Integer top;
    @ApiModelProperty("置顶时间")
    @TableField("top_time")
    private Date topTime;
    @ApiModelProperty("审核流程ID")
    @TableField("audit_id")
    private String auditId;
    @ApiModelProperty("审核状态：0=待审核, 1=审核通过, 2=审核失败")
    @TableField("audit_status")
    private Integer auditStatus;
    @ApiModelProperty("审核拒绝原因")
    @TableField("audit_refuse_msg")
    private String auditRefuseMsg;
    @ApiModelProperty("审核完成时间")
    @TableField("audit_finish_time")
    private Date auditFinishTime;
    @ApiModelProperty("人工拒绝：0=未操作，1=操作")
    @TableField("sys_refuse")
    private Integer sysRefuse;
    @ApiModelProperty("人工拒绝时间")
    @TableField("sys_refuse_time")
    private Date sysRefuseTime;
    @ApiModelProperty("是否为草稿：0=否, 1=是")
    @TableField("draft")
    private Integer draft;
    @ApiModelProperty("草稿时间")
    @TableField("draft_time")
    private Date draftTime;
    @ApiModelProperty("点赞数")
    @TableField("like_count")
    private Long likeCount;
    @ApiModelProperty("评论数")
    @TableField("comment_count")
    private Long commentCount;
    @ApiModelProperty("分享数")
    @TableField("share_count")
    private Long shareCount;
    @ApiModelProperty("是否删除")
    @TableField("is_delete")
    private Integer isDelete;
    @ApiModelProperty("创建时间")
    @TableField("create_time")
    private Date createTime;
    @ApiModelProperty("更新时间")
    @TableField("update_time")
    private Date updateTime;
    @ApiModelProperty("图片")
    @TableField("imgs")
    private String imgs;
    @ApiModelProperty("品牌ID")
    @TableField("brand_id")
    private String brandId;
}

