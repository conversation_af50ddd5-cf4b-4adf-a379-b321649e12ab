package org.springcenter.marketing.modules.context;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


@Data
@ApiModel(
        value = "MemberListQueryContext",
        description = "会员列表查询参数"
)
public class MemberListQueryContext implements Serializable {
    @ApiModelProperty(value = "是否需要集团卡信息 Y-结果返回")
    private String isNeedCliqueMember;
    @ApiModelProperty(value = "是否需要会员渠道信息 Y-结果返回")
    private String isNeedMemberChannel;
    @ApiModelProperty(value = "是否需要会员拓展信息 Y-结果返回")
    private String isNeedMemberExt;
    @ApiModelProperty(value = "查询类型 1：手机号查询，2：微信unionId查询，3：集团卡号，4：微信openId, 5：品牌卡号")
    private Integer queryType;
    @ApiModelProperty(value = "查询关联值")
    private List<String> queryValue;
    @ApiModelProperty(value = "会员卡状态 Y-返回有效卡,N-全部返回")
    private String status;

    public MemberListQueryContext() {
    }

    public MemberListQueryContext buildCardNoList(List<String> cardNoList) {
        this.isNeedCliqueMember = "Y";
        this.status = "Y";
        this.queryType = 5;
        this.queryValue = cardNoList;
        return this;
    }
}
