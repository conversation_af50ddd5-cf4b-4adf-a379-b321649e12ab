package org.springcenter.marketing.modules.integration.cache.Impl;

import org.springcenter.marketing.common.RedDotRecordTypeEnum;
import org.springcenter.marketing.modules.integration.cache.IRedDotRecordCacheService;
import org.springcenter.marketing.modules.mapper.RedDotRecordMapper;
import org.springcenter.marketing.modules.model.RedDotRecord;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class RedDotRecordCacheServiceImpl implements IRedDotRecordCacheService {

    @Resource
    private RedDotRecordMapper redDotRecordMapper;



    public List<RedDotRecord> getRedDotRecordList(String unionId,String brandId){
        RedDotRecord search = new RedDotRecord();
        search.setUnionid(unionId);
        search.setBrandId(brandId);
        search.setRedDotType(RedDotRecordTypeEnum.INTEGRAL.getCode());
        List<RedDotRecord> redDotRecords = redDotRecordMapper.selectDateByType(search);

        return redDotRecords;
    }
}
