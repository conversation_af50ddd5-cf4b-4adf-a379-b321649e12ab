package org.springcenter.marketing.modules.mapper;

import org.apache.ibatis.annotations.Param;
import org.springcenter.marketing.modules.model.RUserExpand;

import java.util.List;

public interface RUserExpandMapper {
    int deleteByPrimaryKey(String id);

    int insert(RUserExpand record);

    int insertSelective(RUserExpand record);

    RUserExpand selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(RUserExpand record);

    int updateByPrimaryKey(RUserExpand record);

    RUserExpand selectByClientVipId(@Param("clientVipId") Long clientVipId);

    RUserExpand selectByUnionIdAndWeId(@Param("unionid") String unionid, @Param("weid")String weid);

    List<RUserExpand> selectByUnionIdAndWeIds(@Param("unionid") String unionid, @Param("weids") List<String> weids);
}