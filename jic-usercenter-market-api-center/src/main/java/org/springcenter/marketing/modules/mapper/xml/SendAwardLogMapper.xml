<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.marketing.modules.mapper.SendAwardLogMapper">

    <resultMap id="BaseResultMap" type="org.springcenter.marketing.modules.model.SendAwardLog">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="unionid" column="unionid" jdbcType="VARCHAR"/>
            <result property="cardno" column="cardno" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="isDel" column="is_del" jdbcType="INTEGER"/>
            <result property="awardType" column="award_type" jdbcType="INTEGER"/>
            <result property="awardName" column="award_name" jdbcType="VARCHAR"/>
            <result property="awardCode" column="award_code" jdbcType="VARCHAR"/>
            <result property="awardNum" column="award_num" jdbcType="INTEGER"/>
            <result property="awardConfigId" column="award_config_id" jdbcType="INTEGER"/>
            <result property="refNo" column="ref_no" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="INTEGER"/>
            <result property="commonInfo" column="common_info" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,unionid,cardno,
        create_time,update_time,is_del,
        award_type,award_name,award_code,
        award_num,award_config_id,ref_no,
        status,common_info
    </sql>
</mapper>
