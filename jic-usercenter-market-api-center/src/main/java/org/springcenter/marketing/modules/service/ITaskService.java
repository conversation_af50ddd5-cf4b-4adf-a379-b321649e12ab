package org.springcenter.marketing.modules.service;

import com.jnby.common.Page;
import org.springcenter.marketing.api.dto.*;
import org.springcenter.marketing.api.dto.miniapp.QueryBirthReq;
import org.springcenter.marketing.modules.entity.BrandSubTrick;
import org.springcenter.marketing.modules.entity.CostTaskPopResp;
import org.springcenter.marketing.modules.entity.TaskUpdateUserInfoReq;

import java.text.ParseException;
import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/2/9 10:17
 */
public interface ITaskService {
    /**
     * 获取操作人列表
     * @param requestData 请求参数
     * @param page 页码
     * @return 返回
     */
    List<TaskOperatorsResp> queryTaskOperators(TaskOperatorsReq requestData, Page page);

    /**
     * 获取任务模板列表
     * @param requestData 请求参数
     * @param page 页码
     * @return 返回
     */
    TaskTemplateListResp queryTaskList(TaskTemplateListReq requestData, Page page);

    /**
     * 根据任务id获取任务详情
     * @param requestData 请求参数
     * @return 返回任务详情
     */
    TaskTemplateDetailResp queryTaskDetail(TaskTemplateDetailReq requestData);

    /**
     * 操作上下架
     * @param requestData 入参
     * @return 出参
     */
    List<TaskTemplateResp> operateTask(OperateTaskTemplateReq requestData);

    /**
     * 查询任务明细
     * @param requestData 入参
     * @return 出参
     */
    List<TaskTemplateDetailsResp> queryTaskTemplateDetails(TaskTemplateDetailsReq requestData, Page page);

    /**
     * 查询用户任务
     * @param requestData 入参
     * @return 出参
     */
    List<UserTaskListResp> queryUserTaskList(UserTaskListReq requestData) throws ParseException;

    /**
     * 监听不同类型处理
     * @param requestData
     */
    Boolean listener(ListenerEvent requestData);

    /**
     * 用户领取或者跳转
     * @param requestData 传参
     * @return 返回
     */
    Boolean clickTaskByUser(UserClickTaskReq requestData);

    /**
     * 下架任务
     */
    void takeOffTheShelfJob();

    /**
     * 批量更新模板时间信息
     * @param requestData
     * @return
     */
    Boolean batchUpdateTaskDate(BatchUpdateTaskTemplateReq requestData);

    /**
     * 将用户活动置为过期 任务模板到期
     */
    void expireUserJob();

    /**
     * 判断当前品牌是否又有效任务
     * @param requestData 品牌
     * @return 返回
     */
    Boolean judgeIsShow(JudgeIsShowReq requestData);

    /**
     * 设置提醒
     * @param requestData 入参
     * @return 返回
     */
    Boolean setReminder(UserClickReminderReq requestData);

    /**
     * 查询用户是否有提醒
     * @param requestData 入参
     * @return 返回
     */
    UserReminderInfoResp queryUserReminder(UserReminderInfoReq requestData);

    /**
     * 公众号任务提醒
     */
    void sendTaskReminderInfo(Long weid);

    /**
     * 判断当前用户是否订阅
     * @param requestData 入参
     * @return 返回
     */
    Boolean queryUserIsSubscribe(UserReminderInfoReq requestData);

    /**
     * 根据任务模版生成任务
     */
    void generateTaskInfo();

    /**
     * 过期任务信息
     */
    void expireTaskInfo();

    /**
     * 根据品牌获取话术
     * @param requestData 品牌id
     * @return 返回话术
     */
    BrandSubTrick querySubscribeTrick(String requestData);

    /**
     * 查询生日
     * 就是根据一个人查他品牌的生日 没有的话 随机返回一个他其他的有的品牌的生日
     * @param requestData
     * @return
     */
    String queryBirth(QueryBirthReq requestData);



    /**
     * 消费任务领取成功弹窗
     * @param requestData 任务id
     * @return 返回弹窗信息
     */
    CostTaskPopResp drawCostPopTask(String requestData);


    /**
     * 查询用户是否有童孩任务
     * @param requestData 用户id
     * @return 返回
     */
    ChildIsDisplayResp queryUserChildTask(UserTaskListReq requestData);

    /**
     * 更新用户基本信息
     * @param requestData 入参
     * @return 返回
     */
    Boolean updateUserInfo(TaskUpdateUserInfoReq requestData);
}
