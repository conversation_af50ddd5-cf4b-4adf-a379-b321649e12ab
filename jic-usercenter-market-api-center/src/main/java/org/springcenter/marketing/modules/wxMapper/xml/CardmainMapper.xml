<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.marketing.modules.wxMapper.CardmainMapper">
  <resultMap id="BaseResultMap" type="org.springcenter.marketing.modules.model.Cardmain">
    <id column="ID" jdbcType="DECIMAL" property="id" />
    <result column="RENTID" jdbcType="DECIMAL" property="rentid" />
    <result column="WEID" jdbcType="DECIMAL" property="weid" />
    <result column="OPENID" jdbcType="VARCHAR" property="openid" />
    <result column="CARDTYPE" jdbcType="DECIMAL" property="cardtype" />
    <result column="CARDNO" jdbcType="VARCHAR" property="cardno" />
    <result column="USERNAME" jdbcType="VARCHAR" property="username" />
    <result column="JFYE" jdbcType="DECIMAL" property="jfye" />
    <result column="JFTOTAL" jdbcType="DECIMAL" property="jftotal" />
    <result column="JFXF" jdbcType="DECIMAL" property="jfxf" />
    <result column="JFSIGN" jdbcType="DECIMAL" property="jfsign" />
    <result column="XFJE" jdbcType="DECIMAL" property="xfje" />
    <result column="TEL" jdbcType="VARCHAR" property="tel" />
    <result column="BIRTHDAY" jdbcType="TIMESTAMP" property="birthday" />
    <result column="ADDRESS" jdbcType="VARCHAR" property="address" />
    <result column="SEX" jdbcType="CHAR" property="sex" />
    <result column="EMAIL" jdbcType="VARCHAR" property="email" />
    <result column="STATUS" jdbcType="CHAR" property="status" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="MODIFYDATE" jdbcType="TIMESTAMP" property="modifydate" />
    <result column="INPUTDATE" jdbcType="TIMESTAMP" property="inputdate" />
    <result column="VALIDDATE" jdbcType="TIMESTAMP" property="validdate" />
    <result column="ISLINK" jdbcType="CHAR" property="islink" />
    <result column="LINKSOURCE" jdbcType="VARCHAR" property="linksource" />
    <result column="LINKID" jdbcType="VARCHAR" property="linkid" />
    <result column="ROLEID" jdbcType="DECIMAL" property="roleid" />
    <result column="KFID" jdbcType="DECIMAL" property="kfid" />
    <result column="KKSTORE" jdbcType="VARCHAR" property="kkstore" />
    <result column="ISSEND" jdbcType="CHAR" property="issend" />
    <result column="SENDTIME" jdbcType="DECIMAL" property="sendtime" />
    <result column="CDMUNIONID" jdbcType="VARCHAR" property="cdmunionid" />
    <result column="CDMSOURCETYPE" jdbcType="VARCHAR" property="cdmsourcetype" />
    <result column="CDMSOURCEVAL" jdbcType="VARCHAR" property="cdmsourceval" />
    <result column="ISPOSTFITTING" jdbcType="CHAR" property="ispostfitting" />
    <result column="POSTFITTINGTIME" jdbcType="DECIMAL" property="postfittingtime" />
    <result column="OYEARPAYAMOUNT" jdbcType="DECIMAL" property="oyearpayamount" />
    <result column="DYEARPAYAMOUNT" jdbcType="DECIMAL" property="dyearpayamount" />
    <result column="OYEARPAYCOUNT" jdbcType="DECIMAL" property="oyearpaycount" />
    <result column="DYEARPAYCOUNT" jdbcType="DECIMAL" property="dyearpaycount" />
    <result column="OMONTHPAYAMOUNT" jdbcType="DECIMAL" property="omonthpayamount" />
    <result column="DMONTHPAYAMOUNT" jdbcType="DECIMAL" property="dmonthpayamount" />
    <result column="OMONTHPAYCOUNT" jdbcType="DECIMAL" property="omonthpaycount" />
    <result column="DMONTHPAYCOUNT" jdbcType="DECIMAL" property="dmonthpaycount" />
    <result column="JNBEAN" jdbcType="DECIMAL" property="jnbean" />
    <result column="BY1" jdbcType="DECIMAL" property="by1" />
    <result column="BY2" jdbcType="VARCHAR" property="by2" />
    <result column="BY3" jdbcType="VARCHAR" property="by3" />
    <result column="WXOPENID" jdbcType="VARCHAR" property="wxopenid" />
    <result column="UUID" jdbcType="VARCHAR" property="uuid" />
    <result column="UNIONID" jdbcType="VARCHAR" property="unionid" />
    <result column="NICKNAME" jdbcType="VARCHAR" property="nickname" />
    <result column="WID" jdbcType="DECIMAL" property="wid" />
    <result column="HEADIMGURL" jdbcType="VARCHAR" property="headimgurl" />
    <result column="CARDID" jdbcType="DECIMAL" property="cardid" />
    <result column="CARDTYPENAME" jdbcType="VARCHAR" property="cardtypename" />
    <result column="CARDTYPEID" jdbcType="DECIMAL" property="cardtypeid" />
    <result column="MEMBERID" jdbcType="DECIMAL" property="memberid" />
    <result column="MEMBERTYPEID" jdbcType="DECIMAL" property="membertypeid" />
    <result column="MEMBERTYPENAME" jdbcType="VARCHAR" property="membertypename" />
    <result column="STOREID" jdbcType="DECIMAL" property="storeid" />
    <result column="STORENAME" jdbcType="VARCHAR" property="storename" />
    <result column="STORECODE" jdbcType="VARCHAR" property="storecode" />
    <result column="CLERKID" jdbcType="DECIMAL" property="clerkid" />
    <result column="CLERKNAME" jdbcType="VARCHAR" property="clerkname" />
    <result column="CLERKCODE" jdbcType="VARCHAR" property="clerkcode" />
    <result column="CUSTOMERID" jdbcType="DECIMAL" property="customerid" />
    <result column="OPENTIME" jdbcType="DECIMAL" property="opentime" />
    <result column="MEMBERCODE" jdbcType="VARCHAR" property="membercode" />
    <result column="ISACTIVE" jdbcType="CHAR" property="isactive" />
    <result column="OPENCHANNEL" jdbcType="VARCHAR" property="openchannel" />
    <result column="INTEGRAL" jdbcType="DECIMAL" property="integral" />
    <result column="ISSUBSCRIBE" jdbcType="CHAR" property="issubscribe" />
    <result column="ISEXTERNALUSER" jdbcType="CHAR" property="isexternaluser" />
    <result column="ISSUBSCRIBEBOX" jdbcType="CHAR" property="issubscribebox" />
    <result column="ISRECEIVEBIRTH" jdbcType="CHAR" property="isreceivebirth" />
    <result column="LASTCONSUMTIME" jdbcType="TIMESTAMP" property="lastconsumtime" />
    <result column="CONSUMTIMES" jdbcType="DECIMAL" property="consumtimes" />
    <result column="TOTCONSUMAMT" jdbcType="DECIMAL" property="totconsumamt" />
    <result column="EXT1" jdbcType="VARCHAR" property="ext1" />
    <result column="EXT2" jdbcType="VARCHAR" property="ext2" />
    <result column="EXT4" jdbcType="VARCHAR" property="ext4" />
    <result column="EXT5" jdbcType="VARCHAR" property="ext5" />
    <result column="EXT6" jdbcType="VARCHAR" property="ext6" />
    <result column="EXT7" jdbcType="VARCHAR" property="ext7" />
    <result column="EXT8" jdbcType="VARCHAR" property="ext8" />
    <result column="EXT9" jdbcType="VARCHAR" property="ext9" />
    <result column="EXT10" jdbcType="VARCHAR" property="ext10" />
    <result column="MEMBERTIME" jdbcType="TIMESTAMP" property="membertime" />
    <result column="MEMBERINTEGRAL" jdbcType="DECIMAL" property="memberintegral" />
    <result column="CLERKPHONE" jdbcType="VARCHAR" property="clerkphone" />
    <result column="CARDSTATUS" jdbcType="DECIMAL" property="cardstatus" />
    <result column="FIRSTCONSUMTIME" jdbcType="TIMESTAMP" property="firstconsumtime" />
    <result column="EXTERNALUSERID" jdbcType="VARCHAR" property="externaluserid" />
    <result column="CARDLVL" jdbcType="DECIMAL" property="cardlvl" />
    <result column="MEMBERLVL" jdbcType="DECIMAL" property="memberlvl" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, RENTID, WEID, OPENID, CARDTYPE, CARDNO, USERNAME, JFYE, JFTOTAL, JFXF, JFSIGN, 
    XFJE, TEL, BIRTHDAY, ADDRESS, SEX, EMAIL, "STATUS", REMARK, MODIFYDATE, INPUTDATE, 
    VALIDDATE, ISLINK, LINKSOURCE, LINKID, ROLEID, KFID, KKSTORE, ISSEND, SENDTIME, CDMUNIONID, 
    CDMSOURCETYPE, CDMSOURCEVAL, ISPOSTFITTING, POSTFITTINGTIME, OYEARPAYAMOUNT, DYEARPAYAMOUNT, 
    OYEARPAYCOUNT, DYEARPAYCOUNT, OMONTHPAYAMOUNT, DMONTHPAYAMOUNT, OMONTHPAYCOUNT, DMONTHPAYCOUNT, 
    JNBEAN, BY1, BY2, BY3, WXOPENID, UUID, UNIONID, NICKNAME, WID, HEADIMGURL, CARDID, 
    CARDTYPENAME, CARDTYPEID, MEMBERID, MEMBERTYPEID, MEMBERTYPENAME, STOREID, STORENAME, 
    STORECODE, CLERKID, CLERKNAME, CLERKCODE, CUSTOMERID, OPENTIME, MEMBERCODE, ISACTIVE, 
    OPENCHANNEL, INTEGRAL, ISSUBSCRIBE, ISEXTERNALUSER, ISSUBSCRIBEBOX, ISRECEIVEBIRTH, 
    LASTCONSUMTIME, CONSUMTIMES, TOTCONSUMAMT, EXT1, EXT2, EXT3, EXT4, EXT5, EXT6, EXT7, 
    EXT8, EXT9, EXT10, MEMBERTIME, MEMBERINTEGRAL, CLERKPHONE, CARDSTATUS, FIRSTCONSUMTIME, 
    EXTERNALUSERID, CARDLVL, MEMBERLVL
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from CARDMAIN
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <select id="selectByUnionIdAndCardTypeId" resultType="org.springcenter.marketing.modules.model.Cardmain">
    select tel from USERWX.CARDMAIN where unionid = #{unionid} and cardtype = #{cardTypeId}
  </select>
    <select id="selectByUnionId" resultMap="BaseResultMap">
      select id,tel from USERWX.CARDMAIN where unionid = #{unionid} and status = 'Y'
    </select>
    <select id="selectByOpenId" resultMap="BaseResultMap">
        select REMARK from USERWX.CARDMAIN
        where OPENID = #{openId}
    </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from CARDMAIN
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" keyColumn="ID" keyProperty="id" parameterType="org.springcenter.marketing.modules.model.Cardmain" useGeneratedKeys="true">
    insert into CARDMAIN (RENTID, WEID, OPENID, 
      CARDTYPE, CARDNO, USERNAME, 
      JFYE, JFTOTAL, JFXF, 
      JFSIGN, XFJE, TEL, 
      BIRTHDAY, ADDRESS, SEX, 
      EMAIL, "STATUS", REMARK, 
      MODIFYDATE, INPUTDATE, VALIDDATE, 
      ISLINK, LINKSOURCE, LINKID, 
      ROLEID, KFID, KKSTORE, 
      ISSEND, SENDTIME, CDMUNIONID, 
      CDMSOURCETYPE, CDMSOURCEVAL, ISPOSTFITTING, 
      POSTFITTINGTIME, OYEARPAYAMOUNT, DYEARPAYAMOUNT, 
      OYEARPAYCOUNT, DYEARPAYCOUNT, OMONTHPAYAMOUNT, 
      DMONTHPAYAMOUNT, OMONTHPAYCOUNT, DMONTHPAYCOUNT, 
      JNBEAN, BY1, BY2, BY3, 
      WXOPENID, UUID, UNIONID, 
      NICKNAME, WID, HEADIMGURL, 
      CARDID, CARDTYPENAME, CARDTYPEID, 
      MEMBERID, MEMBERTYPEID, MEMBERTYPENAME, 
      STOREID, STORENAME, STORECODE, 
      CLERKID, CLERKNAME, CLERKCODE, 
      CUSTOMERID, OPENTIME, MEMBERCODE, 
      ISACTIVE, OPENCHANNEL, INTEGRAL, 
      ISSUBSCRIBE, ISEXTERNALUSER, ISSUBSCRIBEBOX, 
      ISRECEIVEBIRTH, LASTCONSUMTIME, CONSUMTIMES, 
      TOTCONSUMAMT, EXT1, EXT2, 
      EXT3, EXT4, EXT5, EXT6, 
      EXT7, EXT8, EXT9, EXT10, 
      MEMBERTIME, MEMBERINTEGRAL, CLERKPHONE, 
      CARDSTATUS, FIRSTCONSUMTIME, EXTERNALUSERID, 
      CARDLVL, MEMBERLVL)
    values (#{rentid,jdbcType=DECIMAL}, #{weid,jdbcType=DECIMAL}, #{openid,jdbcType=VARCHAR}, 
      #{cardtype,jdbcType=DECIMAL}, #{cardno,jdbcType=VARCHAR}, #{username,jdbcType=VARCHAR}, 
      #{jfye,jdbcType=DECIMAL}, #{jftotal,jdbcType=DECIMAL}, #{jfxf,jdbcType=DECIMAL}, 
      #{jfsign,jdbcType=DECIMAL}, #{xfje,jdbcType=DECIMAL}, #{tel,jdbcType=VARCHAR}, 
      #{birthday,jdbcType=TIMESTAMP}, #{address,jdbcType=VARCHAR}, #{sex,jdbcType=CHAR}, 
      #{email,jdbcType=VARCHAR}, #{status,jdbcType=CHAR}, #{remark,jdbcType=VARCHAR}, 
      #{modifydate,jdbcType=TIMESTAMP}, #{inputdate,jdbcType=TIMESTAMP}, #{validdate,jdbcType=TIMESTAMP}, 
      #{islink,jdbcType=CHAR}, #{linksource,jdbcType=VARCHAR}, #{linkid,jdbcType=VARCHAR}, 
      #{roleid,jdbcType=DECIMAL}, #{kfid,jdbcType=DECIMAL}, #{kkstore,jdbcType=VARCHAR}, 
      #{issend,jdbcType=CHAR}, #{sendtime,jdbcType=DECIMAL}, #{cdmunionid,jdbcType=VARCHAR}, 
      #{cdmsourcetype,jdbcType=VARCHAR}, #{cdmsourceval,jdbcType=VARCHAR}, #{ispostfitting,jdbcType=CHAR}, 
      #{postfittingtime,jdbcType=DECIMAL}, #{oyearpayamount,jdbcType=DECIMAL}, #{dyearpayamount,jdbcType=DECIMAL}, 
      #{oyearpaycount,jdbcType=DECIMAL}, #{dyearpaycount,jdbcType=DECIMAL}, #{omonthpayamount,jdbcType=DECIMAL}, 
      #{dmonthpayamount,jdbcType=DECIMAL}, #{omonthpaycount,jdbcType=DECIMAL}, #{dmonthpaycount,jdbcType=DECIMAL}, 
      #{jnbean,jdbcType=DECIMAL}, #{by1,jdbcType=DECIMAL}, #{by2,jdbcType=VARCHAR}, #{by3,jdbcType=VARCHAR}, 
      #{wxopenid,jdbcType=VARCHAR}, #{uuid,jdbcType=VARCHAR}, #{unionid,jdbcType=VARCHAR}, 
      #{nickname,jdbcType=VARCHAR}, #{wid,jdbcType=DECIMAL}, #{headimgurl,jdbcType=VARCHAR}, 
      #{cardid,jdbcType=DECIMAL}, #{cardtypename,jdbcType=VARCHAR}, #{cardtypeid,jdbcType=DECIMAL}, 
      #{memberid,jdbcType=DECIMAL}, #{membertypeid,jdbcType=DECIMAL}, #{membertypename,jdbcType=VARCHAR}, 
      #{storeid,jdbcType=DECIMAL}, #{storename,jdbcType=VARCHAR}, #{storecode,jdbcType=VARCHAR}, 
      #{clerkid,jdbcType=DECIMAL}, #{clerkname,jdbcType=VARCHAR}, #{clerkcode,jdbcType=VARCHAR}, 
      #{customerid,jdbcType=DECIMAL}, #{opentime,jdbcType=TIMESTAMP}, #{membercode,jdbcType=VARCHAR}, 
      #{isactive,jdbcType=CHAR}, #{openchannel,jdbcType=VARCHAR}, #{integral,jdbcType=DECIMAL}, 
      #{issubscribe,jdbcType=CHAR}, #{isexternaluser,jdbcType=CHAR}, #{issubscribebox,jdbcType=CHAR}, 
      #{isreceivebirth,jdbcType=CHAR}, #{lastconsumtime,jdbcType=TIMESTAMP}, #{consumtimes,jdbcType=DECIMAL}, 
      #{totconsumamt,jdbcType=DECIMAL}, #{ext1,jdbcType=VARCHAR}, #{ext2,jdbcType=VARCHAR}, 
      #{ext3,jdbcType=VARCHAR}, #{ext4,jdbcType=VARCHAR}, #{ext5,jdbcType=VARCHAR}, #{ext6,jdbcType=VARCHAR}, 
      #{ext7,jdbcType=VARCHAR}, #{ext8,jdbcType=VARCHAR}, #{ext9,jdbcType=VARCHAR}, #{ext10,jdbcType=VARCHAR}, 
      #{membertime,jdbcType=TIMESTAMP}, #{memberintegral,jdbcType=DECIMAL}, #{clerkphone,jdbcType=VARCHAR}, 
      #{cardstatus,jdbcType=DECIMAL}, #{firstconsumtime,jdbcType=TIMESTAMP}, #{externaluserid,jdbcType=VARCHAR}, 
      #{cardlvl,jdbcType=DECIMAL}, #{memberlvl,jdbcType=DECIMAL})
  </insert>
  <insert id="insertSelective" keyColumn="ID" keyProperty="id" parameterType="org.springcenter.marketing.modules.model.Cardmain" useGeneratedKeys="true">
    insert into CARDMAIN
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="rentid != null">
        RENTID,
      </if>
      <if test="weid != null">
        WEID,
      </if>
      <if test="openid != null">
        OPENID,
      </if>
      <if test="cardtype != null">
        CARDTYPE,
      </if>
      <if test="cardno != null">
        CARDNO,
      </if>
      <if test="username != null">
        USERNAME,
      </if>
      <if test="jfye != null">
        JFYE,
      </if>
      <if test="jftotal != null">
        JFTOTAL,
      </if>
      <if test="jfxf != null">
        JFXF,
      </if>
      <if test="jfsign != null">
        JFSIGN,
      </if>
      <if test="xfje != null">
        XFJE,
      </if>
      <if test="tel != null">
        TEL,
      </if>
      <if test="birthday != null">
        BIRTHDAY,
      </if>
      <if test="address != null">
        ADDRESS,
      </if>
      <if test="sex != null">
        SEX,
      </if>
      <if test="email != null">
        EMAIL,
      </if>
      <if test="status != null">
        "STATUS",
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="modifydate != null">
        MODIFYDATE,
      </if>
      <if test="inputdate != null">
        INPUTDATE,
      </if>
      <if test="validdate != null">
        VALIDDATE,
      </if>
      <if test="islink != null">
        ISLINK,
      </if>
      <if test="linksource != null">
        LINKSOURCE,
      </if>
      <if test="linkid != null">
        LINKID,
      </if>
      <if test="roleid != null">
        ROLEID,
      </if>
      <if test="kfid != null">
        KFID,
      </if>
      <if test="kkstore != null">
        KKSTORE,
      </if>
      <if test="issend != null">
        ISSEND,
      </if>
      <if test="sendtime != null">
        SENDTIME,
      </if>
      <if test="cdmunionid != null">
        CDMUNIONID,
      </if>
      <if test="cdmsourcetype != null">
        CDMSOURCETYPE,
      </if>
      <if test="cdmsourceval != null">
        CDMSOURCEVAL,
      </if>
      <if test="ispostfitting != null">
        ISPOSTFITTING,
      </if>
      <if test="postfittingtime != null">
        POSTFITTINGTIME,
      </if>
      <if test="oyearpayamount != null">
        OYEARPAYAMOUNT,
      </if>
      <if test="dyearpayamount != null">
        DYEARPAYAMOUNT,
      </if>
      <if test="oyearpaycount != null">
        OYEARPAYCOUNT,
      </if>
      <if test="dyearpaycount != null">
        DYEARPAYCOUNT,
      </if>
      <if test="omonthpayamount != null">
        OMONTHPAYAMOUNT,
      </if>
      <if test="dmonthpayamount != null">
        DMONTHPAYAMOUNT,
      </if>
      <if test="omonthpaycount != null">
        OMONTHPAYCOUNT,
      </if>
      <if test="dmonthpaycount != null">
        DMONTHPAYCOUNT,
      </if>
      <if test="jnbean != null">
        JNBEAN,
      </if>
      <if test="by1 != null">
        BY1,
      </if>
      <if test="by2 != null">
        BY2,
      </if>
      <if test="by3 != null">
        BY3,
      </if>
      <if test="wxopenid != null">
        WXOPENID,
      </if>
      <if test="uuid != null">
        UUID,
      </if>
      <if test="unionid != null">
        UNIONID,
      </if>
      <if test="nickname != null">
        NICKNAME,
      </if>
      <if test="wid != null">
        WID,
      </if>
      <if test="headimgurl != null">
        HEADIMGURL,
      </if>
      <if test="cardid != null">
        CARDID,
      </if>
      <if test="cardtypename != null">
        CARDTYPENAME,
      </if>
      <if test="cardtypeid != null">
        CARDTYPEID,
      </if>
      <if test="memberid != null">
        MEMBERID,
      </if>
      <if test="membertypeid != null">
        MEMBERTYPEID,
      </if>
      <if test="membertypename != null">
        MEMBERTYPENAME,
      </if>
      <if test="storeid != null">
        STOREID,
      </if>
      <if test="storename != null">
        STORENAME,
      </if>
      <if test="storecode != null">
        STORECODE,
      </if>
      <if test="clerkid != null">
        CLERKID,
      </if>
      <if test="clerkname != null">
        CLERKNAME,
      </if>
      <if test="clerkcode != null">
        CLERKCODE,
      </if>
      <if test="customerid != null">
        CUSTOMERID,
      </if>
      <if test="opentime != null">
        OPENTIME,
      </if>
      <if test="membercode != null">
        MEMBERCODE,
      </if>
      <if test="isactive != null">
        ISACTIVE,
      </if>
      <if test="openchannel != null">
        OPENCHANNEL,
      </if>
      <if test="integral != null">
        INTEGRAL,
      </if>
      <if test="issubscribe != null">
        ISSUBSCRIBE,
      </if>
      <if test="isexternaluser != null">
        ISEXTERNALUSER,
      </if>
      <if test="issubscribebox != null">
        ISSUBSCRIBEBOX,
      </if>
      <if test="isreceivebirth != null">
        ISRECEIVEBIRTH,
      </if>
      <if test="lastconsumtime != null">
        LASTCONSUMTIME,
      </if>
      <if test="consumtimes != null">
        CONSUMTIMES,
      </if>
      <if test="totconsumamt != null">
        TOTCONSUMAMT,
      </if>
      <if test="ext1 != null">
        EXT1,
      </if>
      <if test="ext2 != null">
        EXT2,
      </if>
      <if test="ext3 != null">
        EXT3,
      </if>
      <if test="ext4 != null">
        EXT4,
      </if>
      <if test="ext5 != null">
        EXT5,
      </if>
      <if test="ext6 != null">
        EXT6,
      </if>
      <if test="ext7 != null">
        EXT7,
      </if>
      <if test="ext8 != null">
        EXT8,
      </if>
      <if test="ext9 != null">
        EXT9,
      </if>
      <if test="ext10 != null">
        EXT10,
      </if>
      <if test="membertime != null">
        MEMBERTIME,
      </if>
      <if test="memberintegral != null">
        MEMBERINTEGRAL,
      </if>
      <if test="clerkphone != null">
        CLERKPHONE,
      </if>
      <if test="cardstatus != null">
        CARDSTATUS,
      </if>
      <if test="firstconsumtime != null">
        FIRSTCONSUMTIME,
      </if>
      <if test="externaluserid != null">
        EXTERNALUSERID,
      </if>
      <if test="cardlvl != null">
        CARDLVL,
      </if>
      <if test="memberlvl != null">
        MEMBERLVL,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="rentid != null">
        #{rentid,jdbcType=DECIMAL},
      </if>
      <if test="weid != null">
        #{weid,jdbcType=DECIMAL},
      </if>
      <if test="openid != null">
        #{openid,jdbcType=VARCHAR},
      </if>
      <if test="cardtype != null">
        #{cardtype,jdbcType=DECIMAL},
      </if>
      <if test="cardno != null">
        #{cardno,jdbcType=VARCHAR},
      </if>
      <if test="username != null">
        #{username,jdbcType=VARCHAR},
      </if>
      <if test="jfye != null">
        #{jfye,jdbcType=DECIMAL},
      </if>
      <if test="jftotal != null">
        #{jftotal,jdbcType=DECIMAL},
      </if>
      <if test="jfxf != null">
        #{jfxf,jdbcType=DECIMAL},
      </if>
      <if test="jfsign != null">
        #{jfsign,jdbcType=DECIMAL},
      </if>
      <if test="xfje != null">
        #{xfje,jdbcType=DECIMAL},
      </if>
      <if test="tel != null">
        #{tel,jdbcType=VARCHAR},
      </if>
      <if test="birthday != null">
        #{birthday,jdbcType=TIMESTAMP},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="sex != null">
        #{sex,jdbcType=CHAR},
      </if>
      <if test="email != null">
        #{email,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=CHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="modifydate != null">
        #{modifydate,jdbcType=TIMESTAMP},
      </if>
      <if test="inputdate != null">
        #{inputdate,jdbcType=TIMESTAMP},
      </if>
      <if test="validdate != null">
        #{validdate,jdbcType=TIMESTAMP},
      </if>
      <if test="islink != null">
        #{islink,jdbcType=CHAR},
      </if>
      <if test="linksource != null">
        #{linksource,jdbcType=VARCHAR},
      </if>
      <if test="linkid != null">
        #{linkid,jdbcType=VARCHAR},
      </if>
      <if test="roleid != null">
        #{roleid,jdbcType=DECIMAL},
      </if>
      <if test="kfid != null">
        #{kfid,jdbcType=DECIMAL},
      </if>
      <if test="kkstore != null">
        #{kkstore,jdbcType=VARCHAR},
      </if>
      <if test="issend != null">
        #{issend,jdbcType=CHAR},
      </if>
      <if test="sendtime != null">
        #{sendtime,jdbcType=DECIMAL},
      </if>
      <if test="cdmunionid != null">
        #{cdmunionid,jdbcType=VARCHAR},
      </if>
      <if test="cdmsourcetype != null">
        #{cdmsourcetype,jdbcType=VARCHAR},
      </if>
      <if test="cdmsourceval != null">
        #{cdmsourceval,jdbcType=VARCHAR},
      </if>
      <if test="ispostfitting != null">
        #{ispostfitting,jdbcType=CHAR},
      </if>
      <if test="postfittingtime != null">
        #{postfittingtime,jdbcType=DECIMAL},
      </if>
      <if test="oyearpayamount != null">
        #{oyearpayamount,jdbcType=DECIMAL},
      </if>
      <if test="dyearpayamount != null">
        #{dyearpayamount,jdbcType=DECIMAL},
      </if>
      <if test="oyearpaycount != null">
        #{oyearpaycount,jdbcType=DECIMAL},
      </if>
      <if test="dyearpaycount != null">
        #{dyearpaycount,jdbcType=DECIMAL},
      </if>
      <if test="omonthpayamount != null">
        #{omonthpayamount,jdbcType=DECIMAL},
      </if>
      <if test="dmonthpayamount != null">
        #{dmonthpayamount,jdbcType=DECIMAL},
      </if>
      <if test="omonthpaycount != null">
        #{omonthpaycount,jdbcType=DECIMAL},
      </if>
      <if test="dmonthpaycount != null">
        #{dmonthpaycount,jdbcType=DECIMAL},
      </if>
      <if test="jnbean != null">
        #{jnbean,jdbcType=DECIMAL},
      </if>
      <if test="by1 != null">
        #{by1,jdbcType=DECIMAL},
      </if>
      <if test="by2 != null">
        #{by2,jdbcType=VARCHAR},
      </if>
      <if test="by3 != null">
        #{by3,jdbcType=VARCHAR},
      </if>
      <if test="wxopenid != null">
        #{wxopenid,jdbcType=VARCHAR},
      </if>
      <if test="uuid != null">
        #{uuid,jdbcType=VARCHAR},
      </if>
      <if test="unionid != null">
        #{unionid,jdbcType=VARCHAR},
      </if>
      <if test="nickname != null">
        #{nickname,jdbcType=VARCHAR},
      </if>
      <if test="wid != null">
        #{wid,jdbcType=DECIMAL},
      </if>
      <if test="headimgurl != null">
        #{headimgurl,jdbcType=VARCHAR},
      </if>
      <if test="cardid != null">
        #{cardid,jdbcType=DECIMAL},
      </if>
      <if test="cardtypename != null">
        #{cardtypename,jdbcType=VARCHAR},
      </if>
      <if test="cardtypeid != null">
        #{cardtypeid,jdbcType=DECIMAL},
      </if>
      <if test="memberid != null">
        #{memberid,jdbcType=DECIMAL},
      </if>
      <if test="membertypeid != null">
        #{membertypeid,jdbcType=DECIMAL},
      </if>
      <if test="membertypename != null">
        #{membertypename,jdbcType=VARCHAR},
      </if>
      <if test="storeid != null">
        #{storeid,jdbcType=DECIMAL},
      </if>
      <if test="storename != null">
        #{storename,jdbcType=VARCHAR},
      </if>
      <if test="storecode != null">
        #{storecode,jdbcType=VARCHAR},
      </if>
      <if test="clerkid != null">
        #{clerkid,jdbcType=DECIMAL},
      </if>
      <if test="clerkname != null">
        #{clerkname,jdbcType=VARCHAR},
      </if>
      <if test="clerkcode != null">
        #{clerkcode,jdbcType=VARCHAR},
      </if>
      <if test="customerid != null">
        #{customerid,jdbcType=DECIMAL},
      </if>
      <if test="opentime != null">
        #{opentime,jdbcType=TIMESTAMP},
      </if>
      <if test="membercode != null">
        #{membercode,jdbcType=VARCHAR},
      </if>
      <if test="isactive != null">
        #{isactive,jdbcType=CHAR},
      </if>
      <if test="openchannel != null">
        #{openchannel,jdbcType=VARCHAR},
      </if>
      <if test="integral != null">
        #{integral,jdbcType=DECIMAL},
      </if>
      <if test="issubscribe != null">
        #{issubscribe,jdbcType=CHAR},
      </if>
      <if test="isexternaluser != null">
        #{isexternaluser,jdbcType=CHAR},
      </if>
      <if test="issubscribebox != null">
        #{issubscribebox,jdbcType=CHAR},
      </if>
      <if test="isreceivebirth != null">
        #{isreceivebirth,jdbcType=CHAR},
      </if>
      <if test="lastconsumtime != null">
        #{lastconsumtime,jdbcType=TIMESTAMP},
      </if>
      <if test="consumtimes != null">
        #{consumtimes,jdbcType=DECIMAL},
      </if>
      <if test="totconsumamt != null">
        #{totconsumamt,jdbcType=DECIMAL},
      </if>
      <if test="ext1 != null">
        #{ext1,jdbcType=VARCHAR},
      </if>
      <if test="ext2 != null">
        #{ext2,jdbcType=VARCHAR},
      </if>
      <if test="ext3 != null">
        #{ext3,jdbcType=VARCHAR},
      </if>
      <if test="ext4 != null">
        #{ext4,jdbcType=VARCHAR},
      </if>
      <if test="ext5 != null">
        #{ext5,jdbcType=VARCHAR},
      </if>
      <if test="ext6 != null">
        #{ext6,jdbcType=VARCHAR},
      </if>
      <if test="ext7 != null">
        #{ext7,jdbcType=VARCHAR},
      </if>
      <if test="ext8 != null">
        #{ext8,jdbcType=VARCHAR},
      </if>
      <if test="ext9 != null">
        #{ext9,jdbcType=VARCHAR},
      </if>
      <if test="ext10 != null">
        #{ext10,jdbcType=VARCHAR},
      </if>
      <if test="membertime != null">
        #{membertime,jdbcType=TIMESTAMP},
      </if>
      <if test="memberintegral != null">
        #{memberintegral,jdbcType=DECIMAL},
      </if>
      <if test="clerkphone != null">
        #{clerkphone,jdbcType=VARCHAR},
      </if>
      <if test="cardstatus != null">
        #{cardstatus,jdbcType=DECIMAL},
      </if>
      <if test="firstconsumtime != null">
        #{firstconsumtime,jdbcType=TIMESTAMP},
      </if>
      <if test="externaluserid != null">
        #{externaluserid,jdbcType=VARCHAR},
      </if>
      <if test="cardlvl != null">
        #{cardlvl,jdbcType=DECIMAL},
      </if>
      <if test="memberlvl != null">
        #{memberlvl,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="org.springcenter.marketing.modules.model.Cardmain">
    update CARDMAIN
    <set>
      <if test="rentid != null">
        RENTID = #{rentid,jdbcType=DECIMAL},
      </if>
      <if test="weid != null">
        WEID = #{weid,jdbcType=DECIMAL},
      </if>
      <if test="openid != null">
        OPENID = #{openid,jdbcType=VARCHAR},
      </if>
      <if test="cardtype != null">
        CARDTYPE = #{cardtype,jdbcType=DECIMAL},
      </if>
      <if test="cardno != null">
        CARDNO = #{cardno,jdbcType=VARCHAR},
      </if>
      <if test="username != null">
        USERNAME = #{username,jdbcType=VARCHAR},
      </if>
      <if test="jfye != null">
        JFYE = #{jfye,jdbcType=DECIMAL},
      </if>
      <if test="jftotal != null">
        JFTOTAL = #{jftotal,jdbcType=DECIMAL},
      </if>
      <if test="jfxf != null">
        JFXF = #{jfxf,jdbcType=DECIMAL},
      </if>
      <if test="jfsign != null">
        JFSIGN = #{jfsign,jdbcType=DECIMAL},
      </if>
      <if test="xfje != null">
        XFJE = #{xfje,jdbcType=DECIMAL},
      </if>
      <if test="tel != null">
        TEL = #{tel,jdbcType=VARCHAR},
      </if>
      <if test="birthday != null">
        BIRTHDAY = #{birthday,jdbcType=TIMESTAMP},
      </if>
      <if test="address != null">
        ADDRESS = #{address,jdbcType=VARCHAR},
      </if>
      <if test="sex != null">
        SEX = #{sex,jdbcType=CHAR},
      </if>
      <if test="email != null">
        EMAIL = #{email,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        "STATUS" = #{status,jdbcType=CHAR},
      </if>
      <if test="remark != null">
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="modifydate != null">
        MODIFYDATE = #{modifydate,jdbcType=TIMESTAMP},
      </if>
      <if test="inputdate != null">
        INPUTDATE = #{inputdate,jdbcType=TIMESTAMP},
      </if>
      <if test="validdate != null">
        VALIDDATE = #{validdate,jdbcType=TIMESTAMP},
      </if>
      <if test="islink != null">
        ISLINK = #{islink,jdbcType=CHAR},
      </if>
      <if test="linksource != null">
        LINKSOURCE = #{linksource,jdbcType=VARCHAR},
      </if>
      <if test="linkid != null">
        LINKID = #{linkid,jdbcType=VARCHAR},
      </if>
      <if test="roleid != null">
        ROLEID = #{roleid,jdbcType=DECIMAL},
      </if>
      <if test="kfid != null">
        KFID = #{kfid,jdbcType=DECIMAL},
      </if>
      <if test="kkstore != null">
        KKSTORE = #{kkstore,jdbcType=VARCHAR},
      </if>
      <if test="issend != null">
        ISSEND = #{issend,jdbcType=CHAR},
      </if>
      <if test="sendtime != null">
        SENDTIME = #{sendtime,jdbcType=DECIMAL},
      </if>
      <if test="cdmunionid != null">
        CDMUNIONID = #{cdmunionid,jdbcType=VARCHAR},
      </if>
      <if test="cdmsourcetype != null">
        CDMSOURCETYPE = #{cdmsourcetype,jdbcType=VARCHAR},
      </if>
      <if test="cdmsourceval != null">
        CDMSOURCEVAL = #{cdmsourceval,jdbcType=VARCHAR},
      </if>
      <if test="ispostfitting != null">
        ISPOSTFITTING = #{ispostfitting,jdbcType=CHAR},
      </if>
      <if test="postfittingtime != null">
        POSTFITTINGTIME = #{postfittingtime,jdbcType=DECIMAL},
      </if>
      <if test="oyearpayamount != null">
        OYEARPAYAMOUNT = #{oyearpayamount,jdbcType=DECIMAL},
      </if>
      <if test="dyearpayamount != null">
        DYEARPAYAMOUNT = #{dyearpayamount,jdbcType=DECIMAL},
      </if>
      <if test="oyearpaycount != null">
        OYEARPAYCOUNT = #{oyearpaycount,jdbcType=DECIMAL},
      </if>
      <if test="dyearpaycount != null">
        DYEARPAYCOUNT = #{dyearpaycount,jdbcType=DECIMAL},
      </if>
      <if test="omonthpayamount != null">
        OMONTHPAYAMOUNT = #{omonthpayamount,jdbcType=DECIMAL},
      </if>
      <if test="dmonthpayamount != null">
        DMONTHPAYAMOUNT = #{dmonthpayamount,jdbcType=DECIMAL},
      </if>
      <if test="omonthpaycount != null">
        OMONTHPAYCOUNT = #{omonthpaycount,jdbcType=DECIMAL},
      </if>
      <if test="dmonthpaycount != null">
        DMONTHPAYCOUNT = #{dmonthpaycount,jdbcType=DECIMAL},
      </if>
      <if test="jnbean != null">
        JNBEAN = #{jnbean,jdbcType=DECIMAL},
      </if>
      <if test="by1 != null">
        BY1 = #{by1,jdbcType=DECIMAL},
      </if>
      <if test="by2 != null">
        BY2 = #{by2,jdbcType=VARCHAR},
      </if>
      <if test="by3 != null">
        BY3 = #{by3,jdbcType=VARCHAR},
      </if>
      <if test="wxopenid != null">
        WXOPENID = #{wxopenid,jdbcType=VARCHAR},
      </if>
      <if test="uuid != null">
        UUID = #{uuid,jdbcType=VARCHAR},
      </if>
      <if test="unionid != null">
        UNIONID = #{unionid,jdbcType=VARCHAR},
      </if>
      <if test="nickname != null">
        NICKNAME = #{nickname,jdbcType=VARCHAR},
      </if>
      <if test="wid != null">
        WID = #{wid,jdbcType=DECIMAL},
      </if>
      <if test="headimgurl != null">
        HEADIMGURL = #{headimgurl,jdbcType=VARCHAR},
      </if>
      <if test="cardid != null">
        CARDID = #{cardid,jdbcType=DECIMAL},
      </if>
      <if test="cardtypename != null">
        CARDTYPENAME = #{cardtypename,jdbcType=VARCHAR},
      </if>
      <if test="cardtypeid != null">
        CARDTYPEID = #{cardtypeid,jdbcType=DECIMAL},
      </if>
      <if test="memberid != null">
        MEMBERID = #{memberid,jdbcType=DECIMAL},
      </if>
      <if test="membertypeid != null">
        MEMBERTYPEID = #{membertypeid,jdbcType=DECIMAL},
      </if>
      <if test="membertypename != null">
        MEMBERTYPENAME = #{membertypename,jdbcType=VARCHAR},
      </if>
      <if test="storeid != null">
        STOREID = #{storeid,jdbcType=DECIMAL},
      </if>
      <if test="storename != null">
        STORENAME = #{storename,jdbcType=VARCHAR},
      </if>
      <if test="storecode != null">
        STORECODE = #{storecode,jdbcType=VARCHAR},
      </if>
      <if test="clerkid != null">
        CLERKID = #{clerkid,jdbcType=DECIMAL},
      </if>
      <if test="clerkname != null">
        CLERKNAME = #{clerkname,jdbcType=VARCHAR},
      </if>
      <if test="clerkcode != null">
        CLERKCODE = #{clerkcode,jdbcType=VARCHAR},
      </if>
      <if test="customerid != null">
        CUSTOMERID = #{customerid,jdbcType=DECIMAL},
      </if>
      <if test="opentime != null">
        OPENTIME = #{opentime,jdbcType=TIMESTAMP},
      </if>
      <if test="membercode != null">
        MEMBERCODE = #{membercode,jdbcType=VARCHAR},
      </if>
      <if test="isactive != null">
        ISACTIVE = #{isactive,jdbcType=CHAR},
      </if>
      <if test="openchannel != null">
        OPENCHANNEL = #{openchannel,jdbcType=VARCHAR},
      </if>
      <if test="integral != null">
        INTEGRAL = #{integral,jdbcType=DECIMAL},
      </if>
      <if test="issubscribe != null">
        ISSUBSCRIBE = #{issubscribe,jdbcType=CHAR},
      </if>
      <if test="isexternaluser != null">
        ISEXTERNALUSER = #{isexternaluser,jdbcType=CHAR},
      </if>
      <if test="issubscribebox != null">
        ISSUBSCRIBEBOX = #{issubscribebox,jdbcType=CHAR},
      </if>
      <if test="isreceivebirth != null">
        ISRECEIVEBIRTH = #{isreceivebirth,jdbcType=CHAR},
      </if>
      <if test="lastconsumtime != null">
        LASTCONSUMTIME = #{lastconsumtime,jdbcType=TIMESTAMP},
      </if>
      <if test="consumtimes != null">
        CONSUMTIMES = #{consumtimes,jdbcType=DECIMAL},
      </if>
      <if test="totconsumamt != null">
        TOTCONSUMAMT = #{totconsumamt,jdbcType=DECIMAL},
      </if>
      <if test="ext1 != null">
        EXT1 = #{ext1,jdbcType=VARCHAR},
      </if>
      <if test="ext2 != null">
        EXT2 = #{ext2,jdbcType=VARCHAR},
      </if>
      <if test="ext3 != null">
        EXT3 = #{ext3,jdbcType=VARCHAR},
      </if>
      <if test="ext4 != null">
        EXT4 = #{ext4,jdbcType=VARCHAR},
      </if>
      <if test="ext5 != null">
        EXT5 = #{ext5,jdbcType=VARCHAR},
      </if>
      <if test="ext6 != null">
        EXT6 = #{ext6,jdbcType=VARCHAR},
      </if>
      <if test="ext7 != null">
        EXT7 = #{ext7,jdbcType=VARCHAR},
      </if>
      <if test="ext8 != null">
        EXT8 = #{ext8,jdbcType=VARCHAR},
      </if>
      <if test="ext9 != null">
        EXT9 = #{ext9,jdbcType=VARCHAR},
      </if>
      <if test="ext10 != null">
        EXT10 = #{ext10,jdbcType=VARCHAR},
      </if>
      <if test="membertime != null">
        MEMBERTIME = #{membertime,jdbcType=TIMESTAMP},
      </if>
      <if test="memberintegral != null">
        MEMBERINTEGRAL = #{memberintegral,jdbcType=DECIMAL},
      </if>
      <if test="clerkphone != null">
        CLERKPHONE = #{clerkphone,jdbcType=VARCHAR},
      </if>
      <if test="cardstatus != null">
        CARDSTATUS = #{cardstatus,jdbcType=DECIMAL},
      </if>
      <if test="firstconsumtime != null">
        FIRSTCONSUMTIME = #{firstconsumtime,jdbcType=TIMESTAMP},
      </if>
      <if test="externaluserid != null">
        EXTERNALUSERID = #{externaluserid,jdbcType=VARCHAR},
      </if>
      <if test="cardlvl != null">
        CARDLVL = #{cardlvl,jdbcType=DECIMAL},
      </if>
      <if test="memberlvl != null">
        MEMBERLVL = #{memberlvl,jdbcType=DECIMAL},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="org.springcenter.marketing.modules.model.Cardmain">
    update CARDMAIN
    set RENTID = #{rentid,jdbcType=DECIMAL},
      WEID = #{weid,jdbcType=DECIMAL},
      OPENID = #{openid,jdbcType=VARCHAR},
      CARDTYPE = #{cardtype,jdbcType=DECIMAL},
      CARDNO = #{cardno,jdbcType=VARCHAR},
      USERNAME = #{username,jdbcType=VARCHAR},
      JFYE = #{jfye,jdbcType=DECIMAL},
      JFTOTAL = #{jftotal,jdbcType=DECIMAL},
      JFXF = #{jfxf,jdbcType=DECIMAL},
      JFSIGN = #{jfsign,jdbcType=DECIMAL},
      XFJE = #{xfje,jdbcType=DECIMAL},
      TEL = #{tel,jdbcType=VARCHAR},
      BIRTHDAY = #{birthday,jdbcType=TIMESTAMP},
      ADDRESS = #{address,jdbcType=VARCHAR},
      SEX = #{sex,jdbcType=CHAR},
      EMAIL = #{email,jdbcType=VARCHAR},
      "STATUS" = #{status,jdbcType=CHAR},
      REMARK = #{remark,jdbcType=VARCHAR},
      MODIFYDATE = #{modifydate,jdbcType=TIMESTAMP},
      INPUTDATE = #{inputdate,jdbcType=TIMESTAMP},
      VALIDDATE = #{validdate,jdbcType=TIMESTAMP},
      ISLINK = #{islink,jdbcType=CHAR},
      LINKSOURCE = #{linksource,jdbcType=VARCHAR},
      LINKID = #{linkid,jdbcType=VARCHAR},
      ROLEID = #{roleid,jdbcType=DECIMAL},
      KFID = #{kfid,jdbcType=DECIMAL},
      KKSTORE = #{kkstore,jdbcType=VARCHAR},
      ISSEND = #{issend,jdbcType=CHAR},
      SENDTIME = #{sendtime,jdbcType=DECIMAL},
      CDMUNIONID = #{cdmunionid,jdbcType=VARCHAR},
      CDMSOURCETYPE = #{cdmsourcetype,jdbcType=VARCHAR},
      CDMSOURCEVAL = #{cdmsourceval,jdbcType=VARCHAR},
      ISPOSTFITTING = #{ispostfitting,jdbcType=CHAR},
      POSTFITTINGTIME = #{postfittingtime,jdbcType=DECIMAL},
      OYEARPAYAMOUNT = #{oyearpayamount,jdbcType=DECIMAL},
      DYEARPAYAMOUNT = #{dyearpayamount,jdbcType=DECIMAL},
      OYEARPAYCOUNT = #{oyearpaycount,jdbcType=DECIMAL},
      DYEARPAYCOUNT = #{dyearpaycount,jdbcType=DECIMAL},
      OMONTHPAYAMOUNT = #{omonthpayamount,jdbcType=DECIMAL},
      DMONTHPAYAMOUNT = #{dmonthpayamount,jdbcType=DECIMAL},
      OMONTHPAYCOUNT = #{omonthpaycount,jdbcType=DECIMAL},
      DMONTHPAYCOUNT = #{dmonthpaycount,jdbcType=DECIMAL},
      JNBEAN = #{jnbean,jdbcType=DECIMAL},
      BY1 = #{by1,jdbcType=DECIMAL},
      BY2 = #{by2,jdbcType=VARCHAR},
      BY3 = #{by3,jdbcType=VARCHAR},
      WXOPENID = #{wxopenid,jdbcType=VARCHAR},
      UUID = #{uuid,jdbcType=VARCHAR},
      UNIONID = #{unionid,jdbcType=VARCHAR},
      NICKNAME = #{nickname,jdbcType=VARCHAR},
      WID = #{wid,jdbcType=DECIMAL},
      HEADIMGURL = #{headimgurl,jdbcType=VARCHAR},
      CARDID = #{cardid,jdbcType=DECIMAL},
      CARDTYPENAME = #{cardtypename,jdbcType=VARCHAR},
      CARDTYPEID = #{cardtypeid,jdbcType=DECIMAL},
      MEMBERID = #{memberid,jdbcType=DECIMAL},
      MEMBERTYPEID = #{membertypeid,jdbcType=DECIMAL},
      MEMBERTYPENAME = #{membertypename,jdbcType=VARCHAR},
      STOREID = #{storeid,jdbcType=DECIMAL},
      STORENAME = #{storename,jdbcType=VARCHAR},
      STORECODE = #{storecode,jdbcType=VARCHAR},
      CLERKID = #{clerkid,jdbcType=DECIMAL},
      CLERKNAME = #{clerkname,jdbcType=VARCHAR},
      CLERKCODE = #{clerkcode,jdbcType=VARCHAR},
      CUSTOMERID = #{customerid,jdbcType=DECIMAL},
      OPENTIME = #{opentime,jdbcType=TIMESTAMP},
      MEMBERCODE = #{membercode,jdbcType=VARCHAR},
      ISACTIVE = #{isactive,jdbcType=CHAR},
      OPENCHANNEL = #{openchannel,jdbcType=VARCHAR},
      INTEGRAL = #{integral,jdbcType=DECIMAL},
      ISSUBSCRIBE = #{issubscribe,jdbcType=CHAR},
      ISEXTERNALUSER = #{isexternaluser,jdbcType=CHAR},
      ISSUBSCRIBEBOX = #{issubscribebox,jdbcType=CHAR},
      ISRECEIVEBIRTH = #{isreceivebirth,jdbcType=CHAR},
      LASTCONSUMTIME = #{lastconsumtime,jdbcType=TIMESTAMP},
      CONSUMTIMES = #{consumtimes,jdbcType=DECIMAL},
      TOTCONSUMAMT = #{totconsumamt,jdbcType=DECIMAL},
      EXT1 = #{ext1,jdbcType=VARCHAR},
      EXT2 = #{ext2,jdbcType=VARCHAR},
      EXT3 = #{ext3,jdbcType=VARCHAR},
      EXT4 = #{ext4,jdbcType=VARCHAR},
      EXT5 = #{ext5,jdbcType=VARCHAR},
      EXT6 = #{ext6,jdbcType=VARCHAR},
      EXT7 = #{ext7,jdbcType=VARCHAR},
      EXT8 = #{ext8,jdbcType=VARCHAR},
      EXT9 = #{ext9,jdbcType=VARCHAR},
      EXT10 = #{ext10,jdbcType=VARCHAR},
      MEMBERTIME = #{membertime,jdbcType=TIMESTAMP},
      MEMBERINTEGRAL = #{memberintegral,jdbcType=DECIMAL},
      CLERKPHONE = #{clerkphone,jdbcType=VARCHAR},
      CARDSTATUS = #{cardstatus,jdbcType=DECIMAL},
      FIRSTCONSUMTIME = #{firstconsumtime,jdbcType=TIMESTAMP},
      EXTERNALUSERID = #{externaluserid,jdbcType=VARCHAR},
      CARDLVL = #{cardlvl,jdbcType=DECIMAL},
      MEMBERLVL = #{memberlvl,jdbcType=DECIMAL}
    where ID = #{id,jdbcType=DECIMAL}
  </update>


  <select id="selectCardInfoByUnionIdAndWeId" resultMap="BaseResultMap">
    SELECT CARDID, WEID, UNIONID
        FROM USERWX.CARDMAIN
    WHERE UNIONID = #{unionId} AND WEID = #{weId} and status = 'Y'
  </select>

  <select id="selectBaseInfoByUnionIdsAndWeId"
          resultType="org.springcenter.marketing.modules.model.JicBaeInfo">
    SELECT
    WEID, UNIONID, NICKNAME, tel as PHONE, CARDLVL as vipLevel
    FROM userwx.cardmain
    WHERE
    WEID = #{weId}
    and UNIONID in
    <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>

  <select id="selectJicWxfansByUniondIdsAndWeId"
          resultType="org.springcenter.marketing.modules.model.JicWxFans">
    SELECT  WEID, OPENID, NICKNAME as nickname, SUBSCRIBE as subscribe, SUBSCRIBE_TIME as subscribeTime,
    UNSUBSCRIBE_TIME as unsubscribeTime, id, UNIONID as unionid
    FROM userwx.WX_FANS
    WHERE
    <if test="list != null and list.size > 0">
      unionid in
      <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    and weid = #{weId}
  </select>

  <select id="selectJicWxfansByUniondId"
          resultType="org.springcenter.marketing.modules.model.JicWxFans">
    SELECT  WEID, OPENID, NICKNAME as nickname, SUBSCRIBE as subscribe, SUBSCRIBE_TIME as subscribeTime,
    UNSUBSCRIBE_TIME as unsubscribeTime, id, UNIONID as unionid
    FROM userwx.WX_FANS
    WHERE unionid = #{unionId}
  </select>

  <select id="selectJicWxfansByUniondIdAndWeId" resultType="org.springcenter.marketing.modules.model.JicWxFans">
    SELECT  WEID, OPENID, NICKNAME as nickname, SUBSCRIBE as subscribe, SUBSCRIBE_TIME as subscribeTime,
            UNSUBSCRIBE_TIME as unsubscribeTime, id, UNIONID as unionid
    FROM userwx.WX_FANS
    WHERE unionid = #{unionId} and weid = #{weId}
  </select>

  <select id="selectCornByUnionIdAndWeId" resultType="org.springcenter.marketing.modules.model.TaskCornInfo">
    SELECT
      nvl ( bm.WEID, 0 ) AS brandId,
      t.EXTERNAL_UNION_ID AS wxUnionId
    FROM
      userwx.jic_wx_cp_external_contact_msg t
        LEFT JOIN userwx.jic_wx_cp_external e ON t.external_user_id = e.external_userid
        LEFT JOIN userwx.employee_base eb ON to_char(eb.id) = t.user_id
        LEFT JOIN usercust.jic_store_info store ON store.store_code = eb.linkstore
        LEFT JOIN userwx.brand_mapping bm ON bm.arc_id = decode( store.IS_OUTLET, 1, 42, store.brand_id )
    WHERE
      t.EVENT = 'change_external_contact'
      AND t.change_type IN ( 'add_external_contact', 'del_follow_user' )
      AND ( t.EXTERNAL_UNION_ID = #{unionId})
      AND bm.WEID =  #{weId}
    UNION ALL
    SELECT
      nvl ( bm.WEID, 0 ) AS brandId,
      e.unionid AS wxUnionId
    FROM
      userwx.jic_wx_cp_external_contact_msg t
        LEFT JOIN userwx.jic_wx_cp_external e ON t.external_user_id = e.external_userid
        LEFT JOIN userwx.employee_base eb ON to_char(eb.id) = t.user_id
        LEFT JOIN usercust.jic_store_info store ON store.store_code = eb.linkstore
        LEFT JOIN userwx.brand_mapping bm ON bm.arc_id = decode( store.IS_OUTLET, 1, 42, store.brand_id )
    WHERE
      t.EVENT = 'change_external_contact'
      AND t.change_type IN ( 'add_external_contact', 'del_follow_user' )
      AND ( e.unionid = #{unionId})
      AND bm.WEID =  #{weId}
  </select>


  <select id="selectCornByUnionIdAndNoWeId" resultType="org.springcenter.marketing.modules.model.TaskCornInfo">
    SELECT
      nvl ( bm.WEID, 0 ) AS brandId,
      t.EXTERNAL_UNION_ID AS wxUnionId
    FROM
      userwx.jic_wx_cp_external_contact_msg t
        LEFT JOIN userwx.jic_wx_cp_external e ON t.external_user_id = e.external_userid
        LEFT JOIN userwx.employee_base eb ON to_char(eb.id) = t.user_id
        LEFT JOIN usercust.jic_store_info store ON store.store_code = eb.linkstore
        LEFT JOIN userwx.brand_mapping bm ON bm.arc_id = decode( store.IS_OUTLET, 1, 42, store.brand_id )
    WHERE
      t.EVENT = 'change_external_contact'
      AND t.change_type IN ( 'add_external_contact', 'del_follow_user' )
      AND ( t.EXTERNAL_UNION_ID = #{unionId})
    UNION ALL
    SELECT
      nvl ( bm.WEID, 0 ) AS brandId,
      e.unionid AS wxUnionId
    FROM
      userwx.jic_wx_cp_external_contact_msg t
        LEFT JOIN userwx.jic_wx_cp_external e ON t.external_user_id = e.external_userid
        LEFT JOIN userwx.employee_base eb ON to_char(eb.id) = t.user_id
        LEFT JOIN usercust.jic_store_info store ON store.store_code = eb.linkstore
        LEFT JOIN userwx.brand_mapping bm ON bm.arc_id = decode( store.IS_OUTLET, 1, 42, store.brand_id )
    WHERE
      t.EVENT = 'change_external_contact'
      AND t.change_type IN ( 'add_external_contact', 'del_follow_user' )
      AND ( e.unionid = #{unionId})
  </select>

  <select id="selectByCardNo" resultMap="BaseResultMap">
    select 
    UNIONID,WEID,REMARK,NICKNAME
    from CARDMAIN
    where CARDNO = #{cardno,jdbcType=VARCHAR}
  </select>

  <select id="selectByConditionLimit1" resultMap="BaseResultMap">
    select u.*, rownum r from (
    select
      UNIONID,WEID,REMARK,NICKNAME
    from CARDMAIN
    <where>
      <if test="cardno != null and cardno != ''">
        and CARDNO = #{cardno,jdbcType=VARCHAR}
      </if>
      <if test="groupNo != null and groupNo != ''">
        and REMARK = #{groupNo,jdbcType=VARCHAR}
      </if>
      <if test="tel != null and tel != ''">
        and TEL = #{tel,jdbcType=VARCHAR}
      </if>
    </where>
    ) u where rownum = 1
  </select>
    <select id="selectByCardNos" resultMap="BaseResultMap">

      select
        NICKNAME,HEADIMGURL,CARDNO
      from CARDMAIN
      where CARDNO in
            <foreach collection="cardnos" open="(" close=")" item="item" separator=",">
              #{item}
            </foreach>

    </select>
</mapper>