package org.springcenter.marketing.modules.service;


//import com.jnby.application.admin.dto.request.GetFasionerSubStatusReq;
import com.jnby.common.Page;
import com.jnby.common.ResponseResult;
import org.springcenter.marketing.api.checkRightsDto.CheckRightCouponV3DtoForPre;
import org.springcenter.marketing.api.context.UseRightsContext;
import org.springcenter.marketing.api.dto.*;
import org.springcenter.marketing.api.dto.admin.ReSendMaterialReq;
import org.springcenter.marketing.api.dto.admin.ReceiveBirthCouponReq;
import org.springcenter.marketing.api.dto.admin.ReceiveBirthCouponResp;
import org.springcenter.marketing.api.dto.admin.UpdateBoxGiftParamsReq;
import org.springcenter.marketing.api.dto.miniapp.RightsV3ConfigResp;
import org.springcenter.marketing.api.entity.CheckRightsEntity;
import org.springcenter.marketing.modules.event.SendNotifyCouponData;
import org.springcenter.marketing.modules.model.*;

import java.util.List;

public interface RightsV3Service {


    /**
     * 购卡发放权益，
     * 1.传递参数为   卡ID，用户ID, 外部订单号
     * 2.需要把用户的以前的所有权益更改为删除
     * 3.可同步可异步
     */
    public void buyMemberCardSendRights(String bMemberCardId,
                                        String unionid,
                                        String outNo,
                                        BMemberCardType useBMemberCardType,String subId,String storeId);

    /**
     * 验证权益是否可用,或者扣除权益
     *
     * isDeductionRights
     * usedType
     */
    /**
     * 验证权益是否可用,或者扣除权益
     checkRightsEntity
     * @return
     */
    public Boolean checkRightsIsAvailableAndDeductionRights(CheckRightsEntity checkRightsEntity);

    /**
     * 根据fashionerId 查询用户是搭配师卡还是导购卡。
     * @param fashionerId
     * @return
     */
    public Integer getBUserMemberCardIdByFashionerId(String fashionerId);

    /**
     * 搭配师卡id和导购卡id
     * 当为搭配师卡id时候 送导购卡id
     * @return
     */
    public String giftCardId();


    /**
     * 累计权益 (消费满xxx次，触发点 购买  同一笔单子多件，只算一次)
     * 累计扣减权益 (退款 ，整单退  扣次数 )
     * @param boxSn boxSn
     * @param increaseOrDecutionType  1 为增加权益   0 为扣减权益
     * @param applicationParty  卡类型  0  box-导购卡   1 box-搭配师卡
     */
    public void increaseOrDeductionRightsConsumeCoupon(String boxSn,
                                                       Integer increaseOrDecutionType,
                                                       Integer applicationParty);



    /**
     * 累计消费 （扣减或者增加 )  免费续订
     * 非 权益类， 消费累计每满 5000元 ， 可以免费续订一次
     * 需要满足已经开卡
     * @param orderId       订单ID
     * @param customerDetailId   用户ID
     * @param increaseOrDecutionType  0 为增加权益   1 为扣减权益
     */
    public void cumulativeConsumeOrDecution(String orderId,
                                  String customerDetailId,
                                  Integer increaseOrDecutionType);


    /**
        扣减累计消费  获得的免费订阅次数
        before after
     * @param customerDetailId   用户ID
     * @param isCheck  是否是验证  true  验证 false 非验证  (扣减)
     */
    public Boolean deductionOrCheckConsume(String customerDetailId,
                                           Boolean isCheck);

    /**
     * 获取用户可用卡
     * @param userId
     * @param applicationParty  0 box-导购卡   1 box-搭配师卡
     * @return
     */
    public List<BNewUserMemberCard> getUserMemberCardAvailable(String userId, Integer applicationParty);



    /**
     * 根据外部单号恢复次数权益

     */
    public Boolean recoverRights(Integer applicationParty,
                              UseRightsContext context);

    /**
     * 定时失效用户卡
     */
    void expireUserMemberCard();

    /**
     * 权益周期 定时重置   1 每天  2 每周 3 每月 4 每年
     */
    void resetUserRightsJob(Integer cycleType);


    public ResponseResult openCardAndRights(OpenCardAndRightsReq requestData);


    /**
     * 刷数据
     */
    void flushUserMemberCardAndRights();


    /**
     * 根据outNo查询数据  权益数据
     * @param outNo
     * @return
     */
    List<BNewUserRights> selectUserRightsByOutNo(String outNo);

    /**
     * 失效用户卡
     * @param requestData
     * @return
     */
    ResponseResult loseEffectCardByPhone(OpenCardAndRightsReq requestData);

    Integer reflectAppidToApplicationParty(String appId);

    /**
     * 是否有未完成的盒子
     * @param requestData
     * @return
     */
    Integer isHaveUnFinishBox(HaveUnFinishBoxReq requestData);

    /**
     * 处理数据
     * @param requestData
     * @return
     */
    Object dealTryAfterBuyIsHaveUnFinshBox(DealTryAfterBuyIsHaveUnFinishBoxReq requestData);

    /**
     * 刷新缓存  刷新存入失败的缓存
     */
    void flushCache();

    List<BUserMemberCardLog> listForBUserMemberCardLog(ListForBUserMemberCardLogReq requestData, Page page);

    /**
     * 查询所有生效的卡
     * @return
     */
    List<CVipType> cardTypeAllCondition();

    List<PreCalcResp> preCalcExpansionCoupon(List<CheckRightCouponV3DtoForPre> requestData);

    List<BNewUserMemberCardResp> getUserAvailableCardApplicationParty(UserAvailableCardApplicationPartyReq requestData);

    /**
     * 获取正式卡配置，以及正式卡配置的权益
     * @return
     */
    List<SubMemberCardRightsResp> getSubMemberCardRights(SubMemberCardRightsReq subMemberCardRightsReq);

    List<BNewUserBoxGiftResp> getUserBoxGiftRights(List<UserBoxGiftRightsReq> requestData);

    List<BNewUserBoxGiftResp> getUserBoxGiftRightsByConsumeOutNo(UserRightsByConsumeOutNoReq requestData);

    List<BNewUserRightsCouponResp> getUserSubCouponRightsByConsumeOutNo(UserRightsByConsumeOutNoReq requestData);

    List<BNewUserRightsResp> getUserRightsByOutNo(GetUserRightsByOutNoReq requestData);

    List<BNewUserMemberCardResp> getUserAllCardApplicationParty(UserAvailableCardApplicationPartyReq requestData);

    void loseCardByOutNo(LoseCardByOutNoReq requestData);

    /**
     * 膨胀券定时任务补发操作  现阶段操作不行  补不了
     */
    void fixSendBoxCouponSubJob();

    /**
     * 实物礼补发操作
     */
    void fixSendBoxGiftJob();

    /**
     * 同步发货状态
     */
    void syncLogisticsStatus(String id);

    /**
     * 重推仓库制单
     * @param requestData
     */
    void reSendMaterial(ReSendMaterialReq requestData);

    List<BNewUserBoxGiftResp> getBoxGiftByBoxSns(GetBoxGiftByBoxSn requestData);

    BNewUserMemberCardResp getUserCardBySubId(GetUserCardBySubId requestData);

    void batchUseUserRights(List<BatchUseUserRightsReq> requestData);

    List<UserSubV3CouponBySubIdResp> getUserSubV3CouponBySubId(GetUserCardBySubId requestData);

    List<UserRightsBySubIdsDto> getUserRightsBySubIds(GetUserCardBySubId requestData);


    /**
     * 获取当前用户的配置信息
     * @param requestData
     * @return
     */
    RightsV3ConfigResp getRightsV3Config(RightsV3ConfigReq requestData);

    Boolean getIsSendBoxGift(SendBoxGiftFlagReq requestData);

    void resetUserCouponV3(String consumeOutNo);

    /**
     * 领取生日券， 是否可领生日券
     * @param requestData
     * @return
     */
    ReceiveBirthCouponResp receiveBirthCoupon(ReceiveBirthCouponReq requestData);

    /**
     * 异步发放优惠券
     * @param info
     */
    void sendBirthCouponNotify(SendNotifyCouponData info);

    void updateBoxGiftParams(UpdateBoxGiftParamsReq requestData);

    /**
     * filter匹配的权益 匹配门店包 和 匹配人群包
     *
     * @param rights
     * @param customerId
     * @param unionid
     * @param brandId
     * @return
     */
    public List<RightsResp> filterSuitRightsResp(List<RightsResp> rights, Integer customerId, Integer storeId, String unionid, String brandId);

    public List<Integer> targetStoreIds(String storePackageId, List<Integer> storeIds);

    List<ReceiveBirthCouponResp> checkCanReceiveBirthCouponByBrandId(ReceiveBirthCouponReq requestData);

    void addScanCodeLog(ScanCodeLog requestData);
}
