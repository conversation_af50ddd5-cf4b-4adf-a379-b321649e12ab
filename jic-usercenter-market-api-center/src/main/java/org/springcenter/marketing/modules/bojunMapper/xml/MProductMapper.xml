<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.marketing.modules.bojunMapper.MProductMapper">


    <select id="getProductIdsByGoodsFilter" resultType="java.lang.String">
        select id from m_product
        where
        <if test="sqlFilter != null and sqlFilter != '' ">
             id ${sqlFilter}
        </if>
    </select>
    <select id="sumByStoreIdAndProudctIds" resultType="java.lang.Long">
        select nvl(sum(QTYCAN),0) from V_FA_STORAGE where c_store_id = #{storeId} and ISACTIVE = 'Y'
                                     <if test="productIds != null and productIds.size() > 0">
                                         and M_PRODUCT_ID in
                                         <foreach collection="productIds" separator="," item="item" close=")" open="(">
                                             #{item}
                                         </foreach>
                                     </if>
    </select>
</mapper>