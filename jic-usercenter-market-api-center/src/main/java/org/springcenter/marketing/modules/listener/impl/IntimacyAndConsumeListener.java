package org.springcenter.marketing.modules.listener.impl;

import com.alibaba.fastjson.JSONObject;
import com.jnby.common.CommonRequest;
import com.jnby.common.ResponseResult;
import com.jnby.common.util.IdLeaf;
import org.springcenter.marketing.api.constant.IdConstant;
import org.springcenter.marketing.modules.listener.IMessageListener;
import org.springcenter.marketing.modules.listener.db.Table;
import org.springcenter.marketing.modules.mapper.RUserExpandMapper;
import org.springcenter.marketing.modules.model.RUserExpand;
import org.springcenter.marketing.modules.service.IClientVipService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.common.message.Message;
import org.springcenter.marketing.api.dto.ConsumeByVipIdResp;
import org.springcenter.marketing.api.dto.IntimacyByVipIdResp;
import org.springcenter.marketing.modules.service.badge.IBadgeMiniAppService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 2022,5.1活动满减送券
 * <AUTHOR> Yuan
 * @Date 2022/4/20 2:38 下午
 * @Version 1.0
 */
@Component
@Slf4j
@RefreshScope
public class IntimacyAndConsumeListener implements IMessageListener {

    // 为 1 则是开启  0 是关闭
    @Value("${intimacy.switch.flag}")
    private String intimacySwitchFlag;

    @Value("${mq.market.intimacy.topic}")
    private String topic;

    private String intimacyTag = "JIC_RETAIL_DETAIL";

    private String consumeTag = "FA_VIPINTEGRAL_FTP";

    private String M_RETAIL_TAG = "JIC_RETAIL_ORDER_LOG";

    @Autowired
    private RUserExpandMapper rUserExpandMapper;


    @Autowired
    private IClientVipService iClientVipService;

    @Resource
    private IBadgeMiniAppService badgeMiniAppService;

    @Override
    public String getTopic() {
        return topic;
    }

    @Override
    public String getTags() {
        return "JIC_RETAIL_DETAIL || FA_VIPINTEGRAL_FTP || JIC_RETAIL_ORDER_LOG";
    }
    @NewSpan
    @Override
    public ConsumeConcurrentlyStatus consume(Message msg) {

        log.info("consume = topic = {}, tags = {}, keys = {}", topic, msg.getTags(),msg.getKeys());

        //  0 关闭  1 开启
        if(intimacySwitchFlag.equals("0")){
            log.info("开关为关闭状态  consume = topic = {}, tags = {}, keys = {}", topic, msg.getTags(),msg.getKeys());
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        }

        if (msg.getTags().equals(M_RETAIL_TAG)) {
            return processBadge(msg);
        }

        //tag 分组
        boolean equals = msg.getTags().equals(intimacyTag);
        if(equals){

        }else if(msg.getTags().equals(consumeTag)){
            try {
                String obj = new String(msg.getBody(), "UTF-8");
                Table table = Table.fromJson(obj, Table.class);
                String cVipId = table.getColumnByName("C_VIP_ID").getValue().toString();
                ConsumeByVipIdResp consumeByVipId = iClientVipService.getConsumeByVipId(cVipId);
                if(consumeByVipId != null) {
                    log.info("consume = consumeListener = {}", JSONObject.toJSONString(consumeByVipId));
                    ConsumeByVipIdResp data = consumeByVipId;
                    String unionid = data.getUnionid();
                    String weid = data.getWeid();
                    BigDecimal consume = data.getConsume();
                    RUserExpand rUserExpand = rUserExpandMapper.selectByUnionIdAndWeId(unionid,weid);
                    if(rUserExpand != null){
                        RUserExpand update = new RUserExpand();
                        update.setId(rUserExpand.getId());
                        update.setUpdateTime(new Date());
                        if(consume.compareTo(BigDecimal.ZERO) > 0){
                            update.setConsumePointMonth(consume);
                        }else{
                            update.setConsumePointMonth(BigDecimal.ZERO);
                        }

                        rUserExpandMapper.updateByPrimaryKeySelective(update);
                    }else{
                        RUserExpand update = new RUserExpand();
                        update.setId(IdLeaf.getId(IdConstant.RUSEREXPAND));
                        update.setCClientVipId(Long.parseLong(cVipId));
                        update.setUnionid(unionid);
                        update.setWeid(weid);
                        update.setCreateTime(new Date());
                        update.setUpdateTime(new Date());
                        if(consume.compareTo(BigDecimal.ZERO) > 0){
                            update.setConsumePointMonth(consume);
                        }else{
                            update.setConsumePointMonth(BigDecimal.ZERO);
                        }
                        rUserExpandMapper.insertSelective(update);
                    }
                }
            }catch (Exception e){
                log.error("consume  = {}");
                return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
            }



            try {
                String obj = new String(msg.getBody(), "UTF-8");
                Table table = Table.fromJson(obj, Table.class);
                String cVipId = table.getColumnByName("C_VIP_ID").getValue().toString();
                // 获取当前用户当前的 less 亲密值  从userwx cardType中获取品牌
//                CommonRequest<String> commonRequest = new CommonRequest<>();
//                commonRequest.setRequestData(cardno);
                IntimacyByVipIdResp intimacyByVipId = iClientVipService.getIntimacyBycVipId(cVipId);
                if(intimacyByVipId != null){
                    log.info("consume = IntimacyListener = {}", JSONObject.toJSONString(intimacyByVipId));
                    IntimacyByVipIdResp data = intimacyByVipId;
                    String unionid = data.getUnionid();
                    String weid = data.getWeid();
                    RUserExpand rUserExpand = rUserExpandMapper.selectByUnionIdAndWeId(unionid,weid);
                    if(rUserExpand != null){
                        RUserExpand update = new RUserExpand();
                        update.setId(rUserExpand.getId());
                        if(data.getIntimacy().compareTo(BigDecimal.ZERO) > 0){
                            update.setIntimacy(data.getIntimacy());
                        }else{
                            update.setIntimacy(BigDecimal.ZERO);
                        }
                        update.setUpdateTime(new Date());
                        rUserExpandMapper.updateByPrimaryKeySelective(update);
                    }else{
                        RUserExpand insert = new RUserExpand();
                        insert.setId(IdLeaf.getId(IdConstant.RUSEREXPAND));
                        insert.setCClientVipId(Long.parseLong(data.getCVipId()));
                        insert.setUnionid(unionid);
                        insert.setWeid(weid);
                        insert.setCreateTime(new Date());
                        insert.setUpdateTime(new Date());
                        if(data.getIntimacy().compareTo(BigDecimal.ZERO) > 0){
                            insert.setIntimacy(data.getIntimacy());
                        }else{
                            insert.setIntimacy(BigDecimal.ZERO);
                        }
                        insert.setIntimacy(data.getIntimacy());
                        rUserExpandMapper.insertSelective(insert);
                    }
                }
            }catch (Exception e){
                log.error("consume  = {}");
                return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
            }


            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        }

        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

    private ConsumeConcurrentlyStatus processBadge(Message msg) {
        try {
            String obj = new String(msg.getBody(), "UTF-8");
//            log.info("零售单表 收到消息 topic = {}, tags = {}, msg = {}", topic, msg.getTags(), msg.getKeys(), obj);
            Table table = Table.fromJson(obj, Table.class);
            Long value = Long.valueOf(table.getColumnByName("RETAIL_ID").getValue().toString());
            log.info("解析后的ID:[{}]", value);
            if (value != null) {
                badgeMiniAppService.processBadgeTask(value);
            }
        } catch (Exception e) {
            log.error("零售单表 消息处理异常", e);
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

    public static void main(String[] args) {
        String tag = "tag1";
        List<String> tags = Arrays.asList(tag.split(","));
        System.out.println(tags.contains("tag1"));
    }
}
