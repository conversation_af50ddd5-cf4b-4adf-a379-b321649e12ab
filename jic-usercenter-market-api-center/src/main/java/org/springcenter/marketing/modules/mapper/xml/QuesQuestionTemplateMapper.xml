<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.marketing.modules.mapper.QuesQuestionTemplateMapper">

    <resultMap id="BaseResultMap" type="org.springcenter.marketing.modules.model.QuesQuestionTemplate">
            <id property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="quesSceneId" column="QUES_SCENE_ID" jdbcType="VARCHAR"/>
            <result property="pageTitle" column="PAGE_TITLE" jdbcType="VARCHAR"/>
            <result property="remark" column="REMARK" jdbcType="VARCHAR"/>
            <result property="sceneParams" column="SCENE_PARAMS" jdbcType="VARCHAR"/>
            <result property="type" column="TYPE" jdbcType="DECIMAL"/>
            <result property="title" column="TITLE" jdbcType="VARCHAR"/>
            <result property="subTitle" column="SUB_TITLE" jdbcType="VARCHAR"/>
            <result property="assemblyPage" column="ASSEMBLY_PAGE" jdbcType="DECIMAL"/>
            <result property="assemblyJson" column="ASSEMBLY_JSON" jdbcType="VARCHAR"/>
            <result property="autoEject" column="AUTO_EJECT" jdbcType="DECIMAL"/>
            <result property="status" column="STATUS" jdbcType="DECIMAL"/>
            <result property="jumpType" column="JUMP_TYPE" jdbcType="DECIMAL"/>
            <result property="appId" column="APP_ID" jdbcType="VARCHAR"/>
            <result property="pagePath" column="PAGE_PATH" jdbcType="VARCHAR"/>
            <result property="h5Url" column="H5_URL" jdbcType="VARCHAR"/>
            <result property="endButton" column="END_BUTTON" jdbcType="VARCHAR"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="isDel" column="IS_DEL" jdbcType="DECIMAL"/>
        <result property="createBy" column="CREATE_BY" jdbcType="VARCHAR"/>
        <result property="imgUrl" column="IMG_URL" jdbcType="VARCHAR"/>
        <result property="sceneParamsType" column="SCENE_PARAMS_TYPE" jdbcType="VARCHAR"/>
        <result property="overContent" column="over_content" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,QUES_SCENE_ID,PAGE_TITLE,
        REMARK,SCENE_PARAMS,TYPE,
        TITLE,SUB_TITLE,ASSEMBLY_PAGE,
        ASSEMBLY_JSON,AUTO_EJECT,STATUS,
        JUMP_TYPE,APP_ID,PAGE_PATH,
        H5_URL,END_BUTTON,CREATE_TIME,
        UPDATE_TIME,IS_DEL,CREATE_BY,IMG_URL,SCENE_PARAMS_TYPE,over_content
    </sql>
    <select id="selectListByParams" resultMap="BaseResultMap">
        select
        qqt.ID,qqt.QUES_SCENE_ID,qqt.PAGE_TITLE,
        qqt.REMARK,qqt.SCENE_PARAMS,qqt.TYPE,
        qqt.TITLE,qqt.SUB_TITLE,qqt.ASSEMBLY_PAGE,
        qqt.ASSEMBLY_JSON,qqt.AUTO_EJECT,qqt.STATUS,
        qqt.JUMP_TYPE,qqt.APP_ID,qqt.PAGE_PATH,
        qqt.H5_URL,qqt.END_BUTTON,qqt.CREATE_TIME,
        qqt.UPDATE_TIME,qqt.IS_DEL,qqt.CREATE_BY,qqt.IMG_URL,qqt.SCENE_PARAMS_TYPE,qqt.over_content
        from ques_question_template qqt left join ques_question_template_detail qqtd on qqt.id = qqtd.QUESTION_TEMPLATE_ID
        <where>
            and qqt.IS_DEL = 0 and qqtd.IS_DEL = 0
            <if test="id != null and id!= '' ">
                and qqt.id = #{id}
            </if>
            <if test="name != null and name != '' ">
                and qqt.PAGE_TITLE like  concat('%',concat(#{name},'%'))
            </if>
            <if test="suitParams != null and suitParams.size() > 0">
                and qqtd.SUIT_PARAM in
                <foreach collection="suitParams" item="item" separator="," close=")" open="(">
                    #{item}
                </foreach>
            </if>
            <if test="status != null ">
                and qqt.STATUS = #{status}
            </if>
            <if test="createTimeStart != null and createTimeEnd != null">
                and qqt.CREATE_TIME >= #{createTimeStart} and #{createTimeEnd} >= qqt.CREATE_TIME
            </if>
        </where>

        group by  qqt.ID,qqt.QUES_SCENE_ID,qqt.PAGE_TITLE,
        qqt.REMARK,qqt.SCENE_PARAMS,qqt.TYPE,
        qqt.TITLE,qqt.SUB_TITLE,qqt.ASSEMBLY_PAGE,
        qqt.ASSEMBLY_JSON,qqt.AUTO_EJECT,qqt.STATUS,
        qqt.JUMP_TYPE,qqt.APP_ID,qqt.PAGE_PATH,
        qqt.H5_URL,qqt.END_BUTTON,qqt.CREATE_TIME,
        qqt.UPDATE_TIME,qqt.IS_DEL,qqt.CREATE_BY,qqt.IMG_URL,qqt.SCENE_PARAMS_TYPE,qqt.over_content
        order by qqt.UPDATE_TIME desc
    </select>
    <select id="selectActiveBySceneCode" resultMap="BaseResultMap">
        select qqt.ID from ques_question_template qqt left join ques_scene qs
        on qqt.QUES_SCENE_ID = qs.id where qqt.IS_DEL = 0 and qs.IS_DEL = 0 and qqt.STATUS = 1 and qs.STATUS = 1
        and qs.SCENE_CODE = #{code}
    </select>
    <select id="selectBySceneCode" resultMap="BaseResultMap"
            parameterType="java.lang.String">
        select qqt.ID from ques_question_template qqt left join ques_scene qs on qqt.QUES_SCENE_ID = qs.id
          left join  ques_question_template_detail qqtd on qqt.id = qqtd.QUESTION_TEMPLATE_ID
          where qqt.IS_DEL = 0 and qs.IS_DEL = 0  and qs.STATUS = 1 and qqt.status = 1
        and qs.SCENE_CODE = #{code}
            <if test="weid != null and weid != ''">
                and  qqtd.SUIT_PARAM = #{weid}
            </if>
        group by qqt.ID
    </select>
    <select id="selectByTypeAndSuitParamsAndSceneNotThis"
            resultMap="BaseResultMap">
        select qqt.ID from ques_question_template qqt left join ques_question_template_detail qqtd on qqt.id = qqtd.QUESTION_TEMPLATE_ID
        where qqt.id  != #{notThisId} and qqt.TYPE = #{type} and qqt.QUES_SCENE_ID = #{quesSceneId} and qqt.status = #{status} 
        and qqtd.SUIT_PARAM in 
            <foreach collection="suitParams" separator="," item="item" close=")" open="(">
                #{item}
            </foreach>
    </select>
</mapper>
