package org.springcenter.marketing.modules.bojunMapper;


import org.springcenter.marketing.modules.model.CclientVip;
import org.apache.ibatis.annotations.Param;

import java.util.HashMap;
import java.util.List;

public interface CclientVipMapper {
    CclientVip selectByPrimaryKey(Long id);

    List<CclientVip> selectListBySelective(CclientVip cclientVip);

    List<CclientVip> selectByPrimaryKeys(@Param("ids") List<String> ids);


    List<CclientVip> selectByCardNos(@Param("cardNos") List<String> cardNos);

    List<CclientVip> selectByCardTypeIdAndIsActive();
}
