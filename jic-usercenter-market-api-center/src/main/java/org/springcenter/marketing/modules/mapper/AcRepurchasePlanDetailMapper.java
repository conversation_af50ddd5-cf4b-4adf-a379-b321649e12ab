package org.springcenter.marketing.modules.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.marketing.modules.model.AcRepurchasePlanDetail;

import java.util.Date;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【REPURCHASE_PLAN_DETAIL(复购计划子表  关联 活动order表)】的数据库操作Mapper
* @createDate 2024-11-12 09:02:42
* @Entity generator.domain.RepurchasePlanDetail
*/
public interface AcRepurchasePlanDetailMapper extends BaseMapper<AcRepurchasePlanDetail> {

    List<AcRepurchasePlanDetail> selectUserRepurchasePlan(@Param("unionid") String unionid, @Param("weid") String weid, @Param("now") Date date);



    List<String> selectShouldSendMsgAcRepurchaseDetailId(@Param("now") Date date,@Param("nowLong") Long nowLong,@Param("id") String id);

    List<AcRepurchasePlanDetail> selectShouldSendVouhcerAcRepurchaseDetailId(@Param("now") Date date,@Param("nowLong") Long nowLong);

    List<AcRepurchasePlanDetail> selectByOrderId(@Param("orderId") String orderId);
}




