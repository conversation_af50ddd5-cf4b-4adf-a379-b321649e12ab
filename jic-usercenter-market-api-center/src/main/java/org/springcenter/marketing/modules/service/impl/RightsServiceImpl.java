package org.springcenter.marketing.modules.service.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Preconditions;
import com.jnby.common.CommonRequest;
import com.jnby.common.util.IdLeaf;
import org.springcenter.marketing.api.constant.IdConstant;
import org.springcenter.marketing.api.dto.*;
import org.springcenter.marketing.api.dto.admin.ClickShowReq;
import org.springcenter.marketing.api.dto.admin.ClickShowResp;
import org.springcenter.marketing.api.dto.admin.DeleteRightsByIdReq;
import org.springcenter.marketing.api.dto.miniapp.BrandCardMsgReq;
import org.springcenter.marketing.api.dto.miniapp.BrandCardMsgResp;
import org.springcenter.marketing.api.enums.*;
import org.springcenter.marketing.common.constant.ImageConstant;
import org.springcenter.marketing.modules.bojunMapper.CVipTypeMapper;
import org.springcenter.marketing.modules.context.*;
import org.springcenter.marketing.modules.mapper.*;
import org.springcenter.marketing.modules.model.*;
import org.springcenter.marketing.modules.service.IRightsService;
import org.springcenter.marketing.modules.service.IUserVipService;
import org.springcenter.marketing.modules.service.RightsV3Service;
import org.springcenter.marketing.modules.util.RedisService;
import org.springcenter.marketing.modules.wxMapper.JicCardTypeMapper;
import org.springcenter.product.api.enums.IsDeleteEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springcenter.marketing.modules.webapi.IJicInfoHttpApi;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import retrofit2.Response;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RefreshScope
public class RightsServiceImpl implements IRightsService {

    @Autowired
    private BRightsMapper bRightsMapper;

    @Autowired
    private BMemberCardMapper bMemberCardMapper;


    @Value("${wx.brand.commonConfigs}")
    private String commonConfigs;

//    @Autowired
//    private RedisPoolUtil redisPoolUtil;

//    @Autowired
//    private RedisService redisService;

    @Autowired
    private RUserClickRightsLogMapper rUserClickRightsLogMapper;

    @Autowired
    private RUserExpandMapper rUserExpandMapper;

    @Value("${show.less.intimacy}")
    private Integer showLessIntimacy;

    @Autowired
    IUserVipService iUserVipService;


    @Autowired
    private BRightsCustomizeStoreMapper bRightsCustomizeStoreMapper;


    @Autowired
    private RedisService redisService;

    @Autowired
    private JicCardTypeMapper jicCardTypeMapper;

    @Value("${bojin.card.id}")
    private int bojinCard;
    @Value("${baijin.card.id}")
    private int baijinCard;
    @Value("${black.card.id}")
    private int blackCard;

    @Value("${guibin.card.ids}")
    private String guibinCardIds;

    @Autowired
    private IJicInfoHttpApi iJicInfoHttpApi;


    private String[] showBrands = new String[]{"2822095692","5"};


    @Autowired
    private RightsMemberCardRelationMapper rightsMemberCardRelationMapper;


    @Autowired
    private CVipTypeMapper cVipTypeMapper;


    @Autowired
    @Qualifier("marketingTransactionTemplate")
    private TransactionTemplate template;

    @Autowired
    private RightsV3Service rightsV3Service;

    @Value("${intimacy.switch.flag}")
    private String intimacySwitchFlag;

    @Value("${rights.redis.cache.flag}")
    private String rightsRedisCacheFlag;

    @Override
    public void flushCardRightsToRedis() {
        // 获取各个品牌的品牌卡和集团卡
        List<Map> brandConfigs = JSONObject.parseArray(commonConfigs, Map.class);
        Map<String,Object> map = new HashMap<>();
        map.put("weid",BrandConfigData.CARD_CODES);
        brandConfigs.add(map);
        for (Map brandConfig : brandConfigs) {
            // 去重 ， 并且对比展示点亮和不点亮
            duplicateRemoveAndShowLight(brandConfig);
        }
    }

    @Override
    public List<RightsLightDto> getRightsLight(GetRightsLightDto requestData) {
        // redis存储 是按照 品牌名称 + 品牌卡等级存储的
        //  集团卡 前端还是必须传递的    品牌卡如果没有的话  那就去查询一下
        if(requestData.getVipLevel() != null){
            String key = RedisKeysEnum.REDIS_CVIPTYPE_KEY.join(requestData.getBrandId(), requestData.getVipLevel());
            if(rightsRedisCacheFlag.equals("1")){
                Object o = redisService.get(key);
                if(o != null){
                    requestData.setCardLevel(Integer.parseInt(o.toString()));
                }
            }
            if(requestData.getCardLevel() == null){
                // 查询品牌卡信息
                Integer shareLevel = cVipTypeMapper.selectByBrandIdAndLevel(requestData.getBrandId(),requestData.getVipLevel());
                if(shareLevel != null){
                    redisService.set(key,shareLevel,3600*24);
                }
                requestData.setCardLevel(shareLevel);
            }
        }
        // 品牌卡的缓存信息
        Object companyJson = redisService.get(RedisMarketingKeysEnum.RIGHTS.join(BrandConfigData.CARD_CODES, requestData.getCompanyCardLevel()));
        Object brandJson = redisService.get(RedisMarketingKeysEnum.RIGHTS.join(requestData.getBrandId(), requestData.getCardLevel()));
//        String companyJson = RedisTemplateUtil.get(redisPoolUtil, RedisMarketingKeysEnum.RIGHTS.join(BrandConfigData.CARD_CODES, requestData.getCompanyCardLevel()));
//        String brandJson = RedisTemplateUtil.get(redisPoolUtil, RedisMarketingKeysEnum.RIGHTS.join(requestData.getBrandId(), requestData.getCardLevel()));
        if(companyJson == null && brandJson == null){
            log.error("未查询到卡信息 getRightsLight = {}",JSONObject.toJSONString(requestData));
            return new ArrayList<>();
        }
        if(companyJson == null){
            companyJson ="";
        }
        if(brandJson == null){
            brandJson ="";
        }
        if(StringUtils.isBlank(companyJson.toString()) && StringUtils.isBlank(brandJson.toString())){
            log.error("未查询到卡信息 getRightsLight = {}",JSONObject.toJSONString(requestData));
            return new ArrayList<>();
        }
        List<RightsLightDto> companyRights = new ArrayList<>();
        if(StringUtils.isNotBlank(companyJson.toString())){
            companyRights = JSONObject.parseArray(companyJson.toString(), RightsLightDto.class);
        }
        List<RightsLightDto> brandRights = new ArrayList<>();
        if(StringUtils.isNotBlank(brandJson.toString())){
            brandRights = JSONObject.parseArray(brandJson.toString(), RightsLightDto.class);
        }
        List<RightsLightDto> list = new ArrayList<>();

        if(requestData.getIsHaveBrandCard() == 0){
            for (RightsLightDto rightsLightDto : brandRights) {
                rightsLightDto.setLight(0);
                rightsLightDto.setIsShowNew(0);
            }
        }
        if(requestData.getIsHaveGroupCard() == 0){
            for (RightsLightDto rightsLightDto : companyRights) {
                rightsLightDto.setLight(0);
                rightsLightDto.setIsShowNew(0);
            }
        }
        list.addAll(brandRights);
        list.addAll(companyRights);

//        list = threeFront;
        //需要筛选 直营和经销  并且 有门店包
        list = suitTypeFilter(list,requestData);
        if(CollectionUtils.isEmpty(list)){
            return list;
        }

        List<RightsLightDto> brightsList = new ArrayList<>();
        // 分组处理数据  一个内部名称 + 一个类型 分组
        Map<String, List<RightsLightDto>> collect1 = list.stream().collect(Collectors.groupingBy(r -> r.getName() + r.getRightsType()));
        for (String s : collect1.keySet()) {
            if(collect1.get(s).size() > 1){
                collect1.get(s).stream().sorted(new Comparator<RightsLightDto>() {
                    @Override
                    public int compare(RightsLightDto o1, RightsLightDto o2) {
                        return o1.getSorted() - o2.getSorted();
                    }
                });
                brightsList.add(collect1.get(s).get(0));
            }else{
                brightsList.add(collect1.get(s).get(0));
            }
        }

        List<RightsLightDto> threeFront = new ArrayList<>();
        // 积分权益  生日礼遇 洗护特权 强制排在最前面
//        List<RightsLightDto> threeFrontOne = brightsList.stream().filter(r -> r.getInnerName().contains("积分权益")).collect(Collectors.toList());
//        threeFront.addAll(threeFrontOne);
//        List<RightsLightDto> threeFrontTwo = brightsList.stream().filter(r -> r.getRightsType().equals(RightsTypeEnum.BIRTH_COUPON.getCode().longValue())).collect(Collectors.toList());
//        threeFront.addAll(threeFrontTwo);
//        List<RightsLightDto> threeFrontThree = brightsList.stream().filter(r -> r.getInnerName().equals("十年维修")).collect(Collectors.toList());
//        threeFront.addAll(threeFrontThree);
//
//        // 去除这个三个  2024-07-26  生日优惠券仅取一个  多个不管   因直接口头说的  做个标记
//        List<RightsLightDto> leftRights = brightsList.stream().filter(r -> !r.getInnerName().equals("十年维修")
//                && !r.getRightsType().equals(RightsTypeEnum.BIRTH_COUPON.getCode().longValue())
//                && !r.getInnerName().contains("积分权益")).collect(Collectors.toList());

//        if(shouldFiterBirth != null){
//            leftRights = leftRights.stream().filter(r->!r.getId().equals(shouldFiterBirth.getId())).collect(Collectors.toList());
//        }

        // 排序 首先点亮 ，然后卡级别
//        leftRights.sort(new Comparator<RightsLightDto>() {
//            @Override
//            public int compare(RightsLightDto o1, RightsLightDto o2) {
//                if(o2.getLight() - o1.getLight() == 0){
//                    if(o1.getLevelId() - o2.getLevelId() == 0){
//                        if(o1.getSorted() - o2.getSorted() ==0){
//                            return (int)((o1.getCreateTime()/1000) - (o2.getCreateTime()/1000));
//                        }
//                        return o1.getSorted() - o2.getSorted();
//                    }else{
//                        return  o1.getLevelId() - o2.getLevelId();
//                    }
//                }else{
//                    return o2.getLight() - o1.getLight();
//                }
//            }
//        });

        brightsList.sort(new Comparator<RightsLightDto>() {
            @Override
            public int compare(RightsLightDto o1, RightsLightDto o2) {
                // 仅按照排序号进行排序
                if(o1.getSorted() - o2.getSorted() == 0){
                    return (int)((o2.getUpdateTime()/1000) - (o1.getUpdateTime()/1000));
                }else{
                    return o1.getSorted() - o2.getSorted();
                }
            }
        });

        threeFront.addAll(brightsList);
        list = threeFront;

        // 如果还有多个生日礼的话  那么仅保留一个
//        List<RightsLightDto> collect = list.stream().filter(r -> r.getRightsType().equals(RightsTypeEnum.BIRTH_COUPON.getCode().longValue())).collect(Collectors.toList());
//        if(CollectionUtils.isNotEmpty(collect) && collect.size() > 1){
//            list = list.stream().filter(r -> !r.getRightsType().equals(RightsTypeEnum.BIRTH_COUPON.getCode().longValue())).collect(Collectors.toList());
//            if(CollectionUtils.isNotEmpty(list) && list.size() >= 2){
//                list.add(1,collect.get(0));
//            }
//        }

        // 如果品牌卡没有   品牌卡均是未点亮
        if(requestData.getIsHaveBrandCard() == 1 || requestData.getIsHaveGroupCard() == 1) {
            // 全部 不展示showNew 1 是之前的代码  0
            if(intimacySwitchFlag.equals("0")){
                for (RightsLightDto rightsLightDto : list) {
                    rightsLightDto.setIsShowNew(0);
                }
            }else{
                List<String> bRightsIds = list.stream().map(r -> r.getId()).collect(Collectors.toList());
                List<RUserClickRightsLog> rUserClickRightsLogs = rUserClickRightsLogMapper.selectByBRightsIdsAndUnionid(bRightsIds,requestData.getUnionid());
                if(CollectionUtils.isNotEmpty(rUserClickRightsLogs)){
                    HashMap<String, String> bRightsIdAndId = rUserClickRightsLogs.stream().collect(HashMap::new, (k, v) -> k.put(v.getBRightsId(), v.getId()), HashMap::putAll);
//                Map<String, String> bRightsIdAndId = rUserClickRightsLogs.stream().collect(Collectors.toMap(r -> r.getBRightsId(), r -> r.getId()));
                    for (RightsLightDto rightsLightDto : list) {
                        String rUserClickRightsLogId = bRightsIdAndId.get(rightsLightDto.getId());
                        if(StringUtils.isNotBlank(rUserClickRightsLogId)){
                            rightsLightDto.setIsShowNew(0);
                        }
                    }
                }
            }
        }

        MemberCardQueryContext req = new MemberCardQueryContext();
        req.setUnionId(requestData.getUnionid());
        req.setBrandId(requestData.getBrandId());
        CustomerBaseResponse<MemberCardEntity> memberCard = iUserVipService.getMemberCard(req);
        if(memberCard != null && memberCard.getData() != null && StringUtils.isNotBlank(memberCard.getData().getGroupCardNo())){
            if("N".equals(memberCard.getData().getStatus())){
                // 失效  权益全锁
                list.stream().forEach(e->{
                    e.setLight(0);
                });
            }
        }

        return list;
    }

    private List<RightsLightDto> suitTypeFilter(List<RightsLightDto> list,GetRightsLightDto req) {
        List<RightsLightDto> result = new ArrayList<>();
        //筛选过滤
        String storeId = req.getStoreId();
        Integer suitType = req.getSuitType();
//        if(StringUtils.isBlank(storeId) || suitType == null){
//            // 如果用户区分不出来  则  默认取直营门店 和  直营经销均可用   2024-07-31
//            List<RightsLightDto> collect = list.stream().filter(r -> r.getSuitableStoreType().equals(SuitableStoreTypeEnum.DIRECT.getCode())
//                    || r.getSuitableStoreType().equals(SuitableStoreTypeEnum.ALL.getCode())).collect(Collectors.toList());
//            return collect;
//        }
//        if(suitType == -1){
//            return list;
//        }

        // 转化为 rightsResp  筛选门店包
        List<RightsResp> shouldFilter = list.stream().map(e -> {
            RightsResp rightsResp = new RightsResp();
            rightsResp.setRightsId(e.getId());
            rightsResp.setSuitableStoreType(e.getSuitableStoreType());
            rightsResp.setSuitCusType(e.getSuitCusType());
            rightsResp.setPeopleCrowdIds(e.getPeopleCrowdIds());
            return rightsResp;
        }).collect(Collectors.toList());
        List<RightsResp> rightsResps = rightsV3Service.filterSuitRightsResp(shouldFilter, suitType,
                StringUtils.isBlank(storeId) ? null :Integer.parseInt(storeId), req.getUnionid(), req.getBrandId());

        // 获取最终数据
        for (RightsLightDto rightsLightDto : list) {
            for (RightsResp rightsResp : rightsResps) {
                boolean equals = rightsLightDto.getId().equals(rightsResp.getRightsId());
                if(equals){
                    result.add(rightsLightDto);
                    break;
                }
            }
        }

        return result;
    }

    private void setResult(List<RightsLightDto> result, List<RightsLightDto> collect,String storeId) {
        for (RightsLightDto rightsLightDto : collect) {
            if(rightsLightDto.getSuitableStoreType().equals(SuitableStoreTypeEnum.CUSTOMIZE.getCode())){
                // 自定义 则查询
                List<String> list = bRightsCustomizeStoreMapper.selectByRightIdAndStoreId(rightsLightDto.getId(),storeId);
                if(CollectionUtils.isNotEmpty(list)){
                    result.add(rightsLightDto);
                }
            }else{
                result.add(rightsLightDto);
            }
        }
    }

    @Override
    public void clickUserRights(ClickUserRightsDto requestData) {
        // 查询是否已经点击过   点击过则不创建
        RUserClickRightsLog params  =  new RUserClickRightsLog();
        params.setBRightsId(requestData.getBRightsId());
        params.setUnionid(requestData.getUnionid());
        params.setIsDel(IsDeleteEnum.NORMAL.getCode());
        List<RUserClickRightsLog> list = rUserClickRightsLogMapper.selectBySelective(params);
        if(CollectionUtils.isNotEmpty(list)){
            return ;
        }

        RUserClickRightsLog insertData  =  new RUserClickRightsLog();
        insertData.setBRightsId(requestData.getBRightsId());
        insertData.setUnionid(requestData.getUnionid());
        insertData.setIsDel(IsDeleteEnum.NORMAL.getCode());
        insertData.setCreateTime(new Date());
        insertData.setUpdateTime(new Date());
        insertData.setId(IdLeaf.getId(IdConstant.RUCRL));
        rUserClickRightsLogMapper.insertSelective(insertData);
    }

    @Override
    public GetUserBaseInfoResp getUserBaseInfo(GetUserBaseInfoReq requestData) {
        log.info("getUserBaseInfo打印时间 总时间 开始unionid  = {} ,weid = {}",requestData.getUnionid(),requestData.getWeid());
        String key = RedisKeysEnum.USER_BASE_INFO.join(requestData.getUnionid(), requestData.getWeid());
        if(rightsRedisCacheFlag.equals("1")){
            Object result = redisService.get(key);
            if(result != null){
                return JSONObject.parseObject(result.toString(), GetUserBaseInfoResp.class);
            }
        }

        GetUserBaseInfoResp getUserBaseInfoResp = new GetUserBaseInfoResp();
        // 2023-07-13 改   原来是从卡上取的nickName 现在改为 获取用户的接口 userName
        MemberBaseInfoQueryContext memberBaseInfoQueryContext = new MemberBaseInfoQueryContext();
//        memberBaseInfoQueryContext.setUnionId(requestData.getUnionid());
        memberBaseInfoQueryContext.setWid(requestData.getWid());
//        memberBaseInfoQueryContext.setBrandId(requestData.getWeid());
        MemberBaseInfoEntity memberBaseInfoEntity =  Preconditions.checkNotNull(iUserVipService.getUserBaseInfo(memberBaseInfoQueryContext),"用户信息不能为空");
        if(memberBaseInfoEntity != null && memberBaseInfoEntity.getGroupId() != null){
            getUserBaseInfoResp.setNickName(memberBaseInfoEntity.getNickName());

            MemberCardQueryContext req = new MemberCardQueryContext();
            req.setUnionId(requestData.getUnionid());
            // 2024-01-25   这个时候 去掉了 查询status为 Y的 条件， 具体谁去掉的忘了。
//        req.setStatus("Y");
            req.setBrandId(requestData.getWeid());
//        // 2025-01-08   -  修复 准备增加 wid  进行查询  因为合卡的问题，导致有一个品牌两张卡，并且没有过滤状态，  导致查询出来 status为N的
//        // 增加wid查询   pi
//        CustomerBaseResponse<MemberCardEntity> memberCard = iUserVipService.getMemberCardWid(req);
//        if(memberCard != null && memberCard.getData() != null && StringUtils.isNotBlank(memberCard.getData().getGroupCardNo())){
            getUserBaseInfoResp.setIsHaveMemberCard(true);
            getUserBaseInfoResp.setMemberCode(memberBaseInfoEntity.getRemark());
            getUserBaseInfoResp.setMemberLvl(memberBaseInfoEntity.getGroupLevelId() == null? null : memberBaseInfoEntity.getGroupLevelId()+"");
            // 获取集团卡积分接口
            CustomerBaseResponse<MemberIntegralDto> memberIntegralDtoCustomerBaseResponse = iUserVipService.getMemberIntegral(req);
            if(memberIntegralDtoCustomerBaseResponse != null && memberIntegralDtoCustomerBaseResponse.getData() != null){
                getUserBaseInfoResp.setMemberIntegral(memberIntegralDtoCustomerBaseResponse.getData().getTotalIntegral() < 0 ? 0: memberIntegralDtoCustomerBaseResponse.getData().getTotalIntegral());
            }
            getUserBaseInfoResp.setMemberTypeName(memberBaseInfoEntity.getGroupVipTypeName());
            getUserBaseInfoResp.setMemberTypeId(memberBaseInfoEntity.getGroupVipTypeId()+"");
            getUserBaseInfoResp.setMemberDiscount(memberBaseInfoEntity.getGroupVipTypeDiscount());
            getUserBaseInfoResp.setMemberId(memberBaseInfoEntity.getGroupId());
            getUserBaseInfoResp.setStatus("Y");


            // 2024-08-10    增加 isZhux N 和 status 为 N 才会返回N
            if("N".equals(memberBaseInfoEntity.getIsZhux()) && "N".equals(memberBaseInfoEntity.getStatus())){
                getUserBaseInfoResp.setStatus("N");
            }

            getUserBaseInfoResp.setBrandCardLel(memberBaseInfoEntity.getLevelId());
            getUserBaseInfoResp.setBrandCardNo(memberBaseInfoEntity.getCardNo());
            getUserBaseInfoResp.setIsHaveBrandCard(true);
            getUserBaseInfoResp.setBrandTypeName(memberBaseInfoEntity.getVipTypeName());

            getUserBaseInfoResp.setBrandTypeId(memberBaseInfoEntity.getVipTypeId() == null? null:memberBaseInfoEntity.getVipTypeId()+"");
            getUserBaseInfoResp.setBrandId(memberBaseInfoEntity.getBrandId());
            getUserBaseInfoResp.setBrandDiscount(memberBaseInfoEntity.getVipTypeDiscount());
            getUserBaseInfoResp.setBirthday(memberBaseInfoEntity.getBirthDay());
            getUserBaseInfoResp.setId(memberBaseInfoEntity.getId().longValue());
            getUserBaseInfoResp.setSuitType(memberBaseInfoEntity.getCustomerId() == null ? null:memberBaseInfoEntity.getCustomerId());
            getUserBaseInfoResp.setStoreId(memberBaseInfoEntity.getStoreId() == null ? "" : memberBaseInfoEntity.getStoreId()+"");
            getUserBaseInfoResp.setBrandCreateDate(memberBaseInfoEntity.getCreationDate());
            getUserBaseInfoResp.setCardStatusExp(memberBaseInfoEntity.getCardStatusExp());
            getUserBaseInfoResp.setIsZhux(memberBaseInfoEntity.getIsZhux());

            String birthday = memberBaseInfoEntity.getBirthDay();
            if(StringUtils.isNotBlank(birthday)){
                // 获取当前年月
                Date now = new Date();
                String mMdd = DateUtil.format(now, "MM");
                try {
                    boolean equals = birthday.substring(4, 6).equals(mMdd);
                    if (equals) {
                        getUserBaseInfoResp.setIsBirthMonth(true);
                    }
                }catch (Exception e){
                    // log.error("getUserBaseInfo = {}",birthday,e);
                }
            }

        }
        MemberQueryContext memberQueryContext = new MemberQueryContext();
        memberQueryContext.setUnionId(requestData.getUnionid());
//        memberQueryContext.setStatus("Y");
        memberQueryContext.setBrandId(requestData.getWeid());
        CustomerBaseResponse<List<MemberCardEntity>> memberCardList = iUserVipService.getMemberCardList(memberQueryContext);

        if(memberCardList != null && CollectionUtils.isNotEmpty(memberCardList.getData())){
            getUserBaseInfoResp.setVipShareTypeDiscount(memberCardList.getData().get(0).getVipShareTypeDiscount());
            getUserBaseInfoResp.setVipShareTypeName(memberCardList.getData().get(0).getVipShareTypeName());
            getUserBaseInfoResp.setVipShareTypeId(memberCardList.getData().get(0).getVipShareTypeId());
            getUserBaseInfoResp.setVipShareTypeLevelId(memberCardList.getData().get(0).getVipShareTypeLevelId());
//            getUserBaseInfoResp.setIsZhux(memberCardList.getData().get(0).getIsZhux());
            // 无手机号当做无卡
        }


        //获取累计消费
//        if(getUserBaseInfoResp.getId() != null){
        if(intimacySwitchFlag.equals("1")){
            List<String> weids =  new ArrayList<>();
            weids.add(requestData.getWeid());
            weids.add("2822095692");
            List<RUserExpand> rUserExpands  = rUserExpandMapper.selectByUnionIdAndWeIds(requestData.getUnionid(), weids);
            if(CollectionUtils.isNotEmpty(rUserExpands)){
                List<RUserExpand> rUserExpandxs = rUserExpands.stream().filter(r -> r.getWeid().equals(requestData.getWeid())).collect(Collectors.toList());
                List<RUserExpand> rUserExpandSs = rUserExpands.stream().filter(r -> r.getWeid().equals("2822095692")).collect(Collectors.toList());

                if(CollectionUtils.isNotEmpty(rUserExpandxs)){
                    RUserExpand rUserExpandx = rUserExpandxs.get(0);
                    double memberConsume = rUserExpandx.getConsumePointMonth() == null ? 0 : rUserExpandx.getConsumePointMonth().doubleValue();
                    if(memberConsume < 0){
                        memberConsume = 0.0;
                    }
                    getUserBaseInfoResp.setMemberConsume(memberConsume);
                }else{
                    getUserBaseInfoResp.setMemberConsume(0.0);
                }

                if(showLessIntimacy == 1){
                    if(CollectionUtils.isNotEmpty(rUserExpandSs)){
                        RUserExpand rUserExpand = rUserExpandSs.get(0);
                        List<String> strings = Arrays.asList(showBrands);
                        // 包含则展示
                        if(strings.contains(requestData.getWeid())){
                            // 获取亲密值
                            getUserBaseInfoResp.setIsShowLessIntimacy(1);
                            if(rUserExpand != null){
                                //查询亲密值
                                getUserBaseInfoResp.setLessIntimacyValue(rUserExpand.getIntimacy() == null ? "0":rUserExpand.getIntimacy().toString());
                            }else{
                                getUserBaseInfoResp.setLessIntimacyValue("0");
                            }
                        }
                    }
                }
            }
        }

//        }

        //获取消费规则  计算规则
        if(StringUtils.isNotBlank(getUserBaseInfoResp.getBrandTypeId())){
            String join1 = RedisKeysEnum.REDIS_RIGHTS_CVIPTYPE_KEY.join(getUserBaseInfoResp.getBrandTypeId());
            CVipType rViptype = null;
            if(rightsRedisCacheFlag.equals("1")){
                Object o = redisService.get(join1);
                if(o != null){
                    rViptype = JSONObject.parseObject(o.toString(),CVipType.class);
                }
            }
            if(rViptype == null){
                 rViptype = cVipTypeMapper.selectByPrimaryKey(Long.parseLong(getUserBaseInfoResp.getBrandTypeId()));
                if(rViptype != null){
                    // 24小时缓存
                    redisService.set(join1,JSONObject.toJSONString(rViptype),3600 * 24 );
                }
            }

            Long intlOnceUp = rViptype.getIntlOnceUp();
            if(intlOnceUp == null || intlOnceUp > 90000000L){
                intlOnceUp = 99999999L;
            }
            getUserBaseInfoResp.setOneConsumeRule(intlOnceUp);
            // 累计
            Long intlMon1Up = rViptype.getIntlMon1Up();
            if(intlMon1Up == null || intlMon1Up > 90000000L){
                intlMon1Up = 99999999L;
            }
            getUserBaseInfoResp.setConsumeUp(intlMon1Up);
            // 银升金
            getUserBaseInfoResp.setConsumeSilverToGoldRule(intlMon1Up.doubleValue());
            // 金升白金
            getUserBaseInfoResp.setConsumeGoldToPlatinumRule(intlMon1Up.doubleValue());
        }

        CommonRequest<GetRightsLightDto> getRightsLightDtoCommonRequest = new CommonRequest<>();
        GetRightsLightDto getRightsLightDto = new GetRightsLightDto();
        getRightsLightDto.setBrandId(requestData.getWeid());
        getRightsLightDto.setUnionid(requestData.getUnionid());
        getRightsLightDto.setIsHaveBrandCard(1);
        getRightsLightDto.setIsHaveGroupCard(1);
        if(StringUtils.isBlank(getUserBaseInfoResp.getBrandTypeId()) || StringUtils.isBlank(getUserBaseInfoResp.getMemberTypeId())){
            // 纯新用户
            // jic_card_type查询
            if(StringUtils.isBlank(getUserBaseInfoResp.getBrandTypeId())){
                String join1 = RedisKeysEnum.REDIS_RIGHTS_JICCARD_KEY.join(requestData.getWeid(), getUserBaseInfoResp.getBrandCardLel());
                JicCardTypeR jicCardType = null;
                if(rightsRedisCacheFlag.equals("1")){
                    Object o = redisService.get(join1);
                    if(o != null){
                        jicCardType = JSONObject.parseObject(o.toString(),JicCardTypeR.class);
                    }
                }
                if(jicCardType == null){
                    jicCardType = jicCardTypeMapper.getByWeidAndLevel(requestData.getWeid(),getUserBaseInfoResp.getBrandCardLel());
                    if(jicCardType != null){
                        // 24小时缓存
                        redisService.set(join1,JSONObject.toJSONString(jicCardType),3600 * 24 );
                    }
                }
                getUserBaseInfoResp.setBrandTypeId(jicCardType.getId()+"");
                getUserBaseInfoResp.setVipShareTypeId(jicCardType.getId());
            }

            if(StringUtils.isBlank(getUserBaseInfoResp.getMemberTypeId())){
                String join1 = RedisKeysEnum.REDIS_RIGHTS_JICCARDMEMBER_KEY.join(getUserBaseInfoResp.getMemberLvl());
                JicCardTypeR jicCardTypeMember = null;
                if(rightsRedisCacheFlag.equals("1")){
                    Object o = redisService.get(join1);
                    if(o != null){
                        jicCardTypeMember = JSONObject.parseObject(o.toString(),JicCardTypeR.class);
                    }
                }
                if(jicCardTypeMember == null){
                    jicCardTypeMember = jicCardTypeMapper.getByNullAndLevel(getUserBaseInfoResp.getMemberLvl());
                    if(jicCardTypeMember != null){
                        // 24小时缓存
                        redisService.set(join1,JSONObject.toJSONString(jicCardTypeMember),3600 * 24 );
                    }
                }
                getUserBaseInfoResp.setMemberTypeId(jicCardTypeMember.getId()+"");
            }

//            if(StringUtils.isNotBlank(getUserBaseInfoResp.getBrandTypeId())){
//                String join1 = RedisKeysEnum.REDIS_RIGHTS_CVIPTYPE_KEY.join(getUserBaseInfoResp.getBrandTypeId());
//                CVipType rViptype = null;
//                if(rightsRedisCacheFlag.equals("1")){
//                    Object o = redisService.get(join1);
//                    if(o != null){
//                        rViptype = JSONObject.parseObject(o.toString(),CVipType.class);
//                    }
//                }
//                if(rViptype == null){
//                    rViptype = cVipTypeMapper.selectByPrimaryKey(Long.parseLong(getUserBaseInfoResp.getBrandTypeId()));
//                    if(rViptype != null){
//                        // 24小时缓存
//                        redisService.set(join1,JSONObject.toJSONString(rViptype),3600 * 24 );
//                    }
//                }
//
//                Long intlOnceUp = rViptype.getIntlOnceUp();
//                if(intlOnceUp == null || intlOnceUp > 90000000L){
//                    intlOnceUp = 99999999L;
//                }
//                getUserBaseInfoResp.setOneConsumeRule(intlOnceUp);
//                // 累计
//                Long intlMon1Up = rViptype.getIntlMon1Up();
//                if(intlMon1Up == null || intlMon1Up > 90000000L){
//                    intlMon1Up = 99999999L;
//                }
//                getUserBaseInfoResp.setConsumeUp(intlMon1Up);
//                // 银升金
//                getUserBaseInfoResp.setConsumeSilverToGoldRule(intlMon1Up.doubleValue());
//                // 金升白金
//                getUserBaseInfoResp.setConsumeGoldToPlatinumRule(intlMon1Up.doubleValue());
//            }
//            redisService.setAndLogTimes(key, JSONObject.toJSONString(getUserBaseInfoResp),3600 );
            return getUserBaseInfoResp;
        }
        getRightsLightDto.setCardLevel(Integer.parseInt(getUserBaseInfoResp.getBrandTypeId()));
        getRightsLightDto.setCompanyCardLevel(Integer.parseInt(getUserBaseInfoResp.getMemberTypeId()));
        getRightsLightDto.setStoreId(getUserBaseInfoResp.getStoreId());
        getRightsLightDto.setSuitType(getUserBaseInfoResp.getSuitType());

        getRightsLightDtoCommonRequest.setRequestData(getRightsLightDto);
        List<RightsLightDto> rightsLight = getRightsLight(getRightsLightDto);
        if(CollectionUtils.isNotEmpty(rightsLight)){
            List<RightsLightDto> collect = rightsLight.stream().filter(r -> r.getLight() == 1).collect(Collectors.toList());
            getUserBaseInfoResp.setHaveRightsNum(collect.size());
        }
        redisService.setAndLogTimes(key, JSONObject.toJSONString(getUserBaseInfoResp),3600 * 24 );
        log.info("getUserBaseInfo打印时间 总时间 总计时间 结束");
        return getUserBaseInfoResp;
    }

    @Override
    public void saveUserNameAndHeadImg(UserNameAndHeadImgReq requestData) {
        log.info("saveUserNameAndHeadImg = {}",JSONObject.toJSONString(requestData));
        String key = RedisKeysEnum.IMG_HEAD_NICK_NAME.join(requestData.getUnionId());
        redisService.set(key,JSONObject.toJSONString(requestData));
    }

    @Override
    public UserNameAndHeadImgReq getUserNameAndHeadImg(UserNameAndHeadImgReq requestData) {
        String key = RedisKeysEnum.IMG_HEAD_NICK_NAME.join(requestData.getUnionId());
        Object o = redisService.get(key);
        if(o != null){
            return JSONObject.parseObject(o.toString(), UserNameAndHeadImgReq.class);
        }else{
            return null;
        }
    }

    private void duplicateRemoveAndShowLight(Map brandConfig) {
        // 集团和品牌的维度上  权益不会重复
        BMemberCard brandParams = new BMemberCard();
        brandParams.setIsDel(IsDeleteEnum.NORMAL.getCode());
        brandParams.setStatus(MemberCardStatusEnum.USE.getCode());
        brandParams.setApplicableParty(ApplicablePartyEnum.COMPANY.getCode());
        brandParams.setBrandId(brandConfig.get("weid").toString());
        //获取到 各个品牌卡  已经排序过了  银卡 金卡 白金卡
        List<BMemberCard> brandBMemberCard = bMemberCardMapper.selectListBySelective(brandParams);
        if(CollectionUtils.isNotEmpty(brandBMemberCard)){
            Map<String, Integer> idMapLevelId = brandBMemberCard.stream().collect(Collectors.toMap(r -> r.getId(), r -> r.getLevelId()));
            Map<String, Integer> idMapCardLevel = brandBMemberCard.stream().collect(Collectors.toMap(r -> r.getId(), r -> r.getCardLevel()));
            List<String> memberCardIds = brandBMemberCard.stream().map(r -> r.getId()).collect(Collectors.toList());
            //权益信息
            List<BRights> brandRights = bRightsMapper.selectByMemberCardIds(memberCardIds);
            for (BRights brandRight : brandRights) {
                //设置卡级别
                brandRight.setLevelId(idMapLevelId.get(brandRight.getbMemberCardId()));
                brandRight.setCardLevel(idMapCardLevel.get(brandRight.getbMemberCardId()));
            }

            Map<String, List<BRights>> groupByMemberCardId = brandRights.stream().collect(Collectors.groupingBy(r -> r.getbMemberCardId()));
            // 顺序了  银卡  金卡  白金卡
            for (int i =0; i < brandBMemberCard.size();i++) {
                // 银卡 金卡  白金卡
                List<BRights> bRights = groupByMemberCardId.get(brandBMemberCard.get(i).getId());
                List<RightsLightDto> list  = new ArrayList<>(brandRights.size());
                if(CollectionUtils.isEmpty(bRights)){
                    continue;
                }
                mergeDataToRedis(list,bRights,brandRights,brandBMemberCard.get(i));
            }
        }
    }

    private void mergeDataToRedis(List<RightsLightDto> result, List<BRights> bRights, List<BRights> brandRights,BMemberCard bMemberCard) {
        List<String> guibinCardIdsList = Arrays.asList(guibinCardIds.split(","));

        List<BRights> notLights = new ArrayList<>();
        List<BRights> shoulLights = new ArrayList<>();
        // 原始的数据
        List<String> oldOutNameList = bRights.stream().map(r -> r.getOutName()).collect(Collectors.toList());
        // 去除了当前的权益的所有其他权益信息
        List<BRights> notThisRightsAll = brandRights.stream().filter(r -> !r.getbMemberCardId().equals(bMemberCard.getId())).collect(Collectors.toList());
        // 其他的权益不包含  贵宾卡权益
        notThisRightsAll = notThisRightsAll.stream().filter(r -> !guibinCardIdsList.contains(r.getCardLevel()+"")).collect(Collectors.toList());

        // 当前卡是  黑卡  白金卡  铂金卡
        if(bMemberCard.getLevelId() == 3){
            if(guibinCardIdsList.contains(bMemberCard.getCardLevel()+"")){
                // 贵宾卡不处理
            }else{
                if(bMemberCard.getBrandId().equals("5")){
                    // 江南布衣+
                }else if(bMemberCard.getBrandId().equals(BrandConfigData.CARD_CODES)){
                    if(bMemberCard.getCardLevel() == bojinCard){
                        // 集团铂金  去除 白金卡权益  集团黑卡
                        notThisRightsAll = notThisRightsAll.stream().filter(r -> !r.getCardLevel().equals(baijinCard)).collect(Collectors.toList());
                        notThisRightsAll = notThisRightsAll.stream().filter(r -> !r.getCardLevel().equals(blackCard)).collect(Collectors.toList());
                    }else{
                        notThisRightsAll = notThisRightsAll.stream().filter(r -> !r.getLevelId().equals(3)).collect(Collectors.toList());
                    }
                }else{
                    notThisRightsAll = notThisRightsAll.stream().filter(r -> !r.getLevelId().equals(3)).collect(Collectors.toList());
                }
            }
        }else{
            // 当前卡是 银卡  金卡  去除白金卡权益
            if(bMemberCard.getBrandId().equals("5")){
                // 江南布衣+
            } else if(bMemberCard.getBrandId().equals(BrandConfigData.CARD_CODES)){
                notThisRightsAll = notThisRightsAll.stream().filter(r -> !r.getCardLevel().equals(baijinCard)).collect(Collectors.toList());
                notThisRightsAll = notThisRightsAll.stream().filter(r -> !r.getCardLevel().equals(blackCard)).collect(Collectors.toList());
            }else{
                notThisRightsAll = notThisRightsAll.stream().filter(r -> !r.getLevelId().equals(3)).collect(Collectors.toList());
            }
        }

        //如果是江南布衣+  正常展示
//        if(bMemberCard.getBrandId().equals("5")){
//            notThisRightsAll = notThisRightsAll.stream().filter(r -> !r.getLevelId().equals(3)).collect(Collectors.toList());
//        }else{
//            // 非江南布衣+  并且品牌是不是白金卡，则可以展示
//            if(bMemberCard.getLevelId() != 3){
//                notThisRightsAll = notThisRightsAll.stream().filter(r -> !r.getLevelId().equals(3)).collect(Collectors.toList());
//            }
//        }
        //根据外部名称分组
        Map<String, List<BRights>> collect = notThisRightsAll.stream().collect(Collectors.groupingBy(r -> r.getOutName()));

        if(bMemberCard.getLevelId() == 3){
            if(guibinCardIdsList.contains(bMemberCard.getCardLevel()+"")){
                // 贵宾卡不处理
            }else{
                // 江南布衣+
                if(bMemberCard.getBrandId().equals("5")){
                    for (String outName : collect.keySet()) {
                        if(!oldOutNameList.contains(outName)){
                            // 没有的数据
                            List<BRights> notHave = collect.get(outName);
                            // 排序
                            if(CollectionUtils.isNotEmpty(notHave)){
                                List<BRights> sortedData = notHave.stream().sorted(new Comparator<BRights>() {
                                    @Override
                                    public int compare(BRights o1, BRights o2) {
                                        return o1.getLevelId() - o2.getLevelId();
                                    }
                                }).collect(Collectors.toList());

                                for (BRights bRights1 : sortedData) {
                                    if(bMemberCard.getLevelId() >  bRights1.getLevelId()){
                                        // 高级别覆盖低级别 需要点亮
                                        shoulLights.add(bRights1);
                                    }else{
                                        // 未点亮的
                                        notLights.add(bRights1);
                                    }
                                }
                            }
                        }
                    }
                }else if(bMemberCard.getBrandId().equals(BrandConfigData.CARD_CODES)){
                    // 铂金卡
                    if(bMemberCard.getCardLevel() == bojinCard){
                        for (String outName : collect.keySet()) {
                            if(!oldOutNameList.contains(outName)){
                                // 没有的数据
                                List<BRights> notHave = collect.get(outName);
                                // 排序
                                if(CollectionUtils.isNotEmpty(notHave)){
                                    List<BRights> sortedData = notHave.stream().sorted(new Comparator<BRights>() {
                                        @Override
                                        public int compare(BRights o1, BRights o2) {
                                            return o1.getLevelId() - o2.getLevelId();
                                        }
                                    }).collect(Collectors.toList());

                                    for (BRights bRights1 : sortedData) {
                                        if(bMemberCard.getLevelId() >  bRights1.getLevelId()){
                                            // 高级别覆盖低级别 需要点亮
                                            shoulLights.add(bRights1);
                                        }else{
                                            // 未点亮的
                                            notLights.add(bRights1);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }else{
            for (String outName : collect.keySet()) {
                if(!oldOutNameList.contains(outName)){
                    // 没有的数据
                    List<BRights> notHave = collect.get(outName);
                    // 排序
                    if(CollectionUtils.isNotEmpty(notHave)){
                        List<BRights> sortedData = notHave.stream().sorted(new Comparator<BRights>() {
                            @Override
                            public int compare(BRights o1, BRights o2) {
                                return o1.getLevelId() - o2.getLevelId();
                            }
                        }).collect(Collectors.toList());

                        for (BRights bRights1 : sortedData) {
                            if(bMemberCard.getLevelId() >  bRights1.getLevelId()){
                                // 高级别覆盖低级别 需要点亮
                                shoulLights.add(bRights1);
                            }else{
                                // 未点亮的
                                notLights.add(bRights1);
                            }
                        }
                    }
                }
            }
        }

        List<BRights> sortedData = bRights.stream().sorted(new Comparator<BRights>() {
            @Override
            public int compare(BRights o1, BRights o2) {
                return o1.getSorted().intValue() - o2.getSorted().intValue();
            }
        }).collect(Collectors.toList());
        addInResult(result,sortedData,1);
        //高等级覆盖低等级
        addInResult(result,shoulLights,1);
        List<BRights> sortedData2 = notLights.stream().sorted(new Comparator<BRights>() {
            // 先排序 级别
            @Override
            public int compare(BRights o1, BRights o2) {
                if(o1.getLevelId() - o2.getLevelId() == 0){
                    return o1.getSorted().intValue() - o2.getSorted().intValue();
                }else{
                    return o1.getLevelId() - o2.getLevelId();
                }
            }
        }).collect(Collectors.toList());
        addInResult(result,sortedData2,0);
        // 排序 次要排序 先点亮不点亮 然后是排序
        redisService.set(RedisMarketingKeysEnum.RIGHTS.join(bMemberCard.getBrandId(),bMemberCard.getCardLevel()),JSONObject.toJSONString(result));
//        RedisTemplateUtil.setex(redisPoolUtil, RedisMarketingKeysEnum.RIGHTS.join(bMemberCard.getBrandId(),bMemberCard.getCardLevel()),JSONObject.toJSONString(result),7*24*60*60);
        // 存储  按照  品牌id + 卡级别  查询的时候需要查询出来 集团卡  在查询出来品牌卡
        log.info("卡级别 = {}, 品牌 = {} , mergeDataToRedis = {}  ",bRights.get(0).getLevelId(),bMemberCard.getBrandId(),JSONObject.toJSONString(result));

    }

    private void addInResult(List<RightsLightDto> result, List<BRights> sortedData, int light) {
        for (BRights bRight : sortedData) {
            RightsLightDto rightsLightDto = new RightsLightDto();
            rightsLightDto.setId(bRight.getId());
            rightsLightDto.setLight(light);
            rightsLightDto.setName(bRight.getOutName());
            rightsLightDto.setIcon(StringUtils.isBlank(bRight.getIcon()) ? null : bRight.getIcon() + ImageConstant.PRESS_IMG_SUFFIX);
            rightsLightDto.setDetailImg(StringUtils.isBlank(bRight.getDetailImgs()) ? null : bRight.getDetailImgs() + ImageConstant.PRESS_IMG_SUFFIX);
            rightsLightDto.setTag(bRight.getLevelId().toString());
            rightsLightDto.setSorted(bRight.getSorted().intValue());
            rightsLightDto.setActionUrl(bRight.getActionUrl());
            rightsLightDto.setSuitableStoreType(bRight.getSuitableStoreType());
            rightsLightDto.setCreateTime(bRight.getCreateTime().getTime());
            rightsLightDto.setUpdateTime(bRight.getUpdateTime().getTime());
            rightsLightDto.setLevelId(bRight.getLevelId());
            rightsLightDto.setGifImg(StringUtils.isBlank(bRight.getBirthGif()) ? null : bRight.getBirthGif() + ImageConstant.PRESS_IMG_SUFFIX);
            rightsLightDto.setActionName(bRight.getActionName());
            rightsLightDto.setActionType(bRight.getActionType());
            rightsLightDto.setRightsType(bRight.getRightsType());
            rightsLightDto.setInnerName(bRight.getName());
            rightsLightDto.setContent(bRight.getContent());
            rightsLightDto.setExtendJson(bRight.getExtendJson());
            rightsLightDto.setLabel(bRight.getLabel());
            rightsLightDto.setUseRule(bRight.getUseRule());
            rightsLightDto.setSuitCusType(bRight.getSuitCusType());
            rightsLightDto.setPeopleCrowdIds(bRight.getPeopleCrowdIds());
            result.add(rightsLightDto);
        }
    }


    /**
     * 是否显示
     * @param brandId
     * @return
     */
    @Override
    public Boolean isShowIntimacy(String brandId){
        if(showLessIntimacy == 0){
            return false;
        }

        List<String> strings = Arrays.asList(showBrands);
        return strings.contains(brandId);
    }

    @Override
    public Boolean getWeimoMemberIsOpenCard(GetWeimoMemberIsOpenCardReq requestData) {
        log.info("getWeimoMemberIsOpenCardReq = {}",JSONObject.toJSONString(requestData));
        try {
            Response<CustomerBaseResponse<GetWeimoMemberInfo>> execute = iJicInfoHttpApi.findWeimoMemberInfo(requestData).execute();
            log.info("getWeimoMemberIsOpenCardResp = {}",JSONObject.toJSONString(execute));
            log.info("getWeimoMemberIsOpenCardResp = {}",JSONObject.toJSONString(execute.body()));
            CustomerBaseResponse<GetWeimoMemberInfo> body = execute.body();
            GetWeimoMemberInfo data = body.getData();
            if(data == null){
                return false;
            }else{
                List<GetWeimoCardInfoResp> memberCardInfoList = data.getMemberCardInfoList();
                if(CollectionUtils.isNotEmpty(memberCardInfoList)){
                    return true;
                }else{
                    return false;
                }
            }
        }catch (Exception e){
            log.error("获取微盟会员是否开卡异常 =  {}",JSONObject.toJSONString(requestData),e);
        }
        return false;
    }

    @Override
    public GetWeimoCardInfoResp getWeimoMemberCardNo(GetWeimoMemberIsOpenCardReq requestData) {
        log.info("getWeimoMemberCardNoReq = {}",JSONObject.toJSONString(requestData));
        try {
            Response<CustomerBaseResponse<GetWeimoMemberInfo>> execute = iJicInfoHttpApi.findWeimoMemberInfo(requestData).execute();
            log.info("getWeimoMemberCardNoResp = {}",JSONObject.toJSONString(execute));
            log.info("getWeimoMemberCardNoResp = {}",JSONObject.toJSONString(execute.body()));
            CustomerBaseResponse<GetWeimoMemberInfo> body = execute.body();
            GetWeimoMemberInfo data = body.getData();
            if(data == null){
                return null;
            }else{
                List<GetWeimoCardInfoResp> memberCardInfoList = data.getMemberCardInfoList();
                if(CollectionUtils.isNotEmpty(memberCardInfoList)){
                    return memberCardInfoList.get(0);
                }else{
                    return null;
                }
            }
        }catch (Exception e){
            log.error("获取微盟会员卡号异常 =  {}",JSONObject.toJSONString(requestData),e);
        }
        return null;
    }

    @Override
    public List<BrandCardMsgResp> brandCardMsg(BrandCardMsgReq requestData) {
        List<BrandCardMsgResp> resultList = new ArrayList<>();
        // 遍历数据
        List<Map> brandConfigs = JSONObject.parseArray(commonConfigs, Map.class);
        for (Map brandConfig : brandConfigs) {
            String weid = brandConfig.get("weid").toString();
            BrandCardMsgResp brandCardMsgResp = new BrandCardMsgResp();
            brandCardMsgResp.setWeid(weid);
            brandCardMsgResp.setLight(false);
            brandCardMsgResp.setIsZhux("Y");
            brandCardMsgResp.setStatus("N");
            resultList.add(brandCardMsgResp);
        }

        MemberQueryContext memberQueryContext = new MemberQueryContext();
        memberQueryContext.setUnionId(requestData.getUnionid());
        CustomerBaseResponse<List<MemberCardEntity>> memberCardList = iUserVipService.getMemberCardList(memberQueryContext);
        if(memberCardList != null && CollectionUtils.isNotEmpty(memberCardList.getData())){
            for (MemberCardEntity memberCardEntity : memberCardList.getData()) {
                String brandId = memberCardEntity.getBrandId();

                inner:for (BrandCardMsgResp brandCardMsgResp : resultList) {
                    if(brandCardMsgResp.getWeid().equals(brandId) && "N".equals(memberCardEntity.getIsZhux()) && StringUtils.isNotBlank(memberCardEntity.getWid()) ){
                        brandCardMsgResp.setLight(true);
                        brandCardMsgResp.setStatus(memberCardEntity.getStatus());
                        brandCardMsgResp.setIsZhux(memberCardEntity.getIsZhux());
                        break inner;
                    }
                }
            }
        }
        if(StringUtils.isNotBlank(requestData.getWeid())){
            List<BrandCardMsgResp> collect = resultList.stream().filter(r -> r.getWeid().equals(requestData.getWeid())).collect(Collectors.toList());
            List<BrandCardMsgResp> collect2 = resultList.stream().filter(r -> !r.getWeid().equals(requestData.getWeid())).collect(Collectors.toList());

            // 排序  点亮在前  未点亮 在后   第一位的不变
            List<BrandCardMsgResp> collect3 = collect2.stream().filter(r -> r.getLight()).collect(Collectors.toList());
            List<BrandCardMsgResp> collect4 = collect2.stream().filter(r -> !r.getLight()).collect(Collectors.toList());
            collect3.addAll(collect4);
            collect.addAll(collect3);
            resultList = collect;

            if(!requestData.getWeid().equals("11")){
                // 移除 11 weid
                resultList = resultList.stream().filter(r -> !r.getWeid().equals("11")).collect(Collectors.toList());
            }

        }else{
            // 排序  点亮在前  未点亮 在后   第一位的不变
            List<BrandCardMsgResp> collect3 = resultList.stream().filter(r -> r.getLight()).collect(Collectors.toList());
            List<BrandCardMsgResp> collect4 = resultList.stream().filter(r -> !r.getLight()).collect(Collectors.toList());
            collect3.addAll(collect4);
            resultList = collect3;
        }

        return resultList;
    }

    @Override
    public List<RightsMemberCardRelation> findRelationByRightsIds(List<String> rightsIds) {
        List<RightsMemberCardRelation> list = rightsMemberCardRelationMapper.selectByRightsIds(rightsIds);
        return list;
    }

    @Override
    public List<ClickShowResp> clickShow(ClickShowReq requestData) {
        List<ClickShowResp> result = new ArrayList<>();
        List<String> list = new ArrayList<>();
        List<String> params = new ArrayList<>();
        if(StringUtils.isNotBlank(requestData.getRightsId())){
            params.add(requestData.getRightsId());
            List<RightsMemberCardRelation> rightsMemberCardRelations = rightsMemberCardRelationMapper.selectByRightsIds(params);
            if(CollectionUtils.isNotEmpty(rightsMemberCardRelations)){
                List<BMemberCard> memberCardList = bMemberCardMapper.selectByPrimaryKeys(rightsMemberCardRelations.stream().map(r->r.getMemberCardId()).collect(Collectors.toList()));
                memberCardList.stream().sorted(new Comparator<BMemberCard>() {
                    @Override
                    public int compare(BMemberCard o1, BMemberCard o2) {
                        return o2.getStatus() - o1.getStatus();
                    }
                }).forEach(e->{
                    ClickShowResp clickShowResp = new ClickShowResp();
                    clickShowResp.setName(e.getName());
                    clickShowResp.setStatus(e.getStatus());
                    result.add(clickShowResp);
                });
            }
        }else if(StringUtils.isNotBlank(requestData.getMemberCardId())){
            params.add(requestData.getMemberCardId());
            List<RightsMemberCardRelation> rightsMemberCardRelations = rightsMemberCardRelationMapper.selectByMemberCardIds(params);
            if(CollectionUtils.isNotEmpty(rightsMemberCardRelations)){
                List<BRights> rights = bRightsMapper.selectByPrimaryKeys(rightsMemberCardRelations.stream().map(r->r.getRightsId()).collect(Collectors.toList()));
                rights.stream().sorted(new Comparator<BRights>() {
                    @Override
                    public int compare(BRights o1, BRights o2) {
                        return o2.getStatus().intValue() - o1.getStatus().intValue();
                    }
                }).forEach(e->{
                    ClickShowResp clickShowResp = new ClickShowResp();
                    clickShowResp.setName(e.getName());
                    clickShowResp.setStatus(e.getStatus().intValue());
                    result.add(clickShowResp);
                });
            }
        }
        return result;
    }

    @Override
    public void deleteById(DeleteRightsByIdReq requestData) {
        BRights bRights = new BRights();
        bRights.setIsDel(IsDeleteEnum.IS_DELETED.getCode().longValue());
        bRights.setId(requestData.getRightsId());
        rightsMemberCardRelationMapper.updateByRightsId(requestData.getRightsId(), IsDeleteEnum.IS_DELETED.getCode());
        bRightsMapper.updateByPrimaryKeySelective(bRights);
    }
}
