<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.marketing.modules.mapper.BUserMemberCardRenewLogMapper">
  <resultMap id="BaseResultMap" type="org.springcenter.marketing.modules.model.BUserMemberCardRenewLog">
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="USER_ID" jdbcType="VARCHAR" property="userId" />
    <result column="ORDER_ID" jdbcType="VARCHAR" property="orderId" />
    <result column="B_MEMBER_CARD_ID" jdbcType="VARCHAR" property="bMemberCardId" />
    <result column="PAYMENT_TYPE" jdbcType="DECIMAL" property="paymentType" />
    <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate" />
    <result column="RENEW_DAY" jdbcType="DECIMAL" property="renewDay" />
    <result column="PAYMENT_PRICE" jdbcType="DECIMAL" property="paymentPrice" />
    <result column="DISCOUNT_COIN" jdbcType="DECIMAL" property="discountCoin" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, USER_ID, ORDER_ID, B_MEMBER_CARD_ID, PAYMENT_TYPE, CREATE_DATE, RENEW_DAY, PAYMENT_PRICE, 
    DISCOUNT_COIN
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from b_user_member_card_renew_log 
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <select id="selectByOrderId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from b_user_member_card_renew_log
    where ORDER_ID = #{orderId}
  </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from b_user_member_card_renew_log
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="org.springcenter.marketing.modules.model.BUserMemberCardRenewLog">
    insert into b_user_member_card_renew_log (ID, USER_ID, ORDER_ID,
      B_MEMBER_CARD_ID, PAYMENT_TYPE, CREATE_DATE, 
      RENEW_DAY, PAYMENT_PRICE, DISCOUNT_COIN
      )
    values (#{id,jdbcType=VARCHAR}, #{userId,jdbcType=VARCHAR}, #{orderId,jdbcType=VARCHAR}, 
      #{bMemberCardId,jdbcType=VARCHAR}, #{paymentType,jdbcType=DECIMAL}, #{createDate,jdbcType=TIMESTAMP}, 
      #{renewDay,jdbcType=DECIMAL}, #{paymentPrice,jdbcType=DECIMAL}, #{discountCoin,jdbcType=DECIMAL}
      )
  </insert>
  <insert id="insertSelective" parameterType="org.springcenter.marketing.modules.model.BUserMemberCardRenewLog">
    insert into b_user_member_card_renew_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="userId != null">
        USER_ID,
      </if>
      <if test="orderId != null">
        ORDER_ID,
      </if>
      <if test="bMemberCardId != null">
        B_MEMBER_CARD_ID,
      </if>
      <if test="paymentType != null">
        PAYMENT_TYPE,
      </if>
      <if test="createDate != null">
        CREATE_DATE,
      </if>
      <if test="renewDay != null">
        RENEW_DAY,
      </if>
      <if test="paymentPrice != null">
        PAYMENT_PRICE,
      </if>
      <if test="discountCoin != null">
        DISCOUNT_COIN,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="bMemberCardId != null">
        #{bMemberCardId,jdbcType=VARCHAR},
      </if>
      <if test="paymentType != null">
        #{paymentType,jdbcType=DECIMAL},
      </if>
      <if test="createDate != null">
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="renewDay != null">
        #{renewDay,jdbcType=DECIMAL},
      </if>
      <if test="paymentPrice != null">
        #{paymentPrice,jdbcType=DECIMAL},
      </if>
      <if test="discountCoin != null">
        #{discountCoin,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="org.springcenter.marketing.modules.model.BUserMemberCardRenewLog">
    update b_user_member_card_renew_log
    <set>
      <if test="userId != null">
        USER_ID = #{userId,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        ORDER_ID = #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="bMemberCardId != null">
        B_MEMBER_CARD_ID = #{bMemberCardId,jdbcType=VARCHAR},
      </if>
      <if test="paymentType != null">
        PAYMENT_TYPE = #{paymentType,jdbcType=DECIMAL},
      </if>
      <if test="createDate != null">
        CREATE_DATE = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="renewDay != null">
        RENEW_DAY = #{renewDay,jdbcType=DECIMAL},
      </if>
      <if test="paymentPrice != null">
        PAYMENT_PRICE = #{paymentPrice,jdbcType=DECIMAL},
      </if>
      <if test="discountCoin != null">
        DISCOUNT_COIN = #{discountCoin,jdbcType=DECIMAL},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="org.springcenter.marketing.modules.model.BUserMemberCardRenewLog">
    update b_user_member_card_renew_log
    set USER_ID = #{userId,jdbcType=VARCHAR},
      ORDER_ID = #{orderId,jdbcType=VARCHAR},
      B_MEMBER_CARD_ID = #{bMemberCardId,jdbcType=VARCHAR},
      PAYMENT_TYPE = #{paymentType,jdbcType=DECIMAL},
      CREATE_DATE = #{createDate,jdbcType=TIMESTAMP},
      RENEW_DAY = #{renewDay,jdbcType=DECIMAL},
      PAYMENT_PRICE = #{paymentPrice,jdbcType=DECIMAL},
      DISCOUNT_COIN = #{discountCoin,jdbcType=DECIMAL}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
</mapper>