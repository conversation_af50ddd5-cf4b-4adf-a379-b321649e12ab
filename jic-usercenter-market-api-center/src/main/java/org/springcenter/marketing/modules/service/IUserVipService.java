package org.springcenter.marketing.modules.service;


import org.springcenter.marketing.api.dto.CustomerBaseResponse;
import org.springcenter.marketing.api.dto.MemberIntegralDto;
import org.springcenter.marketing.modules.context.*;
import retrofit2.Call;
import retrofit2.http.Body;

import java.util.List;

public interface IUserVipService {

    /**
     * 会员品牌卡列表
     * @param memberQueryContext
     * @return
     */
    CustomerBaseResponse<List<MemberCardEntity>> getMemberCardList(MemberQueryContext memberQueryContext);

    /**
     * 获取用户的昵称和头像
     * @param context
     * @return
     */
    CustomerBaseResponse<List<MemberNameAndHeadImgEntity>> listMemberCardNameAndHeadImg(MemberListQueryContext context);
    /**
     * 会员品牌卡信息
     * @param memberCardQueryContext
     * @return
     */
    CustomerBaseResponse<MemberCardEntity> getMemberCard(MemberCardQueryContext memberCardQueryContext);


    /**
     * 会员品牌卡信息 2
     * @param memberCardQueryContext
     * @return
     */
    CustomerBaseResponse<MemberCardEntity> getMemberCardWid(MemberCardQueryContext memberCardQueryContext);

    /**
     * hu
     * @param memberBaseInfoQueryContext
     * @return
     */
    MemberBaseInfoEntity getUserBaseInfo(MemberBaseInfoQueryContext memberBaseInfoQueryContext);

    /**
     * 获取数据 集团积分
     * @param req
     * @return
     */
    CustomerBaseResponse<MemberIntegralDto> getMemberIntegral(MemberCardQueryContext req);


    /**
     * 获取当前用户是否填写过童孩生日
     * @param req 卡号信息
     * @return 返回
     */
    CustomerBaseResponse<List<KidBirthListEntity>> getKidBirthList(KidBirthListQueryContext req);


    /**
     * 更新用户基本信息
     * @param req 入参
     * @return 返回
     */
    CustomerBaseResponse<Boolean> updateCustomer(MemberUpdateInfoEntity req);
}
