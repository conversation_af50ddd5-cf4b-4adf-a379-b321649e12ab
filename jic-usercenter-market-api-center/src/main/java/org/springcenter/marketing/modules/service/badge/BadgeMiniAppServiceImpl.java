package org.springcenter.marketing.modules.service.badge;

import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Preconditions;
import com.jnby.common.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springcenter.marketing.api.constant.RedisKeyConstant;
import org.springcenter.marketing.api.context.SendWxMsgNewContext;
import org.springcenter.marketing.api.context.SendWxMsgNewContext.WxMpTemplateDataDto;
import org.springcenter.marketing.api.dto.WxMsgBrandConfigDto;
import org.springcenter.marketing.api.dto.miniapp.*;
import org.springcenter.marketing.api.dto.miniapp.BadgeDetailWithGainsResp.GainRecord;
import org.springcenter.marketing.api.dto.miniapp.BadgeUnshownResp.BadgeGainRecord;
import org.springcenter.marketing.modules.bojunMapper.MRetailMapper;
import org.springcenter.marketing.modules.convert.BadgeConvert;
import org.springcenter.marketing.modules.domain.badge.entity.Badge;
import org.springcenter.marketing.modules.domain.badge.entity.BadgeGain;
import org.springcenter.marketing.modules.domain.badge.repository.BadgeGainRepository;
import org.springcenter.marketing.modules.domain.badge.repository.BadgeRepository;
import org.springcenter.marketing.modules.domain.badge.service.BadgeService;
import org.springcenter.marketing.modules.domain.retail.entity.RetailOrder;
import org.springcenter.marketing.modules.domain.retail.repository.RetailOrderRepository;
import org.springcenter.marketing.modules.domain.retail.service.RetailOrderService;
import org.springcenter.marketing.modules.model.BadgeGainRecordLog;
import org.springcenter.marketing.modules.model.BadgeShowRecord;
import org.springcenter.marketing.modules.model.JicWxFans;
import org.springcenter.marketing.modules.service.MessageService;
import org.springcenter.marketing.modules.util.DateUtils;
import org.springcenter.marketing.modules.util.RedissonUtil;
import org.springcenter.marketing.modules.wxMapper.CardmainMapper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 主题勋章-小程序
 */
@Slf4j
@Service
@RefreshScope
public class BadgeMiniAppServiceImpl implements IBadgeMiniAppService {
    /**
     * 勋章消息开关。0=关，1=开
     */
    @Value("${badge.send.msg.switch}")
    private Integer badgeSendMsgSwitch;
    /**
     * 勋章消息白名单，命中则发送消息
     */
    @Value("${badge.send.msg.white.user}")
    private String badgeSendMsgWhiteUser;
    /**
     * 品牌公众号相关
     */
    @Value("${wx.brand.commonConfigs}")
    private String brandNacosConfigs;

    @Resource
    private BadgeService badgeService;

    @Resource
    private RetailOrderService retailOrderService;

    @Resource
    private RedissonUtil lockUtil;

    @Resource
    private BadgeGainRepository badgeGainRepository;

    @Resource
    private IBadgeShowRecordService badgeShowRecordService;

    @Resource
    private BadgeRepository badgeRepository;

    @Resource
    private RetailOrderRepository retailOrderRepository;

    @Resource
    private MRetailMapper mRetailMapper;

    @Resource
    private MessageService messageService;

    @Resource
    private CardmainMapper cardmainMapper;

    @Override
    public List<BadgeDetailEffectResp> listEffectBadgeDetails(BadgeDetailEffectReq req) {
        List<Badge> badgeList = badgeService.listEffectBadge(req, null);
        if (CollectionUtils.isEmpty(badgeList)) {
            return Collections.emptyList();
        }
        // 根据勋章参数来过滤
        List<BadgeDetailEffectResp> respList = BadgeConvert.INSTANCE.badgeDomain2EffectRespList(badgeList);
        //排序respList按照sort数字越大越靠前,如果sort相同，则按照创建时间倒序排序
        respList.sort((b1, b2) -> {
            if (b1.getSort() != null && b2.getSort() != null && !b1.getSort().equals(b2.getSort())) {
                return b2.getSort().compareTo(b1.getSort());
            }
            return b2.getCreateTime().compareTo(b1.getCreateTime());
        });
        return respList;
    }

    /**
     * 处理零售单勋章
     *
     * @param retailId 零售单ID
     * @return 处理结果
     */
    @Override
    public boolean processBadgeTask(Long retailId) {
        String lockKey = RedisKeyConstant.BADGE_PROCESS_RETAIL_ID_LOCK.join(retailId);
        boolean locked = lockUtil.tryLock(lockKey);
        if (!locked) {
            log.error("零售单勋章处理中，请稍后重试 retailId={}", retailId);
            return false;
        }

        try {
            log.info("勋章任务 开始处理 retailId={}", retailId);
            List<Badge> effectBadge = badgeService.getEffectBadge();
            if (CollectionUtils.isEmpty(effectBadge)) {
                log.info("当前没有有效勋章，无需处理");
                return true;
            }
            // 获取零售单及其子单
            RetailOrder retailOrder = retailOrderService.getRetailOrderWithItems(retailId);
            log.info("零售单详情: {}", JSON.toJSONString(retailOrder));
            // 计算勋章获得
            List<BadgeGain> badgeGainList = calcBadgeGainWithLock(retailOrder);
            log.info("获得的勋章: {}", JSON.toJSONString(badgeGainList));
            // 发送勋章通知
            sendBadgeNotification(badgeGainList);
            log.info("勋章任务 处理完成 retailId={}", retailId);
            return true;
        } catch (Exception e) {
            log.error("勋章任务 处理异常 retailId={}", retailId, e);
            throw new RuntimeException("零售单处理勋章异常");
        } finally {
            // 释放锁
            lockUtil.unlock(lockKey);
        }
    }

    private List<BadgeGain> calcBadgeGainWithLock(RetailOrder retailOrder) {
        String docNo = retailOrder.getOrgDocNo();
        Assert.notBlank(docNo, "零售单号不能为空");
        String lockKey = RedisKeyConstant.BADGE_PROCESS_RETAIL_DOCNO_LOCK.join(docNo);
        boolean locked = lockUtil.tryLock(lockKey);
        if (!locked) {
            log.warn("正在处理中，请稍后重试，docNo={}", docNo);
            return Lists.newArrayList();
        }
        // 遍历子单进行勋章处理
        try {
            return badgeService.calcBadgeGain(retailOrder);
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            // 释放锁
            lockUtil.unlock(lockKey);
        }
    }

    /**
     * 获取用户勋章统计信息
     *
     * @param unionId 用户unionId
     * @return 统计信息
     */
    @Override
    public BadgeSummaryResp getBadgeSummary(String unionId) {
        log.info("获取用户勋章统计 unionId={}", unionId);
        BadgeSummaryResp resp = badgeService.getBadgeSummary(unionId);
        log.info("用户勋章统计结果 unionId={}, themeCount={}, badgeCount={}",
                unionId, resp.getThemeCount(), resp.getBadgeCount());
        return resp;
    }

    @Override
    public List<BadgeThemeDetailResp> listAllThemeBadges(String unionId) {
        return badgeService.listAllThemeBadges(unionId);
    }

    /**
     * 发送勋章通知
     */
    public void sendBadgeNotification(List<BadgeGain> badgeGainList) {
        if (CollectionUtils.isEmpty(badgeGainList)) {
            log.info("没有需要发送的消息");
            return;
        }
        // 获取一次所有的勋章、检查是否发送公众号消息
        List<String> badgeBizIds = badgeGainList.stream().map(BadgeGain::getBadgeBizId).distinct().collect(Collectors.toList());
        List<Badge> badges = badgeRepository.listByBizIds(badgeBizIds);
        // 过滤出需要发送消息的勋章
        Map<String, Boolean> needSendMsgBadges = badges.stream()
                .filter(badge -> Boolean.TRUE.equals(badge.getSendMsg()))
                .collect(Collectors.toMap(Badge::getBizId, badge -> Boolean.TRUE));
        // 是否在白名单内，在则发消息，不在则不发
        String unionId = badgeGainList.get(0).getUnionId();
        if (badgeSendMsgSwitch == 1) {
            List<String> split = Lists.newArrayList(badgeSendMsgWhiteUser.split(","));
            if (!split.contains(unionId)) {
                log.info("不是白名单用户，不发送.当前用户:[{}],系统设置的白名单用户:{}", unionId, JSON.toJSONString(badgeSendMsgWhiteUser));
                return;
            }
        }
        List<JicWxFans> jicWxFans = cardmainMapper.selectJicWxfansByUniondId(unionId);
        // 转成key为weid的map
        Map<Long, JicWxFans> wxFansMap = jicWxFans.stream().collect(Collectors.toMap(JicWxFans::getWeid, Function.identity(), (v1, v2) -> v1));

        // 根据品牌映射成appid
        List<WxMsgBrandConfigDto> brandConfigs = JSON.parseArray(brandNacosConfigs, WxMsgBrandConfigDto.class);
        // 转成key为weid的map
        Map<String, WxMsgBrandConfigDto> brandConfigMap = brandConfigs.stream().collect(Collectors.toMap(WxMsgBrandConfigDto::getWeid, Function.identity(), (v1, v2) -> v1));

        for (BadgeGain badgeGain : badgeGainList) {
            if (needSendMsgBadges.get(badgeGain.getBadgeBizId()) == null || !needSendMsgBadges.get(badgeGain.getBadgeBizId())) {
                log.info("当前勋章设置为不发送消息:{}", badgeGain.getBadgeBizId());
                continue;
            }
            Long weId = badgeGain.getWeId();
            WxMsgBrandConfigDto brandConfig = brandConfigMap.get(String.valueOf(weId));
            if (brandConfig == null) {
                log.info("未能匹配到公众号配置 weid = {}", weId);
                continue;
            }
            JicWxFans wxFans = wxFansMap.get(weId);
            if (wxFans == null || StringUtils.isBlank(wxFans.getOpenid()) || StringUtils.isBlank(wxFans.getNickname())) {
                log.info("未能匹配到微信粉丝 wxFans = {}", JSON.toJSONString(wxFans));
                continue;
            }
            List<WxMpTemplateDataDto> data = new ArrayList<>();
            data.add(new WxMpTemplateDataDto("black", "thing13", badgeGain.getNickname()));
            data.add(new WxMpTemplateDataDto("black", "thing18", badgeGain.hasGain() ? "商品下单获得勋章" : "商品退货扣减勋章"));
            data.add(new WxMpTemplateDataDto("black", "time5", DateUtils.formatDate(badgeGain.getCreateTime(), "yyyy-MM-dd")));
            SendWxMsgNewContext build = SendWxMsgNewContext.build(brandConfig, wxFans.getOpenid(), data);
            String url = build.getWxMpTemplateMsgDTO().getMiniProgram().getPagePath() + "?url=https%3A%2F%2Fjnby.ioutu.cn%2Fweb-business%2FJNBY-H5%2FMedal%2FMedalDetail%3FbadgeBizId%3D" + badgeGain.getBadgeBizId();
            build.getWxMpTemplateMsgDTO().getMiniProgram().setPagePath(url);
            log.info("计划发送数据:{}", JSON.toJSONString(build));
            // 发送消息暂时隐藏。避免真实发送。
            messageService.sendWxMsgNew(build);
        }
    }

    /**
     * 根据订单编号和unionId获取未展示过的勋章获得记录
     *
     * @param orderId 订单编号
     * @param unionId 用户unionId
     * @return 未展示过的勋章信息列表
     */
    @Override
    public List<BadgeUnshownResp> getUnshownBadgeGains(String orderId, String unionId) {
        log.info("获取未展示过的勋章获得记录 docNo={}, unionId={}", orderId, unionId);
        // 根据微盟的orderId获取伯俊零售单的编号docNo。
        String docNo = retailOrderRepository.getDocNoByOrderId(orderId);
        if (StringUtils.isBlank(docNo)) {
            log.warn("未找到伯俊零售单编号 orderId={}, unionId={}", orderId, unionId);
            return Collections.emptyList();
        }

        // 1. 获取该用户在该订单下的所有勋章获得记录
        List<BadgeGain> badgeGains = badgeGainRepository.listByRetailNo(docNo);
        if (CollectionUtils.isEmpty(badgeGains)) {
            log.info("未找到勋章获得记录 docNo={}, unionId={}", docNo, unionId);
            return Collections.emptyList();
        }

        // 过滤出该用户的记录
        List<BadgeGain> userBadgeGains = badgeGains.stream()
                .filter(gain -> unionId.equals(gain.getUnionId()) && gain.hasGain() && gain.isActive())
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(userBadgeGains)) {
            log.info("该用户在该订单下没有有效的勋章获得记录 docNo={}, unionId={}", docNo, unionId);
            return Collections.emptyList();
        }

        // 2. 获取勋章获得记录ID列表
        List<String> gainRecordBizIds = userBadgeGains.stream()
                .map(BadgeGain::getBizId)
                .collect(Collectors.toList());

        // 3. 查询已经展示过的记录
        List<BadgeShowRecord> showRecords = badgeShowRecordService.listByGainRecordBizIds(gainRecordBizIds);

        // 4. 获取已展示过的记录ID集合
        final Set<String> shownRecordIds = !CollectionUtils.isEmpty(showRecords) ?
                showRecords.stream()
                        .map(BadgeShowRecord::getGainRecordBizId)
                        .collect(Collectors.toSet()) :
                Collections.emptySet();

        // 5. 过滤出未展示过的记录
        List<BadgeGain> unshownBadgeGains = userBadgeGains.stream()
                .filter(gain -> !shownRecordIds.contains(gain.getBizId()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(unshownBadgeGains)) {
            log.info("没有未展示过的勋章获得记录 docNo={}, unionId={}", docNo, unionId);
            return Collections.emptyList();
        }

        // 6. 按勋章分组整理数据
        Map<String, List<BadgeGain>> badgeGainMap = unshownBadgeGains.stream()
                .collect(Collectors.groupingBy(BadgeGain::getBadgeBizId));

        // 7. 构建返回结果
        List<BadgeUnshownResp> result = new ArrayList<>();
        for (Map.Entry<String, List<BadgeGain>> entry : badgeGainMap.entrySet()) {
            String badgeBizId = entry.getKey();
            List<BadgeGain> gains = entry.getValue();

            if (CollectionUtils.isEmpty(gains)) {
                continue;
            }

            // 获取勋章信息
            Badge badge = badgeRepository.findByBizId(badgeBizId);
            if (badge == null) {
                log.warn("未找到勋章信息 badgeBizId={}", badgeBizId);
                continue;
            }

            BadgeUnshownResp badgeResp = new BadgeUnshownResp();
            badgeResp.setBadgeBizId(badgeBizId);
            badgeResp.setBadgeName(badge.getName());
            badgeResp.setBadgeImage(badge.getImageUrl());

            // 构建获奖记录列表
            List<BadgeGainRecord> gainRecords = new ArrayList<>();
            for (BadgeGain gain : gains) {
                BadgeGainRecord record = new BadgeGainRecord();
                record.setBizId(gain.getBizId());
                record.setRank(gain.getRank());
                record.setSkc(gain.getSkc());
                record.setProductName(gain.getProductName());
                gainRecords.add(record);
            }

            badgeResp.setGainRecords(gainRecords);
            result.add(badgeResp);
        }

        log.info("获取未展示过的勋章信息结果 docNo={}, unionId={}, badgeCount={}, recordCount={}",
                docNo, unionId, result.size(), unshownBadgeGains.size());

        return result;
    }

    /**
     * 根据用户ID和勋章ID获取勋章详情及获取记录
     *
     * @param unionId    用户unionId
     * @param badgeBizId 勋章业务ID
     * @return 勋章详情及获取记录
     */
    @Override
    public BadgeDetailWithGainsResp getBadgeDetailWithGains(String unionId, String badgeBizId) {
        log.info("获取勋章详情及获取记录 unionId={}, badgeBizId={}", unionId, badgeBizId);

        // 参数校验
        if (StringUtils.isBlank(unionId) || StringUtils.isBlank(badgeBizId)) {
            log.warn("参数不合法 unionId={}, badgeBizId={}", unionId, badgeBizId);
            return null;
        }

        // 1. 获取勋章信息
        Badge badge = badgeRepository.findByBizId(badgeBizId);
        if (badge == null) {
            log.warn("未找到勋章信息 badgeBizId={}", badgeBizId);
            return null;
        }

        // 2. 获取用户的勋章获取记录
        List<BadgeGain> userBadgeGains = badgeGainRepository.listByUnionId(unionId);

        // 过滤出当前勋章的获取记录，并只保留有效的记录
        List<BadgeGain> badgeGains = userBadgeGains.stream()
                .filter(gain -> badgeBizId.equals(gain.getBadgeBizId()) && gain.hasGain() && gain.isActive())
                .collect(Collectors.toList());

        // 3. 构建响应数据
        BadgeDetailWithGainsResp resp = BadgeConvert.INSTANCE.badge2BadgeDetailWithGainsResp(badge);
        // 4. 如果有获取记录，构建获取记录列表
        if (CollectionUtils.isEmpty(badgeGains)) {
            resp.setGainRecords(Collections.emptyList());
            log.info("当前勋章未获得");
            return resp;
        }
        log.info("获取记录结果:{}", JSON.toJSONString(badgeGains));
        // 获取这些记录的展示状态
        List<String> gainRecordBizIds = badgeGains.stream()
                .map(BadgeGain::getBizId)
                .collect(Collectors.toList());

        List<BadgeShowRecord> showRecords = badgeShowRecordService.listByGainRecordBizIds(gainRecordBizIds);
        log.info("是否展示过的结果:{}", JSON.toJSONString(showRecords));

        // 将已展示的记录ID转为集合，方便判断
        final Set<String> shownRecordIds = !CollectionUtils.isEmpty(showRecords) ?
                showRecords.stream()
                        .map(BadgeShowRecord::getGainRecordBizId)
                        .collect(Collectors.toSet()) :
                Collections.emptySet();

        // 构建获取记录列表
        List<GainRecord> gainRecords = new ArrayList<>();
        for (BadgeGain gain : badgeGains) {
            GainRecord record = new GainRecord();
            record.setBizId(gain.getBizId());
            record.setRank(gain.getRank());
            record.setSkc(gain.getSkc());
            record.setCreateTime(DateUtils.formatByDateTimePattern(gain.getCreateTime()));
            record.setShown(shownRecordIds.contains(gain.getBizId()));
            record.setProductName(gain.getProductName());
            gainRecords.add(record);
        }

        // 按排名升序排序，展示排名靠前的记录
        gainRecords.sort(Comparator.comparing(GainRecord::getRank));
        resp.setGainRecords(gainRecords);
        return resp;
    }

    /**
     * 获取所有进行中或已结束的勋章
     *
     * @return 勋章列表，包含勋章业务ID和名称
     */
    @Override
    public List<BadgeSimpleInfoResp> listAllActiveAndEndedBadges() {
        log.info("获取所有进行中或已结束的勋章");

        // 获取所有未删除的勋章列表(包括进行中和已结束)
        List<Badge> badges = badgeRepository.findAllStartedBadges();

        if (CollectionUtils.isEmpty(badges)) {
            log.info("未找到进行中或已结束的勋章");
            return Collections.emptyList();
        }

        // 转换为响应对象
        List<BadgeSimpleInfoResp> result = badges.stream().map(badge -> {
            BadgeSimpleInfoResp resp = new BadgeSimpleInfoResp();
            resp.setBadgeBizId(badge.getBizId());
            resp.setBadgeName(badge.getName());
            return resp;
        }).collect(Collectors.toList());

        log.info("获取所有进行中或已结束的勋章结果: count={}", result.size());
        return result;
    }

    /**
     * 查询勋章获取记录变更日志
     *
     * @param req  查询条件，包含勋章bizId、unionId、获得/扣减标识
     * @param page
     * @return 勋章获取记录变更日志列表
     */
    @Override
    public List<BadgeGainLogResp> queryBadgeGainLogs(BadgeGainLogQueryReq req, Page page) {
        // 查询勋章获取记录日志
        List<BadgeGainRecordLog> logs = badgeService.queryBadgeGainLogs(req.getBadgeBizId(), req.getUnionId(), req.getStatus(), page);
        if (CollectionUtils.isEmpty(logs)) {
            log.info("未找到勋章获取记录变更日志");
            return Collections.emptyList();
        }

        return logs.stream().map(BadgeConvert.INSTANCE::log2BadgeGainLog).collect(Collectors.toList());
    }

    @Override
    public void scanOrder(Date fromDate, Date toDate) {
        Preconditions.checkArgument(fromDate != null, "fromDate不能为空");
        Preconditions.checkArgument(toDate != null, "toDate不能为空");
        if (toDate.before(fromDate)) {
            throw new RuntimeException("时间范围有误，请检查");
        }
        // 日期格式化为SQL需要的字符串
        String billDate = DateUtils.formatDate(fromDate, "yyyyMMdd");
        String creationStart = DateUtils.formatByDateTimePattern(fromDate);
        String creationEnd = DateUtils.formatByDateTimePattern(toDate);

        // 查询订单ID列表
        List<Long> orderIds = mRetailMapper.selectOrderIdsForScan(Integer.valueOf(billDate), creationStart, creationEnd);
        if (orderIds == null || orderIds.isEmpty()) {
            log.info("scanOrder: 未查询到符合条件的订单");
            return;
        }
        log.info("scanOrder: 查询到{}个订单，开始处理...", orderIds.size());
        for (Long orderId : orderIds) {
            try {
                processBadgeTask(orderId);
            } catch (Exception e) {
                log.error("scanOrder: 处理订单{}异常", orderId, e);
            }
        }
        log.info("scanOrder: 订单处理完毕，总数:{}", orderIds.size());
    }
}
