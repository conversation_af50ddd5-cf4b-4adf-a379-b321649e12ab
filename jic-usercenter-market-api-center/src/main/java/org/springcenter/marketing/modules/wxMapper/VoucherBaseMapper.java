package org.springcenter.marketing.modules.wxMapper;


import org.apache.ibatis.annotations.Param;
import org.springcenter.marketing.modules.model.VoucherBase;

import java.util.Date;
import java.util.List;

public interface VoucherBaseMapper {

    int updateValidDateByVoucherNo(@Param("voucherNo") String voucherNo,@Param("validDate") Integer validDate);

    List<VoucherBase> selectByUnionIdAndAwardIds(@Param("unionid") String unionid, @Param("awardIds") List<String> awardIds);


    List<VoucherBase> selectByUnionIdAndAwardIdsAll(@Param("unionid") String unionid, @Param("awardIds") List<String> awardIds);

    List<VoucherBase> selectVerifyedByUnionidAndAwardId(@Param("awardId") String awardId, @Param("unionid") String unionid, @Param("createTime") Date createTime);
}
