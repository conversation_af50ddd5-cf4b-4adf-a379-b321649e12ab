package org.springcenter.marketing.modules.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 勋章详情
 *
 * @Author: CodeGenerator
 * @Date: 2025-03-26 09:52:17
 * @Description:
 */
@Data
@TableName("badge_detail")
public class BadgeDetail implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "自增主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    @ApiModelProperty(value = "业务ID")
    @TableField("biz_id")
    private String bizId;
    @ApiModelProperty(value = "主题ID")
    @TableField("badge_theme_id")
    private Integer badgeThemeId;
    @ApiModelProperty(value = "主题业务ID")
    @TableField("theme_biz_id")
    private String themeBizId;
    @ApiModelProperty(value = "主题名称")
    @TableField("badge_theme_name")
    private String badgeThemeName;
    @ApiModelProperty(value = "勋章名称")
    @TableField("name")
    private String name;
    @ApiModelProperty(value = "勋章图路径")
    @TableField("image_url")
    private String imageUrl;
    @ApiModelProperty(value = "勋章简介图路径")
    @TableField("intro_image_url")
    private String introImageUrl;
    @ApiModelProperty(value = "勋章详情图路径")
    @TableField("detail_image_url")
    private String detailImageUrl;
    @ApiModelProperty(value = "开始时间")
    @TableField("start_time")
    private Date startTime;
    @ApiModelProperty(value = "结束时间")
    @TableField("end_time")
    private Date endTime;
    @ApiModelProperty(value = "商品包ID，为空代表全部商品")
    @TableField("product_package_id")
    private String productPackageId;
    @ApiModelProperty(value = "人群包ID")
    @TableField("crowd_package_id")
    private String crowdPackageId;
    @ApiModelProperty(value = "排序值（越大越靠前）")
    @TableField("sort")
    private Integer sort;
    @ApiModelProperty(value = "微商城 是否勾选")
    @TableField("order_type_wsc")
    private Boolean orderTypeWsc;
    @ApiModelProperty(value = "线下订单 是否勾选")
    @TableField("order_type_xxdd")
    private Boolean orderTypeXxdd;
    @ApiModelProperty(value = "BOX 是否勾选")
    @TableField("order_type_box")
    private Boolean orderTypeBox;
    @ApiModelProperty(value = "是否发消息，默认不发")
    @TableField("send_msg")
    private Boolean sendMsg;
    @ApiModelProperty(value = "是否删除（0否，1是）")
    @TableField("is_delete")
    private Boolean isDelete;
    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    private Date createTime;
    @ApiModelProperty(value = "更新时间")
    @TableField("update_time")
    private Date updateTime;
    @ApiModelProperty(value = "创建人")
    @TableField("create_person")
    private String createPerson;
    @ApiModelProperty(value = "小程序配置，JSON格式字符串")
    @TableField("apps")
    private String apps;
}
