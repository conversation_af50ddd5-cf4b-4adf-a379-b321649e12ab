<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.marketing.modules.bojunMapper.EbOrdersoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.springcenter.marketing.modules.model.EbOrderso">
        <id column="ID" property="id" />
        <result column="DOCNO" property="docno" />
        <result column="DOCTYPE" property="doctype" />
        <result column="BILLDATE" property="billdate" />
        <result column="SOURCE" property="source" />
        <result column="STATUS" property="status" />
        <result column="ISACTIVE" property="isactive" />
        <result column="SOURCECODE" property="sourcecode" />
        <result column="DESCRIPTION" property="description" />
        <result column="CLOSE_STATUS" property="closeStatus" />
        <result column="OUT_STATUS" property="outStatus" />
        <result column="FASTNO" property="fastno" />
        <result column="OUTTIME" property="outtime" />
        <result column="EB_EXPRESS_ID" property="ebExpressId" />
        <result column="IS_TAKE" property="isTake" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, DOCNO,DOCTYPE,BILLDATE,SOURCE,STATUS,ISACTIVE,SOURCECODE,DESCRIPTION,
        CLOSE_STATUS, OUT_STATUS,FASTNO,OUTTIME,EB_EXPRESS_ID
    </sql>
    <select id="getEbSoOrder" resultMap="BaseResultMap">
        select close_status, OUT_STATUS,FASTNO , OUTTIME ,DOCNO,EB_EXPRESS_ID,ID from eb_orderso where id = #{id,jdbcType=DECIMAL}
    </select>

    <select id="selectByOmsEbonum" resultType="org.springcenter.marketing.modules.model.EbOrderso">
        SELECT <include refid="Base_Column_List"></include>
            FROM  EB_ORDERSO WHERE oms_ebonum = #{ebNum} and close_status = 1 and status = 2
    </select>

</mapper>