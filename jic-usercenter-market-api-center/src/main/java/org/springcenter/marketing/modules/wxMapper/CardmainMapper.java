package org.springcenter.marketing.modules.wxMapper;

import org.apache.ibatis.annotations.Param;
import org.springcenter.marketing.modules.model.Cardmain;
import org.springcenter.marketing.modules.model.JicBaeInfo;
import org.springcenter.marketing.modules.model.JicWxFans;
import org.springcenter.marketing.modules.model.TaskCornInfo;

import java.util.List;

public interface CardmainMapper {
    int deleteByPrimaryKey(Long id);

    int insert(Cardmain record);

    int insertSelective(Cardmain record);

    Cardmain selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(Cardmain record);

    int updateByPrimaryKey(Cardmain record);

    Cardmain selectByUnionIdAndCardTypeId(@Param("unionid") String unionid, @Param("cardTypeId") Integer cardTypeId);

    List<Cardmain> selectByUnionId(@Param("unionid") String unionid);

    List<Cardmain> selectByOpenId(@Param("openId") String openId);

    /**
     * 根据用户id和品牌id获取卡信息
     * @param unionId 用户id
     * @param weId 品牌id
     * @return 返回
     */
    Cardmain selectCardInfoByUnionIdAndWeId(String unionId, String weId);

    List<JicBaeInfo> selectBaseInfoByUnionIdsAndWeId(@Param("list") List<String> unionIds, @Param("weId") Long weId);

    JicWxFans selectJicWxfansByUniondIdAndWeId(@Param("unionId") String unionId, @Param("weId") String weId);

    List<JicWxFans> selectJicWxfansByUniondIdsAndWeId(@Param("list") List<String> unionId, @Param("weId") Long weId);

    List<JicWxFans> selectJicWxfansByUniondId(@Param("unionId") String unionId);

    List<TaskCornInfo> selectCornByUnionIdAndWeId(@Param("unionId") String unionId, @Param("weId") String weId);

    List<TaskCornInfo> selectCornByUnionIdAndNoWeId(@Param("unionId") String unionId);

    Cardmain selectByCardNo(@Param("cardno") String cardno);

    Cardmain selectByConditionLimit1(@Param("cardno") String cardno, @Param("groupNo") String groupNo, @Param("tel") String tel);

    List<Cardmain> selectByCardNos(@Param("cardnos")List<String> collect1);
}