<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.marketing.modules.mapper.AcRepurchasePlanDetailMapper">

    <resultMap id="BaseResultMap" type="org.springcenter.marketing.modules.model.AcRepurchasePlanDetail">
            <id property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="activityOrderId" column="ACTIVITY_ORDER_ID" jdbcType="VARCHAR"/>
            <result property="startTime" column="START_TIME" jdbcType="DECIMAL"/>
            <result property="endTime" column="END_TIME" jdbcType="DECIMAL"/>
            <result property="isDel" column="IS_DEL" jdbcType="DECIMAL"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="voucherNos" column="VOUCHER_NOS" jdbcType="VARCHAR"/>
            <result property="sortNum" column="SORT_NUM" jdbcType="DECIMAL"/>
            <result property="awardId" column="AWARD_ID" jdbcType="VARCHAR"/>
            <result property="useRuleInnerId" column="USE_RULE_INNER_ID" jdbcType="VARCHAR"/>
        <result property="sendMsgFlag" column="SEND_MSG_FLAG" jdbcType="DECIMAL"/>
        <result property="sendNumber" column="SEND_NUMBER" jdbcType="VARCHAR"/>
        <result property="joinTimes" column="JOIN_TIMES" jdbcType="DECIMAL"/>
        <result property="outName" column="out_name" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,ACTIVITY_ORDER_ID,START_TIME,
        END_TIME,IS_DEL,CREATE_TIME,
        UPDATE_TIME,VOUCHER_NOS,
        SORT_NUM,AWARD_ID,USE_RULE_INNER_ID,SEND_MSG_FLAG,SEND_NUMBER,JOIN_TIMES,out_name
    </sql>
    <select id="selectUserRepurchasePlan"
            resultMap="BaseResultMap">

        select
            rpd.ID,rpd.ACTIVITY_ORDER_ID,rpd.START_TIME,
            rpd.END_TIME,rpd.IS_DEL,rpd.CREATE_TIME,
            rpd.UPDATE_TIME,rpd.VOUCHER_NOS,
            rpd.SORT_NUM,rpd.AWARD_ID,rpd.USE_RULE_INNER_ID,rpd.SEND_MSG_FLAG,rpd.SEND_NUMBER,rpd.JOIN_TIMES,rpd.out_name

            from ac_activity_order jao left join ac_repurchase_plan_detail rpd on jao.ID= rpd.ACTIVITY_ORDER_ID
        where jao.WEID = #{weid} and jao.IS_DEL = 0 and rpd.IS_DEL = 0 and jao.STATUS = 1  and jao.UNIONID = #{unionid}
        and #{now} between jao.SUB_START_TIME and jao.SUB_END_TIME
        order by rpd.SORT_NUM

    </select>


    <select id="selectShouldSendMsgAcRepurchaseDetailId"
            resultType="java.lang.String">

        select
            rpd.ID
        from ac_activity_order jao left join ac_repurchase_plan_detail rpd on jao.ID= rpd.ACTIVITY_ORDER_ID
        where jao.IS_DEL = 0 and rpd.IS_DEL = 0 and jao.STATUS = 1
          and #{now} between jao.SUB_START_TIME and jao.SUB_END_TIME and #{nowLong} between rpd.START_TIME and rpd.END_TIME and rpd.SEND_MSG_FLAG = 0
        and jao.IS_CAN_SEND_ALERT = 1
          <if test="id != null and id != '' ">
              and rpd.id = #{id}
          </if>
        order by rpd.SORT_NUM

    </select>
    <select id="selectShouldSendVouhcerAcRepurchaseDetailId"
            resultMap="BaseResultMap">
        select             rpd.ID,rpd.ACTIVITY_ORDER_ID,rpd.START_TIME,
                           rpd.END_TIME,rpd.IS_DEL,rpd.CREATE_TIME,
                           rpd.UPDATE_TIME,rpd.VOUCHER_NOS,
                           rpd.SORT_NUM,rpd.AWARD_ID,rpd.USE_RULE_INNER_ID,rpd.SEND_MSG_FLAG,rpd.SEND_NUMBER,rpd.JOIN_TIMES
        from ac_activity_order jao left join ac_repurchase_plan_detail rpd on jao.ID= rpd.ACTIVITY_ORDER_ID
        where jao.IS_DEL = 0 and rpd.IS_DEL = 0 and jao.STATUS = 1  and
              #{now} between jao.SUB_START_TIME and jao.SUB_END_TIME and #{nowLong} between rpd.START_TIME and rpd.END_TIME
              and VOUCHER_NOS is null
        order by rpd.SORT_NUM

    </select>
    <select id="selectByOrderId"  resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include> from ac_repurchase_plan_detail
        where ACTIVITY_ORDER_ID =#{orderId} and is_del = 0

    </select>

</mapper>
