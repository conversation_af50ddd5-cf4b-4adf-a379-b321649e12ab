package org.springcenter.marketing.modules.webapi;

import com.xxl.job.core.handler.IJobHandler;
import org.springcenter.marketing.api.dto.CustomerBaseResponse;
import org.springcenter.marketing.api.dto.MemberIntegralDto;
import org.springcenter.marketing.modules.context.*;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.Query;

import java.util.List;

public interface ICustomerVipHttpApi {


    /**
     * 会员品牌卡列表
     * @param memberQueryContext
     * @return
     */
    @POST("/sdk/member-center/member/card/list")
    Call<CustomerBaseResponse<List<MemberCardEntity>>> getMemberCardList(@Body MemberQueryContext memberQueryContext);

    /**
     * 根据不同的类型编号批量查询会员信息。一次查询数据量尽量少一些。别把他们搞挂了
     * 接口文档：
     * http://swagger.jnby.com/jictest/sdk/doc.html#/SDK-%E4%BC%9A%E5%91%98/2.0%E4%BC%9A%E5%91%98%E6%9C%8D%E5%8A%A1%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/SDKGetMemberCardListUsingPOST_1
     */
    @POST("/sdk/member-center/v2.0/member/card/list")
    Call<CustomerBaseResponse<List<MemberListCardEntity>>> listMemberCard(@Body MemberListQueryContext memberListQueryContext);

    /**
     * 批量获取会员昵称和头像（封装为更轻量的返回）
     */
    @POST("/sdk/member-center/v2.0/member/card/list")
    Call<CustomerBaseResponse<List<MemberNameAndHeadImgEntity>>> listMemberCardNameAndHeadImg(@Body MemberListQueryContext memberListQueryContext);

    /**
     * 会员品牌卡信息
     * @param memberCardQueryContext
     * @return
     */
    @POST("/sdk/member-center/member/card")
    Call<CustomerBaseResponse<MemberCardEntity>> getMemberCard(@Body MemberCardQueryContext memberCardQueryContext);

    @POST("/sdk/member-center/member/card/base/info")
    Call<CustomerBaseResponse<MemberBaseInfoEntity>> getUserBaseInfo(@Body MemberBaseInfoQueryContext memberBaseInfoQueryContext);


    @POST("/sdk/member-center/v2.0/member/card/info")
    Call<CustomerBaseResponse<MemberBaseInfoV2Entity>> getUserBaseInfoV2(@Body MemberBaseInfoQueryV2Context memberBaseInfoQueryContext);

    @POST("/sdk/integral-center/integral/info")
    Call<CustomerBaseResponse<MemberIntegralDto>> getMemberIntegral(@Body  MemberCardQueryContext req);

    @POST("/sdk/member-center/member/card/wid")
    Call<CustomerBaseResponse<MemberCardEntity>> getMemberCardWid(MemberCardQueryContext memberCardQueryContext);


    @GET("/activity/jic-activity-api-center-market/birth/kid/list")
    Call<CustomerBaseResponse<List<KidBirthListEntity>>> getKidBirthList(@Query("cardNo") String cardNo);


    @POST("/wxmall/wxmall-center/weimo/crm/customer/update")
    Call<CustomerBaseResponse<Boolean>> updateCustomer(@Body MemberUpdateInfoEntity req);
}
