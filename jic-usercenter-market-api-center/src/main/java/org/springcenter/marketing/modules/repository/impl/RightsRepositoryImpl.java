package org.springcenter.marketing.modules.repository.impl;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.jnby.common.Page;
import com.jnby.common.util.IdLeaf;
import org.springcenter.marketing.api.constant.IdConstant;
import org.springcenter.marketing.api.dto.*;
import org.springcenter.marketing.api.entity.WashCouponPacketEntity;
import org.springcenter.marketing.api.enums.RedisKeysEnum;
import org.springcenter.marketing.api.enums.RightsTypeEnum;
import org.springcenter.marketing.modules.mapper.*;
import org.springcenter.marketing.modules.model.*;
import org.springcenter.marketing.modules.repository.IRightsRepository;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springcenter.marketing.modules.rights.SubV4BoxGiftRight;
import org.springcenter.marketing.modules.service.IRightsMemberCardRelationService;
import org.springcenter.marketing.modules.service.RightsV3Service;
import org.springcenter.marketing.modules.rights.SubV3BoxGiftRight;
import org.springcenter.marketing.modules.util.RedisService;
import org.springcenter.marketing.modules.wxMapper.VoucherVirtualReferConfigMapper;
import org.springcenter.product.api.enums.IsDeleteEnum;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 3/19/21 1:41 PM
 */
@Repository
@RefreshScope
public class RightsRepositoryImpl implements IRightsRepository {

    @Autowired
    private BRightsMapper bRightsMapper;

    @Autowired
    private BRightsCustomizeStoreMapper bRightsCustomizeStoreMapper;

    @Autowired
    private VoucherVirtualReferConfigMapper voucherVirtualReferConfigMapper;

    @Autowired
    private RightsMemberCardRelationMapper rightsMemberCardRelationMapper;

    @Autowired
    private IRightsMemberCardRelationService iRightsMemberCardRelationService;

    @Autowired
    private BNewUserBoxGiftMapper bNewUserBoxGiftMapper;

    @Autowired
    @Qualifier("marketingTransactionTemplate")
    private TransactionTemplate template;

    @Autowired
    private RightsV3Service rightsV3Service;

    @Autowired
    private RedisService redisService;

    // 为 1 则是开启  0 是不开启
    @Value("${rights.redis.cache.flag}")
    private String rightsRedisCacheFlag;

    @Override
    public BRights create(BRights rights) {
        rights.setId(IdLeaf.getId(IdConstant.RIGHTS));
        rights.setCreateTime(new Date());
        rights.setUpdateTime(new Date());
        bRightsMapper.insertSelective(rights);
        return rights;
    }

    @Override
    public void update(BRights rights) {
        rights.setUpdateTime(new Date());
        bRightsMapper.updateByPrimaryKeySelective(rights);
    }

    @Override
    public List<BRights> findAll(BRights rights) {
        return bRightsMapper.selectListBySelective(rights);
    }


    @Override
    public BRights findById(String id) {
        return bRightsMapper.selectByPrimaryKey(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void dealStoreIds(RightsCreateReq requestData, BRights rights, Integer isStoreId) {
        if(CollectionUtils.isNotEmpty(requestData.getList())){

            List<BRightsCustomizeStore> insertBatch = new ArrayList<>(requestData.getList().size());

            //删除之前所有的
            if(StringUtils.isNotEmpty(rights.getId())){
                bRightsCustomizeStoreMapper.deleteByRightsId(rights.getId());
            }
            List<CStoreDto> list = requestData.getList();
            //批量插入
            Date now  = new Date();
            for (CStoreDto cStore : list) {
                BRightsCustomizeStore bRightsCustomizeStore = new BRightsCustomizeStore();
                bRightsCustomizeStore.setId(IdLeaf.getId(IdConstant.CUSTOMIZE));
                bRightsCustomizeStore.setbRightsId(rights.getId());
                bRightsCustomizeStore.setCreateTime(now);
                bRightsCustomizeStore.setStoreId(cStore.getId().toString());
                bRightsCustomizeStore.setStoreCode(cStore.getCode() == null ? cStore.getId().toString():cStore.getCode());
                insertBatch.add(bRightsCustomizeStore);
            }

            List<List<BRightsCustomizeStore>> partition = Lists.partition(insertBatch, 90);
            for (List<BRightsCustomizeStore> bRightsCustomizeStores : partition) {
                bRightsCustomizeStoreMapper.insertBatch(bRightsCustomizeStores);
            }
        }
    }

    @Override
    public List<String> findCustomerStroeIdsByRightId(String rightId) {
        Page page = new Page();
        com.github.pagehelper.Page<Object> hPage = PageHelper.startPage(1, 1000);
        bRightsCustomizeStoreMapper.selectCodesByRightId(rightId);
        PageInfo<String> retPage = new PageInfo(hPage);
        page.setCount(hPage.getTotal());
        page.setPages(retPage.getPages());
        List<String> list = retPage.getList();
        return list;
    }

    @Override
    public void createOrUpdateRights(RightsCreateReq requestData) {
        template.execute(action ->{
            BRights rights = new BRights();
            //生日优惠券  如果不为空  那么进行处理
            if(ObjectUtils.isNotEmpty(requestData.getRightsType())){
                // 生日优惠券
                if(RightsTypeEnum.BIRTH_COUPON.getCode().equals(requestData.getRightsType().intValue())){
                    BirthCouponReq birthCouponReq = new BirthCouponReq();
                    BeanUtils.copyProperties(requestData,birthCouponReq);
                    birthCouponReq.setRule(JSONObject.parseArray(requestData.getUseRule()));
                    String useRule = JSONObject.toJSONString(birthCouponReq);
                    requestData.setUseRule(useRule);
                }

                //优惠券
                if(RightsTypeEnum.COUPON.getCode().equals(requestData.getRightsType().intValue())){
                    CouponReq couponReq = new CouponReq();
                    BeanUtils.copyProperties(requestData,couponReq);
                    couponReq.setRule(JSONObject.parseArray(requestData.getUseRule()));
                    String useRule = JSONObject.toJSONString(couponReq);
                    requestData.setUseRule(useRule);
                }

            }

            if(ObjectUtils.isNotEmpty(requestData.getRightsType())){
                if(RightsTypeEnum.WASH_RIGHTS.getCode().equals(requestData.getRightsType().intValue())){
                    // 如果是洗护权益
                    String useRule = requestData.getUseRule();
                    List<WashCouponPacketEntity> washCouponPacketEntities = JSONObject.parseArray(useRule, WashCouponPacketEntity.class);
                    if(CollectionUtils.isNotEmpty(washCouponPacketEntities)){
                        int i = 1;
                        for (WashCouponPacketEntity washCouponPacketEntity : washCouponPacketEntities) {
                            washCouponPacketEntity.setId(i+"");
                            i++;
                        }
                    }
                    // 修复userule id 自增
                    requestData.setUseRule(JSONObject.toJSONString(washCouponPacketEntities));

                    if(CollectionUtils.isNotEmpty(washCouponPacketEntities)){
                        for (WashCouponPacketEntity washCouponPacketEntity : washCouponPacketEntities) {
                            // 更新数据
                            List<WashCouponPacketEntity.WashCoupon> list = washCouponPacketEntity.getList();
                            for (WashCouponPacketEntity.WashCoupon washCoupon : list) {
                                if(washCoupon.getType() == WashCouponPacketEntity.WASH_TYPE){
                                    VoucherVirtualReferConfig voucherVirtualReferConfig = voucherVirtualReferConfigMapper.selectById(new BigDecimal(washCoupon.getWashCouponId()));
                                    if(ObjectUtils.isNotEmpty(voucherVirtualReferConfig)){
                                        voucherVirtualReferConfig.setOuterid(washCoupon.getOutWashCouponId());
                                        voucherVirtualReferConfigMapper.updateById(voucherVirtualReferConfig);
                                    }else{
                                        voucherVirtualReferConfig = new VoucherVirtualReferConfig();
                                        voucherVirtualReferConfig.setOuterid(washCoupon.getOutWashCouponId());
                                        voucherVirtualReferConfig.setAwardid(new BigDecimal(washCoupon.getWashCouponId()));
                                        voucherVirtualReferConfigMapper.insertSelective(voucherVirtualReferConfig);
                                    }
                                }
                            }
                        }
                    }
                }
            }

            BeanUtils.copyProperties(requestData, rights);
//            rights.setbMemberCardId(requestData.getMemberCardId());
            rights.setStatus(requestData.getStatus() == null ? 1 : requestData.getStatus());
            rights.setIsDel(requestData.getIsDel() == null ? 0 : requestData.getIsDel());
            //
            if(rights.getSuitCusType() != null){
                if(requestData.getIsStoreId() == 2){
                    rights.setSuitCusType(1);
                }else{
                    rights.setSuitCusType(0);
                }
            }


            if (requestData.getId() == null || "".equals(requestData.getId())){
                // 默认停用  新增的时候默认停用
                rights.setStatus(0L);
                rights = create(rights);
                dealStoreIds(requestData,rights,requestData.getIsStoreId());
            }else {
                String id = rights.getId();
                BRights bRights = bRightsMapper.selectByPrimaryKey(id);
                if(rights.getRightsValue() == null){
                    rights.setRightsValue(bRights.getRightsValue());
                }
                // 识别当前type 为 46 收盒礼的时候
                if(RightsTypeEnum.SUB3_BOX_GIFT.getCode().equals(bRights.getRightsType().intValue())){
                    // 识别他俩的数据 是否一致  如果都一致 那么不动   如果有不一致的， 那么进行修改整个数据库中 原来的那个 改为新的 ，仅限于未使用的权益
                    String useRule = rights.getUseRule();
                    String originUseRule = bRights.getUseRule();
                    changeBoxGiftByUseRule(originUseRule,useRule);
                }
                update(rights);
                dealStoreIds(requestData,rights,requestData.getIsStoreId());
            }
            // 如果是禁用 关联关系不动
//            if(rights.getStatus().equals(0L)){
//                rightsMemberCardRelationMapper.updateByRightsId(rights.getId(), IsDeleteEnum.IS_DELETED.getCode());
//            }

            if(CollectionUtils.isNotEmpty(requestData.getMemberCardIds())){
                // 删除之前的信息  添加新的信息
                rightsMemberCardRelationMapper.updateByRightsId(rights.getId(), IsDeleteEnum.IS_DELETED.getCode());
                List<RightsMemberCardRelation> isnertList = new ArrayList<>();
                for (String memberCardId : requestData.getMemberCardIds()) {
                    RightsMemberCardRelation relation = new RightsMemberCardRelation();
                    relation.setId(IdLeaf.getId(IdConstant.RIGHTS_MEMBER_CARD_RELATION));
                    relation.setRightsId(rights.getId());
                    relation.setMemberCardId(memberCardId);
                    relation.setCreateTime(new Date());
                    relation.setUpdateTime(new Date());
                    relation.setIsDel(IsDeleteEnum.NORMAL.getCode());
                    isnertList.add(relation);
                }
                iRightsMemberCardRelationService.saveBatch(isnertList);
            }else{
                //更改状态的时候不进行删除
                if(requestData.getStatus() == null){
                    rightsMemberCardRelationMapper.updateByRightsId(rights.getId(), IsDeleteEnum.IS_DELETED.getCode());
                }
            }
            return action;
        });

    }

    private void changeBoxGiftByUseRule(String originUseRule, String newUseRule) {

        // 转化为对象
        List<SubV4BoxGiftRight> originUse = JSONObject.parseArray(originUseRule, SubV4BoxGiftRight.class);
        List<SubV4BoxGiftRight> newUse = JSONObject.parseArray(newUseRule, SubV4BoxGiftRight.class);

        if(CollectionUtils.isEmpty(originUse) || CollectionUtils.isEmpty(newUse)){
            return ;
        }
        Map<Long, List<SubV3BoxGiftRight>> originMap = originUse.stream().collect(Collectors.toMap(r -> r.getArcBrandId(), r -> r.getList()));
        Map<Long, List<SubV3BoxGiftRight>> newMap = newUse.stream().collect(Collectors.toMap(r -> r.getArcBrandId(), r -> r.getList()));

        Map<String,String> result = new HashMap<>();
        List<String> sendBoxNum = new ArrayList<>();
        // 品牌
        for (Long key : originMap.keySet()) {
            List<SubV3BoxGiftRight> originBoxGift = originMap.get(key);
            List<SubV3BoxGiftRight> newBoxGift = newMap.get(key);
            if(CollectionUtils.isEmpty(newBoxGift)){
                continue;
            }
            // 分组  // 按照顺序
            Map<String, SubV3BoxGiftRight.BoxGiftRight> originMapBoxGift = originBoxGift.stream().collect(Collectors.toMap(r -> r.getKey(), r -> r.getBoxGiftRight()));
            Map<String, SubV3BoxGiftRight.BoxGiftRight> newMapBoxGift = newBoxGift.stream().collect(Collectors.toMap(r -> r.getKey(), r -> r.getBoxGiftRight()));
            for (String s : originMapBoxGift.keySet()) {
                SubV3BoxGiftRight.BoxGiftRight originBoxGiftGift = originMapBoxGift.get(s);
                SubV3BoxGiftRight.BoxGiftRight newBoxGiftGift = newMapBoxGift.get(s);
                if(newBoxGiftGift == null){
                    continue;
                }
                // 处理数据
                if(!originBoxGiftGift.getSku().equals(newBoxGiftGift.getSku())){
                    result.put(originBoxGiftGift.getSku()+","+key+","+s,newBoxGiftGift.getSku()+","+newBoxGiftGift.getName());
                    sendBoxNum.add(s);
                }
            }
        }

        if(result.isEmpty()){
            return ;
        }
        // 如果不为空 进行处理数据  更新未使用的
        for (String originSku : result.keySet()) {
            String[] split1 = originSku.split(",");
            String originSkuOne = split1[0];
            String arcBrandId = split1[1];
            String sendNum = split1[2];

            String newSku = result.get(originSku);
            String[] split = newSku.split(",");
            // 更新
            bNewUserBoxGiftMapper.updateBySku(originSkuOne,split[0],sendNum,split[1],arcBrandId);
        }
    }

    @Override
    public List<RightsResp> getRights(GetRightsReq requestData) {
        List<RightsResp> result  = new ArrayList<>();
        if(1 == requestData.getQueryType()){
            result = getRightsByRedisOrDataBase(requestData);
            for (RightsResp rightsResp : result) {
                if(rightsResp.getSuitableStoreType() == 4){
                    com.github.pagehelper.Page<Object> hPage = PageHelper.startPage(1, 3000);
                    bRightsCustomizeStoreMapper.selectByRightId(rightsResp.getRightsId());
                    PageInfo<String> retPage = new PageInfo(hPage);
                    List<String> stringList = retPage.getList();
                    rightsResp.setSuitableStoreIds(stringList);
                }
            }
        }else{
            // 如果为空 设置为空对象
            if(requestData.getCStoreDto() == null){
                requestData.setCStoreDto(new CStoreDto());
            }

            if(com.baomidou.mybatisplus.core.toolkit.ObjectUtils.isNotEmpty(requestData.getCStoreDto())){
                List<RightsResp> rights = getRightsByRedisOrDataBase(requestData);
                List<RightsResp> rightsResps = rightsV3Service.filterSuitRightsResp(rights, Integer.parseInt(requestData.getCustomerId()),
                        requestData.getStoreId() == null ? null : requestData.getStoreId().intValue(),requestData.getUnionid(),requestData.getBrandId());
                return rightsResps;
            }
        }
        return result;
    }



    public List<RightsResp> getRightsByRedisOrDataBase(GetRightsReq requestData){
        String key = RedisKeysEnum.REDIS_RIGHTS_KEY.join(String.valueOf(requestData.getRightsType()),requestData.getBrandId(),String.valueOf(requestData.getCardLevel()));
        if(rightsRedisCacheFlag.equals("1")){
            Object redisData = redisService.get(key);
            if(redisData != null){
                return JSONObject.parseArray(redisData.toString(),RightsResp.class);
            }
        }
        //  没有 则进行数据库查询 然后进行缓存
        List<RightsResp> rights = bRightsMapper.getRights(requestData);
        if(CollectionUtils.isNotEmpty(rights)){
            redisService.setAndLogTimes(key,JSONObject.toJSONString(rights),600);
        }
        return rights;
    }


    @Override
    public List<BRights> findRights(BRights rights) {
        List<BRights>  list = bRightsMapper.selectRightsBySeletive(rights);
        return list;
    }

    @Override
    public List<BRights> findByIds(List<String> ids) {
        List<BRights> list = bRightsMapper.selectByPrimaryKeys(ids);
        return list;
    }

    @Override
    public void copyRightsById(String rightsId,String userId) {
        // 查询权益  进行复制
        BRights bRights = bRightsMapper.selectByPrimaryKey(rightsId);
        if(bRights == null){
            return ;
        }
        // 查询对应信息   复制
        List<RightsMemberCardRelation> rightsMemberCardRelations = rightsMemberCardRelationMapper.selectByRightsIds(Arrays.asList(bRights.getId()));
        // 查询是否有 storeId 进行复制处理
        Integer suitableStoreType = bRights.getSuitableStoreType();
        List<BRightsCustomizeStore> bRightsCustomizeStores;
        if(suitableStoreType == 4){
            // 查询数据
            bRightsCustomizeStores = bRightsCustomizeStoreMapper.selectAllByRightId(bRights.getId());
        } else {
            bRightsCustomizeStores = new ArrayList<>();
        }
        String id = IdLeaf.getId(IdConstant.RIGHTS);
        bRights.setId(id);
        bRights.setCreateTime(new Date());
        bRights.setUpdateTime(new Date());
        bRights.setName(bRights.getName()+"(复制)");
        bRights.setStatus(0L);
        // 设置更新人
        bRights.setUpdateBy(userId);

        template.execute(action->{
            bRightsMapper.insertSelective(bRights);
            if(CollectionUtils.isNotEmpty(bRightsCustomizeStores)){
                for (BRightsCustomizeStore bRightsCustomizeStore : bRightsCustomizeStores) {
                    bRightsCustomizeStore.setbRightsId(id);
                    bRightsCustomizeStore.setCreateTime(new Date());
                    bRightsCustomizeStore.setUpdateTime(new Date());
                    bRightsCustomizeStore.setId(IdLeaf.getId(IdConstant.CUSTOMIZE));
                }
                bRightsCustomizeStoreMapper.insertBatch(bRightsCustomizeStores);
            }

            if(CollectionUtils.isNotEmpty(rightsMemberCardRelations)){
                for (RightsMemberCardRelation rightsMemberCardRelation : rightsMemberCardRelations) {
                    rightsMemberCardRelation.setRightsId(id);
                    rightsMemberCardRelation.setUpdateTime(new Date());
                    rightsMemberCardRelation.setCreateTime(new Date());
                    rightsMemberCardRelation.setId(IdLeaf.getId(IdConstant.RIGHTS_MEMBER_CARD_RELATION));
                }
                iRightsMemberCardRelationService.saveBatch(rightsMemberCardRelations);
            }
            return action;
        });
    }
}
