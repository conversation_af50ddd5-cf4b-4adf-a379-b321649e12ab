package org.springcenter.marketing.modules.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * @TableName theme_activity_user_award
 */
@Data
@TableName(value ="theme_activity_user_award")
public class ThemeActivityUserAward implements Serializable {
    /**
     * 主键
     */
    @TableId
    private Integer id;

    /**
     * 用户手机号
     */
    @TableField(value = "phone")
    private String phone;

    /**
     * 卡号
     */
    @TableField(value = "cardno")
    private String cardno;


    @TableField(value = "unionid")
    private String unionid;

    /**
     * 最后打卡时间
     */
    @TableField(value = "last_sign_in_time")
    private Date lastSignInTime;

    /**
     * 创建记录时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 0 未删除  1 删除
     */
    @TableField(value = "is_del")
    private Integer isDel;

    /**
     * 签到天数
     */
    @TableField(value = "sign_in_days")
    private Integer signInDays;

    /**
     * 奖励id
     */
    @TableField(value = "award_config_id")
    private Integer awardConfigId;

    /**
     * 类型  0  内部券   1  微信券  2  积分  3 内部商品  4 外部商品
     */
    @TableField(value = "award_type")
    private Integer awardType;

    /**
     * 奖励名称
     */
    @TableField(value = "award_name")
    private String awardName;

    /**
     * 奖励编码  券则为券awardId  微信为微信券id  积分为积分数量   内部商品为 productCode   外部商品不定
     */
    @TableField(value = "award_code")
    private String awardCode;

    /**
     * 发放个数
     */
    @TableField(value = "award_num")
    private Integer awardNum;

    /**
     * 0  未发放  1 已发放
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 存储的发放信息 ，  内部券为券号  微信券id  积分数量  快递号
     */
    @TableField(value = "common_info")
    private String commonInfo;

    @ApiModelProperty(value = "省份")
    private String provinces;

    @ApiModelProperty(value = "城市")
    private String city;

    @ApiModelProperty(value = "区")
    private String districts;

    @ApiModelProperty(value = "详细地址")
    private String address;

    @TableField(value = "theme_activity_id")
    private Integer themeActivityId;


    @TableField(value = "theme_rule_id")
    private Integer themeRuleId;

    @TableField(value = "real_name")
    private String realName;

    @TableField(value = "send_material_phone")
    private String sendMaterialPhone;





}