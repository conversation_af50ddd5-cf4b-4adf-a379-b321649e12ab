<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.marketing.modules.mapper.BSourcePosTemplateMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.springcenter.marketing.modules.model.BSourcePosTemplate">
        <id column="ID" property="id"/>
        <result column="APP_ID" property="appId"/>
        <result column="APP_NAME" property="appName"/>
        <result column="POS_MANAGER_ID" property="posManagerId"/>
        <result column="POS_MANAGER_NAME" property="posManagerName"/>
        <result column="POSITION" property="position"/>

        <result column="IMAGE" property="image"/>
        <result column="SORT_NO" property="sortNo"/>
        <result column="TITLE_TXT" property="titleTxt"/>
        <result column="REMARK" property="remark"/>
        <result column="ADAPT_PEOPLE_ID" property="adaptPeopleId"/>
        <result column="ADAPT_PEOPLE_NAME" property="adaptPeopleName"/>
        <result column="ADAPT_STORE_ID" property="adaptStoreId"/>
        <result column="ADAPT_STORE_NAME" property="adaptStoreName"/>
        <result column="JUMP_TYPE" property="jumpType"/>

        <result column="MINI_APP_ID" property="miniAppId"/>
        <result column="MINI_APP_PATH" property="miniAppPath"/>
        <result column="VIDEO_ID" property="videoId"/>
        <result column="H5_LINK" property="h5Link"/>

        <result column="SHOW_TIME_TYPE" property="showTimeType"/>
        <result column="START_TIME" property="startTime"/>
        <result column="END_TIME" property="endTime"/>
        <result column="BIRTHDAY_TIME" property="birthdayTime"/>
        <result column="ENABLE_STATUS" property="enableStatus"/>

        <result column="EXPOSITORY_CASE" property="expositoryCase"/>
        <result column="SEARCH_TXT" property="searchTxt"/>
        <result column="CREATE_TIME" property="createTime"/>
        <result column="CREATE_BY" property="createBy"/>
        <result column="UPDATE_BY" property="updateBy"/>
        <result column="UPDATE_TIME" property="updateTime"/>
        <result column="DEL_FLAG" property="delFlag"/>
    </resultMap>

    <resultMap id="BaseWithStatusResultMap" type="org.springcenter.marketing.modules.entity.SourcePosTemplateEntity">
        <result column="STATUS" property="status"/>
    </resultMap>
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, APP_ID, APP_NAME, POS_MANAGER_ID, POS_MANAGER_NAME,POSITION, IMAGE,
        SORT_NO, TITLE_TXT, REMARK, ADAPT_PEOPLE_ID, ADAPT_PEOPLE_NAME,ADAPT_STORE_ID, ADAPT_STORE_NAME,JUMP_TYPE,
        MINI_APP_ID,MINI_APP_PATH,VIDEO_ID,H5_LINK,
        SHOW_TIME_TYPE, START_TIME, END_TIME, BIRTHDAY_TIME, ENABLE_STATUS,EXPOSITORY_CASE,SEARCH_TXT,
        CREATE_TIME, CREATE_BY, UPDATE_BY, UPDATE_TIME, DEL_FLAG
    </sql>

    <select id="sourcePosList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from b_source_pos_template
        where DEL_FLAG=0
        <if test="id != null">
            and ID = #{id,jdbcType=VARCHAR}
        </if>
        <if test="appId != null">
            and APP_ID = #{appId,jdbcType=VARCHAR}
        </if>
        <if test="posManagerId != null">
            and POS_MANAGER_ID = #{posManagerId,jdbcType=VARCHAR}
        </if>
        <if test="showTimeType != null">
            and SHOW_TIME_TYPE = #{showTimeType}
        </if>
        <if test="position != null">
            and POSITION = #{position,jdbcType=VARCHAR}
        </if>
        <if test="titleTxt != null">
            and TITLE_TXT like concat( #{titleTxt,jdbcType=VARCHAR},'%')
        </if>
        <if test='status != null and status == "0"'>
            and ENABLE_STATUS = 0
            and #{nowDate,jdbcType=TIMESTAMP} <![CDATA[ < ]]>  START_TIME
        </if>
        <if test='status != null and status == "1"'>
            and ENABLE_STATUS = 0
            and #{nowDate,jdbcType=TIMESTAMP} <![CDATA[ >= ]]>  START_TIME
            and #{nowDate,jdbcType=TIMESTAMP} <![CDATA[ <= ]]>  END_TIME
        </if>
        <if test='status != null and status == "2"'>
            and (
            ENABLE_STATUS=1
            or #{nowDate,jdbcType=TIMESTAMP} <![CDATA[ > ]]>  END_TIME
            )
        </if>

        <if test='startTime != null and startTime !=""'>
            AND (
            START_TIME >= STR_TO_DATE(#{startTime,jdbcType=TIMESTAMP}, '%Y-%m-%d %H:%i:%s')
            OR
            END_TIME <![CDATA[ <= ]]> STR_TO_DATE(#{endTime,jdbcType=TIMESTAMP}, '%Y-%m-%d %H:%i:%s')
            )

        </if>
        order by CREATE_TIME desc
    </select>



    <select id="sourcePosListByTypeAndTime" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from b_source_pos_template
        where DEL_FLAG = 0
        and (
        ENABLE_STATUS = 0
        and #{nowDate,jdbcType=TIMESTAMP} <![CDATA[ <= ]]>  END_TIME
        )
        <if test="showTimeType != null">
            and SHOW_TIME_TYPE = #{showTimeType}
        </if>
        <if test="appId != null">
            and APP_ID = #{appId,jdbcType=VARCHAR}
        </if>
        <if test="position != null">
            and POSITION = #{position}
        </if>

        <if test="sortNo != null">
            and SORT_NO = #{sortNo}
        </if>

        <if test='startTime != null and startTime !=""'>
            AND (
            (START_TIME <![CDATA[ <= ]]> STR_TO_DATE(#{startTime,jdbcType=TIMESTAMP}, '%Y-%m-%d %H:%i:%s')
            AND END_TIME <![CDATA[ >= ]]> STR_TO_DATE(#{startTime,jdbcType=TIMESTAMP}, '%Y-%m-%d %H:%i:%s'))
            OR (START_TIME <![CDATA[ <= ]]> STR_TO_DATE(#{endTime,jdbcType=TIMESTAMP}, '%Y-%m-%d %H:%i:%s')
            AND END_TIME <![CDATA[ >= ]]> STR_TO_DATE(#{endTime,jdbcType=TIMESTAMP}, '%Y-%m-%d %H:%i:%s'))
            )
        </if>

    </select>
    <select id="getInvalidSourceList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from b_source_pos_template
        where DEL_FLAG=0 and ENABLE_STATUS = 0 and SHOW_TIME_TYPE=0
        and END_TIME <![CDATA[ < ]]>  NOW()
    </select>



    <update id="updateInvalidSourceList">
        update b_source_pos_template
        set ENABLE_STATUS = 1
        where ENABLE_STATUS = 0 and
        ID in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>


    <select id="getTakeEffectSourceList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from b_source_pos_template
        where DEL_FLAG=0 and ENABLE_STATUS = 0
        and APP_ID = #{appId,jdbcType=VARCHAR}
        and (
        START_TIME <![CDATA[ <= ]]> #{nowDate,jdbcType=TIMESTAMP}
        and
        END_TIME <![CDATA[ >= ]]> #{nowDate,jdbcType=TIMESTAMP}
        )
    </select>


    <select id="getSourceListByAppId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from b_source_pos_template
        where DEL_FLAG=0 and ENABLE_STATUS = 0
        and APP_ID = #{appId,jdbcType=VARCHAR}
    </select>



    <update id="updateAdaptPeopleById">
        update b_source_pos_template
        set
             ADAPT_PEOPLE_ID = null
            , ADAPT_PEOPLE_NAME = null
        where ID  = #{id}
    </update>


    <update id="updateAdaptStoreById">
        update b_source_pos_template
        set
            ADAPT_STORE_ID = null
            , ADAPT_STORE_NAME = null
            where ID  = #{id}
    </update>



</mapper>