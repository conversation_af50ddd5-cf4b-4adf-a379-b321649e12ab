package org.springcenter.marketing.modules.mapper;

import org.apache.ibatis.annotations.Param;
import org.springcenter.marketing.modules.model.BNewUserRightsCoupon;

import java.util.Date;
import java.util.List;

public interface BNewUserRightsCouponMapper {
    int deleteByPrimaryKey(String id);

    int insert(BNewUserRightsCoupon record);

    int insertSelective(BNewUserRightsCoupon record);

    BNewUserRightsCoupon selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(BNewUserRightsCoupon record);

    int updateByPrimaryKey(BNewUserRightsCoupon record);

    List<BNewUserRightsCoupon> selectRightsCouponByCardType(@Param("unionid") String unionid, @Param("status")Integer status,
                                                            @Param("isDel")Integer isDel, @Param("applicableParty")Integer applicableParty);

    void batchUpdateStatusByPrimaryKeys(@Param("ids") List<String> bNewUserRightCouponIds, @Param("status") Long status);

    /*
    多个有效的权益  正序 随便选一个
     */
    List<BNewUserRightsCoupon> selectAvialableUserRightsCouponByUnionId(@Param("unionid") String unionid,
                                                                        @Param("rightType") Integer rightType,
                                                                        @Param("bMemberCardId") String bMemberCardId,
                                                                        @Param("now") Date date,
                                                                        @Param("outNo") String outNo);


    List<BNewUserRightsCoupon> selectAllUserRightsCouponByUnionId(@Param("unionid") String unionid,
                                                                        @Param("rightType") Integer rightType,
                                                                        @Param("bMemberCardId") String bMemberCardId,
                                                                        @Param("now") Date date);

    List<BNewUserRightsCoupon> selectByConsumeOutNos(@Param("consumeOutNos") List<String> consumeOutNos);

    List<BNewUserRightsCoupon> selectUnSuccessSendOrSendNotUpdate();

    List<BNewUserRightsCoupon> selectByOutNo(@Param("outNo") String outNo);

    List<BNewUserRightsCoupon> selectByOutNos(@Param("outNos") List<String> outNos);

    void updateByConsumeOutNoAndStatus(BNewUserRightsCoupon update);
}