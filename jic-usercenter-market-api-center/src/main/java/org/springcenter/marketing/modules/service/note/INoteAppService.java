package org.springcenter.marketing.modules.service.note;

import com.jnby.common.Page;
import org.springcenter.marketing.api.dto.miniapp.*;

import java.util.List;

public interface INoteAppService {
    List<NoteListItemResp> listNotes(NoteListQueryReq req, Page page);

//    Integer getJoinDays(NoteJoinDaysReq req);
//
//    Integer getMyDraftCount(NoteDraftCountReq req);

    Boolean likeOrCancel(NoteBehaviorReq req);

    // 草稿箱相关方法
    Long saveDraft(NoteDraftSaveReq req);

    Boolean deleteDraft(NoteDraftDeleteReq req);

    List<NoteDraftListItemResp> listDrafts(NoteDraftListQueryReq req, Page page);

    NoteDraftDetailResp getDraftDetail(NoteDraftDetailReq req);
}

