package org.springcenter.marketing.modules.wxMapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.marketing.modules.model.PosRepairOrder;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【POS_REPAIR_ORDER】的数据库操作Mapper
* @createDate 2024-09-12 11:06:51
* @Entity generator.domain.PosRepairOrder
*/
public interface PosRepairOrderMapper{

    List<PosRepairOrder> selectByCardno(@Param("cardNo") String cardNo);


    List<Long> selectProductIdsByOrderId(@Param("orderId") Long orderId);
}




