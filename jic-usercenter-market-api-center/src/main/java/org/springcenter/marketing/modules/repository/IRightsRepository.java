package org.springcenter.marketing.modules.repository;

import org.springcenter.marketing.api.dto.GetRightsReq;
import org.springcenter.marketing.api.dto.RightsCreateReq;
import org.springcenter.marketing.api.dto.RightsResp;
import org.springcenter.marketing.modules.model.BRights;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 3/19/21 11:55 AM
 */
public interface IRightsRepository {

    /**
     * 创建
     * @param rights
     */
    BRights create(BRights rights);

    /**
     * 更新
     * @param rights
     */
    void update(BRights rights);

    /**
     * 查询列表
     * @param rights
     * @return
     */
    List<BRights> findAll(BRights rights);

    /**
     * 详情
     * @param id
     * @return
     */
    BRights findById(String id);

    /**
     * 处理如果是自定义商家id 则进行更新
     * @param requestData
     * @param rights
     */
    void dealStoreIds(RightsCreateReq requestData, BRights rights, Integer isStoreId);

    /**
     * 根据rightId查询绑定的商户ids
     * @param rightId
     * @return
     */
    List<String> findCustomerStroeIdsByRightId(String rightId);

    /**
     * 创建或者更新权益
     * @param requestData
     */
    void createOrUpdateRights(RightsCreateReq requestData);

    List<RightsResp> getRights(GetRightsReq requestData);

    List<BRights> findRights(BRights rights);

    List<BRights> findByIds(List<String> ids);

    void copyRightsById(String rightsId,String userId);


    public List<RightsResp> getRightsByRedisOrDataBase(GetRightsReq requestData);
}
