package org.springcenter.marketing.modules.integration.cache.Impl;


import org.apache.commons.lang3.RandomUtils;
import org.springcenter.marketing.api.enums.RedisKeysEnum;
import org.springcenter.marketing.modules.entity.CommentEntity;
import org.springcenter.marketing.modules.integration.cache.IPeopleCommunityCommentCacheService;
import org.springcenter.marketing.modules.model.PeopleCommunityComment;
import org.springcenter.marketing.modules.util.DateUtils;
import org.springcenter.marketing.modules.util.RedisService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 */
@Service
public class PeopleCommunityCommentCacheServiceImpl implements IPeopleCommunityCommentCacheService {
    @Resource
    private RedisService redisService;

    /**
     * 8小时
     */
    private static int timeCount = 60 * 1 * 60 * 8;


    @Override
    public CommentEntity getCommentList(String unionId, String brandId) {
        CommentEntity commentEntity = new CommentEntity();
        Object object = redisService.get(RedisKeysEnum.PEOPLE_COMMUNITY_COMMENT.join(brandId, unionId));
        if (object != null) {
            String value = object.toString();
            String[] strArr = value.split("\\u0001");
            List<PeopleCommunityComment> comments = new ArrayList<>();
            if(strArr[0].equals("3")){
                PeopleCommunityComment peopleCommunityComment = new PeopleCommunityComment();
                peopleCommunityComment.setUnionId(unionId);
                peopleCommunityComment.setType(3L);
                peopleCommunityComment.setNickName(strArr[1]);
                comments.add(peopleCommunityComment);
                commentEntity.setComments(comments);
                return commentEntity;
            }
            if(strArr[0].equals("2")){
                PeopleCommunityComment peopleCommunityComment = new PeopleCommunityComment();
                peopleCommunityComment.setUnionId(unionId);
                peopleCommunityComment.setType(2L);
                peopleCommunityComment.setNickName(strArr[1]);
                comments.add(peopleCommunityComment);
                commentEntity.setLikes(comments);
            }

            if(strArr[0].equals("4")){
                PeopleCommunityComment peopleCommunityComment = new PeopleCommunityComment();
                peopleCommunityComment.setUnionId(unionId);
                peopleCommunityComment.setType(4L);
                peopleCommunityComment.setNickName(strArr[1]);
                comments.add(peopleCommunityComment);
                commentEntity.setFocus(comments);
            }
        } else {
            String key = RedisKeysEnum.PEOPLE_COMMUNITY_COMMENT_RANDOM.join(brandId, unionId);
            Object random = redisService.get(key);
            if(random != null){
                commentEntity.setRandomCount("-1");
            }else{
                commentEntity.setRandomCount(String.valueOf(RandomUtils.nextInt(30, 99)));
            }
        }
        return commentEntity;
    }

    @Override
    public void delCommentList(String unionId, String brandId) {
          redisService.del(RedisKeysEnum.PEOPLE_COMMUNITY_COMMENT.join(brandId, unionId)); ;
    }

    @Override
    public Boolean clickComment(String unionId, String brandId) {
        String key = RedisKeysEnum.PEOPLE_COMMUNITY_COMMENT_RANDOM.join(brandId, unionId);
        Date now = new Date();
        String formatDate = DateUtils.formatDate(now, "yyyy-MM-dd");
        Date dayLastTime = DateUtils.parseDate(formatDate + " 23:59:59", "yyyy-MM-dd HH:mm:ss");
        long time = (dayLastTime.getTime() - now.getTime()) / 1000;
        redisService.set(key,1,time);
        return true;
    }
}
