package org.springcenter.marketing.modules.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 活动规则天数 奖品信息  
 * @TableName theme_activity_days_rule_award
 */
@Data
@TableName(value ="award_config")
public class AwardConfig implements Serializable {
    /**
     * 主键id
     */
    @TableId
    private Integer id;


    /**
     * 类型  0  内部券   1  微信券  2  积分  3 内部商品  4 外部商品
     */
    @TableField("award_type")
    private Integer awardType;

    /**
     * 奖励名称
     */
    @TableField("award_name")
    private String awardName;

    /**
     * 奖励编码  券则为券awardId  微信为微信券id  积分为积分数量   内部商品为 productCode   外部商品不定
     */
    @TableField("award_code")
    private String awardCode;

    /**
     * 
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 0 正常  1 删除
     */
    @TableField("is_del")
    private Integer isDel;

    /**
     * 发放个数
     */
    @TableField("award_num")
    private Integer awardNum;

    /**
     * 奖励标题
     */
    @TableField(value = "award_title")
    private String awardTitle;

    /**
     * 奖励副标题
     */
    @TableField(value = "award_subtitle")
    private String awardSubtitle;

    @TableField(value = "img")
    private String img;
    /**
     * 库存数量
     */
    @TableField(value = "qty")
    private Integer qty;


}