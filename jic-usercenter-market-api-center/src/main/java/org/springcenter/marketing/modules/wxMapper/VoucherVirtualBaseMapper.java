package org.springcenter.marketing.modules.wxMapper;

import org.apache.ibatis.annotations.Param;
import org.springcenter.marketing.modules.model.VoucherVirtualBase;

import java.math.BigDecimal;
import java.util.List;

public interface VoucherVirtualBaseMapper {
    int deleteByPrimaryKey(BigDecimal id);

    int insert(VoucherVirtualBase record);

    int insertSelective(VoucherVirtualBase record);

    VoucherVirtualBase selectByPrimaryKey(BigDecimal id);

    int updateByPrimaryKeySelective(VoucherVirtualBase record);

    int updateByPrimaryKey(VoucherVirtualBase record);

    VoucherVirtualBase selectByLinkNo(@Param("linkNo") String linkNo);

    List<VoucherVirtualBase> selectByExmemo(@Param("exmemo") String exmemo);

    List<VoucherVirtualBase> selectListByCreateTime(@Param("openId") String openId);
}