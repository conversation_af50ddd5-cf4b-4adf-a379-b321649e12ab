/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springcenter.marketing.modules.wxMapper;

import org.apache.ibatis.annotations.Param;

/**
 * 文件名: org.springcenter.marketing.modules.wxMapper-LuckyDrawUserMapper.java
 * 文件简介:
 *
 * <AUTHOR>
 * @date 2025/5/30 14:58
 *       Copyright (c) 1994 JNBY. All Rights Reserved
 */
public interface LuckyDrawUserMapper {

    int selectCountByUnionId(@Param("luckyId") Long luckyId, @Param("unionId") String unionId);

    int selectCountByOpenId(@Param("luckyId") Long luckyId, @Param("openId") String openId);
}
