<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.marketing.modules.wxMapper.JicShareBaseConfMapper">


  <select id="selectByIds" resultType="org.springcenter.marketing.modules.model.JicShareBaseConf">
    select id as id , uuid as uuid from JIC_SHARE_BASE_CONF where id in
                                                                  <foreach collection="ids" separator="," item="item" close=")" open="(">
                                                                    #{item}
                                                                  </foreach>

  </select>
</mapper>