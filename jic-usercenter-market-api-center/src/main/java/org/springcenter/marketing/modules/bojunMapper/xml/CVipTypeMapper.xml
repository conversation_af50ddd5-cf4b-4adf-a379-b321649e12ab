<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.marketing.modules.bojunMapper.CVipTypeMapper">
  <resultMap id="BaseResultMap" type="org.springcenter.marketing.modules.model.CVipType">
    <id column="ID" jdbcType="DECIMAL" property="id" />
    <result column="AD_CLIENT_ID" jdbcType="DECIMAL" property="adClientId" />
    <result column="AD_ORG_ID" jdbcType="DECIMAL" property="adOrgId" />
    <result column="ISACTIVE" jdbcType="CHAR" property="isactive" />
    <result column="MODIFIERID" jdbcType="DECIMAL" property="modifierid" />
    <result column="CREATIONDATE" jdbcType="TIMESTAMP" property="creationdate" />
    <result column="MODIFIEDDATE" jdbcType="TIMESTAMP" property="modifieddate" />
    <result column="OWNERID" jdbcType="DECIMAL" property="ownerid" />
    <result column="NAME" jdbcType="VARCHAR" property="name" />
    <result column="DESCRIPTION" jdbcType="VARCHAR" property="description" />
    <result column="DISCOUNT" jdbcType="DECIMAL" property="discount" />
    <result column="INTEGRALRATE" jdbcType="DECIMAL" property="integralrate" />
    <result column="REDISCOUNT" jdbcType="DECIMAL" property="rediscount" />
    <result column="CANUPGRADE" jdbcType="CHAR" property="canupgrade" />
    <result column="C_VIPTYPEUP_ID" jdbcType="DECIMAL" property="cViptypeupId" />
    <result column="NEEDINTG" jdbcType="DECIMAL" property="needintg" />
    <result column="CHECKOFFINTG" jdbcType="DECIMAL" property="checkoffintg" />
    <result column="DEFAULTVALID" jdbcType="DECIMAL" property="defaultvalid" />
    <result column="DBINTDAY" jdbcType="CHAR" property="dbintday" />
    <result column="DBINTDNUM" jdbcType="DECIMAL" property="dbintdnum" />
    <result column="DBINTMON" jdbcType="CHAR" property="dbintmon" />
    <result column="DBINTMNUM" jdbcType="DECIMAL" property="dbintmnum" />
    <result column="CODE" jdbcType="VARCHAR" property="code" />
    <result column="INTL_ONCE_UP" jdbcType="DECIMAL" property="intlOnceUp" />
    <result column="INTL_HALFYEAR_UP" jdbcType="DECIMAL" property="intlHalfyearUp" />
    <result column="INTL_YEAR_UP" jdbcType="DECIMAL" property="intlYearUp" />
    <result column="INTL_ONCE_LST" jdbcType="DECIMAL" property="intlOnceLst" />
    <result column="INTL_HALFYEAR_LST" jdbcType="DECIMAL" property="intlHalfyearLst" />
    <result column="INTL_YEAR_LST" jdbcType="DECIMAL" property="intlYearLst" />
    <result column="INTL_GREATER_OFF" jdbcType="DECIMAL" property="intlGreaterOff" />
    <result column="INTL_SMALLER_OFF" jdbcType="DECIMAL" property="intlSmallerOff" />
    <result column="INTL_TWOYEAR_UP" jdbcType="DECIMAL" property="intlTwoyearUp" />
    <result column="INTL_TWOYEAR_LST" jdbcType="DECIMAL" property="intlTwoyearLst" />
    <result column="INTL_TYGREATER_OFF" jdbcType="DECIMAL" property="intlTygreaterOff" />
    <result column="INTL_TYSMALLER_OFF" jdbcType="DECIMAL" property="intlTysmallerOff" />
    <result column="DEFAULTVALID_INTG" jdbcType="DECIMAL" property="defaultvalidIntg" />
    <result column="INTL_ONCE_UP2" jdbcType="DECIMAL" property="intlOnceUp2" />
    <result column="INTL_YEAR_UP2" jdbcType="DECIMAL" property="intlYearUp2" />
    <result column="INTL_TWOYEAR_UP2" jdbcType="DECIMAL" property="intlTwoyearUp2" />
    <result column="INTL_GREATER_OFF2" jdbcType="DECIMAL" property="intlGreaterOff2" />
    <result column="INTL_SMALLER_OFF2" jdbcType="DECIMAL" property="intlSmallerOff2" />
    <result column="INTL_TYGREATER_OFF2" jdbcType="DECIMAL" property="intlTygreaterOff2" />
    <result column="INTL_TYSMALLER_OFF2" jdbcType="DECIMAL" property="intlTysmallerOff2" />
    <result column="DEFAULTVALID_UPINTG" jdbcType="DECIMAL" property="defaultvalidUpintg" />
    <result column="BRITHDISATM" jdbcType="DECIMAL" property="brithdisatm" />
    <result column="BRITHDAYDIS" jdbcType="DECIMAL" property="brithdaydis" />
    <result column="ISSHARE_BIRTHDAY" jdbcType="DECIMAL" property="isshareBirthday" />
    <result column="VALIDDAILY_INTG" jdbcType="DECIMAL" property="validdailyIntg" />
    <result column="VALIDDAILY_UPINTG" jdbcType="DECIMAL" property="validdailyUpintg" />
    <result column="VALID_DAILY" jdbcType="DECIMAL" property="validDaily" />
    <result column="XKVALIDDAILY_UPINTG" jdbcType="DECIMAL" property="xkvaliddailyUpintg" />
    <result column="XKVALIDDAILY_INTG" jdbcType="DECIMAL" property="xkvaliddailyIntg" />
    <result column="XKVALID_DAILY" jdbcType="DECIMAL" property="xkvalidDaily" />
    <result column="INTEGRAL_UPDEAL" jdbcType="DECIMAL" property="integralUpdeal" />
    <result column="INTEGRAL_DEAL" jdbcType="CHAR" property="integralDeal" />
    <result column="IFCHARGE" jdbcType="CHAR" property="ifcharge" />
    <result column="IS_LIMITMININTL" jdbcType="CHAR" property="isLimitminintl" />
    <result column="DE_VALIDDAILY_OFFINTG" jdbcType="DECIMAL" property="deValiddailyOffintg" />
    <result column="DE_VALIDDAILY_INTG" jdbcType="DECIMAL" property="deValiddailyIntg" />
    <result column="DE_VALIDDAILY_OFF" jdbcType="DECIMAL" property="deValiddailyOff" />
    <result column="IS_PASSWORD_CHECK" jdbcType="CHAR" property="isPasswordCheck" />
    <result column="BRITHDAYQTY" jdbcType="DECIMAL" property="brithdayqty" />
    <result column="MON1" jdbcType="DECIMAL" property="mon1" />
    <result column="INTL_MON1_UP" jdbcType="DECIMAL" property="intlMon1Up" />
    <result column="MON2" jdbcType="DECIMAL" property="mon2" />
    <result column="INTL_MON2_UP" jdbcType="DECIMAL" property="intlMon2Up" />
    <result column="IS_LIMITMIN" jdbcType="CHAR" property="isLimitmin" />
    <result column="WECHATFANS" jdbcType="CHAR" property="wechatfans" />
    <result column="IS_AUTOACTIVE" jdbcType="CHAR" property="isAutoactive" />
    <result column="IS_SMSCODE" jdbcType="CHAR" property="isSmscode" />
    <result column="ISSCAN" jdbcType="CHAR" property="isscan" />
    <result column="DISCOUNT_LIMIT" jdbcType="DECIMAL" property="discountLimit" />
    <result column="C_VIP_DALEI" jdbcType="DECIMAL" property="cVipDalei" />
    <result column="C_CONSUMEAREA_ID" jdbcType="DECIMAL" property="cConsumeareaId" />
    <result column="SUBACCOUNT_DEFAULT_TYPE" jdbcType="CHAR" property="subaccountDefaultType" />
    <result column="IS_LIST_LIMIT" jdbcType="CHAR" property="isListLimit" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, AD_CLIENT_ID, AD_ORG_ID, ISACTIVE, MODIFIERID, CREATIONDATE, MODIFIEDDATE, OWNERID, 
    NAME, DESCRIPTION, DISCOUNT, INTEGRALRATE, REDISCOUNT, CANUPGRADE, C_VIPTYPEUP_ID, 
    NEEDINTG, CHECKOFFINTG, DEFAULTVALID, DBINTDAY, DBINTDNUM, DBINTMON, DBINTMNUM, CODE, 
    INTL_ONCE_UP, INTL_HALFYEAR_UP, INTL_YEAR_UP, INTL_ONCE_LST, INTL_HALFYEAR_LST, INTL_YEAR_LST, 
    INTL_GREATER_OFF, INTL_SMALLER_OFF, INTL_TWOYEAR_UP, INTL_TWOYEAR_LST, INTL_TYGREATER_OFF, 
    INTL_TYSMALLER_OFF, DEFAULTVALID_INTG, INTL_ONCE_UP2, INTL_YEAR_UP2, INTL_TWOYEAR_UP2, 
    INTL_GREATER_OFF2, INTL_SMALLER_OFF2, INTL_TYGREATER_OFF2, INTL_TYSMALLER_OFF2, DEFAULTVALID_UPINTG, 
    BRITHDISATM, BRITHDAYDIS, ISSHARE_BIRTHDAY, VALIDDAILY_INTG, VALIDDAILY_UPINTG, VALID_DAILY, 
    XKVALIDDAILY_UPINTG, XKVALIDDAILY_INTG, XKVALID_DAILY, INTEGRAL_UPDEAL, INTEGRAL_DEAL, 
    IFCHARGE, IS_LIMITMININTL, DE_VALIDDAILY_OFFINTG, DE_VALIDDAILY_INTG, DE_VALIDDAILY_OFF, 
    IS_PASSWORD_CHECK, BRITHDAYQTY, MON1, INTL_MON1_UP, MON2, INTL_MON2_UP, IS_LIMITMIN, 
    WECHATFANS, IS_AUTOACTIVE, IS_SMSCODE, ISSCAN, DISCOUNT_LIMIT, C_VIP_DALEI, C_CONSUMEAREA_ID, 
    SUBACCOUNT_DEFAULT_TYPE, IS_LIST_LIMIT
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap">
    select INTL_ONCE_UP , id , INTL_MON1_UP  from C_VIPTYPE where ID = #{id}
  </select>
    <select id="selectByBrandIdAndLevel" resultType="java.lang.Integer">
        select CAS_C_VIPTYPE_ID from C_VIPTYPE_STORE_REF where isactive = 'Y' and BRANDID = #{brandId} and c_viptype_sort = #{vipLevel}
    </select>

</mapper>