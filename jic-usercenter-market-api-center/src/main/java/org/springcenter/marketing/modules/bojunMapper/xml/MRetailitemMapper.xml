<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.marketing.modules.bojunMapper.MRetailitemMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.springcenter.marketing.modules.model.MRetailitem">
        <id column="ID" property="id" />
        <result column="AD_CLIENT_ID" property="adClientId" />
        <result column="AD_ORG_ID" property="adOrgId" />
        <result column="ISACTIVE" property="isactive" />
        <result column="CREATIONDATE" property="creationdate" />
        <result column="OWNERID" property="ownerid" />
        <result column="MODIFIEDDATE" property="modifieddate" />
        <result column="MODIFIERID" property="modifierid" />
        <result column="M_RETAIL_ID" property="mRetailId" />
        <result column="ORDERNO" property="orderno" />
        <result column="C_VIP_ID" property="cVipId" />
        <result column="SALESREP_ID" property="salesrepId" />
        <result column="M_PRODUCT_ID" property="mProductId" />
        <result column="M_ATTRIBUTESETINSTANCE_ID" property="mAttributesetinstanceId" />
        <result column="QTY" property="qty" />
        <result column="PRICELIST" property="pricelist" />
        <result column="PRICEACTUAL" property="priceactual" />
        <result column="DISCOUNT" property="discount" />
        <result column="DESCRIPTION" property="description" />
        <result column="TOT_AMT_LIST" property="totAmtList" />
        <result column="TOT_AMT_ACTUAL" property="totAmtActual" />
        <result column="STATUS" property="status" />
        <result column="TYPE" property="type" />
        <result column="ORGDOCNO" property="orgdocno" />
        <result column="MARKBALTYPE" property="markbaltype" />
        <result column="M_PRODUCTALIAS_ID" property="mProductaliasId" />
        <result column="C_MARKBALTYPE_ID" property="cMarkbaltypeId" />
        <result column="OLD_TRACE" property="oldTrace" />
        <result column="OLD_AUTHCODE" property="oldAuthcode" />
        <result column="OLD_DATETIME" property="oldDatetime" />
        <result column="OLD_FLAG" property="oldFlag" />
        <result column="OLD_REMARK" property="oldRemark" />
        <result column="HANDDIS" property="handdis" />
        <result column="INTEGRAL" property="integral" />
        <result column="SIG_TDEFPOSDIS_ID" property="sigTdefposdisId" />
        <result column="COM_TDEFPOSDIS_ID" property="comTdefposdisId" />
        <result column="C_INTEGRALMIN_ID" property="cIntegralminId" />
        <result column="B_RETAILDISSKU_ID" property="bRetaildisskuId" />
        <result column="ORG_M_RETAILITEM_ID" property="orgMRetailitemId" />
        <result column="RETURNQTY" property="returnqty" />
        <result column="RQTY" property="rqty" />
        <result column="SALESREP_ID2" property="salesrepId2" />
        <result column="SALESREP_ID3" property="salesrepId3" />
        <result column="SALESREPS_NAME" property="salesrepsName" />
        <result column="SALESREPS_ID" property="salesrepsId" />
        <result column="C_VIPBIRDIS_ID" property="cVipbirdisId" />
        <result column="IS_INTEGRAL" property="isIntegral" />
        <result column="NC_STATUS" property="ncStatus" />
        <result column="NC_STATUSTIME" property="ncStatustime" />
        <result column="NC_STATUSERID" property="ncStatuserid" />
        <result column="M_RETAILITEM_ID" property="mRetailitemId" />
        <result column="RCANQTY" property="rcanqty" />
        <result column="EB_SO_ID" property="ebSoId" />
        <result column="WEBPOS_RETREASON_ID" property="webposRetreasonId" />
        <result column="SALESREPS_RATE" property="salesrepsRate" />
        <result column="SET_STATUS" property="setStatus" />
        <result column="SETERID" property="seterid" />
        <result column="SETTIME" property="settime" />
        <result column="TBQTY" property="tbqty" />
        <result column="RETTBQTY" property="rettbqty" />
        <result column="RETQTY" property="retqty" />
        <result column="NC_ZGSTATUS" property="ncZgstatus" />
        <result column="SIG_TDEFPOSDIS_MOREID" property="sigTdefposdisMoreid" />
        <result column="COM_TDEFPOSDIS_MOREID" property="comTdefposdisMoreid" />
        <result column="B_RETAILDISSKU_MOREID" property="bRetaildisskuMoreid" />
        <result column="IS_EXC" property="isExc" />
        <result column="M_DIM1_ID" property="mDim1Id" />
        <result column="MASTERCODE_PDT" property="mastercodePdt" />
        <result column="ACTUALLY_PRICE" property="actuallyPrice" />
        <result column="SALESREP_NAME" property="salesrepName" />
        <result column="C_DISPLAY_AREA_ID" property="cDisplayAreaId" />
        <result column="C_DISPLAY_TYPE_NAME" property="cDisplayTypeName" />
        <result column="IS_CLOUD" property="isCloud" />
        <result column="USEINTEGRAL" property="useintegral" />
        <result column="IS_SWAP" property="isSwap" />
        <result column="C_VIPBIRDISPROITEM_ID" property="cVipbirdisproitemId" />
        <result column="PROMOTION" property="promotion" />
        <result column="PRICEACTUAL_WITHOUTTAX" property="priceactualWithouttax" />
        <result column="C_STORE_LOCATION_ID" property="cStoreLocationId" />
        <result column="TAXRATE" property="taxrate" />
        <result column="VOU_NO" property="vouNo" />
        <result column="IS_MART_READ" property="isMartRead" />
        <result column="INTEGRAL_NEW" property="integralNew" />
        <result column="NUMEB" property="numeb" />
        <result column="MODIFYAMT_REASON_ID" property="modifyamtReasonId" />
        <result column="PICOUPONDIS" property="picoupondis" />
        <result column="IS_CHECK" property="isCheck" />
        <result column="OFF_PERCENT" property="offPercent" />
        <result column="VIRTUALPRICE" property="virtualprice" />
        <result column="ACTUALPRICE" property="actualprice" />
        <result column="DELIVERYDATE" property="deliverydate" />
        <result column="CHANGEPRICE_USERID" property="changepriceUserid" />
        <result column="CNY_TOT_AMT_ACTUAL" property="cnyTotAmtActual" />
        <result column="ISEXCHANGE" property="isexchange" />
        <result column="FASTNO_PRICE" property="fastnoPrice" />
        <result column="COMMON01" property="common01" />
        <result column="C_REALNAME" property="cRealname" />
        <result column="C_REALSTORE" property="cRealstore" />
        <result column="C_INVENTORY" property="cInventory" />
        <result column="IS_EXPRESS" property="isExpress" />
        <result column="COMMON03" property="common03" />
        <result column="COMMON02" property="common02" />
        <result column="PRICESETTLE" property="pricesettle" />
        <result column="TOT_PASSENGER_AMT" property="totPassengerAmt" />
        <result column="CUSTOM_REASON_ID" property="customReasonId" />
        <result column="DELIVERYTYPE" property="deliverytype" />
        <result column="ISBLESSING" property="isblessing" />
        <result column="UNTAXEDPRICE" property="untaxedprice" />
        <result column="TAX_AMOUNT1" property="taxAmount1" />
        <result column="TAX_AMOUNT2" property="taxAmount2" />
        <result column="TAXRATE1" property="taxrate1" />
        <result column="TAXRATE2" property="taxrate2" />
        <result column="DISCOUNTAMT" property="discountamt" />
        <result column="WEIGHT" property="weight" />
        <result column="UNIT" property="unit" />
        <result column="INTEGRAL_VALUE" property="integralValue" />
        <result column="LINENO" property="lineno" />
        <result column="SUBACCOUNT_ID" property="subaccountId" />
        <result column="ENTERING_REASON_ID" property="enteringReasonId" />
        <result column="AMOUNTAFTERDIS" property="amountafterdis" />
        <result column="AMOUNTOFUSEDGOODS" property="amountofusedgoods" />
        <result column="RESERVEPAYCODE" property="reservepaycode" />
        <result column="DELIVERYTIME" property="deliverytime" />
        <result column="INSTALLMAINTENANCE" property="installmaintenance" />
        <result column="DOORREMARKS" property="doorremarks" />
        <result column="SALECODE" property="salecode" />
        <result column="VOU_DISCOUNTAM" property="vouDiscountam" />
        <result column="BATCH_NO" property="batchNo" />
        <result column="PRODUCTION_DATE" property="productionDate" />
        <result column="SHELFLIFE_NO" property="shelflifeNo" />
        <result column="IS_REVERSE_O2O" property="isReverseO2o" />
        <result column="AUTHORREALNAME" property="authorrealname" />
        <result column="AUTHOREMAIL" property="authoremail" />
        <result column="ISDROPPRICE" property="isdropprice" />
        <result column="EANCODE" property="eancode" />
        <result column="DM_PRO_ENAME" property="dmProEname" />
        <result column="MALLCARD" property="mallcard" />
        <result column="DISCOUNTAMTLIST" property="discountamtlist" />
        <result column="IS_DEL_SALE" property="isDelSale" />
        <result column="SKU_GROUP_NO" property="skuGroupNo" />
        <result column="MARKET_RATE" property="marketRate" />
        <result column="INTSCODE" property="intscode" />
        <result column="M_RETAIL_DISCOUNTACTIVITY" property="mRetailDiscountactivity" />
        <result column="no" property="no" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, AD_CLIENT_ID, AD_ORG_ID, ISACTIVE, CREATIONDATE, OWNERID, MODIFIEDDATE, MODIFIERID, M_RETAIL_ID, ORDERNO, C_VIP_ID, SALESREP_ID, M_PRODUCT_ID, M_ATTRIBUTESETINSTANCE_ID, QTY, PRICELIST, PRICEACTUAL, DISCOUNT, DESCRIPTION, TOT_AMT_LIST, TOT_AMT_ACTUAL, STATUS, TYPE, ORGDOCNO, MARKBALTYPE, M_PRODUCTALIAS_ID, C_MARKBALTYPE_ID, OLD_TRACE, OLD_AUTHCODE, OLD_DATETIME, OLD_FLAG, OLD_REMARK, HANDDIS, INTEGRAL, SIG_TDEFPOSDIS_ID, COM_TDEFPOSDIS_ID, C_INTEGRALMIN_ID, B_RETAILDISSKU_ID, ORG_M_RETAILITEM_ID, RETURNQTY, RQTY, SALESREP_ID2, SALESREP_ID3, SALESREPS_NAME, SALESREPS_ID, C_VIPBIRDIS_ID, IS_INTEGRAL, NC_STATUS, NC_STATUSTIME, NC_STATUSERID, M_RETAILITEM_ID, RCANQTY, EB_SO_ID, WEBPOS_RETREASON_ID, SALESREPS_RATE, SET_STATUS, SETERID, SETTIME, TBQTY, RETTBQTY, RETQTY, NC_ZGSTATUS, SIG_TDEFPOSDIS_MOREID, COM_TDEFPOSDIS_MOREID, B_RETAILDISSKU_MOREID, IS_EXC, M_DIM1_ID, MASTERCODE_PDT, ACTUALLY_PRICE, SALESREP_NAME, C_DISPLAY_AREA_ID, C_DISPLAY_TYPE_NAME, IS_CLOUD, USEINTEGRAL, IS_SWAP, C_VIPBIRDISPROITEM_ID, PROMOTION, PRICEACTUAL_WITHOUTTAX, C_STORE_LOCATION_ID, TAXRATE, VOU_NO, IS_MART_READ, INTEGRAL_NEW, NUMEB, MODIFYAMT_REASON_ID, PICOUPONDIS, IS_CHECK, OFF_PERCENT, VIRTUALPRICE, ACTUALPRICE, DELIVERYDATE, CHANGEPRICE_USERID, CNY_TOT_AMT_ACTUAL, ISEXCHANGE, FASTNO_PRICE, COMMON01, C_REALNAME, C_REALSTORE, C_INVENTORY, IS_EXPRESS, COMMON03, COMMON02, PRICESETTLE, TOT_PASSENGER_AMT, CUSTOM_REASON_ID, DELIVERYTYPE, ISBLESSING, UNTAXEDPRICE, TAX_AMOUNT1, TAX_AMOUNT2, TAXRATE1, TAXRATE2, DISCOUNTAMT, WEIGHT, UNIT, INTEGRAL_VALUE, LINENO, SUBACCOUNT_ID, ENTERING_REASON_ID, AMOUNTAFTERDIS, AMOUNTOFUSEDGOODS, RESERVEPAYCODE, DELIVERYTIME, INSTALLMAINTENANCE, DOORREMARKS, SALECODE, VOU_DISCOUNTAM, BATCH_NO, PRODUCTION_DATE, SHELFLIFE_NO, IS_REVERSE_O2O, AUTHORREALNAME, AUTHOREMAIL, ISDROPPRICE, EANCODE, DM_PRO_ENAME, MALLCARD, DISCOUNTAMTLIST, IS_DEL_SALE, SKU_GROUP_NO, MARKET_RATE, INTSCODE, M_RETAIL_DISCOUNTACTIVITY
    </sql>
    <select id="selectDataByMreatilIdAndProductId"
           resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include> from M_RETAILITEM
        where
        M_RETAIL_ID in
        <foreach collection="mRetailIds" open=" (" close=" ) " item="item" separator=",">
            #{item}
        </foreach>
        and M_PRODUCTALIAS_ID = #{productId}



    </select>
    <select id="selectCanRefundItemByMRetailIds" resultMap="BaseResultMap">
        select b.no,-a.qty qty,a.m_retail_id,a.id from m_retailitem a,m_product_alias b
           where a.m_retail_id  in
            <foreach collection="list" separator="," open="(" close=")" item="item">
                #{item}
            </foreach>
            and a.m_productalias_id = b.id and a.qty &lt; 0


    </select>
    <select id="selectRefundDataByDocNo" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include> from m_retailitem
        where ORGDOCNO = #{docNo}
        and type = 2
        and ISACTIVE = 'Y'
    </select>
    <select id="selectByMretailId" resultMap="BaseResultMap">
        select M_PRODUCT_ID,ID, TOT_AMT_ACTUAL, RCANQTY, PRICEACTUAL from M_RETAILITEM where ISACTIVE = 'Y' and M_RETAIL_ID = #{mretailid}
    </select>


</mapper>