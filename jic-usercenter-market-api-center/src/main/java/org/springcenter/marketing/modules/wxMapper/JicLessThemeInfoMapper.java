package org.springcenter.marketing.modules.wxMapper;

import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface JicLessThemeInfoMapper {
    /**
     * 获取活动id
     * @param brandId
     * @param storeCode
     * @return
     */
    List<String> geLeiIdList(@Param("brandId") String brandId,@Param("storeCode") String storeCode);



    List<String> geLeiIdListByBrandId(@Param("brandId") String brandId);



    List<String> geLeiIdListByStoreCode(@Param("storeCode") String storeCode);


    /**
     * 获取礼品卡开关
     * @param brandId
     * @return
     */
    List<String> getGiftSwitch(@Param("brandId") String brandId);
}
