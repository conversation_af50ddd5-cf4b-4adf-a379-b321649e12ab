<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.marketing.modules.bojunMapper.CVoucherStoreMapper">
        <insert id="insertSelective">
            INSERT INTO C_VOUCHERS_STORE
            (id,
             AD_CLIENT_ID,
             AD_ORG_ID,
             C_VOUCHERS_ID,
             C_STORE_ID,
             OWNERID,
             MODIFIERID,
             CREATIONDATE,
             MODIFIEDDATE,
             ISACTIVE)
            values ( get_sequences('c_vouchers_store'),
                   37,
                   27,
                   #{vouchersId},
                   #{storeId},
                   42293,
                   42293,
                   SYSDATE,
                   SYSDATE,
                   'Y')

        </insert>
        <update id="updateIsActiveByIds">
            update     c_vouchers_store set ISACTIVE = 'N' where id in
                                                                 <foreach collection="ids" open="(" close=")" item="item" separator=",">
                                                                         #{item}
                                                                 </foreach>

        </update>


        <select id="selectByCVouhcerId" resultType="java.lang.Long">
            select id from  c_vouchers_store where c_vouchers_id =#{voucherId} and ISACTIVE = 'Y'

        </select>
</mapper>
