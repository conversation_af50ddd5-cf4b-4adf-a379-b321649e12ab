package org.springcenter.marketing.modules.domain.badge.repository;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.google.common.base.Preconditions;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springcenter.marketing.modules.convert.BadgeConvert;
import org.springcenter.marketing.modules.domain.badge.entity.Badge;
import org.springcenter.marketing.modules.mapper.BadgeDetailMapper;
import org.springcenter.marketing.modules.model.BadgeDetail;
import org.springframework.aop.framework.AopContext;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

/**
 * 勋章仓储实现
 */
@Slf4j
@Repository
public class BadgeRepositoryImpl implements BadgeRepository {

    @Resource
    private BadgeDetailMapper badgeDetailMapper;

    @Cacheable(cacheNames = "market-redis-cache", key = "'badge:findActiveBadges'")
    @Override
    public List<Badge> findActiveBadges() {
        log.info("未命中缓存 查询所有勋章详情");
        // 查询有效的勋章
        Date now = new Date();
        LambdaQueryWrapper<BadgeDetail> query = new LambdaQueryWrapper<>();
        query.le(BadgeDetail::getStartTime, now)
                .ge(BadgeDetail::getEndTime, now)
                .eq(BadgeDetail::getIsDelete, false);

        List<BadgeDetail> badgeDetails = badgeDetailMapper.selectList(query);
        return convertToDomainEntities(badgeDetails);
    }

    @Cacheable(cacheNames = "market-redis-cache", key = "'badge:findAllStartedBadges'")
    @Override
    public List<Badge> findAllStartedBadges() {
        log.info("未命中缓存 查询所有未删除且已开始的勋章");
        // 查询所有未删除且已开始的勋章(包括进行中和已结束)
        Date now = new Date();
        LambdaQueryWrapper<BadgeDetail> query = new LambdaQueryWrapper<>();
        query.le(BadgeDetail::getStartTime, now) // 开始时间小于等于当前时间（已开始）
                .eq(BadgeDetail::getIsDelete, false);

        List<BadgeDetail> badgeDetails = badgeDetailMapper.selectList(query);
        return convertToDomainEntities(badgeDetails);
    }

    @Caching(evict={
            @CacheEvict(cacheNames = "market-redis-cache",key="'badge:findActiveBadges'"),
            @CacheEvict(cacheNames = "market-redis-cache",key="'badge:findAllStartedBadges'"),
            @CacheEvict(cacheNames = "market-redis-cache", key = "'badge:findByBizId:'+'*'", allEntries = true)
    })
    public void flushAllBadgesCache() {
        log.info("清空全部勋章的缓存");
    }

    @Cacheable(cacheNames = "market-redis-cache", key = "'badge:findByBizId:'+#bizId", unless="#result == null")
    @Override
    public Badge findByBizId(String bizId) {
        log.info("未命中缓存 查询勋章详情 勋章id:[{}]", bizId);
        LambdaQueryWrapper<BadgeDetail> query = new LambdaQueryWrapper<>();
        query.eq(BadgeDetail::getBizId, bizId)
                .eq(BadgeDetail::getIsDelete, false);

        BadgeDetail badgeDetail = badgeDetailMapper.selectOne(query);
        return BadgeConvert.INSTANCE.model2DomainEntity(badgeDetail);
    }

    /**
     * 批量转换实体
     */
    private List<Badge> convertToDomainEntities(List<BadgeDetail> badgeDetails) {
        if (badgeDetails == null) {
            return new ArrayList<>();
        }
        return badgeDetails.stream()
                .map(BadgeConvert.INSTANCE::model2DomainEntity)
                .collect(Collectors.toList());
    }

    @Override
    public List<Badge> listByBizIds(List<String> badgeBizIds) {
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(badgeBizIds), "bizIds不能为空");
        List<Badge>  badgeDetailList = new ArrayList<>();
        badgeBizIds.forEach(bizId -> {
            badgeDetailList.add(((BadgeRepositoryImpl) AopContext.currentProxy()).findByBizId(bizId));
        });
        return badgeDetailList;
//        LambdaQueryWrapper<BadgeDetail> query = new LambdaQueryWrapper<>();
//        query.in(BadgeDetail::getBizId, badgeBizIds)
//                .eq(BadgeDetail::getIsDelete, false);
//        List<BadgeDetail> badgeDetails = badgeDetailMapper.selectList(query);
//        return convertToDomainEntities(badgeDetails);
    }
}