package org.springcenter.marketing.modules.integration.cache.Impl;

import com.alibaba.fastjson.JSONObject;
import org.springcenter.marketing.api.dto.GetAssetsMenuResp;
import org.springcenter.marketing.api.enums.RedisKeysEnum;
import org.springcenter.marketing.modules.integration.cache.IMenuCacheService;
import org.springcenter.marketing.modules.service.IMenuService;
import org.springcenter.marketing.modules.util.RedisService;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class MenuCacheServiceImpl implements IMenuCacheService {

    @Resource
    private IMenuService iMenuService;


    @Resource
    private RedisService redisService;

    /**
     * 24小时
     */
    private static int timeCount = 60 * 1 * 60 * 24;



    @Override
    public Boolean getAssetsMenu(String brandId) {
        Boolean flag = iMenuService.getAssetsMenu(brandId);
        return flag;
    }


    /**
     * 通配符 删除缓存
     */
    @Override
    @CacheEvict(cacheNames = "market-redis-cache", key = "'AssetsMenu:'+'*'", allEntries = true)
    public void clearAssetsMenuCache(){

    }

    /**
     * 指定缓存
     * @param brandId
     */
    @Override
    @Caching(evict={@CacheEvict(cacheNames = "market-redis-cache",key="'AssetsMenu:'+#brandId")})
    public void clearAssetsMenuCacheById(String brandId) {

    }

    @Override
    @Cacheable(cacheNames = "market-redis-cache",key = "'AssetsMenu:'+#brandId")
    public GetAssetsMenuResp getAllAssetsMenu(String brandId) {
        GetAssetsMenuResp getAssetsMenuResp = iMenuService.getAllAssetsMenu(brandId);
        return getAssetsMenuResp;
    }
}
