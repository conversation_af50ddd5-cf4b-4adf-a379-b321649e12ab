package org.springcenter.marketing.modules.bojunMapper;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springcenter.marketing.modules.model.CVipType;

import java.util.List;

public interface CVipTypeMapper {

    @Select("select id, name, description, discount from c_viptype where isactive = 'Y'")
    List<CVipType> selectAll();


    CVipType selectByPrimaryKey(@Param("id") Long id);

    Integer selectByBrandIdAndLevel(@Param("brandId") String brandId, @Param("vipLevel") Integer vipLevel);
}