<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.marketing.modules.mapper.BNewUserRightsCouponMapper">
  <resultMap id="BaseResultMap" type="org.springcenter.marketing.modules.model.BNewUserRightsCoupon">
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="RIGHTS_TYPE" jdbcType="DECIMAL" property="rightsType" />
    <result column="UNIONID" jdbcType="VARCHAR" property="unionid" />
    <result column="STATUS" jdbcType="DECIMAL" property="status" />
    <result column="COUPON_AMOUNT" jdbcType="VARCHAR" property="couponAmount" />
    <result column="AWARDID" jdbcType="VARCHAR" property="awardid" />
    <result column="NUM" jdbcType="DECIMAL" property="num" />
    <result column="CALC_PARAM" jdbcType="VARCHAR" property="calcParam" />
    <result column="OUT_NO" jdbcType="VARCHAR" property="outNo" />
    <result column="B_RIGHTS_ID" jdbcType="VARCHAR" property="bRightsId" />
    <result column="COUPON_JSON_ID" jdbcType="VARCHAR" property="couponJsonId" />

    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="IS_DEL" jdbcType="DECIMAL" property="isDel" />
    <result column="SEND_STATUS" jdbcType="DECIMAL" property="sendStatus" />

    <result column="START_TIME" jdbcType="TIMESTAMP" property="startTime" />
    <result column="END_TIME" jdbcType="TIMESTAMP" property="endTime" />

    <result column="VOUCHER_NOS" jdbcType="VARCHAR" property="voucherNos" />
    <result column="USE_RULE_SNAPSHOT" jdbcType="VARCHAR" property="useRuleSnapshot" />

    <result column="CONSUME_OUT_NO" jdbcType="VARCHAR" property="consumeOutNo" />
    <result column="IS_FIRST" jdbcType="DECIMAL" property="isFirst" />
    <result column="IS_EXPANSION" jdbcType="DECIMAL" property="isExpansion" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, RIGHTS_TYPE, UNIONID, STATUS, COUPON_AMOUNT, AWARDID, NUM, CALC_PARAM, OUT_NO,
    B_RIGHTS_ID, COUPON_JSON_ID,CREATE_TIME,UPDATE_TIME,IS_DEL,SEND_STATUS,START_TIME,END_TIME,VOUCHER_NOS,
    USE_RULE_SNAPSHOT,CONSUME_OUT_NO,IS_FIRST,IS_EXPANSION
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from b_new_user_rights_coupon 
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <select id="selectRightsCouponByCardType"
          resultMap="BaseResultMap">
    select     bur.ID from b_new_user_rights_coupon bur
                         left join b_rights br on bur.B_RIGHTS_ID = br.ID
                         left join b_new_user_member_card bumc on bumc.out_no = bur.out_no
    where   bur.IS_DEL = #{isDel} and bur.STATUS = #{status} and bur.UNIONID = #{unionid} and bumc.APPLICABLE_PARTY = #{applicableParty}

  </select>
  <select id="selectAvialableUserRightsCouponByUnionId"
          resultMap="BaseResultMap">

      select
    burcc.ID, burcc.RIGHTS_TYPE, burcc.UNIONID, burcc.STATUS, burcc.COUPON_AMOUNT, burcc.AWARDID, burcc.NUM, burcc.CALC_PARAM, burcc.OUT_NO,
    burcc.B_RIGHTS_ID, burcc.COUPON_JSON_ID,burcc.CREATE_TIME,burcc.UPDATE_TIME,burcc.IS_DEL,burcc.SEND_STATUS,burcc.START_TIME,burcc.END_TIME,burcc.VOUCHER_NOS,
    burcc.USE_RULE_SNAPSHOT,burcc.CONSUME_OUT_NO,burcc.IS_FIRST,burcc.IS_EXPANSION

      from b_new_user_rights_coupon burcc left join b_rights br on burcc.B_RIGHTS_ID = br.ID
      inner join b_new_user_member_card bumc on bumc.out_no = burcc.out_no
      where  burcc.IS_DEL = 0
        and burcc.UNIONID = #{unionid}
        and br.rights_type = #{rightType} and br.B_MEMBER_CARD_ID = #{bMemberCardId}
        and burcc.send_status = 0 and burcc.STATUS = 1
      <if test="now != null">
        and #{now} >= burcc.start_time and burcc.end_time >= #{now}
      </if>
      <if test="outNo != null">
        and burcc.OUT_NO = #{outNo}
      </if>
     order by burcc.id

  </select>
  <select id="selectAllUserRightsCouponByUnionId"
          resultMap="BaseResultMap">


    select
    burcc.ID, burcc.RIGHTS_TYPE, burcc.UNIONID, burcc.STATUS, burcc.COUPON_AMOUNT, burcc.AWARDID, burcc.NUM, burcc.CALC_PARAM, burcc.OUT_NO,
    burcc.B_RIGHTS_ID, burcc.COUPON_JSON_ID,burcc.CREATE_TIME,burcc.UPDATE_TIME,burcc.IS_DEL,burcc.SEND_STATUS,burcc.START_TIME,burcc.END_TIME,burcc.VOUCHER_NOS,
    burcc.USE_RULE_SNAPSHOT,burcc.CONSUME_OUT_NO,burcc.IS_FIRST,burcc.IS_EXPANSION
    from b_new_user_rights_coupon burcc left join b_rights br on burcc.B_RIGHTS_ID = br.ID
    inner join b_new_user_member_card bumc on bumc.out_no = burcc.out_no
    where  burcc.IS_DEL = 0
    and burcc.UNIONID = #{unionid}
    and br.rights_type = #{rightType} and br.B_MEMBER_CARD_ID = #{bMemberCardId}
    <if test="now != null">
      and #{now} >= burcc.start_time and burcc.end_time >= #{now}
    </if>
    order by burcc.update_time desc
  </select>
  <select id="selectByConsumeOutNos"
         resultMap="BaseResultMap">
    select <include refid="Base_Column_List"></include> from b_new_user_rights_coupon
    where CONSUME_OUT_NO in
    <foreach collection="consumeOutNos" item="item" close=")" open="(" separator=",">
      #{item}
    </foreach>
  </select>
  <select id="selectUnSuccessSendOrSendNotUpdate"
          resultMap="BaseResultMap">
    select <include refid="Base_Column_List"></include> from b_new_user_rights_coupon
    where STATUS = 4 and IS_DEL = 0 and CONSUME_OUT_NO is not null and SEND_STATUS = 1 and VOUCHER_NOS is null
  </select>
    <select id="selectByOutNo" resultMap="BaseResultMap">
      select <include refid="Base_Column_List"></include> from b_new_user_rights_coupon where out_no = #{outNo}
    </select>
  <select id="selectByOutNos" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"></include> from b_new_user_rights_coupon where out_no in
                                          <foreach collection="outNos" item="item" close=")" open="(" separator=",">
                                            #{item}
                                          </foreach>
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from b_new_user_rights_coupon
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="org.springcenter.marketing.modules.model.BNewUserRightsCoupon">
    insert into b_new_user_rights_coupon (ID, RIGHTS_TYPE, UNIONID, 
      STATUS, COUPON_AMOUNT, AWARDID,
      NUM, CALC_PARAM, OUT_NO, 
      B_RIGHTS_ID, COUPON_JSON_ID)
    values (#{id,jdbcType=VARCHAR}, #{rightsType,jdbcType=DECIMAL}, #{unionid,jdbcType=VARCHAR}, 
      #{status,jdbcType=DECIMAL}, #{couponAmount,jdbcType=VARCHAR}, #{awardid,jdbcType=VARCHAR}, 
      #{num,jdbcType=DECIMAL}, #{calcParam,jdbcType=VARCHAR}, #{outNo,jdbcType=VARCHAR}, 
      #{bRightsId,jdbcType=VARCHAR}, #{couponJsonId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="org.springcenter.marketing.modules.model.BNewUserRightsCoupon">
    insert into b_new_user_rights_coupon
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="rightsType != null">
        RIGHTS_TYPE,
      </if>
      <if test="unionid != null">
        UNIONID,
      </if>
      <if test="status != null">
        STATUS,
      </if>
      <if test="couponAmount != null">
        COUPON_AMOUNT,
      </if>
      <if test="awardid != null">
        AWARDID,
      </if>
      <if test="num != null">
        NUM,
      </if>
      <if test="calcParam != null">
        CALC_PARAM,
      </if>
      <if test="outNo != null">
        OUT_NO,
      </if>
      <if test="bRightsId != null">
        B_RIGHTS_ID,
      </if>
      <if test="couponJsonId != null">
        COUPON_JSON_ID,
      </if>

      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="isDel != null">
        IS_DEL,
      </if>
      <if test="sendStatus != null">
        SEND_STATUS,
      </if>

      <if test="startTime != null">
        START_TIME,
      </if>
      <if test="endTime != null">
        END_TIME,
      </if>
      <if test="voucherNos != null">
        VOUCHER_NOS,
      </if>
      <if test="useRuleSnapshot != null">
        USE_RULE_SNAPSHOT,
      </if>
      <if test="consumeOutNo != null">
        CONSUME_OUT_NO,
      </if>
      <if test="isFirst != null">
        IS_FIRST,
      </if>
      <if test="isExpansion != null">
        IS_EXPANSION,
      </if>

    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="rightsType != null">
        #{rightsType,jdbcType=DECIMAL},
      </if>
      <if test="unionid != null">
        #{unionid,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=DECIMAL},
      </if>
      <if test="couponAmount != null">
        #{couponAmount,jdbcType=VARCHAR},
      </if>
      <if test="awardid != null">
        #{awardid,jdbcType=VARCHAR},
      </if>
      <if test="num != null">
        #{num,jdbcType=DECIMAL},
      </if>
      <if test="calcParam != null">
        #{calcParam,jdbcType=VARCHAR},
      </if>
      <if test="outNo != null">
        #{outNo,jdbcType=VARCHAR},
      </if>
      <if test="bRightsId != null">
        #{bRightsId,jdbcType=VARCHAR},
      </if>
      <if test="couponJsonId != null">
        #{couponJsonId,jdbcType=VARCHAR},
      </if>

      <if test="createTime != null">
        #{createTime},
      </if>
      <if test="updateTime != null">
        #{updateTime},
      </if>
      <if test="isDel != null">
        #{isDel},
      </if>
      <if test="sendStatus != null">
        #{sendStatus},
      </if>

      <if test="startTime != null">
        #{startTime},
      </if>
      <if test="endTime != null">
        #{endTime},
      </if>
      <if test="voucherNos != null">
        #{voucherNos},
      </if>
      <if test="useRuleSnapshot != null">
        #{useRuleSnapshot},
      </if>
      <if test="consumeOutNo != null">
        #{consumeOutNo},
      </if>
      <if test="isFirst != null">
        #{isFirst},
      </if>
      <if test="isExpansion != null">
        #{isExpansion},
      </if>

    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="org.springcenter.marketing.modules.model.BNewUserRightsCoupon">
    update b_new_user_rights_coupon
    <set>
      <if test="rightsType != null">
        RIGHTS_TYPE = #{rightsType,jdbcType=DECIMAL},
      </if>
      <if test="unionid != null">
        UNIONID = #{unionid,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        STATUS = #{status,jdbcType=DECIMAL},
      </if>
      <if test="couponAmount != null">
        COUPON_AMOUNT = #{couponAmount,jdbcType=VARCHAR},
      </if>
      <if test="awardid != null">
        AWARDID = #{awardid,jdbcType=VARCHAR},
      </if>
      <if test="num != null">
        NUM = #{num,jdbcType=DECIMAL},
      </if>
      <if test="calcParam != null">
        CALC_PARAM = #{calcParam,jdbcType=VARCHAR},
      </if>
      <if test="outNo != null">
        OUT_NO = #{outNo,jdbcType=VARCHAR},
      </if>
      <if test="bRightsId != null">
        B_RIGHTS_ID = #{bRightsId,jdbcType=VARCHAR},
      </if>
      <if test="couponJsonId != null">
        COUPON_JSON_ID = #{couponJsonId,jdbcType=VARCHAR},
      </if>

      <if test="createTime != null">
        CREATE_TIME = #{createTime},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime},
      </if>
      <if test="isDel != null">
        IS_DEL = #{isDel},
      </if>
      <if test="sendStatus != null">
        SEND_STATUS = #{sendStatus},
      </if>
      <if test="startTime != null">
        START_TIME = #{startTime},
      </if>
      <if test="endTime != null">
        END_TIME = #{endTime},
      </if>
      <if test="voucherNos != null">
        VOUCHER_NOS = #{voucherNos},
      </if>
      <if test="useRuleSnapshot != null">
        USE_RULE_SNAPSHOT = #{useRuleSnapshot},
      </if>
      <if test="consumeOutNo != null">
        CONSUME_OUT_NO = #{consumeOutNo},
      </if>
      <if test="isFirst != null">
        IS_FIRST  = #{isFirst},
      </if>
      <if test="isExpansion != null">
        IS_EXPANSION  = #{isExpansion},
      </if>

    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="org.springcenter.marketing.modules.model.BNewUserRightsCoupon">
    update b_new_user_rights_coupon
    set RIGHTS_TYPE = #{rightsType,jdbcType=DECIMAL},
      UNIONID = #{unionid,jdbcType=VARCHAR},
      STATUS = #{status,jdbcType=DECIMAL},
      COUPON_AMOUNT = #{couponAmount,jdbcType=VARCHAR},
      AWARDID = #{awardid,jdbcType=VARCHAR},
      NUM = #{num,jdbcType=DECIMAL},
      CALC_PARAM = #{calcParam,jdbcType=VARCHAR},
      OUT_NO = #{outNo,jdbcType=VARCHAR},
      B_RIGHTS_ID = #{bRightsId,jdbcType=VARCHAR},
      COUPON_JSON_ID = #{couponJsonId,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="batchUpdateStatusByPrimaryKeys">
    update b_new_user_rights_coupon set STATUS = #{status}
    where
    <if test="ids != null and ids.size() > 0">
      id  in
      <foreach collection="ids" item="item" open="(" close=")" separator=",">
        #{item}
      </foreach>
    </if>

  </update>
  <update id="updateByConsumeOutNoAndStatus">
    update b_new_user_rights_coupon set STATUS = #{status}, SEND_STATUS = #{sendStatus} ,CONSUME_OUT_NO = ''
    where id = #{id}

  </update>
</mapper>