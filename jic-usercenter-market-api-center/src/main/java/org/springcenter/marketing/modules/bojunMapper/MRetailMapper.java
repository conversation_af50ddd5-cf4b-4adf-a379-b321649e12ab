package org.springcenter.marketing.modules.bojunMapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.marketing.api.dto.miniapp.FindRecordMsgByTemplateIdResp;
import org.springcenter.marketing.modules.model.MRetail;
import org.springcenter.marketing.modules.model.MRetailInfoEntity;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 零售单数据库操作接口
 */
public interface MRetailMapper extends BaseMapper<MRetail> {
    /**
     * 查询指定条件下的订单ID列表
     */
    List<Long> selectOrderIdsForScan(@Param("billDate") Integer billDate,
                                     @Param("creationStart") String creationStart,
                                     @Param("creationEnd") String creationEnd);

    /**
     * 计算消费总额
     */
    public BigDecimal sumConsume(@Param("vipId") String vipId, @Param("startDate") Integer startDate,@Param("endDate") Integer endDate);

    List<FindRecordMsgByTemplateIdResp> selectMretailByVipIdAndOrderType(@Param("vipId") Long cVipId, @Param("createDate") Date createTime);

    List<FindRecordMsgByTemplateIdResp> selectMretailByVipIdAndDdlys(@Param("vipId") Long cVipId, @Param("ddlys") List<String> ddlys, @Param("createDate") Date createTime);


    MRetailInfoEntity selectByVipIdAndTypeAndChannel(@Param("vipId") Long vipId, @Param("list") List<String> byChannel);


    void callCreateRetailRefundOrder(Map<String, Object> map);

    Long selectIdByDocNo(@Param("docNo") String docNo);
}
