package org.springcenter.marketing.modules.integration.cache;

import org.springcenter.marketing.api.dto.GetAssetsMenuResp;

public interface IMenuCacheService {


    /**
     * 资产按钮
     * @param brandId
     * @return
     */
    Boolean getAssetsMenu(String brandId);


    /**
     * 清除资产缓存 通配符
     */
    void clearAssetsMenuCache();


    /**
     * 清除指定缓存
     * @param brandId
     */
    void clearAssetsMenuCacheById(String brandId);
    /**
     * 获取资产按钮
     * @param brandId
     * @return
     */
    GetAssetsMenuResp getAllAssetsMenu(String brandId);
}
