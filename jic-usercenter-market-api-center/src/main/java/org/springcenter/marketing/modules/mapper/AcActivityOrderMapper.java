package org.springcenter.marketing.modules.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.marketing.api.dto.admin.AdminActivityOrderReq;
import org.springcenter.marketing.modules.model.AcActivityOrder;

import java.util.Date;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【AC_ACTIVITY_ORDER(活动order表)】的数据库操作Mapper
* @createDate 2024-11-12 09:02:25
* @Entity generator.domain.JicActivityOrder
*/
public interface AcActivityOrderMapper extends BaseMapper<AcActivityOrder> {
    // 进行中和完成都算购买
    List<AcActivityOrder> selectEfftiveOrderByUnionid(@Param("unionid") String unionid);

    /**
     *  查询 已经支付的 并且没删除的 根据参数
     * @param requestData
     * @return
     */
    List<AcActivityOrder> selectAlreadyPayOrderList(AdminActivityOrderReq requestData);

    List<AcActivityOrder> selectEfftiveOrderByUnionidAndWeidAndDate(@Param("unionid") String unionid, @Param("weid") String weid, @Param("now") Date now);

    /**
     * 取消订单  取消10分钟以上的订单
     */
    void cancelActivityOrder();

    List<AcActivityOrder> selectShouldCancelActivityOrder();
    // 进行中的单子
    List<AcActivityOrder> selectEfftiveStatusOneOrderByUnionid(@Param("unionid") String unionid);
}




