package org.springcenter.marketing.modules.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.jnby.common.CommonRequest;
import com.jnby.common.Page;
import com.jnby.common.ResponseResult;
import com.jnby.common.util.IdLeaf;
import org.killbill.bus.api.PersistentBus;
import org.springcenter.marketing.api.checkRightsDto.CheckBoxGiftRightsDto;
import org.springcenter.marketing.api.checkRightsDto.CheckRightCouponV3Dto;
import org.springcenter.marketing.api.checkRightsDto.CheckRightCouponV3DtoForPre;
import org.springcenter.marketing.api.constant.BirthCouponSendLogStatusConstatnt;
import org.springcenter.marketing.api.constant.IdConstant;
import org.springcenter.marketing.api.constant.RedisKeyConstant;
import org.springcenter.marketing.api.constant.SendMaterialCouponLogTypeConstant;
import org.springcenter.marketing.api.context.GetEbInfoReq;
import org.springcenter.marketing.api.context.SendMaterialBoxGiftContext;
import org.springcenter.marketing.api.context.UseRightsContext;
import org.springcenter.marketing.api.dto.*;
import org.springcenter.marketing.api.dto.admin.*;
import org.springcenter.marketing.api.dto.miniapp.RightsV3ConfigResp;
import org.springcenter.marketing.api.entity.CheckRightsEntity;
import org.springcenter.marketing.api.enums.*;
import org.springcenter.marketing.modules.bojunMapper.CStoreMapper;
import org.springcenter.marketing.modules.bojunMapper.CVipTypeMapper;
import org.springcenter.marketing.modules.bojunMapper.MRetailMapper;
import org.springcenter.marketing.modules.context.*;
import org.springcenter.marketing.modules.entity.*;
import org.springcenter.marketing.modules.event.SendBirthCouponEvent;
import org.springcenter.marketing.modules.event.SendNotifyCouponData;
import org.springcenter.marketing.modules.event.bus.SendBirthCouponEventBus;
import org.springcenter.marketing.modules.mapper.*;
import org.springcenter.marketing.modules.model.*;
import org.springcenter.marketing.modules.repository.IRightsRepository;
import org.springcenter.marketing.modules.rights.ApplyForBoxRights;
import org.springcenter.marketing.modules.rights.SubV3BoxGiftRight;
import org.springcenter.marketing.modules.rights.SubV3CouponRight;
import org.springcenter.marketing.modules.rights.SubV4BoxGiftRight;
import org.springcenter.marketing.modules.service.IPackageStoresService;
import org.springcenter.marketing.modules.service.IUserVipService;
import org.springcenter.marketing.modules.service.IVoucherService;
import org.springcenter.marketing.modules.service.RightsV3Service;
import org.springcenter.marketing.modules.util.DateUtil;
import org.springcenter.marketing.modules.util.DateUtils;
import org.springcenter.marketing.modules.util.RedisService;
import org.springcenter.marketing.modules.util.RedissonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springcenter.marketing.modules.webapi.IJicInfoHttpApi;
import org.springcenter.marketing.modules.wxMapper.*;
import org.springcenter.product.api.IProductApi;
import org.springcenter.product.api.IProductStoreApi;
import org.springcenter.product.api.dto.ProductSkcResp;
import org.springcenter.product.api.dto.QueryGoodsSkcListReq;
import org.springcenter.product.api.dto.SkuProductInfoResp;
import org.springcenter.retail.api.IRetailApi;
import org.springcenter.retail.api.dto.req.OrderPush2JSTDTO;
import org.springcenter.retail.api.dto.req.OrderQuery2JSTDTO;
import org.springcenter.retail.api.dto.resp.PushOrder2JSTRespDTO;
import org.springcenter.retail.api.dto.resp.QueryOrder2JSTRespDTO;
import org.springframework.beans.BeanUtils;
import org.springcenter.product.api.enums.IsDeleteEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import retrofit2.Response;

import java.math.BigDecimal;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Service
@RefreshScope
@Slf4j
public class RightsV3ServiceImpl implements RightsV3Service {

    @Autowired
    private BRightsMapper bRightsMapper;
    
    @Autowired
    private BNewUserRightsMapper bNewUserRightsMapper;

    @Autowired
    private BUserRightsMaterialMapper bUserRightsMaterialMapper;

    @Autowired
    private BUserRightsConsumeCouponMapper bUserRightsConsumeCouponMapper;

    @Autowired
    private BNewAddUserRightLogMapper bNewAddUserRightLogMapper;

    @Autowired
    @Qualifier("marketingTransactionTemplate")
    private TransactionTemplate template;

    @Autowired
    private RedissonUtil redissonUtil;

    @Autowired
    private BNewUserMemberCardMapper bNewUserMemberCardMapper;

    /**
     * 初始化ruleNum  累计获取免费订阅
     */
    @Value("${rights.v3.rulenum}")
    private BigDecimal ruleNum;

    @Value("${rights.v3.bmembercardIds}")
    private String bMemberCardIds;

    @Autowired
    private BMemberCardTypeMapper bNewMemberCardTypeMapper;

    @Autowired
    private BUserRightsLogMapper bUserRightsLogMapper;


    @Autowired
    private BMemberCardMapper bNewMemberCardMapper;

    @Autowired
    private BUserMemberCardLogMapper bUserMemberCardLogMapper;


    @Autowired
    private BUserApplyForBoxLogMapper bUserApplyForBoxLogMapper;

//    @Autowired
//    private RedisPoolUtil redisPoolUtil;

    @Autowired
    private BGoodsSkuMapper bGoodsSkuMapper;

    @Autowired
    private BMemberCardMapper bMemberCardMapper;

    @Autowired
    private BUserMemberCardRenewLogMapper renewLogMapper;

    @Autowired
    private RedisService redisService;

    @Value("${reflect.appid.applicationparty}")
    private String reflectData;

    @Autowired
    private RedisCacheLogMapper redisCacheLogMapper;

    @Autowired
    private CVipTypeMapper cVipTypeMapper;

    @Autowired
    private BNewUserRightsCouponMapper bNewUserRightsCouponMapper;

    @Autowired
    private IVoucherService iVoucherService;

    @Autowired
    private BNewUserBoxGiftMapper bNewUserBoxGiftMapper;


    @Autowired
    private IJicInfoHttpApi iJicInfoHttpApi;

    @Autowired
    private IProductApi iProductApi;

    @Autowired
    private VoucherBaseMapper voucherBaseMapper;

    @Autowired
    private IProductStoreApi iProductStoreApi;

    @Autowired
    IUserVipService iUserVipService;

    @Autowired
    private BRightsCustomizeStoreMapper bRightsCustomizeStoreMapper;

    @Autowired
    private BirthCouponSendLogMapper birthCouponSendLogMapper;


    @Autowired
    private MRetailMapper mRetailMapper;


    @Autowired
    private JicVoucherSendRecordMapper jicVoucherSendRecordMapper;

    @Autowired
    private SendBirthCouponEventBus sendBirthCouponEventBus;

    @Autowired
    private JicSendCouponLogMapper jicSendCouponLogMapper;

    @Autowired
    private SendMaterialCouponLogMapper sendMaterialCouponLogMapper;

    @Autowired
    private IPackageStoresService iPackageStoresService;
    @Autowired
    private IRetailApi retailApi;

    @Value("${jushuitan.shopid}")
    private String  jstShopId;

    @Value("${birth.weid}")
    private String  birthWeid;

    @Autowired
    private IRightsRepository rightsRepository;

    //对于生日礼来说  0 就是直接所有人都返回不可领取   1 就是正常了
    @Value("${intimacy.switch.flag}")
    private String intimacySwitchFlag;

    @Value("${rights.redis.cache.flag}")
    private String rightsRedisCacheFlag;

    @Autowired
    private ScanCodeLogMapper scanCodeLogMapper;

    @Autowired
    private CStoreMapper cStoreMapper;


    @Value("${notsend.gift.storeid}")
    private String notSendGiftStoreId;


    private static String $gt = "gt";
    private static String $gte = "gte";
    private static String $lt = "lt";
    private static String $lte = "lte";

    @Override
    public void buyMemberCardSendRights(String bMemberCardId,
                                        String unionid,
                                        String outNo,
                                        BMemberCardType useBMemberCardType,String subId,String storeId) {
        // 验证是否重复发放
        List<BNewAddUserRightLog> bAddUserRightLog = bNewAddUserRightLogMapper.selectByOutNo(outNo);
        if(CollectionUtils.isNotEmpty(bAddUserRightLog)){
            throw  new RuntimeException("重复调用！outNo = "+outNo);
        }
        String redisKey = "buyMemberCardSendRights:" + outNo;
        boolean flag = redissonUtil.tryLock(redisKey);
        if(!flag){
            log.info("buyMemberCardSendRights 加锁失败！");
            return ;
        }

        //查询卡信息

        BMemberCard bMemberCard = bNewMemberCardMapper.selectByPrimaryKey(bMemberCardId);
        if(ObjectUtils.isEmpty(bMemberCard)){
            log.info("buyMemberCardSendRights 未查询到卡信息 ！");
            return ;
        }


        if(useBMemberCardType.getDays() == null){
            log.info("buyMemberCardSendRights 未查询到合适的卡类型的天数 ！");
            return ;
        }

        Date startDate  = new Date();
        String s = DateUtils.formatDate(startDate, "yyyy-MM-dd");
        startDate = DateUtils.parseDate(s + " 00:00:00", "yyyy-MM-dd hh:mm:ss");

        Date endDate  = DateUtils.addDays(startDate,useBMemberCardType.getDays().intValue());
        String end = DateUtils.formatDate(endDate, "yyyy-MM-dd");
        endDate = DateUtils.parseDate(end + " 23:59:59", "yyyy-MM-dd hh:mm:ss");

        try{
            // 获取卡上对应的权益信息
            BRights params = new BRights();
            params.setIsDel(IsDeleteEnum.NORMAL.getCode().longValue());
            params.setStatus(StatusEnum.NORMAL.getCode().longValue());
            params.setbMemberCardId(bMemberCardId);
            List<BRights> bRightsList = bRightsMapper.selectListBySelective(params);
            if(CollectionUtils.isEmpty(bRightsList)){
                throw  new RuntimeException("该卡未配置任何权益配置信息!bMemberCardId = "+bMemberCardId);
            }
            //次数类权益
            List<BRights> bUserRightsList  = new ArrayList<>();
            //实物类权益
            List<BRights> bUserRightsMaterialList  = new ArrayList<>();
            //消费叠加类权益
            List<BRights> bUserRightsConsumeCouponList  = new ArrayList<>();
            //优惠券类权益
            List<BRights> bUserCouponList  = new ArrayList<>();
            //实物发送快递
            List<BRights> bUserSendMaterialList  = new ArrayList<>();

            for (BRights bRights : bRightsList) {
                if(RightsTypeEnum.APPLY_BOX.getCode().longValue() == bRights.getRightsType().longValue()
                  ||  RightsTypeEnum.APPLY_BOX_SUBCRIBE.getCode().longValue() == bRights.getRightsType().longValue()
                  ||  RightsTypeEnum.APPLY_BOX_FREE.getCode().longValue() == bRights.getRightsType().longValue()
                ){
                    bUserRightsList.add(bRights);
                }else if(RightsTypeEnum.DIY_THEME_BOX.getCode().longValue() == bRights.getRightsType().longValue()){
                    bUserRightsList.add(bRights);
                }else if(RightsTypeEnum.NEW_CUSTOMER_GIFT.getCode().longValue() == bRights.getRightsType().longValue()){
                    bUserRightsMaterialList.add(bRights);
                }else if(RightsTypeEnum.FAVOR_GIFT.getCode().longValue() == bRights.getRightsType().longValue()){
                    bUserRightsMaterialList.add(bRights);
                }else if(RightsTypeEnum.FASHIONER_SEND_BOX.getCode().longValue() == bRights.getRightsType().longValue()){
                    bUserRightsConsumeCouponList.add(bRights);
                }else if(RightsTypeEnum.FIRST_ON_TRIAL_AFTER_BUY.getCode().longValue() == bRights.getRightsType().longValue()){
                    bUserRightsList.add(bRights);
                }else if(RightsTypeEnum.SUB3_COUPON.getCode().longValue() == bRights.getRightsType().longValue()){
                    bUserCouponList.add(bRights);
                }else if(RightsTypeEnum.SUB3_BOX_GIFT.getCode().longValue() == bRights.getRightsType().longValue()){
                    bUserSendMaterialList.add(bRights);
                }
            }

//            //次数类权益
//            List<BRights> bUserRightsList = bRightsList.stream().filter(e -> RightsTypeEnum.getBUserRightsList().contains(e.getRightsType().intValue())).collect(Collectors.toList());
//            //实物类权益
//            List<BRights> bUserRightsMaterialList = bRightsList.stream().filter(e -> RightsTypeEnum.getBUserRightsMaterialList().contains(e.getRightsType().intValue())).collect(Collectors.toList());
//            //消费叠加类权益
//            List<BRights> bUserRightsConsumeCouponList = bRightsList.stream().filter(e -> RightsTypeEnum.getBUserRightsConsumeCouponList().contains(e.getRightsType().intValue())).collect(Collectors.toList());

            log.info("bUserRightsList = {}",bUserRightsList);
            log.info("bUserRightsList = {}",bUserRightsMaterialList);
            log.info("bUserRightsList = {}",bUserRightsConsumeCouponList);
            log.info("bUserRightsList = {}",bUserCouponList);
            log.info("bUserRightsList = {}",bUserSendMaterialList);

            //次数类权益进行发放 组装
            List<BNewUserRights>  insertBUserRightsList = buildBUserRightsInsertList(bUserRightsList,startDate,endDate,outNo,unionid);

            //组装实物类/券类 权益
            List<BUserRightsMaterial>  insertBUserRightsMaterialList = buildBUserRightsMaterialInsertList(bUserRightsMaterialList,startDate,endDate,outNo,unionid);

            //组装消费叠加类的权益
            List<BUserRightsConsumeCoupon>  insertBuserRightsConsumeCouponList = buildBUserRightsConsumeCouponInsertList(bUserRightsConsumeCouponList,startDate,endDate,outNo,unionid);

            // 订阅3.0优惠券类
            List<BNewUserRightsCoupon>  bNewUserRightsCouponList = buildBUserRightsCouponInsertList(bUserCouponList,insertBUserRightsList,startDate,endDate,outNo,unionid);

            // 发送快递类
            List<BNewUserBoxGift>  bNewUserBoxGifts = buildBUserBoxGiftInsertList(bUserSendMaterialList,startDate,endDate,outNo,unionid,subId,storeId);


            template.execute(action->{
                //失效用户的权益
//                expireUserRights(unionid,bRightsList,bMemberCard);

                if(CollectionUtils.isNotEmpty(insertBUserRightsList)){
                    insertBUserRightsList.stream().forEach(e-> {
                                bNewUserRightsMapper.insertSelective(e);
                                if(RightsTypeEnum.FIRST_ON_TRIAL_AFTER_BUY.getCode().longValue() == e.getRightsType().longValue()){
                                    //缓存剩余次数 根据类型
                                    redisService.setAndLog(RedisKeyConstant.B_USER_RIGHTS_KEYS_TOTAL_NUM.join(bMemberCard.getApplicableParty(),unionid),e.getTotalNum());
                                    redisService.setAndLog(RedisKeyConstant.B_USER_RIGHTS_KEYS_LIMIT_NUM.join(bMemberCard.getApplicableParty(),unionid),e.getLimitNumber());
//                                    RedisTemplateUtil.set(redisPoolUtil, RedisKeysEnum.B_USER_RIGHTS_KEYS_TOTAL_NUM.join(bMemberCard.getApplicableParty(),unionid),e.getTotalNum()+"");
//                                    RedisTemplateUtil.set(redisPoolUtil,RedisKeysEnum.B_USER_RIGHTS_KEYS_LIMIT_NUM.join(bMemberCard.getApplicableParty(),unionid),e.getLimitNumber()+"");
                                }
                            }
                    );

                }

                if(CollectionUtils.isNotEmpty(insertBUserRightsMaterialList)){
                    insertBUserRightsMaterialList.stream().forEach(e->bUserRightsMaterialMapper.insertSelective(e));
                }

                if(CollectionUtils.isNotEmpty(insertBuserRightsConsumeCouponList)){
                    insertBuserRightsConsumeCouponList.stream().forEach(e->bUserRightsConsumeCouponMapper.insertSelective(e));
                }

                if(CollectionUtils.isNotEmpty(bNewUserRightsCouponList)){
                    bNewUserRightsCouponList.stream().forEach(e->bNewUserRightsCouponMapper.insertSelective(e));
                }

                if(CollectionUtils.isNotEmpty(bNewUserBoxGifts)){
                    bNewUserBoxGifts.stream().forEach(e->bNewUserBoxGiftMapper.insertSelective(e));
                }

                insertBAddUserRightLog(unionid,outNo);
                return true;
            });
        }catch (Exception e){
            throw  e;
        }finally {
            redissonUtil.unlock(redisKey);
        }
    }

    private List<BNewUserBoxGift> buildBUserBoxGiftInsertList(List<BRights> bUserSendMaterialList, Date startDate, Date endDate, String outNo, String unionid,
                                                              String subId, String storeId) {
        // 如果是null 那么默认jnby  有值 找到对应值  2 默认是JNBY
        Long arcBrandId = 2L;
        // 查询订阅信息

        if(StringUtils.isNotBlank(storeId)) {
            List<Long> list = new ArrayList<>();
            list.add(Long.parseLong(storeId));
            List<CStore> cStores = cStoreMapper.selectCStoreByIds(list);
            if (CollectionUtils.isNotEmpty(cStores)) {
                if (cStores.get(0).getcArcbrandId() != null) {
                    arcBrandId = cStores.get(0).getcArcbrandId();
                }
            }
        }


        List<BNewUserBoxGift> result = new ArrayList<>();

        for (BRights bRights : bUserSendMaterialList) {
            String useRule = bRights.getUseRule();
            // 处理json串
            // 老的  [{"key":1,"boxGiftRight":{"sku":"9O221388025103","name":"长袖衬衣-中卡其-M","price":"1395"}},{"key":3,"boxGiftRight":{"sku":"9NB11364062103","name":"短袖T恤-大红-M","price":"595"}},{"key":6,"boxGiftRight":{"sku":"7L3A0009025403","name":"帽子-纸棕-M","price":"295"}}]
            // 新的   [{"arcBrandId":"","list":[{"key":1,"boxGiftRight":{"sku":"9O221388025103","name":"长袖衬衣-中卡其-M","price":"1395"}},{"key":3,"boxGiftRight":{"sku":"9NB11364062103","name":"短袖T恤-大红-M","price":"595"}},{"key":6,"boxGiftRight":{"sku":"7L3A0009025403","name":"帽子-纸棕-M","price":"295"}}]}]
            List<SubV3BoxGiftRight> subV3BoxGiftRights  = new ArrayList<>();
            List<SubV4BoxGiftRight> subV4BoxGiftRights = JSONObject.parseArray(useRule, SubV4BoxGiftRight.class);
            if(CollectionUtils.isEmpty(subV4BoxGiftRights) || subV4BoxGiftRights.get(0).getArcBrandId() == null){
                // 使用老的
                subV3BoxGiftRights = JSONObject.parseArray(useRule, SubV3BoxGiftRight.class);
            }else{
                // 使用新的
                Long finalArcBrandId = arcBrandId;
                List<SubV4BoxGiftRight> collect = subV4BoxGiftRights.stream().filter(r -> r.getArcBrandId().equals(finalArcBrandId)).collect(Collectors.toList());
                if(CollectionUtils.isEmpty(collect)){
                    // 未配置jnby 收盒礼  默认发jnby的
                     collect = subV4BoxGiftRights.stream().filter(r -> r.getArcBrandId().equals(2L)).collect(Collectors.toList());
                     log.info("未配置收盒礼  默认发放jnby的  arcBrandId = {}",finalArcBrandId);
                }
                subV3BoxGiftRights = collect.get(0).getList();
            }

            for (SubV3BoxGiftRight subV3BoxGiftRight : subV3BoxGiftRights) {
                BNewUserBoxGift bNewUserBoxGift = new BNewUserBoxGift();
                bNewUserBoxGift.setId(IdLeaf.getId(IdConstant.B_NEW_USER_BOX_GIFT));
                bNewUserBoxGift.setBRightsId(bRights.getId());
                bNewUserBoxGift.setCreateTime(new Date());
                bNewUserBoxGift.setUpdateTime(new Date());
                bNewUserBoxGift.setOutNo(outNo);
                bNewUserBoxGift.setUnionid(unionid);
                bNewUserBoxGift.setIsDel(IsDeleteEnum.NORMAL.getCode().shortValue());
                bNewUserBoxGift.setRightsType(bRights.getRightsType());
                bNewUserBoxGift.setStatus(StatusEnum.NORMAL.getCode());
                bNewUserBoxGift.setSendStatus(0);
                bNewUserBoxGift.setStartTime(startDate);
                bNewUserBoxGift.setEndTime(endDate);

                bNewUserBoxGift.setPrice(new BigDecimal(subV3BoxGiftRight.getBoxGiftRight().getPrice()));
                bNewUserBoxGift.setBoxGiftName(subV3BoxGiftRight.getBoxGiftRight().getName());
                bNewUserBoxGift.setSku(subV3BoxGiftRight.getBoxGiftRight().getSku());
                bNewUserBoxGift.setSendNum(Integer.parseInt(subV3BoxGiftRight.getKey()));
                bNewUserBoxGift.setUseSendType(1);
                bNewUserBoxGift.setArcBrandId(arcBrandId.toString());

                result.add(bNewUserBoxGift);
            }
        }
        return  result;
    }

    private List<BNewUserRightsCoupon> buildBUserRightsCouponInsertList(List<BRights> bUserCouponList,
                                                                        List<BNewUserRights> insertBUserRightsList,
                                                                        Date startDate, Date endDate,
                                                                        String outNo, String unionid) {
        List<BNewUserRightsCoupon> result = new ArrayList<>();
        for (BRights bRights : bUserCouponList) {
            // 封装参数
            String useRule = bRights.getUseRule();
            SubV3CouponRight subV3CouponRight = JSONObject.parseObject(useRule, SubV3CouponRight.class);
            List<SubV3CouponRight.CouponConfig> firstBox = subV3CouponRight.getFirstBox();
            List<SubV3CouponRight.CouponConfig> otherBox = subV3CouponRight.getOtherBox();
            if(CollectionUtils.isNotEmpty(insertBUserRightsList)){
                List<BNewUserRights> collect = insertBUserRightsList.stream().filter(r -> r.getRightsType().equals(RightsTypeEnum.APPLY_BOX_SUBCRIBE.getCode().longValue())).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(collect)){
                    Long totalNum = collect.get(0).getTotalNum();
                    for (int i = 0 ; i < 1 ; i ++){
                        if(CollectionUtils.isEmpty(firstBox)){
                            break;
                        }
                        SubV3CouponRight.CouponConfig couponConfig = null;
                        if(i == 0){
                             couponConfig = firstBox.get(0);
                        }

//                        else{
//                             couponConfig = otherBox.get(0);
//                        }

                        BNewUserRightsCoupon bNewUserRightsCoupon = new BNewUserRightsCoupon();
                        bNewUserRightsCoupon.setId(IdLeaf.getId(IdConstant.B_NEW_USER_RIGHTS_COUPON));
                        bNewUserRightsCoupon.setBRightsId(bRights.getId());
                        bNewUserRightsCoupon.setCreateTime(new Date());
                        bNewUserRightsCoupon.setUpdateTime(new Date());
                        bNewUserRightsCoupon.setOutNo(outNo);
                        bNewUserRightsCoupon.setUnionid(unionid);
                        bNewUserRightsCoupon.setIsDel(IsDeleteEnum.NORMAL.getCode().shortValue());
                        bNewUserRightsCoupon.setCouponAmount(couponConfig.getCouponAmount());
                        bNewUserRightsCoupon.setCouponJsonId(couponConfig.getId());
                        bNewUserRightsCoupon.setAwardid(couponConfig.getAwardId());
                        bNewUserRightsCoupon.setNum(Integer.parseInt(couponConfig.getNum()));
                        bNewUserRightsCoupon.setRightsType(bRights.getRightsType());
                        bNewUserRightsCoupon.setStatus(StatusEnum.NORMAL.getCode());
                        bNewUserRightsCoupon.setSendStatus(0);
                        bNewUserRightsCoupon.setStartTime(startDate);
                        bNewUserRightsCoupon.setEndTime(endDate);
                        bNewUserRightsCoupon.setUseRuleSnapshot(useRule);

                        result.add(bNewUserRightsCoupon);
                    }
                }
            }
        }
        return result;
    }

    private List<BUserRightsConsumeCoupon> buildBUserRightsConsumeCouponInsertList(List<BRights> bUserRightsConsumeCouponList,
                                                                                   Date startDate, Date endDate, String outNo, String unionid) {
        List<BUserRightsConsumeCoupon> insertBuserRightsConsumeCouponList = new ArrayList<>();
        return insertBuserRightsConsumeCouponList;
    }

    private List<BUserRightsMaterial> buildBUserRightsMaterialInsertList(List<BRights> bUserRightsMaterialList, Date startDate, Date endDate,
                                                                         String outNo, String unionid) {
        List<BUserRightsMaterial> insertBUserRightsMaterialList = new ArrayList<>();
        return insertBUserRightsMaterialList;
    }

    /**
     * 封装数据
     * @param bUserRightsList
     * @param startDate
     * @param endDate
     * @param outNo
     * @param unionid
     * @return
     */
    private List<BNewUserRights> buildBUserRightsInsertList(List<BRights> bUserRightsList,
                                                         Date startDate,
                                                         Date endDate,
                                                         String outNo,
                                                         String unionid) {
        List<BNewUserRights> result = new ArrayList<>();
        for (BRights bRights : bUserRightsList) {
            //1 为永久发放一次，需要查看该用户是否已经获取了权益，不管有没有失效  有无删除
            if(bRights.getIsOnceForAll() == 1){
                BNewUserRights bUserRights = new BNewUserRights();
                bUserRights.setUnionid(unionid);
                bUserRights.setbRightsId(bRights.getId());
                List<BNewUserRights> findBUserRightsList = bNewUserRightsMapper.selectListBySelective(bUserRights);
                if(CollectionUtils.isNotEmpty(findBUserRightsList)){
                    continue;
                }
            }

            String useRule = bRights.getUseRule();
            List<ApplyForBoxRights> applyForBoxRightsList = JSONObject.parseArray(useRule, ApplyForBoxRights.class);
            ApplyForBoxRights applyForBoxRights = applyForBoxRightsList.get(0);
            BNewUserRights userRights = new BNewUserRights();
            userRights.setId(IdLeaf.getId(IdConstant.B_NEW_USER_RIGHTS));
            userRights.setbRightsId(bRights.getId());
            userRights.setCreateTime(new Date());
            userRights.setUpdateTime(new Date());
            userRights.setTotalNum((long) applyForBoxRights.getTotalNum());
            userRights.setOutNo(outNo);
            userRights.setEndTime(endDate);
            userRights.setStartTime(startDate);
            userRights.setUnionid(unionid);
            userRights.setStatus(UserRightsStatusEnum.NORMAL.getCode().shortValue());
            userRights.setIsDel(IsDeleteEnum.NORMAL.getCode().shortValue());
            userRights.setLimitNumber(applyForBoxRights.getLimitNumber() == null? 0 : applyForBoxRights.getLimitNumber());
            userRights.setRightsType(bRights.getRightsType());
            result.add(userRights);
        }
        return result;
    }

    /**
     * 失效当前的这种卡的类型的  用户的权益
     * @param unionid
     * @param bMemberCard
     */
    public void expireUserRights(String unionid, List<BRights> bRightsList, BMemberCard bMemberCard) {
        // 查询到卡类型  相同卡类型全部失效掉
        Integer applicableParty = bMemberCard.getApplicableParty();

        List<String> brightsIds = bRightsList.stream().map(r -> r.getId()).collect(Collectors.toList());

        List<BUserRightsMaterial> shouldUpdateIsDelListBUserRightsMaterial =
                bUserRightsMaterialMapper.selectMaterialRightsByCardType(unionid,StatusEnum.NORMAL.getCode()
                        ,IsDeleteEnum.NORMAL.getCode(),applicableParty);

        List<BNewUserRights> shouldUpdateIsDelListBUserRights =
                bNewUserRightsMapper.selectUserRightsByCardType(unionid,StatusEnum.NORMAL.getCode(), IsDeleteEnum.NORMAL.getCode(),applicableParty);

        List<BUserRightsConsumeCoupon> findBUserRightsList =
                bUserRightsConsumeCouponMapper.selectConsumeRightsByCardType(unionid,StatusEnum.NORMAL.getCode(),IsDeleteEnum.NORMAL.getCode(),applicableParty);

        List<BNewUserRightsCoupon> bNewUserRightsCouponList =
                bNewUserRightsCouponMapper.selectRightsCouponByCardType(unionid,StatusEnum.NORMAL.getCode(),IsDeleteEnum.NORMAL.getCode(),applicableParty);



        List<String> materialRightsIds = shouldUpdateIsDelListBUserRightsMaterial.stream().map(r -> r.getId()).collect(Collectors.toList());
        List<String> rightsIds = shouldUpdateIsDelListBUserRights.stream().map(r -> r.getId()).collect(Collectors.toList());
        List<String> consumeCouponIds = findBUserRightsList.stream().map(r -> r.getId()).collect(Collectors.toList());
        List<String> bNewUserRightCouponIds = bNewUserRightsCouponList.stream().map(r -> r.getId()).collect(Collectors.toList());

        if(CollectionUtils.isNotEmpty(rightsIds)){
            bNewUserRightsMapper.batchUpdateStatusByPrimaryKeys(rightsIds,UserRightsStatusEnum.EXPIRED.getCode().longValue());
        }
        if(CollectionUtils.isNotEmpty(materialRightsIds)){
            bUserRightsMaterialMapper.batchUpdateStatusByPrimaryKeys(materialRightsIds,UserRightsStatusEnum.EXPIRED.getCode().longValue());
        }
        if(CollectionUtils.isNotEmpty(consumeCouponIds)){
            bUserRightsConsumeCouponMapper.batchUpdateStatusByPrimaryKeys(consumeCouponIds,UserRightsStatusEnum.EXPIRED.getCode().longValue());
        }
        if(CollectionUtils.isNotEmpty(bNewUserRightCouponIds)){
            bNewUserRightsCouponMapper.batchUpdateStatusByPrimaryKeys(bNewUserRightCouponIds,UserRightsStatusEnum.EXPIRED.getCode().longValue());
        }
    }

    @Override
    public Boolean checkRightsIsAvailableAndDeductionRights(CheckRightsEntity checkRightsEntity ){
        log.info("checkRightsIsAvailableAndDeductionRights  json = {}",JSONObject.toJSONString(checkRightsEntity));
        RightsTypeEnum rightsTypeEnum = checkRightsEntity.getRightsTypeEnum();
        Integer applicationParty = checkRightsEntity.getApplicationParty();
        String unionid = checkRightsEntity.getUnionid();
        Boolean isDeductionRights = checkRightsEntity.getIsDeductionRights();
        String outNo = checkRightsEntity.getOutNo();
        Integer number = checkRightsEntity.getNumber();
        CardTypeEnum cardTypeEnum = checkRightsEntity.getCardTypeEnum();
        Date date = checkRightsEntity.getDate();
        if(date == null){
            date = new Date();
            if(isDeductionRights && !checkRightsEntity.getIsHold()){
                date = null;
            }
        }

        log.info("checkRightsIsAvailableAndDeductionRights  rightsTypeEnumDesc = {},rightsTypeEnumCode = {},isDeductionRights={} ,cardType = {},unionid = {},cardTypeEnum= {}",
                rightsTypeEnum.getDesc(),rightsTypeEnum.getCode(),isDeductionRights,applicationParty,unionid,cardTypeEnum);

        List<BNewUserMemberCard> bUserMemberCardList =
                bNewUserMemberCardMapper.selectAvailableMemberCardByUserIdAndCardType(unionid, applicationParty, date);
        if(CollectionUtils.isEmpty(bUserMemberCardList)){
            log.info("checkRightsIsAvailableAndDeductionRights  效验卡是否有效，卡已经失效! cardType = {} , unionid = {}",applicationParty,unionid);
            return false;
        }

        List<BNewUserMemberCard> collect = bUserMemberCardList.stream().filter(r -> r.getCardType().equals(cardTypeEnum.getCode())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(collect)){
            log.info("checkRightsIsAvailableAndDeductionRights  效验卡是否有效，卡已经失效! cardType = {} , unionid = {}, 当前卡id已经失效 = {}",applicationParty,unionid,checkRightsEntity.getMemberCardId());
            return false;
        }

        String bMemberCardId = collect.get(0).getbMemberCardId();
        String memberCardOutNo = collect.get(0).getOutNo();

        //开始验证权益  次数类权益
        boolean contains = RightsTypeEnum.APPLY_BOX.getCode().longValue() == rightsTypeEnum.getCode().longValue() ||
                RightsTypeEnum.APPLY_BOX_SUBCRIBE.getCode().longValue() == rightsTypeEnum.getCode().longValue() ||
                RightsTypeEnum.APPLY_BOX_FREE.getCode().longValue() == rightsTypeEnum.getCode().longValue() ||
                RightsTypeEnum.DIY_THEME_BOX.getCode().longValue() == rightsTypeEnum.getCode().longValue() ||
                RightsTypeEnum.FIRST_ON_TRIAL_AFTER_BUY.getCode().longValue() == rightsTypeEnum.getCode().longValue();

        if(contains){
            // 获取该卡的对应的这种类型的权益   占用权益正常跑   使用权益则从log里面查
            // 校验权益 正常走原来的
            List<BNewUserRights> list;
            if(!checkRightsEntity.getIsDeductionRights()){
                list = bNewUserRightsMapper.selectAvailableUserRightsByUserId(unionid,rightsTypeEnum.getCode(),bMemberCardId, date, memberCardOutNo, applicationParty);
            }else{
                if(checkRightsEntity.getIsHold()){
                    list = bNewUserRightsMapper.selectAvailableUserRightsByUserId(unionid,rightsTypeEnum.getCode(),bMemberCardId, date, memberCardOutNo, applicationParty);
                }else{
                    list = new ArrayList<>();
                    // 使用权益
                    List<BNewUserRightsLog> bNewUserRightsLogs = bUserRightsLogMapper.selectByOutNo(outNo);
                    if(CollectionUtils.isNotEmpty(bNewUserRightsLogs)){
                        String getbNewUserRightsId = bNewUserRightsLogs.get(0).getbNewUserRightsId();
                        BNewUserRights bNewUserRights = bNewUserRightsMapper.selectByPrimaryKey(getbNewUserRightsId);
                        list.add(bNewUserRights);
                    }
                }
            }

            if(CollectionUtils.isEmpty(list)){
                log.info("checkRightsIsAvailableAndDeductionRights  效验次数权益是否有效，次数权益已无效! cardType = {} , unionid = {}",
                        applicationParty,unionid);
                return false;
            }
            //效验是否有次数可以使用
            long surplusNum = list.get(0).getTotalNum() - list.get(0).getUsedNum();
            if(surplusNum <= 0){
                log.info("checkRightsIsAvailableAndDeductionRights  效验是否有剩余次数，已无剩余次数! bMemberCardId = {} , unionid = {} , totalNum = {}, holdNum = {}, usedNum = {}"
                        ,bMemberCardId,unionid,list.get(0).getTotalNum(),list.get(0).getHoldNum(),list.get(0).getUsedNum());
                //无使用次数时候直接更新为已经用完
                if(list.get(0).getTotalNum()- list.get(0).getUsedNum() <= 0){
                    template.execute(action->{
                        BNewUserRights update  = new BNewUserRights();
                        update.setStatus(UserRightsStatusEnum.OVER.getCode().shortValue());
                        update.setId(list.get(0).getId());
                        update.setUpdateTime(new Date());
                        bNewUserRightsMapper.updateByPrimaryKeySelective(update);
                        //更新已经用完
                        if(RightsTypeEnum.FIRST_ON_TRIAL_AFTER_BUY.getCode().longValue() == rightsTypeEnum.getCode().longValue()){
                            redisService.setAndLog(RedisKeyConstant.B_USER_RIGHTS_KEYS_TOTAL_NUM.join(bUserMemberCardList.get(0).getApplicationParty(),unionid),0);
                        }
                        return true;
                    });
                }

                return false;
            }
            if(RightsTypeEnum.FIRST_ON_TRIAL_AFTER_BUY.getCode().longValue() == rightsTypeEnum.getCode().longValue() &&
                    list.get(0).getLimitNumber()  < number){
                log.info("checkRightsIsAvailableAndDeductionRights  效验是否 试用件数是否够，已试用件数! bMemberCardId = {} , unionid = {} , limitNumber = {}, number = {}"
                        ,bMemberCardId,unionid,list.get(0).getLimitNumber(),number);
                return false;
            }

            if(isDeductionRights){
                // 查看outNo 是否已经消耗过
                if(checkRightsEntity.getIsHold()){
                    List<BNewUserRightsLog> bNewUserRightsLogs = bUserRightsLogMapper.selectByOutNo(outNo);
                    if(CollectionUtils.isNotEmpty(bNewUserRightsLogs)){
                        // 如果都是已经回滚过的  那么可以进行消耗
                        List<BNewUserRightsLog> collect1 = bNewUserRightsLogs.stream().filter(r -> r.getStatus().equals(1)).collect(Collectors.toList());
                        if(collect1.size() != bNewUserRightsLogs.size()){
                            log.info("消耗次数权益 outNo = {} , 当前outNo已经消耗过 不允许再次消耗 当做消耗成功",outNo);
                            return true;
                        }
                    }
                }

                template.execute(action->{
                        //使用权益  占用权益
                        if(checkRightsEntity.getIsHold()){
                            bNewUserRightsMapper.updateHoldRightsV3(list.get(0).getId(),unionid);
                        }else{
                            // 使用权益
                            bNewUserRightsMapper.updateUsedRightsV3(list.get(0).getId(),unionid);

                            long leaveNum = list.get(0).getTotalNum() -  list.get(0).getUsedNum();

                            //更新次数减一
                            if(RightsTypeEnum.FIRST_ON_TRIAL_AFTER_BUY.getCode().longValue() == rightsTypeEnum.getCode().longValue()){
                                long num = leaveNum - 1;
                                redisService.setAndLog(RedisKeyConstant.B_USER_RIGHTS_KEYS_TOTAL_NUM.join(bUserMemberCardList.get(0).getApplicationParty(),unionid),num);
                                redisService.setAndLog(RedisKeyConstant.B_USER_RIGHTS_IS_HAVE_TRY_AFTER_BUY.join(bUserMemberCardList.get(0).getApplicationParty(),unionid),1);
                            }

                            if(leaveNum == 1L){
                                BNewUserRights update  = new BNewUserRights();
                                update.setStatus(UserRightsStatusEnum.OVER.getCode().shortValue());
                                update.setId(list.get(0).getId());
                                update.setUpdateTime(new Date());
                                bNewUserRightsMapper.updateByPrimaryKeySelective(update);
                            }
                        }
                        insertBUserRightsLog(list.get(0),outNo,applicationParty,rightsTypeEnum,checkRightsEntity.getIsHold());
                        return true;
                });
            }
            return true;
        }

        //验证是否还有实物券类权益
        boolean isHaveMaterial = RightsTypeEnum.NEW_CUSTOMER_GIFT.getCode().longValue() == rightsTypeEnum.getCode().longValue() ||
                RightsTypeEnum.FAVOR_GIFT.getCode().longValue() == rightsTypeEnum.getCode().longValue();
        if(isHaveMaterial){
            //获取该卡对应的这种类型的权益
            List<BUserRightsMaterial> list = bUserRightsMaterialMapper.selectAvialableUserRightsMaterialByUserId(unionid,
                    rightsTypeEnum.getCode(),bMemberCardId,date, null);
            if(CollectionUtils.isEmpty(list)){
                log.info("checkRightsIsAvailableAndDeductionRights  效验实物/券类权益是否有效，实物/券类权益已无效! bMemberCardId = {} , userId = {}",bMemberCardId,unionid);
                return false;
            }
            if(isDeductionRights){
                template.execute(action->{
                        BUserRightsMaterial update  = new BUserRightsMaterial();
                        update.setStatus(UserRightsStatusEnum.OVER.getCode());
                        update.setId(list.get(0).getId());
                        update.setUpdateTime(new Date());
                        bUserRightsMaterialMapper.updateByPrimaryKeySelective(update);
                        return true;
                });
            }
            return true;
        }

        //消耗类权益
        boolean isHaveUserConsume = RightsTypeEnum.FASHIONER_SEND_BOX.getCode().longValue() == rightsTypeEnum.getCode().longValue();
        if(isHaveUserConsume){
            List<BUserRightsConsumeCoupon> list = bUserRightsConsumeCouponMapper.selectAvialableUserRightsConsumeCouponByUserId(unionid,rightsTypeEnum.getCode(),bMemberCardId, date, null);
            if(CollectionUtils.isEmpty(list)){
                log.info("checkRightsIsAvailableAndDeductionRights  效验累计类类权益是否有效，累计类权益已无效! bMemberCardId = {} , userId = {}",bMemberCardId,unionid);
                return false;
            }

            //相等
            if($gte.equals(list.get(0).getMethodName())){
                if(list.get(0).getTotalValue() < list.get(0).getRuleValue()){
                    log.info("checkRightsIsAvailableAndDeductionRights  效验累计类类权益是否有效，累计类权益规则次数未满足! bMemberCardId = {} , userId = {},totalValue = {},ruleValue= {}",
                            bMemberCardId,unionid,list.get(0).getTotalValue(),list.get(0).getRuleValue());
                    return false;
                }
            }else if($gt.equals(list.get(0).getMethodName())){
                if(list.get(0).getTotalValue() <= list.get(0).getRuleValue()){
                    log.info("checkRightsIsAvailableAndDeductionRights  效验累计类类权益是否有效，累计类权益规则次数未满足! bMemberCardId = {} , userId = {},totalValue = {},ruleValue= {}",
                            bMemberCardId,unionid,list.get(0).getTotalValue(),list.get(0).getRuleValue());
                    return false;
                }
            }else if($lte.equals(list.get(0).getMethodName())){
                if(list.get(0).getTotalValue() > list.get(0).getRuleValue()){
                    log.info("checkRightsIsAvailableAndDeductionRights  效验累计类类权益是否有效，累计类权益规则次数未满足! bMemberCardId = {} , userId = {},totalValue = {},ruleValue= {}",
                            bMemberCardId,unionid,list.get(0).getTotalValue(),list.get(0).getRuleValue());
                    return false;
                }
            }else if($lt.equals(list.get(0).getMethodName())){
                if(list.get(0).getTotalValue() >= list.get(0).getRuleValue()){
                    log.info("checkRightsIsAvailableAndDeductionRights  效验累计类类权益是否有效，累计类权益规则次数未满足! bMemberCardId = {} , userId = {},totalValue = {},ruleValue= {}",
                            bMemberCardId,unionid,list.get(0).getTotalValue(),list.get(0).getRuleValue());
                    return false;
                }
            }else{
                log.info("checkRightsIsAvailableAndDeductionRights  methodName 不在  gte lte gt lt 内");
                return false;
            }

            if(isDeductionRights){
                template.execute(action->{
                    BUserRightsConsumeCoupon update  = new BUserRightsConsumeCoupon();
                    update.setStatus(UserRightsStatusEnum.OVER.getCode());
                    update.setId(list.get(0).getId());
                    update.setUpdateTime(new Date());
                    bUserRightsConsumeCouponMapper.updateByPrimaryKeySelective(update);
                    return true;
                });
            }
            return true;
        }


        // 验证订阅3.0优惠券权益
        boolean isHaveSub3 = RightsTypeEnum.SUB3_COUPON.getCode().longValue() == rightsTypeEnum.getCode().longValue();
        if(isHaveSub3){
            // 找到可用的订阅3.0优惠券权益
            List<BNewUserRightsCoupon> list =
                    bNewUserRightsCouponMapper.selectAvialableUserRightsCouponByUnionId(unionid,rightsTypeEnum.getCode(),bMemberCardId, date, memberCardOutNo);
            if(isDeductionRights){
                if(CollectionUtils.isEmpty(list)){
                    log.info("checkRightsIsAvailableAndDeductionRights  校验3.0膨胀券规则类权益，权益已无效  订阅3.5 如果是消耗 则直接返回成功 没有的情况下! bMemberCardId = {} , unionid = {}",bMemberCardId,unionid);
                    return true;
                }
            }else{
                if(CollectionUtils.isEmpty(list)){
                    log.info("checkRightsIsAvailableAndDeductionRights  校验3.0膨胀券规则类权益，权益已无效! bMemberCardId = {} , unionid = {}",bMemberCardId,unionid);
                    return false;
                }
            }
            if(isDeductionRights){
                if(StringUtils.isNotBlank(outNo)){
                    List<String> consumeOutNo = new ArrayList<>();
                    consumeOutNo.add(outNo);
                    List<BNewUserRightsCoupon> bNewUserRightsCouponList = bNewUserRightsCouponMapper.selectByConsumeOutNos(consumeOutNo);
                    if(CollectionUtils.isNotEmpty(bNewUserRightsCouponList)){
                        log.info("该outNo 已经消耗过, outNo = {}",outNo);
                        return true;
                    }
                }

                Object checkRightsTypeEnumsParams = checkRightsEntity.getCheckRightsTypeEnumsParams();
                if(checkRightsTypeEnumsParams != null){
                    CheckRightCouponV3Dto checkRightCouponV3Dto = JSONObject.parseObject(JSONObject.toJSONString(checkRightsTypeEnumsParams), CheckRightCouponV3Dto.class);
                    // 为空则不是最后一个节点
                    // 拿到之前所有的已经用完的信息  获取到以前的金额
                    List<BNewUserRightsCoupon> bNewUserRightsCouponList = bNewUserRightsCouponMapper.selectAllUserRightsCouponByUnionId(unionid, rightsTypeEnum.getCode(), bMemberCardId, date);
                    List<BNewUserRightsCoupon> alreadySend = bNewUserRightsCouponList.stream().filter(r -> r.getStatus().equals(UserRightsStatusEnum.OVER.getCode())).collect(Collectors.toList());

                    // 验证权益      // 准备开始膨胀
                    BNewUserRightsCoupon bNewUserRightsCoupon = list.get(0);

                    BNewUserRightsCoupon update = new BNewUserRightsCoupon();
                    update.setId(bNewUserRightsCoupon.getId());
                    //外部消耗id
                    update.setConsumeOutNo(outNo);
                    update.setSendStatus(1);
                    update.setStatus(UserRightsStatusEnum.OVER.getCode());
                    update.setCalcParam(JSONObject.toJSONString(checkRightsTypeEnumsParams));
                    update.setUpdateTime(new Date());
                    // 更新为发送中
                    template.execute(action->{
                        bNewUserRightsCouponMapper.updateByPrimaryKeySelective(update);
                        return action;
                    });

                    // 首个盒子  不膨胀   仅发送优惠券
                    // 当前金额为 0  则不膨胀 仅发送   2024-12-02 膨胀券去除
                    if(!checkRightCouponV3Dto.getIsFirstBox()){
                        // 获取是否首次
                        if(bNewUserRightsCouponList.size() == list.size()){
                            checkRightCouponV3Dto.setIsFirstBox(true);
                        }
                        if(StringUtils.isBlank(checkRightCouponV3Dto.getCurrentAmount()) || checkRightCouponV3Dto.getCurrentAmount().equals("0")){
                            // 获取历史金额
                            BigDecimal alreadyAmount = BigDecimal.ZERO;

                            if(CollectionUtils.isNotEmpty(alreadySend)){
                                CheckRightCouponV3Dto che = JSONObject.parseObject(alreadySend.get(0).getCalcParam(), CheckRightCouponV3Dto.class);
                                alreadyAmount = alreadyAmount.add(new BigDecimal(che.getTotalAmount()));
                            }
                            //当前金额
                            BigDecimal subtract = new BigDecimal(checkRightCouponV3Dto.getTotalAmount()).subtract(alreadyAmount);
                            if(subtract.compareTo(BigDecimal.ZERO) >= 0){
                                checkRightCouponV3Dto.setCurrentAmount(subtract.toString());
                            }else{
                                checkRightCouponV3Dto.setCurrentAmount("0");
                            }
                            BigDecimal calcHistory = new BigDecimal(checkRightCouponV3Dto.getTotalAmount()).subtract(new BigDecimal(checkRightCouponV3Dto.getCurrentAmount()));
                            checkRightCouponV3Dto.setTotalAmount(calcHistory.toString());
                        }


                        // 膨胀 更新之后 进行发送优惠券
                        List<SubV3CouponRight.CouponConfig> expansions = calcExpansion(checkRightCouponV3Dto,null,bNewUserRightsCoupon.getUseRuleSnapshot());
                        if(CollectionUtils.isNotEmpty(expansions)){
                            // 创建记录
                            template.execute(action->{
                                for (SubV3CouponRight.CouponConfig expansion : expansions) {
                                    SendMaterialCouponLog sendMaterialCouponLog = new SendMaterialCouponLog();
                                    sendMaterialCouponLog.setId(IdLeaf.getId(IdConstant.SEND_MATERIAL_COUPON_LOG));
                                    sendMaterialCouponLog.setOutId(update.getId());
                                    sendMaterialCouponLog.setType(SendMaterialCouponLogTypeConstant.COUPON);
                                    sendMaterialCouponLog.setCreateTime(new Date());
                                    sendMaterialCouponLog.setUpdateTime(new Date());
                                    sendMaterialCouponLog.setIsDel(IsDeleteEnum.NORMAL.getCode());
                                    sendMaterialCouponLog.setAwardid(expansion.getAwardId());
                                    sendMaterialCouponLog.setNum(Integer.parseInt(expansion.getNum()));
                                    sendMaterialCouponLog.setPrice(expansion.getCouponAmount());
                                    sendMaterialCouponLog.setName(expansion.getCouponName());
                                    sendMaterialCouponLogMapper.insertSelective(sendMaterialCouponLog);
                                    update.setIsExpansion(expansion.getIsExpansion()?1:0);
                                }
                                if(checkRightCouponV3Dto.getIsFirstBox()){
                                    update.setIsFirst(1);
                                }
                                update.setUpdateTime(new Date());
                                bNewUserRightsCouponMapper.updateByPrimaryKeySelective(update);
                                return action;
                            });
                        }
                    }
                    // 首个盒子才发送优惠券
                    if(checkRightCouponV3Dto.getIsFirstBox()){
                        SendCouponBySdkEntity sendCouponBySdkEntity = sendCouponBySdk(bNewUserRightsCoupon);
                        if(sendCouponBySdkEntity.getSendFalg()){
                            BNewUserRightsCoupon update2 = new BNewUserRightsCoupon();
                            update2.setId(bNewUserRightsCoupon.getId());
                            update2.setSendStatus(2);
                            update2.setUpdateTime(new Date());
                            update2.setVoucherNos(sendCouponBySdkEntity.getVoucherNo());
                            List<SendCouponBySdkEntity.VoucherReq> voucherReqs = sendCouponBySdkEntity.getVoucherReqList();
                            List<SendCouponBySdkEntity.VoucherResp> voucherRespList = sendCouponBySdkEntity.getVoucherRespList();
                            // 更新数据
                            if(CollectionUtils.isNotEmpty(sendCouponBySdkEntity.getVoucherReqList()) && CollectionUtils.isNotEmpty(sendCouponBySdkEntity.getVoucherRespList())){
                                int r= 0;
                                int j = 0;
                                for (SendCouponBySdkEntity.VoucherReq voucherReq : voucherReqs) {
                                    Long num = voucherReq.getNum();
                                    j += num.intValue();
                                    List<SendCouponBySdkEntity.VoucherResp> voucherResps = voucherRespList.subList(r, j);
                                    for (SendCouponBySdkEntity.VoucherResp voucherResp : voucherResps) {
                                        voucherResp.setAwardId(voucherReq.getAwardId());
                                    }
                                    r += num.intValue();
                                }
                            }
                            template.execute(action->{
                                if(CollectionUtils.isNotEmpty(voucherRespList)){
                                    List<SendMaterialCouponLog> sendMaterialCouponLogs = sendMaterialCouponLogMapper.selectByOutIdAndType(update2.getId(),
                                            SendMaterialCouponLogTypeConstant.COUPON,
                                            IsDeleteEnum.NORMAL.getCode());
                                    // 处理数据
                                    for (SendMaterialCouponLog sendMaterialCouponLog : sendMaterialCouponLogs) {
                                        List<String> voucherNos = new ArrayList<>();
                                        for (SendCouponBySdkEntity.VoucherResp voucherResp : voucherRespList) {
                                            if(voucherResp.getAwardId().equals(Long.parseLong(sendMaterialCouponLog.getAwardid()))){
                                                voucherNos.add(voucherResp.getCode());
                                            }
                                        }
                                        sendMaterialCouponLog.setVoucherNos(voucherNos.stream().collect(Collectors.joining(",")));
                                    }
                                    if(CollectionUtils.isNotEmpty(sendMaterialCouponLogs)){
                                        for (SendMaterialCouponLog sendMaterialCouponLog : sendMaterialCouponLogs) {
                                            sendMaterialCouponLogMapper.updateSelective(sendMaterialCouponLog);
                                        }
                                    }
                                }
                                bNewUserRightsCouponMapper.updateByPrimaryKeySelective(update2);
                                return action;
                            });
                        }
                    }


                    // 查看是否是最后一个节点
                    List<BNewUserRightsCoupon> allUserRightsCouponByUnionId = bNewUserRightsCouponMapper.selectAllUserRightsCouponByUnionId(unionid, rightsTypeEnum.getCode(), bMemberCardId, date);
                    if(CollectionUtils.isNotEmpty(allUserRightsCouponByUnionId)){
                        // 获取consumeOutNo
                        List<String> collect1 = allUserRightsCouponByUnionId.stream().filter(r -> StringUtils.isNotBlank(r.getConsumeOutNo())).map(r -> r.getConsumeOutNo()).collect(Collectors.toList());
                        // 获取
                        List<BNewUserRights> bNewUserRights = bNewUserRightsMapper.selectbyOutNo(allUserRightsCouponByUnionId.get(0).getOutNo());
                        if(CollectionUtils.isNotEmpty(bNewUserRights)){
                            List<BNewUserRightsLog> bNewUserRightsLogs = bUserRightsLogMapper.selectByRightsId(bNewUserRights.get(0).getId());
                            if(CollectionUtils.isNotEmpty(bNewUserRightsLogs)){
                                Set<String> collect2 = bNewUserRightsLogs.stream().map(r -> r.getOutNo()).collect(Collectors.toSet());
                                if(collect2.size() == 6 && collect1.size() != 6){
                                        List<BNewUserRightsCoupon> leaveList =
                                                bNewUserRightsCouponMapper.selectAvialableUserRightsCouponByUnionId(unionid,rightsTypeEnum.getCode(),bMemberCardId, date, memberCardOutNo);
                                        if(CollectionUtils.isNotEmpty(leaveList)){
                                            template.execute(action->{
                                                for (BNewUserRightsCoupon newUserRightsCoupon : leaveList) {
                                                    BNewUserRightsCoupon shouldUpdate = new BNewUserRightsCoupon();
                                                    shouldUpdate.setStatus(0);
                                                    shouldUpdate.setId(newUserRightsCoupon.getId());
                                                    bNewUserRightsCouponMapper.updateByPrimaryKeySelective(shouldUpdate);
                                                }
                                                return action;
                                            });
                                        }
                                }
                            }
                        }
                    }
                    return true;
                }
            }
        }

        // 验证订阅3.0 实物权益
        boolean isHaveSub3BoxGift = RightsTypeEnum.SUB3_BOX_GIFT.getCode().longValue() == rightsTypeEnum.getCode().longValue();
        if(isHaveSub3BoxGift){
            // 找到可用的订阅3.0优惠券权益
            List<BNewUserBoxGift> list =
                    bNewUserBoxGiftMapper.selectAvialableUserRightsBoxGiftByUnionId(unionid,rightsTypeEnum.getCode(),bMemberCardId, date, memberCardOutNo);
            if(CollectionUtils.isEmpty(list)){
                log.info("checkRightsIsAvailableAndDeductionRights  校验3.0膨胀券规则类权益，权益已无效! bMemberCardId = {} , unionid = {}",bMemberCardId,unionid);
                return false;
            }

            if(isDeductionRights){
                if(StringUtils.isNotBlank(outNo)){
                    List<String> consumeOutNo = new ArrayList<>();
                    consumeOutNo.add(outNo);
                    List<BNewUserBoxGift> bNewUserBoxGifts = bNewUserBoxGiftMapper.selectByConsumeOutNos(consumeOutNo);
                    if(CollectionUtils.isNotEmpty(bNewUserBoxGifts)){
                        log.info("该outNo 已经消耗过, outNo = {}",outNo);
                        return true;
                    }
                }

                Object checkRightsTypeEnumsParams = checkRightsEntity.getCheckRightsTypeEnumsParams();
                if(checkRightsTypeEnumsParams != null){
                    // 如果已经有消耗的数据  那么查看更新时间
                    Boolean isShouldSend = true;
                    List<BNewUserBoxGift> allList =
                            bNewUserBoxGiftMapper.selectUserRightsBoxGiftByUnionId(unionid,rightsTypeEnum.getCode(),bMemberCardId, null, null);
                    if(CollectionUtils.isNotEmpty(allList)){
                        // 筛选发送时间不为空的
                        List<BNewUserBoxGift> collect1 = allList.stream().filter(r -> r.getSendBoxGiftTime() != null).collect(Collectors.toList());
                        if(CollectionUtils.isNotEmpty(collect1)){
                            List<BNewUserBoxGift> collect2 = collect1.stream().sorted(new Comparator<BNewUserBoxGift>() {
                                @Override
                                public int compare(BNewUserBoxGift o1, BNewUserBoxGift o2) {
                                    return o2.getSendBoxGiftTime().intValue() - o1.getSendBoxGiftTime().intValue();
                                }
                            }).collect(Collectors.toList());
                            // 最大值
                            BNewUserBoxGift bNewUserBoxGift = collect2.get(0);
                            if(DateUtils.formatDate(new Date(),"yyyyMM").equals(bNewUserBoxGift.getSendBoxGiftTime().toString())){
                                isShouldSend = false;
                            }
                        }
                    }

                    CheckBoxGiftRightsDto checkBoxGiftRightsDto = JSONObject.parseObject(JSONObject.toJSONString(checkRightsTypeEnumsParams), CheckBoxGiftRightsDto.class);
                    List<BNewUserBoxGift> shouldSend = list.stream().filter(r -> r.getSendNum().equals(checkBoxGiftRightsDto.getSendBoxNum())).collect(Collectors.toList());
                    if(CollectionUtils.isNotEmpty(shouldSend) && isShouldSend){
                        BNewUserBoxGift bNewUserBoxGift = shouldSend.get(0);
                        BNewUserBoxGift update = new BNewUserBoxGift();
                        update.setId(bNewUserBoxGift.getId());
                        //外部消耗id
                        update.setConsumeOutNo(outNo);
                        update.setSendStatus(1);
                        update.setUpdateTime(new Date());
                        update.setStatus(UserRightsStatusEnum.OVER.getCode());
                        update.setBoxSn(checkBoxGiftRightsDto.getSendMaterialDto().getSourceCode());
                        update.setCusPhone(checkBoxGiftRightsDto.getSendMaterialDto().getCusPhone());
                        update.setLogisticsStatus(1);
                        update.setSendBoxGiftTime(Long.parseLong(DateUtils.formatDate(new Date(),"yyyyMM")));
                        // 更新为发送中
                        template.execute(action->{
                            bNewUserBoxGiftMapper.updateByPrimaryKeySelective(update);
                            return action;
                        });
                        // 根据订阅id 查询到 邀请用户订阅的信息
                        // 查询订阅信息
                        Boolean sendFlag = false;

                        if(StringUtils.isNotBlank(checkBoxGiftRightsDto.getSendMaterialDto().getBoxStoreId())) {
                            //  如果在这里面 则直接结束
                            List<String> list1 = Arrays.asList(notSendGiftStoreId.split(","));
                            if (list1.contains(checkBoxGiftRightsDto.getSendMaterialDto().getBoxStoreId())) {
                                sendFlag = true;
                            }
                        }

                        // 默认 是走聚水潭的
                        if(!sendFlag){
                            sendFlag = sendMaterialBoxGift(bNewUserBoxGift,checkBoxGiftRightsDto);
                        }

                        if(sendFlag){
                            BNewUserBoxGift update2 = new BNewUserBoxGift();
                            update2.setId(bNewUserBoxGift.getId());
                            update2.setSendStatus(2);
                            update2.setUpdateTime(new Date());
                            template.execute(action->{
                                bNewUserBoxGiftMapper.updateByPrimaryKeySelective(update2);
                                return action;
                            });
                        }
                    }else if(CollectionUtils.isNotEmpty(shouldSend) && !isShouldSend){
                        // 失效这个权益
                        BNewUserBoxGift bNewUserBoxGift = shouldSend.get(0);
                        BNewUserBoxGift update = new BNewUserBoxGift();
                        update.setId(bNewUserBoxGift.getId());
                        //外部消耗id
                        update.setConsumeOutNo(outNo);
                        update.setSendStatus(-1);
                        update.setUpdateTime(new Date());
                        update.setStatus(UserRightsStatusEnum.FORBID.getCode());
                        update.setBoxSn(checkBoxGiftRightsDto.getSendMaterialDto().getSourceCode());
                        update.setCusPhone(checkBoxGiftRightsDto.getSendMaterialDto().getCusPhone());
                        update.setSendBoxGiftTime(Long.parseLong(DateUtils.formatDate(new Date(),"yyyyMM")));
                        update.setLogisticsStatus(-1);
                        // 更新为发送中
                        template.execute(action->{
                            bNewUserBoxGiftMapper.updateByPrimaryKeySelective(update);
                            return action;
                        });

                    }
                    return true;
                }
            }
        }

        return true;
    }

    private Boolean sendMaterialBoxGift(BNewUserBoxGift bNewUserBoxGift, CheckBoxGiftRightsDto checkBoxGiftRightsDto) {
        //TODO  需要发送快递
        CheckBoxGiftRightsDto.SendMaterialDto sendMaterialDto = checkBoxGiftRightsDto.getSendMaterialDto();
        String sku = bNewUserBoxGift.getSku();
        //封装product
        List<CheckBoxGiftRightsDto.ProductItem> productItems = new ArrayList<>();
        CheckBoxGiftRightsDto.ProductItem productItem = new CheckBoxGiftRightsDto.ProductItem();
        //根据sku查询数据  查询productId和 codeId

        CommonRequest<String> request = new CommonRequest<>();
        request.setRequestData(sku);
        ResponseResult<SkuProductInfoResp> productInfoBySku = iProductApi.getProductInfoBySku(request);
        if(productInfoBySku.getCode() != 0){
            log.info("sendMaterialBoxGift查询sku未查询到数据 = {}",sku);
            return false;
        }
        String productId = productInfoBySku.getData().getProductId();
        String skuId = productInfoBySku.getData().getSkuId();

        productItem.setId(productId);       //productId
        productItem.setCodeId(skuId);   //skuid
        productItem.setSkuCode(sku);
        productItem.setExchangeSkuCode(sku);
        productItem.setExchangeCodeId(skuId);

        productItems.add(productItem);
        List<CheckBoxGiftRightsDto.Payitem> payitems = new ArrayList<>();
        CheckBoxGiftRightsDto.Payitem payitem = new CheckBoxGiftRightsDto.Payitem();
        payitem.setId(skuId);  // skuid
        payitems.add(payitem);
        sendMaterialDto.setProductItem(productItems);
        sendMaterialDto.setPayitem(payitems);

        // 更新数据
        template.execute(action->{
            BNewUserBoxGift update = new BNewUserBoxGift();
            update.setId(bNewUserBoxGift.getId());
            update.setConsumeParams(JSONObject.toJSONString(sendMaterialDto));
            bNewUserBoxGiftMapper.updateByPrimaryKeySelective(update);
            return action;
        });

        // 使用聚水潭进行发货
        if(bNewUserBoxGift.getUseSendType().equals(1)){
            List<OrderPush2JSTDTO> orderPush2JSTDTOList = new ArrayList<>();
            OrderPush2JSTDTO orderPush2JSTDTO = new OrderPush2JSTDTO();
            orderPush2JSTDTO.setOrderDate(DateUtil.formatToStr(new Date(),DateUtil.DATE_FORMAT_YMDHM));
            String soId = sendMaterialDto.getSourceCode() + sku;
            orderPush2JSTDTO.setOuterSoId(soId);
            orderPush2JSTDTO.setSoId(soId);
            orderPush2JSTDTO.setPayAmount(BigDecimal.ZERO);
            orderPush2JSTDTO.setReceiverAddress(sendMaterialDto.getReceiver_address());
            orderPush2JSTDTO.setReceiverCity(sendMaterialDto.getC_city_name());
            orderPush2JSTDTO.setReceiverState(sendMaterialDto.getC_province_name());
            orderPush2JSTDTO.setReceiverDistrict(sendMaterialDto.getC_district_name());
            orderPush2JSTDTO.setReceiverName(sendMaterialDto.getReceiver_name());
            orderPush2JSTDTO.setReceiverPhone(sendMaterialDto.getReceiver_mobile());
            orderPush2JSTDTO.setRemark(bNewUserBoxGift.getBoxSn());
            orderPush2JSTDTO.setShopId(Integer.parseInt(jstShopId));
            List<OrderPush2JSTDTO.OrderPush2JSTDTOItem> items = new ArrayList<>();
            OrderPush2JSTDTO.OrderPush2JSTDTOItem item = new OrderPush2JSTDTO.OrderPush2JSTDTOItem();
            item.setAmount(BigDecimal.ZERO);
            item.setBasePrice(BigDecimal.ZERO);
            item.setName(bNewUserBoxGift.getBoxGiftName());
            item.setOuterOiId(bNewUserBoxGift.getId());
            item.setQty(1);
            item.setShopSkuId(sku);
            item.setSkuId(sku);
            items.add(item);
            orderPush2JSTDTO.setItems(items);
            orderPush2JSTDTOList.add(orderPush2JSTDTO);
            log.info("通过聚水潭进行发货  入参= {}",JSONObject.toJSONString(orderPush2JSTDTOList));
            try {
                ResponseResult<PushOrder2JSTRespDTO> pushOrder2JSTRespDTOResponseResult = retailApi.jstPushOrder(orderPush2JSTDTOList);
                log.info("通过聚水潭进行发货  返回= {}",JSONObject.toJSONString(pushOrder2JSTRespDTOResponseResult));
                if(pushOrder2JSTRespDTOResponseResult.getCode() == 0){
                    if(pushOrder2JSTRespDTOResponseResult.getData() != null && CollectionUtils.isNotEmpty(pushOrder2JSTRespDTOResponseResult.getData().getDatas())){
                        Boolean issuccess = pushOrder2JSTRespDTOResponseResult.getData().getDatas().get(0).getIssuccess();
                        if(issuccess){
                            BNewUserBoxGift update = new BNewUserBoxGift();
                            update.setId(bNewUserBoxGift.getId());
                            update.setEbOrder(pushOrder2JSTRespDTOResponseResult.getData().getDatas().get(0).getSoId());
                            bNewUserBoxGiftMapper.updateByPrimaryKeySelective(update);
                            return true;
                        }else{
                            BNewUserBoxGift update = new BNewUserBoxGift();
                            update.setId(bNewUserBoxGift.getId());
                            update.setEbOrder(pushOrder2JSTRespDTOResponseResult.getData().getDatas().get(0).getMsg());
                            bNewUserBoxGiftMapper.updateByPrimaryKeySelective(update);
                            return true;
                        }
                    }
                }
            }catch (Exception e ){
                log.error("聚水潭  发送订阅3.0实物礼品异常   bNewUserBoxGift = {}, checkBoxGiftRightsDto = {}",JSONObject.toJSONString(bNewUserBoxGift),
                        JSONObject.toJSONString(checkBoxGiftRightsDto),e);
            }
            return false;
        }

        try {
            log.info("sendMaterialBoxGift  checkBoxGiftRightsDto = {}, bNewUserBoxGift = {}",JSONObject.toJSONString(checkBoxGiftRightsDto),JSONObject.toJSONString(bNewUserBoxGift));
            // 发送快递
            Response<CustomerBaseResponse<SendMaterialBoxGiftContext>> execute = iJicInfoHttpApi.sendMaterialBoxGift(sendMaterialDto).execute();
            log.info("sendMaterialBoxGift = {}",JSONObject.toJSONString(execute));
            log.info("sendMaterialBoxGift = {}",JSONObject.toJSONString(execute.body()));
            CustomerBaseResponse<SendMaterialBoxGiftContext> body = execute.body();
            if(execute.isSuccessful()){
                boolean b = execute.body().getCode() == 200;
                if(b){
                    SendMaterialBoxGiftContext data = execute.body().getData();
                    template.execute(action->{
                        BNewUserBoxGift update = new BNewUserBoxGift();
                        update.setId(bNewUserBoxGift.getId());
                        update.setEbOrder(data.getErrMsg());
                        bNewUserBoxGiftMapper.updateByPrimaryKeySelective(update);
                        return action;
                    });
                    return true;
                }
            }
        }catch (Exception e){
            log.error("发送订阅3.0实物礼品异常   bNewUserBoxGift = {}, checkBoxGiftRightsDto = {}",JSONObject.toJSONString(bNewUserBoxGift),
                    JSONObject.toJSONString(checkBoxGiftRightsDto),e);
        }
        return false;
    }

    private SendCouponBySdkEntity sendCouponBySdk(BNewUserRightsCoupon bak) {
        BNewUserRightsCoupon bNewUserRightsCoupon = bNewUserRightsCouponMapper.selectByPrimaryKey(bak.getId());

        // 发券  根据brighs里面配置的发放 TODO
        SendCouponBySdkEntity sendCouponBySdkEntity = new SendCouponBySdkEntity();

        MemberQueryContext memberQueryContext = new MemberQueryContext();
        memberQueryContext.setUnionId(bNewUserRightsCoupon.getUnionid());
        memberQueryContext.setStatus("Y");
        String openId = "";
        try {
            CustomerBaseResponse<List<MemberCardEntity>> memberCardList = iUserVipService.getMemberCardList(memberQueryContext);
            if(CollectionUtils.isNotEmpty(memberCardList.getData())){
                openId = memberCardList.getData().get(0).getOpenId();
                if(openId.contains(memberCardList.getData().get(0).getCardNo())){
                    openId = "";
                }
            }
        }catch (Exception e){
            log.info("sendCouponBySdk 查询用户卡列表失败 ",e);
        }

        JICVoucherSendReqEntity jicVoucherSendReqEntity = new JICVoucherSendReqEntity();
        jicVoucherSendReqEntity.setOpenId(StringUtils.isBlank(openId)?bNewUserRightsCoupon.getUnionid():openId);

        List<RulesList> rulesLists = new ArrayList<>();
        // 查询新表
        List<SendMaterialCouponLog> sendMaterialCouponLogs = sendMaterialCouponLogMapper.selectByOutIdAndType(bak.getId(),
                SendMaterialCouponLogTypeConstant.COUPON,
                IsDeleteEnum.NORMAL.getCode());

        if(CollectionUtils.isNotEmpty(sendMaterialCouponLogs)){
            for (SendMaterialCouponLog sendMaterialCouponLog : sendMaterialCouponLogs) {
                RulesList rules = new RulesList();
                rules.setAwardId(Long.parseLong(sendMaterialCouponLog.getAwardid()));
                rules.setNum(sendMaterialCouponLog.getNum().longValue());
                rulesLists.add(rules);
            }
        }else{
            if(StringUtils.isNotBlank(bNewUserRightsCoupon.getAwardid())){
                RulesList rules = new RulesList();
                rules.setAwardId(Long.parseLong(bNewUserRightsCoupon.getAwardid()));
                rules.setNum(bNewUserRightsCoupon.getNum().longValue());
                rulesLists.add(rules);
            }
        }

        if(CollectionUtils.isEmpty(rulesLists)){
            throw  new RuntimeException("发券错误!规则id为空，不能发放!");
        }

        jicVoucherSendReqEntity.setRulesList(rulesLists);
        JICVoucherSendRespEntity jicVoucherSendRespEntity = iVoucherService.sendVoucher(jicVoucherSendReqEntity);
        if (jicVoucherSendRespEntity != null && jicVoucherSendRespEntity.getCode() == 200 && ObjectUtils.isNotEmpty(jicVoucherSendRespEntity.getData())) {
            String voucher = jicVoucherSendRespEntity.getData().getR_voucher();
            if (StringUtils.isBlank(voucher)) {
                log.error("发券失败 msg = {}, e = {}", jicVoucherSendRespEntity.getData().getRetmsg(), jicVoucherSendRespEntity);
                throw  new RuntimeException("发券失败，调用发券接口失败，不能发放!");
            }
            List<SendCouponBySdkEntity.VoucherResp> maps = JSONObject.parseArray(voucher, SendCouponBySdkEntity.VoucherResp.class);
            List<SendCouponBySdkEntity.VoucherReq> reqInJson = JSONObject.parseArray(JSONObject.toJSONString(rulesLists), SendCouponBySdkEntity.VoucherReq.class);
            StringBuffer voucherCode = new StringBuffer();

            for(int i =0 ; i< maps.size(); i++){
                if(i == maps.size() - 1){
                    voucherCode.append(maps.get(i).getCode());
                }else{
                    voucherCode.append(maps.get(i).getCode());
                    voucherCode.append(",");
                }
            }
            sendCouponBySdkEntity.setSendFalg(true);
            sendCouponBySdkEntity.setVoucherNo(voucherCode.toString());
            sendCouponBySdkEntity.setVoucherRespList(maps);
            sendCouponBySdkEntity.setVoucherReqList(reqInJson);
        }else if(jicVoucherSendRespEntity != null){
            log.error("发券失败 jicVoucherSendReqEntityReq = {}, resp = {}", JSONObject.toJSONString(jicVoucherSendReqEntity), JSONObject.toJSONString(jicVoucherSendRespEntity));
            throw  new RuntimeException("发券失败，发券接口返回信息错误，不能发放!");
        }
        return sendCouponBySdkEntity;
    }

    /**
     *
     * @param checkRightCouponV3Dto
     * @param bRightsId
     * @param useRuleSnapshot
     * @return
     */
    private List<SubV3CouponRight.CouponConfig> calcExpansion(CheckRightCouponV3Dto checkRightCouponV3Dto, String bRightsId,String useRuleSnapshot) {
        // 计算

        SubV3CouponRight subV3CouponRight = null;
        if(StringUtils.isNotBlank(bRightsId)){
            BRights bRights = bRightsMapper.selectByPrimaryKey(bRightsId);
            subV3CouponRight   = JSONObject.parseObject(bRights.getUseRule(), SubV3CouponRight.class);
        }else{
            subV3CouponRight   = JSONObject.parseObject(useRuleSnapshot, SubV3CouponRight.class);
        }

        List<SubV3CouponRight.ExpansionRuleData> expansionRule = subV3CouponRight.getExpansionRule();
        List<SubV3CouponRight.CouponConfig> firstBox = subV3CouponRight.getFirstBox();
        List<SubV3CouponRight.CouponConfig> otherBox = subV3CouponRight.getOtherBox();
        // 只会返回 firstBox
        if(true){
            return firstBox;
        }

        if(checkRightCouponV3Dto.getIsFirstBox()){
            return firstBox;
        }

        // 1. 取模  取历史金额的
        if(CollectionUtils.isNotEmpty(expansionRule)){
            // 历史金额
            String historyTotalAmount = checkRightCouponV3Dto.getTotalAmount();
            // 本次消费金额
            String currentAmount = checkRightCouponV3Dto.getCurrentAmount();
            // 历史金额
            BigDecimal historyTotalAmountBigdecimal = new BigDecimal(historyTotalAmount);
            // 从最开始开始算 + 本次消费金额  计算的金额
            BigDecimal calcAmount = new BigDecimal(currentAmount).add(new BigDecimal(historyTotalAmount));
            String totalAmount = expansionRule.get(expansionRule.size() - 1).getTotalAmount();

            // 总金额大于 1万  并且 历史小于1万
            if(calcAmount.compareTo(new BigDecimal(totalAmount)) >= 0  && historyTotalAmountBigdecimal.compareTo(new BigDecimal(totalAmount)) < 0){
                // 最高档
                List<SubV3CouponRight.CouponConfig> couponConfig = expansionRule.get(expansionRule.size() - 1).getList();
                for (SubV3CouponRight.CouponConfig config : couponConfig) {
                    config.setLimit(expansionRule.get(expansionRule.size() - 1).getTotalAmount());
                    config.setNextLevelCalcAmount("-1");
                    config.setIsAlert(true);
                    config.setIsExpansion(true);
                }

                return couponConfig;
            }

            // 历史大于1万
            if(historyTotalAmountBigdecimal.compareTo(new BigDecimal(totalAmount)) >= 0){
                // 最高档
                List<SubV3CouponRight.CouponConfig> couponConfig = expansionRule.get(expansionRule.size() - 1).getList();
                for (SubV3CouponRight.CouponConfig config : couponConfig) {
                    config.setLimit(expansionRule.get(expansionRule.size() - 1).getTotalAmount());
                    config.setNextLevelCalcAmount("-1");
                    config.setIsAlert(false);
                    config.setIsExpansion(true);
                }
                return couponConfig;
            }

            // 规则
            if(checkRightCouponV3Dto.getIsCalcNextLevel()){
                for(int i = 0 ; i < expansionRule.size() ; i++){
                    // 金额
                    BigDecimal rulePrice = new BigDecimal(expansionRule.get(i).getTotalAmount());

                    if(i == expansionRule.size()-1){
                        // 小于最大的规则金额 并且 最终金额大于等于最大规则金额
                        if(historyTotalAmountBigdecimal.compareTo(rulePrice) < 0 && calcAmount.compareTo(rulePrice) >= 0){
                            expansionRule.get(i).getList().get(0).setIsAlert(true);
                        }

                        if(calcAmount.compareTo(rulePrice) < 0){
                            List<SubV3CouponRight.CouponConfig> couponConfig = expansionRule.get(i).getList();
                            BigDecimal diffAmount = rulePrice.subtract(calcAmount);
                            for (SubV3CouponRight.CouponConfig config : couponConfig) {
                                config.setLimit(expansionRule.get(i).getTotalAmount());
                                config.setNextLevelCalcAmount(String.valueOf(diffAmount));
                            }
                            return couponConfig;
                        }else{
                            // 到顶了
                            List<SubV3CouponRight.CouponConfig> couponConfig = expansionRule.get(i).getList();
                            for (SubV3CouponRight.CouponConfig config : couponConfig) {
                                config.setLimit(expansionRule.get(i).getTotalAmount());
                                config.setNextLevelCalcAmount("-1");
                            }
                            return couponConfig;
                        }
                    }else{
                        // 只计算满足条件的
                        if(calcAmount.compareTo(rulePrice) < 0){
                            List<SubV3CouponRight.CouponConfig> couponConfig = expansionRule.get(i).getList();
                            BigDecimal diffAmount = rulePrice.subtract(calcAmount);
                            for (SubV3CouponRight.CouponConfig config : couponConfig) {
                                config.setLimit(expansionRule.get(i).getTotalAmount());
                                config.setNextLevelCalcAmount(String.valueOf(diffAmount));
                            }
                            return couponConfig;
                        }
                    }
                }
            }else{

                for(int i = expansionRule.size() -1; i >= 0; i--){
                    // 规则金额
                    BigDecimal rulePrice = new BigDecimal(expansionRule.get(i).getTotalAmount());
                    // 只计算满足条件的
                    if(calcAmount.compareTo(rulePrice) >= 0 && historyTotalAmountBigdecimal.compareTo(rulePrice) < 0){
//                        expansionRule.get(i).getList().get(0).setIsAlert(true);
                        List<SubV3CouponRight.CouponConfig> couponConfig = expansionRule.get(i).getList();
                        for (SubV3CouponRight.CouponConfig config : couponConfig) {
                            config.setIsAlert(true);
                            config.setLimit(expansionRule.get(i).getTotalAmount());
                            config.setIsExpansion(true);
                        }
                        return couponConfig;
                    }
                }
            }
        }
        // 不膨胀
        return otherBox;
    }

    @Override
    public Integer getBUserMemberCardIdByFashionerId(String fashionerId) {

        return 1;
    }


    @Override
    public String giftCardId() {
        //第一位 导购 卡 第二位搭配师卡id
        return   bMemberCardIds;

    }

    public void insertBUserRightsLog(BNewUserRights bUserRights, String outNo, Integer applicationParty, RightsTypeEnum rightsTypeEnum,Boolean isHold) {
        BNewUserRightsLog bNewUserRightsLog = new BNewUserRightsLog();
        bNewUserRightsLog.setId(IdLeaf.getId(IdConstant.B_NEW_USER_RIGHTS_LOG));
        bNewUserRightsLog.setbNewUserRightsId(bUserRights.getId());
        bNewUserRightsLog.setUnionid(bUserRights.getUnionid());
        bNewUserRightsLog.setApplicationParty(applicationParty);
        bNewUserRightsLog.setOutNo(outNo);
        if(isHold){
            bNewUserRightsLog.setBeforeUsedNum(bUserRights.getUsedNum().intValue());
            bNewUserRightsLog.setAfterUsedNum(bUserRights.getUsedNum().intValue());
            bNewUserRightsLog.setBeforeHold(bUserRights.getHoldNum().intValue());
            bNewUserRightsLog.setAfterHold(bUserRights.getHoldNum().intValue() + 1);
        }else{
            bNewUserRightsLog.setBeforeUsedNum(bUserRights.getUsedNum().intValue());
            bNewUserRightsLog.setAfterUsedNum(bUserRights.getUsedNum().intValue() +1);
            if(bUserRights.getHoldNum() > 0){
                bNewUserRightsLog.setBeforeHold(bUserRights.getHoldNum().intValue());
                bNewUserRightsLog.setAfterHold(bUserRights.getHoldNum().intValue()-1);
            }else{
                bNewUserRightsLog.setBeforeHold(bUserRights.getHoldNum().intValue());
                bNewUserRightsLog.setAfterHold(bUserRights.getHoldNum().intValue());
            }
        }
        bNewUserRightsLog.setStatus(0);
        bNewUserRightsLog.setIsHaveUnFinish(1);
        bNewUserRightsLog.setCreateTime(new Date());
        bNewUserRightsLog.setUpdateTime(new Date());
        bUserRightsLogMapper.insertSelective(bNewUserRightsLog);
        //更新redis
        if(RightsTypeEnum.FIRST_ON_TRIAL_AFTER_BUY.getCode().longValue() == rightsTypeEnum.getCode().longValue()){
            String key = RedisKeysEnum.B_USER_HAVE_UN_FINISH_BOX.join(applicationParty,bUserRights.getUnionid());
            redisService.setAndLog(key,1);
        }
    }


    @Override
    public void increaseOrDeductionRightsConsumeCoupon(String boxSn, Integer increaseOrDecutionType,Integer cardType) {
    }

    @Override
    public void cumulativeConsumeOrDecution(String orderId,
                                  String customerDetailId,
                                  Integer increaseOrDecutionType) {
    }

    public void insertBConsumeFreeSubLog(String bConsumeFreeSubId,
                                         String orderId,
                                         String customerDetailId,
                                         BigDecimal finalBeforeCumulativeNum,
                                         BigDecimal finalAfterCumulativeNum,
                                         Integer finalBeforeAvaliableNum,
                                         Integer finalAfterAvaliableNum) {
    }

    @Override
    public Boolean deductionOrCheckConsume(String customerDetailId,Boolean isCheck){
        return true;
    }

    /**
     * 验证用户卡是否可用+
     * @param userId
     * @param applicationParty
     * @return
     */
    @Override
    public List<BNewUserMemberCard> getUserMemberCardAvailable(String userId, Integer applicationParty) {
        return bNewUserMemberCardMapper.selectAvailableMemberCardByUserIdAndCardType(userId,applicationParty,new Date());
    }



    /**
     * 获取 法和服务频次  搭配组成
     发盒&还货双向包邮物流
     每次搭配的件数&可搭多少次
     * @param bRightsList
     * @return
     */
    private List<BRights> getFourRights(List<BRights> bRightsList) {
        if(CollectionUtils.isEmpty(bRightsList)){
            return new ArrayList<>();
        }
        List<BRights> collect = bRightsList.stream().filter(e -> e.getRightsType().equals(RightsTypeEnum.ONLY_SHOW.getCode().longValue())).collect(Collectors.toList());
        return collect;
    }

    @Override
    public Boolean recoverRights(Integer applicationParty, UseRightsContext context) {
        if(context.getIsHold() == null){
            context.setIsHold(true);
        }
        AtomicReference<Boolean> reoverBoolean = new AtomicReference<>(false);
        List<BNewUserRightsLog> bNewUserRightsLogs = bUserRightsLogMapper.selectByOutNo(context.getOutNo());
        if(CollectionUtils.isNotEmpty(bNewUserRightsLogs)){
            BNewUserRightsLog bNewUserRightsLog = bNewUserRightsLogs.get(0);
            if(bNewUserRightsLog.getStatus() == 1){
                // 当前的条已经恢复过了
                return false;
            }
        }
        if(CollectionUtils.isEmpty(bNewUserRightsLogs)){
            log.info("根据外部单号未查询到可以回滚的数据 context = {}",context);
            return true;
        }

        template.execute(action->{
            BNewUserRightsLog bNewUserRightsLog = bNewUserRightsLogs.get(0);
            String bNewUserRightsId = bNewUserRightsLog.getbNewUserRightsId();
            BNewUserRights bNewUserRights = bNewUserRightsMapper.selectById(bNewUserRightsId);
            BRights bRights = bRightsMapper.selectByPrimaryKey(bNewUserRights.getbRightsId());
            int updateNum = 0;
            if(context.getIsHold()){
                 updateNum = bNewUserRightsMapper.updateReleaseUserNum(bNewUserRights.getId());
            }else{
                updateNum = bNewUserRightsMapper.updateReleaseUsedNumV2(bNewUserRights.getId());
            }
            if(updateNum > 0){
                List<BNewUserMemberCard> bNewUserMemberCards = bNewUserMemberCardMapper.selectByOutNo(bNewUserRights.getOutNo());
//                List<BNewUserMemberCard> bNewUserMemberCards = bNewUserMemberCardMapper.selectAvailableMemberCardByUserId(context.getUnionid(), bRights.getbMemberCardId(), null);
                context.setUserRightsId(bNewUserRights.getId());
                updateBNewUserRightsLog(context.getOutNo());
                if(bRights.getRightsType().equals(RightsTypeEnum.APPLY_BOX.getCode().longValue())){
                    //先试后买
                }else if(bRights.getRightsType().equals(RightsTypeEnum.FIRST_ON_TRIAL_AFTER_BUY.getCode().longValue())){
                    long num = bNewUserRights.getTotalNum() - bNewUserRights.getUsedNum() - bNewUserRights.getHoldNum() + 1;
                    if(bNewUserRights.getTotalNum() >= num){
                        redisService.setAndLog(RedisKeyConstant.B_USER_RIGHTS_KEYS_TOTAL_NUM.join(bNewUserMemberCards.get(0).getApplicationParty(),context.getUnionid()),num);
                    }
                    reoverBoolean.set(true);
                }
                reoverBoolean.set(true);
            }
            return action;
        });
        return reoverBoolean.get();
    }


    public void updateBNewUserRightsLog(String outNo) {
        List<BNewUserRightsLog> bNewUserRightsLogs = bUserRightsLogMapper.selectByOutNo(outNo);
        if(CollectionUtils.isNotEmpty(bNewUserRightsLogs)){
            for (BNewUserRightsLog bNewUserRightsLog : bNewUserRightsLogs) {
                BNewUserRightsLog update = new BNewUserRightsLog();
                update.setId(bNewUserRightsLog.getId());
                update.setStatus(1);
                if(bNewUserRightsLog.getIsHaveUnFinish() == 1){
                    update.setIsHaveUnFinish(0);
                }
                bUserRightsLogMapper.updateByPrimarySelevtive(update);
                if(bNewUserRightsLog.getIsHaveUnFinish() == 1){
                    String key = RedisKeysEnum.B_USER_HAVE_UN_FINISH_BOX.join(bNewUserRightsLogs.get(0).getApplicationParty(),bNewUserRightsLogs.get(0).getUnionid());
                    redisService.set(key,0);
                }
            }
        }
        //更新redis
    }


    private void finishCustomerExpand(BNewUserRights bUserRight,Integer applicationParty,UseRightsContext context) {

//        //更新剩余次数
//        BNewUserRights bUserRights1 = bNewUserRightsMapper.selectByPrimaryKey(bUserRight.getId());
//        long surpleCount = bUserRights1.getTotalNum() - bUserRights1.getUsedNum() - bUserRights1.getHoldNum();
//
//        //更新已经用完
//        BCustomerExpand customerExpand = new BCustomerExpand();
//        customerExpand.setUserId(context.getCustomerId());
//        if(ApplicationPartyEnum.GUIDE.getCardType().equals(applicationParty)){
//            customerExpand.setGuideSubStatus(1);
//            customerExpand.setGuideSurplusCount(new BigDecimal(surpleCount+""));
//        }else {
//            customerExpand.setFashionerSubStatus(1);
//            customerExpand.setGuideSurplusCount(new BigDecimal(surpleCount+""));
//        }
//        bCustomerExpandMapper.updateById(customerExpand);
    }



    @Override
    public void expireUserMemberCard() {
        //        //找到所有没有失效的卡
        com.github.pagehelper.Page<Object> hPageTotal = PageHelper.startPage(1, 100);
        List<BNewUserMemberCard> shouldUpdateStatusByDateTotal = bNewUserMemberCardMapper.findShouldUpdateStatusByDate(new Date());
        PageInfo<BNewUserMemberCard> pageInfoTotal = new PageInfo(hPageTotal);
        long total = pageInfoTotal.getTotal();

        long pageTotal = 0;
        int pageSize = 100;
        if(total % pageSize == 0){
            pageTotal = total / pageSize;
        }else{
            pageTotal = total / pageSize + 1;
        }

        for(int i = 1 ; i <= pageTotal; i++){
            // 分页查询
            com.github.pagehelper.Page<Object> hPage = PageHelper.startPage(i, 100);
            bNewUserMemberCardMapper.findShouldUpdateStatusByDate(new Date());
            PageInfo<BNewUserMemberCard> pageInfo = new PageInfo(hPage);
            List<BNewUserMemberCard> result = pageInfo.getList();

            try {
                for (BNewUserMemberCard bNewUserMemberCard : result) {
                    // 查询权益
                    List<BNewUserRights> bNewUserRights = bNewUserRightsMapper.selectbyOutNo(bNewUserMemberCard.getOutNo());
                    for (BNewUserRights bNewUserRight : bNewUserRights) {
                        if(bNewUserMemberCard.getApplicationParty().equals(ApplicablePartyEnum.COMPANY.getCode())){
                            redisService.setAndLog(RedisKeyConstant.B_USER_RIGHTS_KEYS_TOTAL_NUM.join(bNewUserMemberCard.getApplicationParty(), bNewUserRight.getUnionid()),"-1");
                        }
                   }
                }

            }catch (Exception e){
                log.error("expireUserMemberCard =   page = {} ,Data = {}",e, i,JSONObject.toJSONString(result));
                continue;
            }
        }

        // 定时失效用户卡信息
        bNewUserMemberCardMapper.batchUpdateStatutsByDate(new Date());
    }

    @Override
    public void resetUserRightsJob(Integer cycleType) {
        //        //找到所有没有失效的卡
        com.github.pagehelper.Page<Object> hPageTotal = PageHelper.startPage(1, 100);
        List<BNewUserMemberCard> bUserMemberCardsList = bNewUserMemberCardMapper.selectValidListSelective(DateUtils.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss"), null);
        PageInfo<BNewUserMemberCard> pageInfoTotal = new PageInfo(hPageTotal);
        long total = pageInfoTotal.getTotal();

        long pageTotal = 0;
        int pageSize = 100;
        if(total % pageSize == 0){
            pageTotal = total / pageSize;
        }else{
            pageTotal = total / pageSize + 1;
        }

        for(int i = 1 ; i <= pageTotal; i++){
            // 分页查询
            com.github.pagehelper.Page<Object> hPage = PageHelper.startPage(i, 100);
            bNewUserMemberCardMapper.selectValidListSelective(DateUtils.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss"), null);
            PageInfo<BNewUserMemberCard> pageInfo = new PageInfo(hPage);
            List<BNewUserMemberCard> bUserMemberCards = pageInfo.getList();

            try {

                List<BUserRightsConsumeCoupon> bUserRightsConsumeCoupons = new ArrayList<>();

                List<BUserRightsMaterial> bUserRightsMaterials = new ArrayList<>();

                List<BNewUserRights> findBUserRightsList = new ArrayList<>();

                Map<String, List<BNewUserMemberCard>> bUserMemberCardBroupByMemberCardId = bUserMemberCards.stream().collect(Collectors.groupingBy(r -> r.getbMemberCardId()));
                for (String bmemberCardId : bUserMemberCardBroupByMemberCardId.keySet()) {
                    // 找到所有的cycleType的卡类型
                    BRights params = new BRights();
                    params.setCycleType(cycleType);
                    params.setIsDel(IsDeleteEnum.NORMAL.getCode().longValue());
                    params.setStatus(StatusEnum.NORMAL.getCode().longValue());
                    params.setbMemberCardId(bmemberCardId);
                    List<BRights> bRights = bRightsMapper.selectListBySelective(params);

                    //次数类权益
                    List<BRights> bUserRightsList = bRights.stream().filter(e -> RightsTypeEnum.getBUserRightsList().contains(e.getRightsType().intValue())).collect(Collectors.toList());
                    //实物类权益
                    List<BRights> bUserRightsMaterialList = bRights.stream().filter(e -> RightsTypeEnum.getBUserRightsMaterialList().contains(e.getRightsType().intValue())).collect(Collectors.toList());
                    //消费叠加类权益
                    List<BRights> bUserRightsConsumeCouponList = bRights.stream().filter(e -> RightsTypeEnum.getBUserRightsConsumeCouponList().contains(e.getRightsType().intValue())).collect(Collectors.toList());

                    List<BNewUserMemberCard> bUserMemberCards1 = bUserMemberCardBroupByMemberCardId.get(bmemberCardId);

                    //用户信息
                    List<List<String>> partition = new ArrayList<>();
                    //只过滤不为空的
                    List<String> outNos = bUserMemberCards1.stream().filter(r->StringUtils.isNotBlank(r.getOutNo())).map(r -> r.getOutNo()).collect(Collectors.toList());
                    if(outNos.size() > 90){
                        partition = Lists.partition(outNos, 90);
                    }else{
                        partition.add(outNos);
                    }

                    if(CollectionUtils.isNotEmpty(bUserRightsList)){
                        for (List<String> partitionOutNos : partition) {
                            findBUserRightsList.addAll(bNewUserRightsMapper.selectByRightsIds(bUserRightsList.stream().map(r->r.getId()).collect(Collectors.toList()),partitionOutNos));
                        }
                    }


                    if(CollectionUtils.isNotEmpty(bUserRightsMaterialList)){
                        for (List<String> partitionOutNos : partition) {
                            bUserRightsMaterials.addAll(bUserRightsMaterialMapper.selectByRightsIds(bUserRightsMaterialList.stream().map(r->r.getId()).collect(Collectors.toList()),partitionOutNos));
                        }
                    }


                    if(CollectionUtils.isNotEmpty(bUserRightsConsumeCouponList)){
                        for (List<String> partitionOutNos : partition) {
                            bUserRightsConsumeCoupons.addAll(bUserRightsConsumeCouponMapper.selectByRightsIds(bUserRightsConsumeCouponList.stream().map(r->r.getId()).collect(Collectors.toList()),partitionOutNos));
                        }
                    }
                }


                List<BNewUserRights> finalFindBUserRightsList = findBUserRightsList;
                List<BUserRightsMaterial> finalBUserRightsMaterials = bUserRightsMaterials;
                List<BUserRightsConsumeCoupon> finalBUserRightsConsumeCoupons = bUserRightsConsumeCoupons;
                template.execute(action->{
                    if(CollectionUtils.isNotEmpty(finalFindBUserRightsList)){
                        if(finalFindBUserRightsList.size() > 90){
                            List<List<BNewUserRights>> partition = Lists.partition(finalFindBUserRightsList, 90);
                            for (List<BNewUserRights> bUserRights : partition) {
                                List<String> collect = bUserRights.stream().map(r -> r.getId()).collect(Collectors.toList());

                                bNewUserRightsMapper.batchUpdateStatusUsedNumHoldNumByPrimaryKeys(collect,StatusEnum.NORMAL.getCode().longValue());
                                for (BNewUserRights bNewUserRights : bUserRights) {

                                    insertBAddUserRightLog(bNewUserRights.getUnionid(),bNewUserRights.getOutNo());
                                    if(bNewUserRights.getRightsType().equals(RightsTypeEnum.FIRST_ON_TRIAL_AFTER_BUY.getCode().longValue())){
                                        // 更新次数
                                        BRights shouldUpdate = bRightsMapper.selectByPrimaryKey(bNewUserRights.getbRightsId());
                                        if(shouldUpdate != null){
                                            List<ApplyForBoxRights> applyForBoxRightsList = JSONObject.parseArray(shouldUpdate.getUseRule(), ApplyForBoxRights.class);
                                            ApplyForBoxRights applyForBoxRights = applyForBoxRightsList.get(0);

                                            // 更新次数
                                            BNewUserRights updateBNewUserRights = new BNewUserRights();
                                            updateBNewUserRights.setId(bNewUserRights.getId());
                                            updateBNewUserRights.setTotalNum((long)applyForBoxRights.getTotalNum());
                                            updateBNewUserRights.setLimitNumber(applyForBoxRights.getLimitNumber());
                                            bNewUserRightsMapper.updateByPrimaryKeySelective(updateBNewUserRights);
                                        }

                                        List<BNewUserMemberCard> bNewUserMemberCards = bNewUserMemberCardMapper.selectByOutNo(bNewUserRights.getOutNo());
                                        if(CollectionUtils.isNotEmpty(bNewUserMemberCards)){
                                            BNewUserMemberCard bNewUserMemberCard = bNewUserMemberCards.get(0);
                                            String memberCardId = bNewUserMemberCard.getbMemberCardId();
                                            BMemberCard bMemberCard = bNewMemberCardMapper.selectByPrimaryKey(memberCardId);
                                            BNewUserRights newBnewUserRights = bNewUserRightsMapper.selectById(bNewUserRights.getId());
                                            redisService.setAndLog(RedisKeyConstant.B_USER_RIGHTS_KEYS_TOTAL_NUM.join(bMemberCard.getApplicableParty(), bNewUserRights.getUnionid()),newBnewUserRights.getTotalNum()+"");
                                        }
                                   }
                                }
                            }
                        }else{
                            bNewUserRightsMapper.batchUpdateStatusUsedNumHoldNumByPrimaryKeys(finalFindBUserRightsList.stream().map(r->r.getId()).collect(Collectors.toList()),StatusEnum.NORMAL.getCode().longValue());
                            for (BNewUserRights bNewUserRights : finalFindBUserRightsList) {
                                insertBAddUserRightLog(bNewUserRights.getUnionid(),bNewUserRights.getOutNo());
                                if(bNewUserRights.getRightsType().equals(RightsTypeEnum.FIRST_ON_TRIAL_AFTER_BUY.getCode().longValue())){

                                    BRights shouldUpdate = bRightsMapper.selectByPrimaryKey(bNewUserRights.getbRightsId());
                                    if(shouldUpdate != null){
                                        List<ApplyForBoxRights> applyForBoxRightsList = JSONObject.parseArray(shouldUpdate.getUseRule(), ApplyForBoxRights.class);
                                        ApplyForBoxRights applyForBoxRights = applyForBoxRightsList.get(0);

                                        // 更新次数
                                        BNewUserRights updateBNewUserRights = new BNewUserRights();
                                        updateBNewUserRights.setId(bNewUserRights.getId());
                                        updateBNewUserRights.setTotalNum((long)applyForBoxRights.getTotalNum());
                                        updateBNewUserRights.setLimitNumber(applyForBoxRights.getLimitNumber());
                                        bNewUserRightsMapper.updateByPrimaryKeySelective(updateBNewUserRights);
                                    }

                                    List<BNewUserMemberCard> bNewUserMemberCards = bNewUserMemberCardMapper.selectByOutNo(bNewUserRights.getOutNo());
                                    if(CollectionUtils.isNotEmpty(bNewUserMemberCards)){
                                        BNewUserMemberCard bNewUserMemberCard = bNewUserMemberCards.get(0);
                                        String memberCardId = bNewUserMemberCard.getbMemberCardId();
                                        BMemberCard bMemberCard = bNewMemberCardMapper.selectByPrimaryKey(memberCardId);
                                        BNewUserRights newBnewUserRights = bNewUserRightsMapper.selectById(bNewUserRights.getId());
                                        redisService.setAndLog(RedisKeyConstant.B_USER_RIGHTS_KEYS_TOTAL_NUM.join(bMemberCard.getApplicableParty(), bNewUserRights.getUnionid()),newBnewUserRights.getTotalNum()+"");
                                    }                                }
                            }
                        }
                    }
                    if(CollectionUtils.isNotEmpty(finalBUserRightsMaterials)){
                        if(finalBUserRightsMaterials.size() > 90){
                            List<List<BUserRightsMaterial>> partition = Lists.partition(finalBUserRightsMaterials, 90);
                            for (List<BUserRightsMaterial> bUserRightsMaterials1 : partition) {
                                bUserRightsMaterialMapper.batchUpdateStatusByPrimaryKeys(bUserRightsMaterials1.stream().map(r->r.getId()).collect(Collectors.toList()),StatusEnum.NORMAL.getCode().longValue());
                            }
                        }else{
                            bUserRightsMaterialMapper.batchUpdateStatusByPrimaryKeys(finalBUserRightsMaterials.stream().map(r->r.getId()).collect(Collectors.toList()),StatusEnum.NORMAL.getCode().longValue());
                        }
                    }
                    if(CollectionUtils.isNotEmpty(finalBUserRightsConsumeCoupons)){
                        if(finalBUserRightsConsumeCoupons.size() > 90){
                            List<List<BUserRightsConsumeCoupon>> partition = Lists.partition(finalBUserRightsConsumeCoupons, 90);
                            for (List<BUserRightsConsumeCoupon> bUserRightsConsumeCoupons1 : partition) {
                                bUserRightsMaterialMapper.batchUpdateStatusByPrimaryKeys(bUserRightsConsumeCoupons1.stream().map(r->r.getId()).collect(Collectors.toList()),StatusEnum.NORMAL.getCode().longValue());
                            }
                        }else{
                            bUserRightsConsumeCouponMapper.batchUpdateStatusByPrimaryKeys(finalBUserRightsConsumeCoupons.stream().map(r->r.getId()).collect(Collectors.toList()),StatusEnum.NORMAL.getCode().longValue());
                        }
                    }
                    return true;
                });

            }catch (Exception e){
                log.error("resetUserRightsJob =   page = {} ,Data = {}",e, i,JSONObject.toJSONString(bUserMemberCards));
                continue;
            }
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult openCardAndRights(OpenCardAndRightsReq requestData) {
        String subcribeId = null;
        if(StringUtils.isBlank(requestData.getSubcribeId())){
            subcribeId = IdLeaf.getId(IdConstant.HTKK);
        }else{
            subcribeId = requestData.getSubcribeId();
        }

        String unionid = requestData.getUnionid();
        String bMemberCardId = requestData.getMemberCardId();
        Boolean isBlack = requestData.getIsBlack();
        // 1. 进行开卡
        BMemberCardType bMemberCardType = new BMemberCardType();
        bMemberCardType.setBMemberCardId(requestData.getMemberCardId());
        bMemberCardType.setIsDel(IsDeleteEnum.NORMAL.getCode());
        bMemberCardType.setStatus(1);
        List<BMemberCardType> bMemberCardTypes = bNewMemberCardTypeMapper.selectListBySelective(bMemberCardType);
        List<BGoodsSku> bGoodsSkuList = bGoodsSkuMapper.selectListByIds(bMemberCardTypes.stream().map(r -> r.getBGoodsSkuId()).collect(Collectors.toList()));
        BMemberCard template = bMemberCardMapper.selectByPrimaryKey(bMemberCardId);

        String bGoodsSkuId = null;
        BMemberCardType userCard = null ;
        //BOX
        if(template.getApplicableParty().equals(ApplicablePartyEnum.BOX.getCode())){
            for (BGoodsSku bGoodsSku : bGoodsSkuList) {
                if(requestData.getSuitCardLevel().equals(bGoodsSku.getSuitCardLevel())){
                    bGoodsSkuId  = bGoodsSku.getId();
                }
            }

            if(StringUtils.isBlank(bGoodsSkuId)){
                return ResponseResult.error("未查询到合适的卡适用类型");
            }

            for (BMemberCardType memberCardType : bMemberCardTypes) {
                if(bGoodsSkuId.equals(memberCardType.getBGoodsSkuId())){
                    userCard  = memberCardType;
                }
            }
        }else{
            BGoodsSku bGoodsSku = bGoodsSkuMapper.selectByPrimaryKey(bMemberCardTypes.get(0).getBGoodsSkuId());
            bGoodsSkuId = bGoodsSku.getId();
            userCard = bMemberCardTypes.get(0);
        }

        // 创建会员卡 非黑名单
        if(!isBlack){

            //需要查询用户是否有可用的卡
//            List<BNewUserMemberCard> bUserMemberCards = bNewUserMemberCardMapper.selectValidListSelective(DateUtils.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss"),
//                    unionid);

            List<BNewUserMemberCard> bUserMemberCards = bNewUserMemberCardMapper.selectByUnionidAndCardId(
                    unionid,bMemberCardId);

            //筛选当前的这张用户的可用的卡
            List<BNewUserMemberCard> validUserMemberCard = bUserMemberCards.stream().filter(r -> r.getApplicationParty().equals(template.getApplicableParty())
                    && r.getbMemberCardId().equals(bMemberCardId)).collect(Collectors.toList());
            //删除这个账户
//            if(CollectionUtils.isNotEmpty(validUserMemberCard)){
//                for (BNewUserMemberCard userMemberCard : validUserMemberCard) {
//                    BNewUserMemberCard bUserMemberCard  = new BNewUserMemberCard();
//                    bUserMemberCard.setId(userMemberCard.getId());
//                    bUserMemberCard.setStatus((short)3);
//                    bNewUserMemberCardMapper.updateByPrimaryKeySelective(bUserMemberCard);
//                    bNewUserMemberCardMapper.deleteByPrimaryKey(userMemberCard.getId());
//                }
//            }

            //更新账户数据
            String outNo = IdLeaf.getId(IdConstant.B_NEW_USER_MEMBER_CARD);
            if(CollectionUtils.isEmpty(validUserMemberCard)){
                BNewUserMemberCard memberCard = new BNewUserMemberCard();
                memberCard.setUnionid(unionid);
                memberCard.setbMemberCardId(template.getId());
                memberCard.setId(IdLeaf.getId(IdConstant.B_NEW_USER_MEMBER_CARD));
                memberCard.setCreateTime(new Date());
                memberCard.setUpdateTime(new Date());
                memberCard.setCardType(template.getType());
                memberCard.setApplicationParty(template.getApplicableParty());
                memberCard.setStartTime(DateUtil.getDate2Time(DateUtil.getCurrentTime()*1000));
                memberCard.setEndTime(DateUtil.getSomeDayEndTimes(DateUtil.addDate(memberCard.getStartTime(), userCard.getDays().intValue())));
                memberCard.setStatus(StatusEnum.NORMAL.getCode().shortValue());
                memberCard.setOutNo(outNo);
                memberCard.setSubscribeId(subcribeId);
                bNewUserMemberCardMapper.insertSelective(memberCard);
            }else{
                BNewUserMemberCard bNewUserMemberCard = validUserMemberCard.get(0);
                bNewUserMemberCard.setOutNo(outNo);
                if(StringUtils.isBlank(subcribeId)){
                    subcribeId = "";
                }
                bNewUserMemberCard.setSubscribeId(subcribeId);
                bNewUserMemberCard.setStatus(StatusEnum.NORMAL.getCode().shortValue());
                bNewUserMemberCard.setStartTime(DateUtil.getDate2Time(DateUtil.getCurrentTime()*1000));
                bNewUserMemberCard.setEndTime(DateUtil.getSomeDayEndTimes(DateUtil.addDate(bNewUserMemberCard.getStartTime(), userCard.getDays().intValue())));
                bNewUserMemberCard.setUpdateTime(new Date());
                bNewUserMemberCardMapper.updateByPrimaryKeySelective(bNewUserMemberCard);
            }

            //记录日志
            BUserMemberCardRenewLog renewLog = new BUserMemberCardRenewLog();
            renewLog.setUserId(requestData.getCustomerId());
            renewLog.setPaymentPrice(null);
            renewLog.setRenewDay(userCard.getDays());
            renewLog.setbMemberCardId(template.getId());
            renewLog.setDiscountCoin(null);
            renewLog.setCreateDate(new Date());
            renewLog.setId(IdLeaf.getId(IdConstant.B_USER_MEMBER_CARD_RENEW_LOG));

            BUserMemberCardRenewLog bUserMemberCardRenewLog = renewLogMapper.selectByOrderId(subcribeId);
            if(bUserMemberCardRenewLog != null){
                renewLog.setOrderId(subcribeId+"-guide");
            }else{
                renewLog.setOrderId(subcribeId);
            }
            renewLogMapper.insertSelective(renewLog);

            //发放权益
            buyMemberCardSendRights(bMemberCardId,unionid,outNo,userCard,subcribeId,requestData.getStoreId());
        }
//            subscribeByAdminOpen.subscribeByAdmin(customerDetails.get(0).getUnionid(),requestData.getMemberCardId());
        // 2. 进行开权益
//            buyMemberCardSendRights(requestData.getMemberCardId(),customerDetails.get(0).getId(),outNo);
        // 3. 进行后台开卡记录
        BMemberCard bMemberCard = bNewMemberCardMapper.selectByPrimaryKey(bMemberCardId);

        BUserMemberCardLog bUserMemberCardLog = new BUserMemberCardLog();
        bUserMemberCardLog.setCardName(bMemberCard.getName());
        bUserMemberCardLog.setCreateBy(requestData.getUserName());
        bUserMemberCardLog.setCreateTime(new Date());
        bUserMemberCardLog.setUpdateTime(new Date());
        bUserMemberCardLog.setId(IdLeaf.getId(IdConstant.B_USER_MEMBER_CARD_LOG));
        bUserMemberCardLog.setMemberCardId(bMemberCard.getId());
        bUserMemberCardLog.setOutNo(subcribeId);
        if(isBlack){
            bUserMemberCardLog.setStatus(0);
            bUserMemberCardLog.setRemark("用户为黑名单用户，无法开通微商城先试后买权益");
        }else{
            bUserMemberCardLog.setStatus(1);
        }

        bUserMemberCardLog.setUserId(requestData.getCustomerId());
        bUserMemberCardLog.setUserName(requestData.getCustomerName());
        bUserMemberCardLogMapper.insertSelective(bUserMemberCardLog);
        return ResponseResult.success();
    }


    @Override
    public void flushUserMemberCardAndRights() {
    }



    @Override
    public List<BNewUserRights> selectUserRightsByOutNo(String outNo) {
        return bNewUserRightsMapper.selectbyOutNo(outNo);
    }

    @Override
    public ResponseResult loseEffectCardByPhone(OpenCardAndRightsReq requestData) {

        List<BNewUserMemberCard> bUserMemberCards = bNewUserMemberCardMapper.selectValidListSelective(DateUtils.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss"), requestData.getUnionid());
        //筛选当前的这张用户的可用的卡
        //失效这张卡 所有卡
        if(CollectionUtils.isNotEmpty(bUserMemberCards)){
            template.execute(action->{
                for (BNewUserMemberCard userMemberCard : bUserMemberCards) {
                    BNewUserMemberCard bUserMemberCard  = new BNewUserMemberCard();
                    bUserMemberCard.setId(userMemberCard.getId());
                    bUserMemberCard.setStatus((short)3);
                    bNewUserMemberCardMapper.updateByPrimaryKeySelective(bUserMemberCard);
                    // 失效缓存
                    redisService.setAndLog(RedisKeyConstant.B_USER_RIGHTS_KEYS_TOTAL_NUM.join(userMemberCard.getApplicationParty(), userMemberCard.getUnionid()),"-1");
                    redisService.setAndLog(RedisKeyConstant.B_USER_RIGHTS_KEYS_LIMIT_NUM.join(userMemberCard.getApplicationParty(), userMemberCard.getUnionid()),"0");
                }
                return action;
            });
        }
        return ResponseResult.success();
    }

    @Override
    public Integer reflectAppidToApplicationParty(String appId) {
        List<Map> map = JSONObject.parseArray(reflectData, Map.class);
        for (Map map1 : map) {
            Object o = map1.get(appId);
            if(o != null){
                return Integer.parseInt(o.toString());
            }
        }
        throw  new RuntimeException("reflectAppidToApplicationParty"+appId+"未匹配");
    }

    @Override
    public Integer isHaveUnFinishBox(HaveUnFinishBoxReq requestData) {
        Integer reflectAppidToApplicationParty = reflectAppidToApplicationParty(requestData.getAppId());
        String key = RedisKeysEnum.B_USER_HAVE_UN_FINISH_BOX.join( reflectAppidToApplicationParty,requestData.getUnionId());
        Object num = redisService.get(key);
        if(num  == null){
            //查询库
            List<Integer> nums  = bUserRightsLogMapper.selectByApplicationPartyAndUnionId(requestData.getUnionId(),reflectAppidToApplicationParty);
            if(CollectionUtils.isNotEmpty(nums)){
                 num = nums.get(0);
                redisService.setAndLog(key,num);
                return Integer.parseInt(num.toString());
            }else{
                redisService.setAndLog(key,0);
                return 0;
            }
        }else{
            return Integer.parseInt(num.toString());
        }
    }

    @Override
    public Object dealTryAfterBuyIsHaveUnFinshBox(DealTryAfterBuyIsHaveUnFinishBoxReq requestData) {
        List<BNewUserRightsLog> bNewUserRightsLogs = bUserRightsLogMapper.selectByOutNo(requestData.getBoxId());
        if(CollectionUtils.isNotEmpty(bNewUserRightsLogs)){
            template.execute(action->{
                BNewUserRightsLog bNewUserRightsLog = bNewUserRightsLogs.get(0);
                BNewUserRightsLog update = new BNewUserRightsLog();
                update.setId(bNewUserRightsLog.getId());
                update.setIsHaveUnFinish(0);
                bUserRightsLogMapper.updateByPrimarySelevtive(update);

                String key = RedisKeysEnum.B_USER_HAVE_UN_FINISH_BOX.join(bNewUserRightsLogs.get(0).getApplicationParty(),bNewUserRightsLogs.get(0).getUnionid());
                redisService.setAndLog(key,0);
                return action;
            });

        }
        return null;
    }

    @Override
    public void flushCache() {
        List<RedisCacheLog> list = redisCacheLogMapper.selectIsDeal(0);
        for (RedisCacheLog redisCacheLog : list) {
            boolean b = redisService.setAndLog(redisCacheLog.getKey(), redisCacheLog.getValue());
            if(b){
                redisCacheLog.setIsDeal(1);
                redisCacheLogMapper.updateSelective(redisCacheLog);
            }
        }
    }

    @Override
    public List<BUserMemberCardLog> listForBUserMemberCardLog(ListForBUserMemberCardLogReq requestData, Page page) {
        com.github.pagehelper.Page<Object> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        bUserMemberCardLogMapper.selectByCreateTime(requestData.getStartTime(),requestData.getEndTime());
        PageInfo<BUserMemberCardLog> retPage = new PageInfo(hPage);
        page.setCount(hPage.getTotal());
        page.setPages(retPage.getPages());
        return retPage.getList();
    }

    @Override
    public List<CVipType> cardTypeAllCondition() {
        List<CVipType> cVipTypes = cVipTypeMapper.selectAll();
        return cVipTypes;
    }

    @Override
    public List<PreCalcResp> preCalcExpansionCoupon(List<CheckRightCouponV3DtoForPre> requestData) {
        List<PreCalcResp> resp = new ArrayList<>();
        List<CheckRightCouponV3DtoForPre> collect = requestData.stream().filter(r -> StringUtils.isNotBlank(r.getMemberCardId())).collect(Collectors.toList());

        for (CheckRightCouponV3DtoForPre requestDatum : collect) {
            PreCalcResp preCalcResp = new PreCalcResp();
            BeanUtils.copyProperties(requestDatum,preCalcResp);

            List<BNewUserRightsCoupon> list =
                    bNewUserRightsCouponMapper.selectAvialableUserRightsCouponByUnionId(requestDatum.getUnionId(), requestDatum.getRightsTypeEnum().getCode(),
                            requestDatum.getMemberCardId(), new Date(), null);

            List<BNewUserRightsCoupon> listAll =
                    bNewUserRightsCouponMapper.selectAllUserRightsCouponByUnionId(requestDatum.getUnionId(), requestDatum.getRightsTypeEnum().getCode(),
                            requestDatum.getMemberCardId(), new Date());

            if(CollectionUtils.isNotEmpty(list)){
                CheckRightCouponV3Dto checkRightCouponV3Dto = new CheckRightCouponV3Dto();
                BeanUtils.copyProperties(requestDatum,checkRightCouponV3Dto);

                if(list.size() == listAll.size()){
                    checkRightCouponV3Dto.setIsFirstBox(true);
                }
                // 判断是否 当前金额是 0
                if(StringUtils.isBlank(checkRightCouponV3Dto.getCurrentAmount()) || checkRightCouponV3Dto.getCurrentAmount().equals("0")){
                    BigDecimal alreadyAmount = BigDecimal.ZERO;
                    List<BNewUserRightsCoupon> alreadySend = listAll.stream().filter(r -> r.getStatus().equals(UserRightsStatusEnum.OVER.getCode())).collect(Collectors.toList());

                    if(CollectionUtils.isNotEmpty(alreadySend)){
                        CheckRightCouponV3Dto che = JSONObject.parseObject(alreadySend.get(0).getCalcParam(), CheckRightCouponV3Dto.class);
                        alreadyAmount = alreadyAmount.add(new BigDecimal(che.getTotalAmount()));
                    }
                    //当前金额
                    BigDecimal subtract = new BigDecimal(checkRightCouponV3Dto.getTotalAmount()).subtract(alreadyAmount);
                    if(subtract.compareTo(BigDecimal.ZERO) >= 0){
                        checkRightCouponV3Dto.setCurrentAmount(subtract.toString());
                    }else{
                        checkRightCouponV3Dto.setCurrentAmount("0");
                    }
                    // 重新设置历史总金额
                    BigDecimal calcHistory = new BigDecimal(checkRightCouponV3Dto.getTotalAmount()).subtract(new BigDecimal(checkRightCouponV3Dto.getCurrentAmount()));
                    checkRightCouponV3Dto.setTotalAmount(calcHistory.toString());
                }


                if(requestDatum.getIsCalcNextLevel()){
                    // 下一个档位
                    CheckRightCouponV3Dto bak = new CheckRightCouponV3Dto();
                    BeanUtils.copyProperties(checkRightCouponV3Dto,bak);
                    if(checkRightCouponV3Dto.getIsFirstBox()){
                        bak.setIsFirstBox(false);
                    }

                    List<SubV3CouponRight.CouponConfig> couponConfig = calcExpansion(bak,null,list.get(0).getUseRuleSnapshot());
                    // 当前档位

                    checkRightCouponV3Dto.setIsCalcNextLevel(false);
                    List<SubV3CouponRight.CouponConfig> couponConfig2 = calcExpansion(checkRightCouponV3Dto,null,list.get(0).getUseRuleSnapshot());

                    CouponConfigResp couponConfigResp = new CouponConfigResp();
                    if(CollectionUtils.isNotEmpty(couponConfig)){
                        BeanUtils.copyProperties(couponConfig.get(0),couponConfigResp);
                    }

                    CouponConfigResp couponConfigResp2 = new CouponConfigResp();
                    if(CollectionUtils.isNotEmpty(couponConfig2)){
                        BeanUtils.copyProperties(couponConfig2.get(0),couponConfigResp2);
                        preCalcResp.setNextCouponConfigResp(couponConfigResp2);
                    }
                    preCalcResp.setNextNextCouponConfigResp(couponConfigResp);

                }else{
                    List<SubV3CouponRight.CouponConfig> couponConfig = calcExpansion(checkRightCouponV3Dto,null,list.get(0).getUseRuleSnapshot());
                    CouponConfigResp couponConfigResp = new CouponConfigResp();
                    if(CollectionUtils.isNotEmpty(couponConfig)){
                        BeanUtils.copyProperties(couponConfig.get(0),couponConfigResp);
                        preCalcResp.setNextCouponConfigResp(couponConfigResp);
                    }
                }
                if(!checkRightCouponV3Dto.getIsFirstBox()){
                    preCalcResp.setNextNextCouponConfigResp(null);
                    preCalcResp.setNextCouponConfigResp(null);
                }
            }
            resp.add(preCalcResp);
        }
        return resp;
    }

    @Override
    public List<BNewUserMemberCardResp> getUserAvailableCardApplicationParty(UserAvailableCardApplicationPartyReq requestData) {
        List<BNewUserMemberCard> bUserMemberCardList =
                bNewUserMemberCardMapper.selectAvailableMemberCardByUserIdAndCardType(requestData.getUnionid(), requestData.getApplicationParty(), new Date());
        List<BNewUserMemberCardResp> bNewUserMemberCardResps = JSONObject.parseArray(JSONObject.toJSONString(bUserMemberCardList), BNewUserMemberCardResp.class);
        return bNewUserMemberCardResps;
    }

    @Override
    public List<SubMemberCardRightsResp> getSubMemberCardRights(SubMemberCardRightsReq subMemberCardRightsReq) {
        List<SubMemberCardRightsResp> result = new ArrayList<>();

        List<BMemberCard> bMemberCards = new ArrayList<>();
        if(!subMemberCardRightsReq.getSelectCardType()){
            Integer cardType = subMemberCardRightsReq.getCardType();
            //获取卡以及权益  多个 取 box类型的， 可用的  未删除的
            BMemberCard bMemberCard = new BMemberCard();
            bMemberCard.setType(cardType);
            bMemberCard.setIsDel(IsDeleteEnum.NORMAL.getCode());
            bMemberCard.setStatus(StatusEnum.NORMAL.getCode());
            bMemberCard.setApplicableParty(ApplicablePartyEnum.BOX.getCode());
            bMemberCards = bMemberCardMapper.selectListBySelective(bMemberCard);

        }else{
            Integer cardType = subMemberCardRightsReq.getCardType();
            //获取卡以及权益  多个 取 box类型的， 可用的  未删除的
            BMemberCard bMemberCard = new BMemberCard();
            bMemberCard.setTypes(subMemberCardRightsReq.getCardTypes());
            bMemberCard.setIsDel(IsDeleteEnum.NORMAL.getCode());
            bMemberCard.setStatus(StatusEnum.NORMAL.getCode());
            bMemberCard.setApplicableParty(ApplicablePartyEnum.BOX.getCode());
            bMemberCards = bMemberCardMapper.selectListBySelective(bMemberCard);
        }

        if(CollectionUtils.isEmpty(bMemberCards)){
            return new ArrayList<>();
        }

        List<String> ids = bMemberCards.stream().map(r -> r.getId()).collect(Collectors.toList());
        List<BMemberCardType> bMemberCardTypes = bNewMemberCardTypeMapper.selectByMemberCardIds(ids);
        Map<String, List<BMemberCardType>> groupByMemberCardId = bMemberCardTypes.stream().collect(Collectors.groupingBy(r -> r.getBMemberCardId()));

        // 查询sku
        List<String> skuIds = bMemberCardTypes.stream().map(r -> r.getBGoodsSkuId()).collect(Collectors.toList());
        List<BGoodsSku> bGoodsSkus = bGoodsSkuMapper.selectListByIds(skuIds);
        Map<String, List<BGoodsSku>> groupById = bGoodsSkus.stream().collect(Collectors.groupingBy(r -> r.getId()));

        // 查询权益
        List<BRights> bRights = bRightsMapper.selectByMemberCardIds(ids);
        Map<String, List<BRights>> groupByBmemberCardId = bRights.stream().collect(Collectors.groupingBy(r -> r.getbMemberCardId()));


        for (BMemberCard memberCard : bMemberCards) {
            // 多个
            List<BRights> bRights1 = groupByBmemberCardId.get(memberCard.getId());

            List<BMemberCardType> bMemberCardTypeList = groupByMemberCardId.get(memberCard.getId());
            if(subMemberCardRightsReq.getSuitCardLevel() != null){
                for (BMemberCardType bMemberCardType : bMemberCardTypeList) {
                    List<BGoodsSku> bGoodsSkuList = groupById.get(bMemberCardType.getBGoodsSkuId());
                    if(subMemberCardRightsReq.getSuitCardLevel().equals(bGoodsSkuList.get(0).getSuitCardLevel())){
                        BGoodsSku bGoodsSku = bGoodsSkuList.get(0);

                        SubMemberCardRightsResp subMemberCardRightsResp = new SubMemberCardRightsResp();
                        subMemberCardRightsResp.setCardIntegralExchange(bGoodsSku.getExchangePoint() == null ? BigDecimal.ZERO: bGoodsSku.getExchangePoint());
                        subMemberCardRightsResp.setMarkThePrice(bGoodsSku.getMarkThePrice());
                        subMemberCardRightsResp.setPrice(bGoodsSku.getPrice() == null ? BigDecimal.ZERO: new BigDecimal(bGoodsSku.getPrice()));
                        subMemberCardRightsResp.setCardValue(memberCard.getCardValue());
                        subMemberCardRightsResp.setBMemberCardId(memberCard.getId());
                        subMemberCardRightsResp.setSuitCardLevel(bGoodsSku.getSuitCardLevel());
                        subMemberCardRightsResp.setHaveRenewalOffer(bGoodsSku.getHaveRenewalOffer());
                        subMemberCardRightsResp.setSubExpireDays(bGoodsSku.getSubExpireDays());
                        subMemberCardRightsResp.setDiscount(bGoodsSku.getDiscount());
                        subMemberCardRightsResp.setCardType(memberCard.getType());
                        subMemberCardRightsResp.setDays(bMemberCardType.getDays());
                        if(bMemberCardType.getDays() != null){
                            subMemberCardRightsResp.setStartDate(DateUtil.getDate2Time(DateUtil.getCurrentTime()*1000));
                            subMemberCardRightsResp.setEndDate(DateUtil.getSomeDayEndTimes(DateUtil.addDate(subMemberCardRightsResp.getStartDate(), bMemberCardType.getDays().intValue())));
                        }


                        if(StringUtils.isNotBlank(bGoodsSku.getDiscount())){
                            BigDecimal multiply = subMemberCardRightsResp.getPrice().multiply(new BigDecimal(bGoodsSku.getDiscount()));
                            BigDecimal decimal1 = multiply.setScale(0, BigDecimal.ROUND_UP);
                            subMemberCardRightsResp.setDisPrice(decimal1);
                            BigDecimal multiply1 = subMemberCardRightsResp.getCardIntegralExchange().multiply(new BigDecimal(bGoodsSku.getDiscount()));
                            BigDecimal decimal = multiply1.setScale(0, BigDecimal.ROUND_UP);
                            subMemberCardRightsResp.setDisCardIntegralExchange(decimal);
                        }
                        List<BRightsDto> collect = bRights1.stream().sorted(new Comparator<BRights>() {
                            @Override
                            public int compare(BRights o1, BRights o2) {
                                if(o1.getSorted().intValue() - o2.getSorted().intValue() == 0){
                                    if(o2.getCreateTime().getTime() - o1.getSorted().intValue() > 0){
                                        return 1;
                                    }else if(o2.getCreateTime().getTime() - o1.getSorted().intValue() < 0){
                                        return -1;
                                    }
                                    return 0;
                                }
                                return o1.getSorted().intValue() - o2.getSorted().intValue();
                            }
                        }).map(r -> {
                            BRightsDto rightsDto = new BRightsDto();
                            BeanUtils.copyProperties(r, rightsDto);
                            return rightsDto;
                        }).collect(Collectors.toList());
                        subMemberCardRightsResp.setRightsRespList(collect);
                        result.add(subMemberCardRightsResp);
                    }
                }
            }else{
                for (BMemberCardType bMemberCardType : bMemberCardTypeList) {
                    List<BGoodsSku> bGoodsSkuList = groupById.get(bMemberCardType.getBGoodsSkuId());
                    BGoodsSku bGoodsSku = bGoodsSkuList.get(0);

                    SubMemberCardRightsResp subMemberCardRightsResp = new SubMemberCardRightsResp();
                    subMemberCardRightsResp.setCardIntegralExchange(bGoodsSku.getExchangePoint() == null ? BigDecimal.ZERO: bGoodsSku.getExchangePoint());
                    subMemberCardRightsResp.setMarkThePrice(bGoodsSku.getMarkThePrice());
                    subMemberCardRightsResp.setPrice(bGoodsSku.getPrice() == null ? BigDecimal.ZERO: new BigDecimal(bGoodsSku.getPrice()));
                    subMemberCardRightsResp.setCardValue(memberCard.getCardValue());
                    subMemberCardRightsResp.setBMemberCardId(memberCard.getId());
                    subMemberCardRightsResp.setSuitCardLevel(bGoodsSku.getSuitCardLevel());
                    subMemberCardRightsResp.setHaveRenewalOffer(bGoodsSku.getHaveRenewalOffer());
                    subMemberCardRightsResp.setSubExpireDays(bGoodsSku.getSubExpireDays());
                    subMemberCardRightsResp.setDiscount(bGoodsSku.getDiscount());
                    subMemberCardRightsResp.setCardType(memberCard.getType());
                    subMemberCardRightsResp.setDays(bMemberCardType.getDays());
                    if(bMemberCardType.getDays() != null){
                        subMemberCardRightsResp.setStartDate(DateUtil.getDate2Time(DateUtil.getCurrentTime()*1000));
                        subMemberCardRightsResp.setEndDate(DateUtil.getSomeDayEndTimes(DateUtil.addDate(subMemberCardRightsResp.getStartDate(), bMemberCardType.getDays().intValue())));
                    }

                    if(StringUtils.isNotBlank(bGoodsSku.getDiscount())){
                        BigDecimal multiply = subMemberCardRightsResp.getPrice().multiply(new BigDecimal(bGoodsSku.getDiscount()));
                        BigDecimal decimal1 = multiply.setScale(0, BigDecimal.ROUND_UP);
                        subMemberCardRightsResp.setDisPrice(decimal1);
                        BigDecimal multiply1 = subMemberCardRightsResp.getCardIntegralExchange().multiply(new BigDecimal(bGoodsSku.getDiscount()));
                        BigDecimal decimal = multiply1.setScale(0, BigDecimal.ROUND_UP);
                        subMemberCardRightsResp.setDisCardIntegralExchange(decimal);
                    }


                    List<BRightsDto> collect = bRights1.stream().sorted(new Comparator<BRights>() {
                        @Override
                        public int compare(BRights o1, BRights o2) {
                            return o1.getSorted().intValue() - o2.getSorted().intValue();
                        }
                    }).map(r -> {
                        BRightsDto rightsDto = new BRightsDto();
                        BeanUtils.copyProperties(r, rightsDto);
                        return rightsDto;
                    }).collect(Collectors.toList());
                    subMemberCardRightsResp.setRightsRespList(collect);
                    result.add(subMemberCardRightsResp);
                }
            }
        }
        return result;
    }

    @Override
    public List<BNewUserBoxGiftResp> getUserBoxGiftRights(List<UserBoxGiftRightsReq> requestData) {
        List<BNewUserBoxGiftResp> result = new ArrayList<>();
        List<UserBoxGiftRightsReq> collect1 = requestData.stream().filter(r -> StringUtils.isNotBlank(r.getMemberCardId())).collect(Collectors.toList());


        for (UserBoxGiftRightsReq requestDatum : collect1) {
            List<BNewUserBoxGift> bNewUserBoxGifts = bNewUserBoxGiftMapper.selectUserRightsBoxGiftByUnionId(requestDatum.getUnionid(), requestDatum.getRightsTypeEnum().getCode(), requestDatum.getMemberCardId(), null, null);
            if(CollectionUtils.isEmpty(bNewUserBoxGifts)){
                return result;
            }
            Integer totalNum = requestDatum.getSendNumIndex();
            if(CollectionUtils.isNotEmpty(bNewUserBoxGifts)){
                List<BNewUserBoxGift> collect = bNewUserBoxGifts.stream().filter(r -> r.getSendNum().equals(totalNum)).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(collect)){
                    List<BNewUserBoxGiftResp> resps = collect.stream().map(r -> {
                        BNewUserBoxGiftResp bNewUserBoxGiftResp = new BNewUserBoxGiftResp();
                        BeanUtils.copyProperties(r, bNewUserBoxGiftResp);
                        bNewUserBoxGiftResp.setSendNumIndex(requestDatum.getSendNumIndex());
                        return bNewUserBoxGiftResp;
                    }).collect(Collectors.toList());
                    result.addAll(resps);
                }
            }
        }
        return result;
    }

    @Override
    public List<BNewUserBoxGiftResp> getUserBoxGiftRightsByConsumeOutNo(UserRightsByConsumeOutNoReq requestData) {
        List<BNewUserBoxGiftResp> list = new ArrayList<>();

        if(CollectionUtils.isEmpty(requestData.getConsumeOutNos())){
            return list;
        }
        List<BNewUserBoxGift> bNewUserBoxGifts = bNewUserBoxGiftMapper.selectByConsumeOutNosAndStatus(requestData.getConsumeOutNos());
        for (BNewUserBoxGift bNewUserBoxGift : bNewUserBoxGifts) {
            BNewUserBoxGiftResp bNewUserBoxGiftResp = new BNewUserBoxGiftResp();
            BeanUtils.copyProperties(bNewUserBoxGift,bNewUserBoxGiftResp);
            list.add(bNewUserBoxGiftResp);
        }
        return list;
    }

    @Override
    public List<BNewUserRightsCouponResp> getUserSubCouponRightsByConsumeOutNo(UserRightsByConsumeOutNoReq requestData) {

        List<BNewUserRightsCouponResp> list = new ArrayList<>();

        if(CollectionUtils.isEmpty(requestData.getConsumeOutNos())){
            return list;
        }
        List<BNewUserRightsCoupon> bNewUserRightsCouponList = bNewUserRightsCouponMapper.selectByConsumeOutNos(requestData.getConsumeOutNos());
        Map<String,List<SendMaterialCouponLog>> map = new HashMap<>();

        if(CollectionUtils.isNotEmpty(bNewUserRightsCouponList)){
            // 获取兼容的表的券
            List<String> collect = bNewUserRightsCouponList.stream().map(r -> r.getId()).collect(Collectors.toList());
            List<SendMaterialCouponLog>   sendMaterialCouponLogs = sendMaterialCouponLogMapper.selectByOutIdsAndType(collect, SendMaterialCouponLogTypeConstant.COUPON, IsDeleteEnum.NORMAL.getCode());
            map = sendMaterialCouponLogs.stream().collect(Collectors.groupingBy(r -> r.getOutId()));
        }

        for (BNewUserRightsCoupon bNewUserRightsCoupon : bNewUserRightsCouponList) {
            BNewUserRightsCouponResp bNewUserRightsCouponResp = new BNewUserRightsCouponResp();
            BeanUtils.copyProperties(bNewUserRightsCoupon,bNewUserRightsCouponResp);

            List<SendMaterialCouponLog> sendMaterialCouponLogs = map.get(bNewUserRightsCoupon.getId());
            if(CollectionUtils.isNotEmpty(sendMaterialCouponLogs)){
                List<String> voucherNos = new ArrayList<>();
                for (SendMaterialCouponLog sendMaterialCouponLog : sendMaterialCouponLogs) {
                    if(StringUtils.isNotBlank(sendMaterialCouponLog.getVoucherNos())){
                        List<String> list1 = Arrays.asList(sendMaterialCouponLog.getVoucherNos().split(","));
                        voucherNos.addAll(list1);
                    }
                }
                String jsonString = JSONObject.toJSONString(sendMaterialCouponLogs);
                bNewUserRightsCouponResp.setSendMaterialCouponLogRespList(JSONObject.parseArray(jsonString,SendMaterialCouponLogResp.class));
                bNewUserRightsCouponResp.setVoucherNos(voucherNos.stream().collect(Collectors.joining(",")));
            }else{
                // 为空 则进行老数据到新数据的处理
                List<SendMaterialCouponLogResp> sendMaterialCouponLogRespList = new ArrayList<>();
                SendMaterialCouponLogResp sendMaterialCouponLogResp = new SendMaterialCouponLogResp();
                sendMaterialCouponLogResp.setAwardid(bNewUserRightsCouponResp.getAwardid());
                sendMaterialCouponLogResp.setNum(bNewUserRightsCouponResp.getNum());
                sendMaterialCouponLogResp.setType(SendMaterialCouponLogTypeConstant.COUPON);
                sendMaterialCouponLogResp.setIsDel(IsDeleteEnum.NORMAL.getCode());
                sendMaterialCouponLogResp.setPrice(bNewUserRightsCouponResp.getCouponAmount());
                sendMaterialCouponLogResp.setCreateTime(new Date());
                sendMaterialCouponLogResp.setUpdateTime(new Date());
                sendMaterialCouponLogResp.setVoucherNos(bNewUserRightsCouponResp.getVoucherNos());
                sendMaterialCouponLogResp.setOutId(bNewUserRightsCouponResp.getBRightsId());
                sendMaterialCouponLogRespList.add(sendMaterialCouponLogResp);
                bNewUserRightsCouponResp.setSendMaterialCouponLogRespList(sendMaterialCouponLogRespList);
            }
            list.add(bNewUserRightsCouponResp);
        }
        return list;
    }

    @Override
    public List<BNewUserRightsResp> getUserRightsByOutNo(GetUserRightsByOutNoReq requestData) {
        List<BNewUserRightsResp> resp = new ArrayList<>();
        List<BNewUserRights> bNewUserRights = bNewUserRightsMapper.selectbyOutNo(requestData.getOutNo());
        for (BNewUserRights bNewUserRight : bNewUserRights) {
            BNewUserRightsResp rightsResp = new BNewUserRightsResp();
            BeanUtils.copyProperties(bNewUserRight,rightsResp);
            resp.add(rightsResp);
        }
        return resp;
    }

    @Override
    public List<BNewUserMemberCardResp> getUserAllCardApplicationParty(UserAvailableCardApplicationPartyReq requestData) {
        List<BNewUserMemberCard> bUserMemberCardList =
                bNewUserMemberCardMapper.selectAllMemberCardByUserIdAndCardType(requestData.getUnionid(), requestData.getApplicationParty(),null);
        List<BNewUserMemberCardResp> bNewUserMemberCardResps = JSONObject.parseArray(JSONObject.toJSONString(bUserMemberCardList), BNewUserMemberCardResp.class);
        return bNewUserMemberCardResps;
    }

    @Override
    public void loseCardByOutNo(LoseCardByOutNoReq requestData) {
        // 根据订阅id查询到卡 然后失效当前的卡
        BNewUserMemberCard bNewUserMemberCard = new BNewUserMemberCard();
        bNewUserMemberCard.setSubscribeId(requestData.getSubcribeId());
        bNewUserMemberCard.setIsDel((short)0);
        List<BNewUserMemberCard> bNewUserMemberCards = bNewUserMemberCardMapper.selectListSelective(bNewUserMemberCard);

        if(CollectionUtils.isNotEmpty(bNewUserMemberCards)){
            template.execute(action->{
                for (BNewUserMemberCard userMemberCard : bNewUserMemberCards) {
                    BNewUserMemberCard bUserMemberCard  = new BNewUserMemberCard();
                    bUserMemberCard.setId(userMemberCard.getId());
                    bUserMemberCard.setStatus((short)3);
                    bUserMemberCard.setEndTime(new Date());
                    bUserMemberCard.setUpdateTime(new Date());
                    bNewUserMemberCardMapper.updateByPrimaryKeySelective(bUserMemberCard);
                    // 失效缓存
                 }
                return action;
            });
        }
    }

    @Override
    public void fixSendBoxCouponSubJob() {

        com.github.pagehelper.Page<Object> hPageTotal = PageHelper.startPage(1, 100);
        // 查询已经发送过  状态为发送中，状态为 已用完  未删除的
        bNewUserRightsCouponMapper.selectUnSuccessSendOrSendNotUpdate();
        PageInfo<BNewUserRightsCoupon> pageInfoTotal = new PageInfo(hPageTotal);
        long total = pageInfoTotal.getTotal();
        long pageTotal = 0;
        int pageSize = 100;
        if(total % pageSize == 0){
            pageTotal = total / pageSize;
        }else{
            pageTotal = total / pageSize + 1;
        }

        for(int i = 1 ; i <= pageTotal; i++){
            com.github.pagehelper.Page<Object> hPageTotal2 = PageHelper.startPage(1, 100);
            // 查询预约时间为当月的，今年的券 状态为已经预约
            bNewUserRightsCouponMapper.selectUnSuccessSendOrSendNotUpdate();
            PageInfo<BNewUserRightsCoupon> pageInfoTotal2 = new PageInfo(hPageTotal2);
            List<BNewUserRightsCoupon> list = pageInfoTotal2.getList();
            for (BNewUserRightsCoupon bNewUserRightsCoupon : list) {
                // 查询当前的券有没有发送
                List<String> awardIds = new ArrayList<>();
                String awardid = bNewUserRightsCoupon.getAwardid();
                if(StringUtils.isNotBlank(awardid)){
                    String[] split = awardid.split(",");
                    List<String> list1 = Arrays.asList(split);
                    awardIds.addAll(list1);
                }
                List<VoucherBase> voucherBases = voucherBaseMapper.selectByUnionIdAndAwardIds(bNewUserRightsCoupon.getUnionid(),awardIds);
                // 更新
                if(CollectionUtils.isNotEmpty(voucherBases)){
                    List<String> strings = new ArrayList<>();
                    for (VoucherBase voucherBase : voucherBases) {
                        strings.add(voucherBase.getLinksource());
                    }
                    String collect = strings.stream().collect(Collectors.joining(","));
                    BNewUserRightsCoupon update  = new BNewUserRightsCoupon();
                    update.setId(bNewUserRightsCoupon.getId());
                    update.setVoucherNos(collect);
                    update.setSendStatus(2);
                    update.setUpdateTime(new Date());
                    template.execute(action->{
                        bNewUserRightsCouponMapper.updateByPrimaryKeySelective(update);
                        return action;
                    });
                }else{
                    // 重新发送
                    SendCouponBySdkEntity sendCouponBySdkEntity = sendCouponBySdk(bNewUserRightsCoupon);
                    if(sendCouponBySdkEntity.getSendFalg()){
                        BNewUserRightsCoupon update2 = new BNewUserRightsCoupon();
                        update2.setId(bNewUserRightsCoupon.getId());
                        update2.setSendStatus(2);
                        update2.setUpdateTime(new Date());
                        update2.setVoucherNos(sendCouponBySdkEntity.getVoucherNo());
                        List<SendCouponBySdkEntity.VoucherReq> voucherReqs = sendCouponBySdkEntity.getVoucherReqList();
                        List<SendCouponBySdkEntity.VoucherResp> voucherRespList = sendCouponBySdkEntity.getVoucherRespList();
                        // 更新数据
                        if(CollectionUtils.isNotEmpty(sendCouponBySdkEntity.getVoucherReqList()) && CollectionUtils.isNotEmpty(sendCouponBySdkEntity.getVoucherRespList())){
                            int r= 0;
                            int j = 0;
                            for (SendCouponBySdkEntity.VoucherReq voucherReq : voucherReqs) {
                                Long num = voucherReq.getNum();
                                j += num.intValue();
                                List<SendCouponBySdkEntity.VoucherResp> voucherResps = voucherRespList.subList(r, j);
                                for (SendCouponBySdkEntity.VoucherResp voucherResp : voucherResps) {
                                    voucherResp.setAwardId(voucherReq.getAwardId());
                                }
                                r += num.intValue();
                            }
                        }
                        template.execute(action->{
                            if(CollectionUtils.isNotEmpty(voucherRespList)){
                                List<SendMaterialCouponLog> sendMaterialCouponLogs = sendMaterialCouponLogMapper.selectByOutIdAndType(update2.getId(),
                                        SendMaterialCouponLogTypeConstant.COUPON,
                                        IsDeleteEnum.NORMAL.getCode());
                                // 处理数据
                                for (SendMaterialCouponLog sendMaterialCouponLog : sendMaterialCouponLogs) {
                                    List<String> voucherNos = new ArrayList<>();
                                    for (SendCouponBySdkEntity.VoucherResp voucherResp : voucherRespList) {
                                        if(voucherResp.getAwardId().equals(Long.parseLong(sendMaterialCouponLog.getAwardid()))){
                                            voucherNos.add(voucherResp.getCode());
                                        }
                                    }
                                    sendMaterialCouponLog.setVoucherNos(voucherNos.stream().collect(Collectors.joining(",")));
                                }
                                if(CollectionUtils.isNotEmpty(sendMaterialCouponLogs)){
                                    for (SendMaterialCouponLog sendMaterialCouponLog : sendMaterialCouponLogs) {
                                        sendMaterialCouponLogMapper.updateSelective(sendMaterialCouponLog);
                                    }
                                }
                            }
                            bNewUserRightsCouponMapper.updateByPrimaryKeySelective(update2);
                            return action;
                        });
                    }
                }
            }
        }
    }

    @Override
    public void fixSendBoxGiftJob() {

//        com.github.pagehelper.Page<Object> hPageTotal = PageHelper.startPage(1, 100);
//        // 查询已经发送过  状态为发送中，状态为 已用完  未删除的  只补发一次  如果还没有补发成功， 则将发送状态更改为  -1
//        bNewUserBoxGiftMapper.selectUnSuccessSendOrSendNotUpdate();
//        PageInfo<BNewUserBoxGift> pageInfoTotal = new PageInfo(hPageTotal);
//        long total = pageInfoTotal.getTotal();
//        long pageTotal = 0;
//        int pageSize = 100;
//        if(total % pageSize == 0){
//            pageTotal = total / pageSize;
//        }else{
//            pageTotal = total / pageSize + 1;
//        }
//
//        for(int i = 1 ; i <= pageTotal; i++){
//            com.github.pagehelper.Page<Object> hPageTotal2 = PageHelper.startPage(1, 100);
//            // 查询预约时间为当月的，今年的券 状态为已经预约
//            bNewUserBoxGiftMapper.selectUnSuccessSendOrSendNotUpdate();
//            PageInfo<BNewUserBoxGift> pageInfoTotal2 = new PageInfo(hPageTotal2);
//            List<BNewUserBoxGift> list = pageInfoTotal2.getList();
//            for (BNewUserBoxGift bNewUserBoxGift : list) {
//                // 查询是否已经发送了实物礼品
//                String consumeParams = bNewUserBoxGift.getConsumeParams();
//                CheckBoxGiftRightsDto checkBoxGiftRightsDto = JSONObject.parseObject(consumeParams, CheckBoxGiftRightsDto.class);
//                // 根据外部单号查询ebso  如果有值 则代表已经创建了  TODO
//                List<String> sourceCodes = new ArrayList<>();
//                sourceCodes.add(bNewUserBoxGift.getBoxSn());
//                List<MaterialBySourceCodesContext> materialBySourceCodesContexts = getEbInfo(sourceCodes);
//                // 更新
//                if(CollectionUtils.isNotEmpty(materialBySourceCodesContexts)){
//                    BNewUserBoxGift update2 = new BNewUserBoxGift();
//                    update2.setId(bNewUserBoxGift.getId());
//                    update2.setSendStatus(2);
//                    update2.setUpdateTime(new Date());
//                    update2.setLogisticsStatus(Integer.parseInt(materialBySourceCodesContexts.get(0).getOutStatus()));
//                    template.execute(action->{
//                        bNewUserBoxGiftMapper.updateByPrimaryKeySelective(update2);
//                        return action;
//                    });
//                }else{
//                    // 重新发送
//                    Boolean sendMaterialBoxGift = sendMaterialBoxGift(bNewUserBoxGift, checkBoxGiftRightsDto);
//                    if(sendMaterialBoxGift){
//                        BNewUserBoxGift update2 = new BNewUserBoxGift();
//                        update2.setId(bNewUserBoxGift.getId());
//                        update2.setSendStatus(2);
//                        update2.setUpdateTime(new Date());
//                        template.execute(action->{
//                            bNewUserBoxGiftMapper.updateByPrimaryKeySelective(update2);
//                            return action;
//                        });
//                    }else{
//                        BNewUserBoxGift update2 = new BNewUserBoxGift();
//                        update2.setId(bNewUserBoxGift.getId());
//                        update2.setSendStatus(-1);
//                        update2.setUpdateTime(new Date());
//                        template.execute(action->{
//                            bNewUserBoxGiftMapper.updateByPrimaryKeySelective(update2);
//                            return action;
//                        });
//                    }
//                }
//            }
//        }
    }

    @Override
    public void syncLogisticsStatus(String id) {
        com.github.pagehelper.Page<Object> hPageTotal = PageHelper.startPage(1, 100);
        // 查询创建时间距离当前时间在一个月内的  并且是未发货的
        bNewUserBoxGiftMapper.syncLogisticsStatus(id);
        PageInfo<BNewUserBoxGift> pageInfoTotal = new PageInfo(hPageTotal);
        long total = pageInfoTotal.getTotal();
        long pageTotal = 0;
        int pageSize = 100;
        if(total % pageSize == 0){
            pageTotal = total / pageSize;
        }else{
            pageTotal = total / pageSize + 1;
        }
        List<BNewUserBoxGift> updateList = new ArrayList<>();

        for(int i = 1 ; i <= pageTotal; i++) {
            com.github.pagehelper.Page<Object> hPageTotal2 = PageHelper.startPage(i, 100);
            // 查询预约时间为当月的，今年的券 状态为已经预约
            bNewUserBoxGiftMapper.syncLogisticsStatus(id);
            PageInfo<BNewUserBoxGift> pageInfoTotal2 = new PageInfo(hPageTotal2);
            List<BNewUserBoxGift> list = pageInfoTotal2.getList();
            if (CollectionUtils.isNotEmpty(list)) {
                //区分掉聚水潭的数据  和   内淘的数据
                List<BNewUserBoxGift> jstList = list.stream().filter(r -> r.getUseSendType().equals(1)).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(jstList)){
                    //分为 20个每列
                    List<List<BNewUserBoxGift>> partition = Lists.partition(jstList, 19);
                    for (List<BNewUserBoxGift> bNewUserBoxGifts : partition) {

                        List<String> jstSoIds = bNewUserBoxGifts.stream().map(r -> r.getBoxSn() + r.getSku()).collect(Collectors.toList());
                        List<QueryOrder2JSTRespDTO> jstInfo = getJstInfo(jstSoIds);
                        if (CollectionUtils.isNotEmpty(jstInfo)) {
                            Map<String, List<QueryOrder2JSTRespDTO>> groupBySourceCode = jstInfo.stream().collect(Collectors.groupingBy(r -> r.getSoId()));
                            for (BNewUserBoxGift bNewUserBoxGift : bNewUserBoxGifts) {
                                List<QueryOrder2JSTRespDTO> materialBySourceCodesContexts = groupBySourceCode.get(bNewUserBoxGift.getBoxSn() + bNewUserBoxGift.getSku());
                                if (CollectionUtils.isNotEmpty(materialBySourceCodesContexts) && StringUtils.isNotBlank(materialBySourceCodesContexts.get(0).getLId())) {
                                    BNewUserBoxGift update2 = new BNewUserBoxGift();
                                    update2.setId(bNewUserBoxGift.getId());
                                    update2.setUpdateTime(new Date());
                                    update2.setExpressNo(materialBySourceCodesContexts.get(0).getLId());
                                    update2.setLogisticsStatus(2);
                                    updateList.add(update2);
                                }
                            }
                        }
                    }
                }


                // 内淘
                List<BNewUserBoxGift> neitaoList = list.stream().filter(r -> r.getUseSendType().equals(0)).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(neitaoList)){
                    List<String> collect = neitaoList.stream().map(r -> r.getBoxSn()).collect(Collectors.toList());
                    List<MaterialBySourceCodesContext> ebInfo = getEbInfo(collect);
                    if (CollectionUtils.isNotEmpty(ebInfo)) {
                        Map<String, List<MaterialBySourceCodesContext>> groupBySourceCode = ebInfo.stream().collect(Collectors.groupingBy(r -> r.getSourceCode()));
                        for (BNewUserBoxGift bNewUserBoxGift : neitaoList) {
                            List<MaterialBySourceCodesContext> materialBySourceCodesContexts = groupBySourceCode.get(bNewUserBoxGift.getBoxSn());
                            if (CollectionUtils.isNotEmpty(materialBySourceCodesContexts) && StringUtils.isNotBlank(materialBySourceCodesContexts.get(0).getExpressNo())) {
                                BNewUserBoxGift update2 = new BNewUserBoxGift();
                                update2.setId(bNewUserBoxGift.getId());
                                update2.setUpdateTime(new Date());
                                update2.setExpressNo(materialBySourceCodesContexts.get(0).getExpressNo());
                                update2.setLogisticsStatus(Integer.parseInt(materialBySourceCodesContexts.get(0).getOutStatus()));
                                updateList.add(update2);
                            }
                        }
                    }
                }
            }
        }

        if(CollectionUtils.isNotEmpty(updateList)){
            for (BNewUserBoxGift bNewUserBoxGift : updateList) {
                bNewUserBoxGiftMapper.updateByPrimaryKeySelective(bNewUserBoxGift);
            }
        }
    }

    private List<QueryOrder2JSTRespDTO> getJstInfo(List<String> jstSoIds) {
        // 查询到数据
        OrderQuery2JSTDTO orderQuery2JSTDTO = new OrderQuery2JSTDTO();
        orderQuery2JSTDTO.setSoIds(jstSoIds);
        orderQuery2JSTDTO.setShopId(Integer.parseInt(jstShopId));
        log.info("通过聚水潭进行查询是否发货信息 getJstInfo req= {}",JSONObject.toJSONString(orderQuery2JSTDTO));
        try {
            ResponseResult<List<QueryOrder2JSTRespDTO>> listResponseResult = retailApi.queryOrder(orderQuery2JSTDTO);
            log.info("通过聚水潭进行查询是否发货信息 getJstInfo resp = {}",JSONObject.toJSONString(listResponseResult));
            if(listResponseResult.getCode() == 0){
                return listResponseResult.getData();
            }
        }catch (Exception e ){
            log.info("通过聚水潭进行查询是否发货信息 发生错误  req = {}",JSONObject.toJSONString(orderQuery2JSTDTO),e);
        }
        return null;
    }


    @Override
    public void reSendMaterial(ReSendMaterialReq requestData) {
        BNewUserBoxGift bNewUserBoxGift = bNewUserBoxGiftMapper.selectByPrimaryKey(requestData.getId());
        if(bNewUserBoxGift != null){
            String consumeParams = bNewUserBoxGift.getConsumeParams();
            CheckBoxGiftRightsDto checkBoxGiftRightsDto = new CheckBoxGiftRightsDto();
            CheckBoxGiftRightsDto.SendMaterialDto sendMaterialDto = JSONObject.parseObject(consumeParams, CheckBoxGiftRightsDto.SendMaterialDto.class);
            checkBoxGiftRightsDto.setSendMaterialDto(sendMaterialDto);
            Boolean sendMaterialBoxGift = sendMaterialBoxGift(bNewUserBoxGift, checkBoxGiftRightsDto);
            if(sendMaterialBoxGift){
                BNewUserBoxGift update2 = new BNewUserBoxGift();
                update2.setId(bNewUserBoxGift.getId());
                update2.setSendStatus(2);
                update2.setUpdateTime(new Date());
                template.execute(action->{
                    bNewUserBoxGiftMapper.updateByPrimaryKeySelective(update2);
                    return action;
                });
            }
        }
    }

    @Override
    public List<BNewUserBoxGiftResp> getBoxGiftByBoxSns(GetBoxGiftByBoxSn requestData) {
        List<BNewUserBoxGiftResp> list = new ArrayList<>();

        if(CollectionUtils.isEmpty(requestData.getBoxSns())){
            return list;
        }
        List<BNewUserBoxGift> bNewUserBoxGifts = bNewUserBoxGiftMapper.selectByBoxSns(requestData.getBoxSns());
        if(CollectionUtils.isNotEmpty(bNewUserBoxGifts)){
            // 查询图片信息
            CommonRequest<QueryGoodsSkcListReq> commonRequest = new CommonRequest();
            Page page = new Page();
            page.setPageSize(200);
            page.setPageNo(1);
            commonRequest.setPage(page);
            QueryGoodsSkcListReq queryGoodsSkcListReq = new QueryGoodsSkcListReq();
            List<String> skcs = bNewUserBoxGifts.stream().filter(r -> StringUtils.isNotBlank(r.getSku())).map(r -> r.getSku().substring(0, r.getSku().length() - 2)).collect(Collectors.toList());
            queryGoodsSkcListReq.setProductCodes(skcs);
            commonRequest.setRequestData(queryGoodsSkcListReq);
            ResponseResult<List<ProductSkcResp>> listResponseResult = iProductStoreApi.queryStoreGoodSkc(commonRequest);
            Map<String, List<ProductSkcResp>> groupByName= new HashMap<>();
            Map<String, List<ProductSkcResp>> groupBySkc= new HashMap<>();

            if(CollectionUtils.isNotEmpty(listResponseResult.getData())){
                List<ProductSkcResp> data = listResponseResult.getData();
                groupByName = data.stream().collect(Collectors.groupingBy(r -> r.getName()));
                groupBySkc = data.stream().collect(Collectors.groupingBy(r -> r.getName()+r.getColorno()));
            }
            for (BNewUserBoxGift bNewUserBoxGift : bNewUserBoxGifts) {
                BNewUserBoxGiftResp bNewUserBoxGiftResp = new BNewUserBoxGiftResp();
                BeanUtils.copyProperties(bNewUserBoxGift,bNewUserBoxGiftResp);
                List<ProductSkcResp> productSkcResps = groupByName.get(bNewUserBoxGift.getSku());
                if(CollectionUtils.isNotEmpty(productSkcResps)){
                    bNewUserBoxGiftResp.setImgUrl(productSkcResps.get(0).getImgurl());
                    bNewUserBoxGiftResp.setBrandName(productSkcResps.get(0).getBrand());
                    bNewUserBoxGiftResp.setColorName(productSkcResps.get(0).getColor_name());
                    bNewUserBoxGiftResp.setColorNo(productSkcResps.get(0).getColorno());
                    if(CollectionUtils.isNotEmpty(productSkcResps.get(0).getSkus())){
                        bNewUserBoxGiftResp.setSizeName(productSkcResps.get(0).getSkus().get(0).getSize_name());
                    }
                }
                if(StringUtils.isBlank(bNewUserBoxGiftResp.getImgUrl())){
                    List<ProductSkcResp> productSkcRespList = groupBySkc.get(bNewUserBoxGift.getSku().substring(0,bNewUserBoxGiftResp.getSku().length()-2));
                    if(CollectionUtils.isNotEmpty(productSkcRespList)){
                        bNewUserBoxGiftResp.setImgUrl(productSkcRespList.get(0).getImgurl());
                        bNewUserBoxGiftResp.setBrandName(productSkcRespList.get(0).getBrand());
                        bNewUserBoxGiftResp.setColorName(productSkcRespList.get(0).getColor_name());
                        bNewUserBoxGiftResp.setColorNo(productSkcRespList.get(0).getColorno());
                        if(CollectionUtils.isNotEmpty(productSkcRespList.get(0).getSkus())){
                            bNewUserBoxGiftResp.setSizeName(productSkcRespList.get(0).getSkus().get(0).getSize_name());
                        }
                    }
                }
                list.add(bNewUserBoxGiftResp);
            }
        }
        return list;
    }

    @Override
    public BNewUserMemberCardResp getUserCardBySubId(GetUserCardBySubId requestData) {
//        BNewUserMemberCardResp bNewUserMemberCardResp = new BNewUserMemberCardResp();
////        BNewUserMemberCard bNewUserMemberCard = bNewUserMemberCardMapper.selectBySubId(requestData.getSubIds().get(0));
//        BeanUtils.copyProperties(bNewUserMemberCard,bNewUserMemberCardResp);
//        return bNewUserMemberCardResp;
        return null;
    }

    @Override
    public void batchUseUserRights(List<BatchUseUserRightsReq> requestData) {
        log.info("batchUseUserRights  json = {}",JSONObject.toJSONString(requestData));
        if(CollectionUtils.isEmpty(requestData)){
            return ;
        }

        for (BatchUseUserRightsReq requestDatum : requestData) {
            try {
                List<BNewUserRightsLog> bNewUserRightsLogs = bUserRightsLogMapper.selectByOutNo(requestDatum.getConsumeOutNo());
                if(CollectionUtils.isNotEmpty(bNewUserRightsLogs)){
                    // 如果都是已经回滚过的  那么可以进行消耗
                    List<BNewUserRightsLog> collect1 = bNewUserRightsLogs.stream().filter(r -> r.getStatus().equals(1)).collect(Collectors.toList());
                    if(collect1.size() != bNewUserRightsLogs.size()){
                        log.info("消耗次数权益 outNo = {} , 当前outNo已经消耗过 不允许再次消耗 当做消耗成功",requestDatum.getConsumeOutNo());
                        continue;
                    }
                }
                // 查询到卡信息
                BNewUserMemberCard bNewUserMemberCard = bNewUserMemberCardMapper.selectBySubId(requestDatum.getSubId());
                if(bNewUserMemberCard == null){
                    continue;
                }
                String outNo = bNewUserMemberCard.getOutNo();
                List<BNewUserRights> bNewUserRights = bNewUserRightsMapper.selectbyOutNo(outNo);
                if(CollectionUtils.isEmpty(bNewUserRights)){
                    return ;
                }
                BNewUserRights bNewUserRights1 = bNewUserRights.get(0);
                List<String> ids  = new ArrayList<>();
                ids.add(bNewUserRights1.getId());

                List<String> updateUsed = new ArrayList<>();
                List<String> updateHold = new ArrayList<>();

                if(CollectionUtils.isNotEmpty(bNewUserRightsLogs) && bNewUserRightsLogs.size() == 1){
                    updateHold.add(bNewUserRights1.getId());
                }else{
                    updateUsed.add(bNewUserRights1.getId());
                }

                insertBUserRightsLog(bNewUserRights1,requestDatum.getConsumeOutNo(),ApplicablePartyEnum.BOX.getCode(),RightsTypeEnum.APPLY_BOX_SUBCRIBE,false);

                if(CollectionUtils.isNotEmpty(updateUsed)){
                    bNewUserRightsMapper.batchUpdateUsedNumByPrimaryKeys(ids);
                }
                if(CollectionUtils.isNotEmpty(updateHold)){
                    bNewUserRightsMapper.batchUpdateUsedNumAndHoldByPrimaryKeys(updateHold);
                }

            }catch (Exception e){
                log.error("batchUseUserRights = {}",JSONObject.toJSONString(requestDatum));
                continue;
            }
        }
    }

    @Override
    public List<UserSubV3CouponBySubIdResp> getUserSubV3CouponBySubId(GetUserCardBySubId requestData) {
        List<UserSubV3CouponBySubIdResp> subV3CouponBySubIdResp = new ArrayList<>();
        List<BNewUserMemberCard> bNewUserMemberCard = bNewUserMemberCardMapper.selectBySubIds(requestData.getSubIds());
        if(CollectionUtils.isNotEmpty(bNewUserMemberCard)){
            List<String> outNos = bNewUserMemberCard.stream().map(r -> r.getOutNo()).collect(Collectors.toList());
            // outNo
            Map<String, List<BNewUserMemberCard>> collect = bNewUserMemberCard.stream().collect(Collectors.groupingBy(r -> r.getOutNo()));

            List<BNewUserRightsCoupon> list = bNewUserRightsCouponMapper.selectByOutNos(outNos);
            for (BNewUserRightsCoupon bNewUserRightsCoupon : list) {
                UserSubV3CouponBySubIdResp userSubV3CouponBySubIdResp = new UserSubV3CouponBySubIdResp();
                userSubV3CouponBySubIdResp.setSubId(collect.get(bNewUserRightsCoupon.getOutNo()).get(0).getSubscribeId());
                userSubV3CouponBySubIdResp.setUseRule(bNewUserRightsCoupon.getUseRuleSnapshot());
                subV3CouponBySubIdResp.add(userSubV3CouponBySubIdResp);
            }
        }
        return subV3CouponBySubIdResp;
    }

    @Override
    public List<UserRightsBySubIdsDto> getUserRightsBySubIds(GetUserCardBySubId requestData) {
        List<UserRightsBySubIdsDto> subV3CouponBySubIdResp = new ArrayList<>();
        List<BUserMemberCardLog> bUserMemberCardLogs = bUserMemberCardLogMapper.selectBySubIds(requestData.getSubIds());
        if (CollectionUtils.isNotEmpty(bUserMemberCardLogs)) {
            for (BUserMemberCardLog bUserMemberCardLog : bUserMemberCardLogs) {
                // 查询是单次卡 还是 正式卡
                BMemberCard bMemberCard = bNewMemberCardMapper.selectByPrimaryKey(bUserMemberCardLog.getMemberCardId());
                UserRightsBySubIdsDto userSubV3CouponBySubIdResp = new UserRightsBySubIdsDto();
                userSubV3CouponBySubIdResp.setSubId(bUserMemberCardLog.getOutNo());
                userSubV3CouponBySubIdResp.setRightsName(bUserMemberCardLog.getCardName());
                if (bMemberCard != null) {
                    if (bMemberCard.getType().equals(CardTypeEnum.SUB.getCode())) {
                        userSubV3CouponBySubIdResp.setUsedNum("6");
                    } else {
                        userSubV3CouponBySubIdResp.setUsedNum("1");
                    }
                } else {
                    userSubV3CouponBySubIdResp.setUsedNum("6");
                }
                subV3CouponBySubIdResp.add(userSubV3CouponBySubIdResp);
            }
        }
        return subV3CouponBySubIdResp;
    }

    @Override
    public RightsV3ConfigResp getRightsV3Config(RightsV3ConfigReq requestData) {
        RightsV3ConfigResp resp = new RightsV3ConfigResp();
        BNewUserMemberCard bNewUserMemberCard = bNewUserMemberCardMapper.selectBySubId(requestData.getSubId());
        if(bNewUserMemberCard  == null){
            return null;
        }
        // 查询  膨胀券信息
        List<BNewUserRightsCoupon> bNewUserRightsCouponList = bNewUserRightsCouponMapper.selectByOutNo(bNewUserMemberCard.getOutNo());
        if(CollectionUtils.isNotEmpty(bNewUserRightsCouponList)){
            String useRuleSnapshot = bNewUserRightsCouponList.get(0).getUseRuleSnapshot();
            SubV3CouponRight subV3CouponRight   = JSONObject.parseObject(useRuleSnapshot, SubV3CouponRight.class);
//            int couponNum = ( bNewUserRightsCouponList.size() - 1 ) * Integer.parseInt(subV3CouponRight.getOtherBox().get(0).getNum())
//                    + Integer.parseInt(subV3CouponRight.getFirstBox().get(0).getNum());
            int couponNum = Integer.parseInt(subV3CouponRight.getFirstBox().get(0).getNum());
            resp.setCouponNum(couponNum +"");
            resp.setCouponAmount(subV3CouponRight.getFirstBox().get(0).getCouponAmount());
        }

        // 查询 收盒礼信息
        List<BNewUserBoxGift> bNewUserBoxGifts = bNewUserBoxGiftMapper.selectByOutNo(bNewUserMemberCard.getOutNo());
        if(CollectionUtils.isNotEmpty(bNewUserBoxGifts)){
            resp.setBoxGiftNum(bNewUserBoxGifts.size()+"");
        }
        return resp;
    }

    @Override
    public Boolean getIsSendBoxGift(SendBoxGiftFlagReq requestData) {
        BNewUserMemberCard bNewUserMemberCard = bNewUserMemberCardMapper.selectBySubId(requestData.getSubId());
        if(bNewUserMemberCard != null){
            String outNo = bNewUserMemberCard.getOutNo();
            List<BNewUserBoxGift> bNewUserBoxGifts = bNewUserBoxGiftMapper.selectByOutNo(outNo);
            // 筛选不为空的 sendBoxGiftTime
            List<BNewUserBoxGift> collect = bNewUserBoxGifts.stream().filter(r -> r.getSendBoxGiftTime() != null).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(collect)){
                return false;
            }
            Date date = new Date();
            String formatDate = DateUtils.formatDate(date, "yyyyMM");
            for (BNewUserBoxGift bNewUserBoxGift : collect) {
                if(bNewUserBoxGift.getSendBoxGiftTime().toString().equals(formatDate)){
                    return true;
                }
            }
        }
        return false;
    }

    @Override
    public void resetUserCouponV3(String consumeOutNo) {
        List<String> consumeOutNos = new ArrayList<>();
        consumeOutNos.add(consumeOutNo);
        List<BNewUserRightsCoupon> bNewUserRightsCouponList = bNewUserRightsCouponMapper.selectByConsumeOutNos(consumeOutNos);
        if(CollectionUtils.isNotEmpty(bNewUserRightsCouponList)){
            BNewUserRightsCoupon update = new BNewUserRightsCoupon();
            update.setStatus(1);
            update.setId(bNewUserRightsCouponList.get(0).getId());
            update.setSendStatus(0);
            bNewUserRightsCouponMapper.updateByConsumeOutNoAndStatus(update);
        }


    }

    @Override
    public ReceiveBirthCouponResp receiveBirthCoupon(ReceiveBirthCouponReq requestData) {
        ReceiveBirthCouponResp resp = new ReceiveBirthCouponResp();

        MemberCardQueryContext req = new MemberCardQueryContext();
        req.setCardNo(requestData.getCardNo());
        req.setStatus("Y");
        CustomerBaseResponse<MemberCardEntity> memberCard = iUserVipService.getMemberCard(req);
        if(memberCard != null && memberCard.getData() != null && StringUtils.isNotBlank(memberCard.getData().getGroupCardNo())) {
            CheckCanReceiveDto checkCanReceiveDto = checkCanReceive(memberCard.getData(), requestData);

            if(!checkCanReceiveDto.getStatus()){
                BeanUtils.copyProperties(checkCanReceiveDto,resp);
                return resp;
            }
            // 仅check
            if(!requestData.getIsReceive()){
                BeanUtils.copyProperties(checkCanReceiveDto,resp);
                return resp;
            }
            // 异步掉   然后返回成功

            try {
                SendNotifyCouponData sendNotifyCouponData = new SendNotifyCouponData();
                BeanUtils.copyProperties(checkCanReceiveDto,sendNotifyCouponData);
                sendNotifyCouponData.setMemberCardEntity(memberCard.getData());
                SendBirthCouponEvent activeVoucherChangeVoucherReq = new SendBirthCouponEvent(sendNotifyCouponData,
                        System.currentTimeMillis(),
                        System.currentTimeMillis(),
                        UUID.randomUUID()
                );
                log.info("receiveBirthCoupon usertoken = {}  cardNo = {}",activeVoucherChangeVoucherReq.getUserToken(),memberCard.getData().getCardNo());
                // 临时标记
                redisService.set(RedisKeyConstant.RECEIVEBIRTHCOUPON.join(requestData.getCardNo()), 1);
                sendBirthCouponEventBus.post(activeVoucherChangeVoucherReq);
            } catch (PersistentBus.EventBusException e) {
                log.info("receiveBirthCoupon 异步出现错误",e);
                throw new RuntimeException(e);
            }

            BeanUtils.copyProperties(checkCanReceiveDto,resp);
            resp.setStatus(true);
            resp.setMsg("领取成功!");
            return resp;

        }
        resp.setMsg("未查询到用户卡信息");
        return resp;
    }

    @Override
    public void sendBirthCouponNotify(SendNotifyCouponData info) {
        String key = RedisKeysEnum.SEND_BIRTH_COUPON_NOTIFY.join(info.getMemberCardEntity().getCardNo(), info.getMemberCardEntity().getBrandId());
        boolean b = redissonUtil.tryLock(key,0,20);
        if(!b){
            log.info("sendBirthCouponNotify 被多次调用，加锁失败",JSONObject.toJSONString(info));
            return ;
        }
        log.info("sendBirthCouponNotify 已经锁定，开始处理数据 = {}  lockKey = {}",info.getMemberCardEntity().getCardNo(),key);

        // 领取操作 创建数据
        try {
            int yearStart = Integer.parseInt(DateUtils.formatDate(new Date(), "yyyy") + "01");

            ReceiveBirthCouponReq req = new ReceiveBirthCouponReq();
            req.setCardNo(info.getMemberCardEntity().getCardNo());
            List<MemberCardEntity> memberCardEntities = new ArrayList<>();
            memberCardEntities.add(info.getMemberCardEntity());
            CheckCanReceiveDto checkCanReceiveDto = checkCanReceive(info.getMemberCardEntity(), req);
            List<RightsResp> rightsResps = checkCanReceiveDto.getRightsResps();

            log.info("checkCanReceive 结束 获取参数为 ={}",JSONObject.toJSONString(rightsResps));

            List<RightsResp> collect = rightsResps.stream().filter(r -> r.getStatus()).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(collect)){
                //无可发放的生日优惠券
                log.info("无可发放的生日优惠券! info = {}",JSONObject.toJSONString(info));
                return ;
            }

//            // 需要验证当前的这个券是否已经发放过 根据rightsId进行验证
//            List<BirthCouponSendLog> sendLogs  = birthCouponSendLogMapper.selectByCardNoSendMonth(yearStart,info.getMemberCardEntity().getCardNo()
//                    ,BirthCouponSendLogStatusConstatnt.SEND);
//            if(CollectionUtils.isNotEmpty(sendLogs)){
//                // 剔除可发放权益
//                List<String> alreadySendRightsIds = sendLogs.stream().map(r -> r.getRightsId()).collect(Collectors.toList());
//                collect = collect.stream().filter(r -> !alreadySendRightsIds.contains(r.getRightsId())).collect(Collectors.toList());
//            }
//            if(CollectionUtils.isEmpty(collect)){
//                log.info("过滤掉已经发放的生日优惠券 然后无数据! info = {}",JSONObject.toJSONString(info));
//                return ;
//            }

            // 重新赋值为可发放的权益
            info.setRightsResps(collect);

            List<BirthCouponSendLog> birthCouponSendLogs  = birthCouponSendLogMapper.selectByCardNoSendMonth(yearStart,info.getMemberCardEntity().getCardNo()
                    ,BirthCouponSendLogStatusConstatnt.NOT_SEND);
            List<String> sendIds= new ArrayList<>();
            if(CollectionUtils.isNotEmpty(birthCouponSendLogs)){
                List<String> voucehrList  = new ArrayList<>();
                // 有领取失败的数据的情况下，  需要查询是否真正发放了优惠券  如果真正的发放了优惠券  则不再发放  ，进行修改 更新优惠券信息 更新状态  否则 才会继续发放
                for (BirthCouponSendLog birthCouponSendLog : birthCouponSendLogs) {
                    List<JicVoucherSendRecord> jicVoucherSendRecords = jicVoucherSendRecordMapper.selectByReferOrder(birthCouponSendLog.getOutNo());
                    if(CollectionUtils.isNotEmpty(jicVoucherSendRecords)){
                        // 拼接 然后进行塞入
                        String voucherNos = jicVoucherSendRecords.stream().filter(r -> StringUtils.isNotBlank(r.getVoucherNo())).map(r -> r.getVoucherNo()).collect(Collectors.joining(","));
                        voucehrList.addAll(jicVoucherSendRecords.stream().filter(r -> StringUtils.isNotBlank(r.getVoucherNo())).map(r -> r.getVoucherNo()).collect(Collectors.toList()));
                        BirthCouponSendLog update = new BirthCouponSendLog();
                        update.setId(birthCouponSendLog.getId());
                        update.setStatus(BirthCouponSendLogStatusConstatnt.SEND);
                        update.setVoucherNos(voucherNos);
                        birthCouponSendLogMapper.updateByPrimaryKeySelective(update);
                    }else{
                        sendIds.add(birthCouponSendLog.getId());
                    }
                }
            }else{
                sendIds = buildAndInsertSendLog(info.getMemberCardEntity().getTel(),info.getMemberCardEntity().getCardNo(),info.getRightsResps());
            }

            //发放优惠券
            if(CollectionUtils.isNotEmpty(sendIds)){
                List<SendCouponBySdkEntity> sendCouponBySdkEntitys= sendCoupon(info.getMemberCardEntity().getOpenId(), info.getRightsResps(),sendIds);
                if(CollectionUtils.isNotEmpty(sendCouponBySdkEntitys)){
                    for (int i = 0 ; i < sendCouponBySdkEntitys.size();i++){
                        SendCouponBySdkEntity sendCouponBySdkEntity = sendCouponBySdkEntitys.get(i);
                        if(sendCouponBySdkEntity != null && sendCouponBySdkEntity.getSendFalg()){
                            //发放成功  更改发放记录信息
                            for (String sendId : sendIds) {
                                // 相同则进行更新
                                if(sendCouponBySdkEntity.getId().equals(sendId)){
                                    BirthCouponSendLog update = new BirthCouponSendLog();
                                    update.setId(sendId);
                                    update.setStatus(BirthCouponSendLogStatusConstatnt.SEND);
                                    update.setVoucherNos(sendCouponBySdkEntity.getVoucherNo());
                                    birthCouponSendLogMapper.updateByPrimaryKeySelective(update);
                                }
                            }
                        }
                    }
                    // 通知建园那边的接口 发放优惠券通知建园
                    try {
                        ChangeEntryEntitu changeEntryEntitu = new ChangeEntryEntitu();
                        changeEntryEntitu.setVipId(info.getMemberCardEntity().getId());
                        log.info("调用接口更新发放生日券信息 changeEntry  = {}",JSONObject.toJSONString(changeEntryEntitu));
                        Response<CustomerBaseResponse<Boolean>> execute = iJicInfoHttpApi.changeEntry(changeEntryEntitu).execute();
                        log.info("调用接口更新发放生日券信息 changeEntry  = {}",JSONObject.toJSONString(execute.body()));

                    }catch (Exception e){
                        log.info("调用接口更新发放生日券信息 changeEntry 出错= {}",JSONObject.toJSONString(info.getMemberCardEntity()),e);
                    }
                }
            }
        }catch (Exception e){
            log.error("sendBirthCouponNotify 异步发券出现错误 ",e);
        }finally {
            //清楚标记
            redissonUtil.unlock(key);
            redisService.del(RedisKeyConstant.RECEIVEBIRTHCOUPON.join(info.getMemberCardEntity().getCardNo()));
        }
    }

    @Override
    public void updateBoxGiftParams(UpdateBoxGiftParamsReq requestData) {
        if(StringUtils.isBlank(requestData.getId())){
            return ;
        }
        //查询数据
        BNewUserBoxGift bNewUserBoxGift = bNewUserBoxGiftMapper.selectByPrimaryKey(requestData.getId());
        String consumeParams = bNewUserBoxGift.getConsumeParams();
        if(StringUtils.isBlank(consumeParams)){
            return ;
        }
        JSONObject jsonObject = JSONObject.parseObject(consumeParams);
        jsonObject.put("c_province_name",requestData.getProvinceName());
        jsonObject.put("c_city_name",requestData.getCityName());
        jsonObject.put("c_district_name",requestData.getDistrictName());
        jsonObject.put("receiver_name",requestData.getReceiverName());
        jsonObject.put("receiver_mobile",requestData.getReceiverPhone());
        jsonObject.put("receiver_address",requestData.getReceiverAddress());

        String jsonString = JSONObject.toJSONString(jsonObject);

        BNewUserBoxGift update = new BNewUserBoxGift();
        update.setId(bNewUserBoxGift.getId());
        update.setConsumeParams(jsonString);
        bNewUserBoxGiftMapper.updateByPrimaryKeySelective(update);
        // 更新ebNum为空
        bNewUserBoxGiftMapper.updateEbNumIsNull(update);


    }

    private List<SendCouponBySdkEntity> sendCoupon(String openId, List<RightsResp> rightsResps, List<String> sendIds) {
        //发放优惠券
        List<SendCouponBySdkEntity> resultList = new ArrayList<>();
        int j = 0;
        for (RightsResp rightsResp : rightsResps) {

            BirthCouponSendLog birthCouponSendLog = birthCouponSendLogMapper.selectByPrimaryKey(sendIds.get(j));

            Map map = JSONObject.parseObject(rightsResp.getUseRule(), Map.class);
            Object rule = map.get("rule");
            List<Map> mapList = JSONObject.parseArray(JSONObject.toJSONString(rule), Map.class);

            SendCouponBySdkEntity sendCouponBySdkEntity = new SendCouponBySdkEntity();
            // 获取唯一id
            sendCouponBySdkEntity.setId(sendIds.get(j));
            JICVoucherSendReqEntity jicVoucherSendReqEntity = new JICVoucherSendReqEntity();
            jicVoucherSendReqEntity.setReferOrder(birthCouponSendLog.getOutNo());
            jicVoucherSendReqEntity.setOpenId(openId);
            List<RulesList> rulesLists = new ArrayList<>();

            for (Map map1 : mapList) {
                RulesList rules = new RulesList();
                String couponId = map1.get("couponId").toString();
                String num = map1.get("num").toString();
                rules.setAwardId(Long.parseLong(couponId));
                rules.setNum(Long.parseLong(num));
                rulesLists.add(rules);
            }
            jicVoucherSendReqEntity.setRulesList(rulesLists);
            JICVoucherSendRespEntity jicVoucherSendRespEntity = iVoucherService.sendVoucher(jicVoucherSendReqEntity);
            if (jicVoucherSendRespEntity != null && jicVoucherSendRespEntity.getCode() == 200 && ObjectUtils.isNotEmpty(jicVoucherSendRespEntity.getData())) {
                String voucher = jicVoucherSendRespEntity.getData().getR_voucher();
                if (StringUtils.isBlank(voucher)) {
                    BirthCouponSendLog updateBirth = new BirthCouponSendLog();
                    updateBirth.setIsDel(IsDeleteEnum.IS_DELETED.getCode());
                    updateBirth.setId(birthCouponSendLog.getId());
                    birthCouponSendLogMapper.updateByPrimaryKeySelective(updateBirth);
                    j++;
                    log.error("sendCoupon  发券失败 msg = {}, e = {}", jicVoucherSendRespEntity.getData().getRetmsg(), jicVoucherSendRespEntity);
                    continue;
                }
                if(voucher.equals("[]")){
                    // 删除这条记录
                    BirthCouponSendLog updateBirth = new BirthCouponSendLog();
                    updateBirth.setIsDel(IsDeleteEnum.IS_DELETED.getCode());
                    updateBirth.setId(birthCouponSendLog.getId());
                    birthCouponSendLogMapper.updateByPrimaryKeySelective(updateBirth);
                    j++;
                    log.error("sendCoupon  发券失败 msg = {}, e = {}", jicVoucherSendRespEntity.getData().getRetmsg(), jicVoucherSendRespEntity);
                    continue;
                }

                List<Map> maps = JSONObject.parseArray(voucher, Map.class);
                StringBuffer voucherCode = new StringBuffer();

                for(int i =0 ; i< maps.size(); i++){
                    if(i == maps.size() - 1){
                        voucherCode.append(maps.get(i).get("code").toString());
                    }else{
                        voucherCode.append(maps.get(i).get("code").toString());
                        voucherCode.append(",");
                    }
                }
                sendCouponBySdkEntity.setSendFalg(true);
                sendCouponBySdkEntity.setVoucherNo(voucherCode.toString());
                resultList.add(sendCouponBySdkEntity);
            }else if(jicVoucherSendRespEntity != null){
                log.error("sendCoupon  发券失败 jicVoucherSendReqEntityReq = {}, resp = {}", JSONObject.toJSONString(jicVoucherSendReqEntity), JSONObject.toJSONString(jicVoucherSendRespEntity));
            }
            j++;
        }
        return resultList;
    }

    private List<String> buildAndInsertSendLog(String tel, String cardNo, List<RightsResp> rightsResps) {
        List<BirthCouponSendLog> birthCouponSendLogs = new ArrayList<>();
        for (RightsResp rightsResp : rightsResps) {
            BirthCouponSendLog birthCouponSendLog = new BirthCouponSendLog();
            birthCouponSendLog.setId(IdLeaf.getId(IdConstant.BIRTH_COUPON_SEND_LOG));
            birthCouponSendLog.setCardNo(cardNo);
            birthCouponSendLog.setSendMonth(Integer.parseInt(DateUtils.formatDate(new Date(),"yyyyMM")));
            birthCouponSendLog.setRightsId(rightsResp.getRightsId());
            birthCouponSendLog.setMembmerCardId(rightsResp.getRightsPackageId());
            birthCouponSendLog.setOutNo(IdConstant.SEND_COUPON_PRIMARY+IdLeaf.getId(IdConstant.SEND_COUPON_PRIMARY));
            birthCouponSendLog.setStatus(BirthCouponSendLogStatusConstatnt.NOT_SEND);
            birthCouponSendLog.setPhone(tel);
            birthCouponSendLog.setCreateTime(new Date());
            birthCouponSendLog.setUpdateTime(new Date());
            birthCouponSendLogs.add(birthCouponSendLog);
        }

        template.execute(action->{
            for (BirthCouponSendLog birthCouponSendLog : birthCouponSendLogs) {
                birthCouponSendLogMapper.insertSelective(birthCouponSendLog);
            }
            return action;
        });
        // 返回id
        List<String> finalIds = new ArrayList<>();
        for (BirthCouponSendLog birthCouponSendLog : birthCouponSendLogs) {
            finalIds.add(birthCouponSendLog.getId());
        }
        return finalIds;
    }


    private CheckCanReceiveDto checkCanReceive(MemberCardEntity data, ReceiveBirthCouponReq requestData) {
        CheckCanReceiveDto resp = new CheckCanReceiveDto();
        resp.setWid(data.getWid());
        resp.setLevelId(data.getVipShareTypeLevelId()+"");
        //1. 校验用户是否已经领取过今年的生日礼
        String birthday = data.getBirthday();
        // 小童  蓬马 按照babyBirth取值
        if("4".equals(data.getBrandId()) || "6924108367".equals(data.getBrandId())){
            if(data.getBabyBirthday()!= null){
                birthday = data.getBabyBirthday().toString();
            }else{
                birthday = null;
            }
        }

        if(StringUtils.isNotBlank(birthday) && birthday.equals("-1")){
            birthday = null;
        }

        int yearStart = Integer.parseInt(DateUtils.formatDate(new Date(), "yyyy") + "01");

        if(requestData.getCardTypeId() != null){
            GetRightsReq getRightsReq2 = new GetRightsReq();
            getRightsReq2.setRightsType(RightsTypeEnum.BIRTH_COUPON.getCode());
            getRightsReq2.setBrandId(data.getBrandId());
            getRightsReq2.setCardLevel(requestData.getCardTypeId());
            List<RightsResp> rights2 = rightsRepository.getRightsByRedisOrDataBase(getRightsReq2);
            if(CollectionUtils.isNotEmpty(rights2)){
                List<RightsResp> finalRights = filterSuitRightsResp(rights2, data.getCustomerId(), data.getStoreId(), data.getUnionId(), data.getBrandId());
                for (RightsResp finalRight : finalRights) {

                    //1. 校验权益与用户当前的卡的生日是否匹配
                    Map map = JSONObject.parseObject(finalRight.getUseRule(), Map.class);

                    // 生日月前x天  生日月后x天
                    String startDrawTimeConfigType = map.get("startDrawTimeConfigType").toString();
                    String endDrawTimeConfigType = map.get("endDrawTimeConfigType").toString();
                    String yearConsume = map.get("yearConsume") == null ? "0":map.get("yearConsume").toString();
                    String startDrawTimeConfigNum = "0";
                    String endDrawTimeConfigNum = "0";
                    if(!startDrawTimeConfigType.equals("0")){
                        startDrawTimeConfigNum = map.get("startDrawTimeConfigNum").toString();
                    }
                    if(!endDrawTimeConfigType.equals("0")){
                        endDrawTimeConfigNum = map.get("endDrawTimeConfigNum").toString();
                    }
                    if(StringUtils.isNotBlank(birthday)){
                        //获取用户今年生日
                        String birthDayNowYear = DateUtils.formatDate(new Date(), "yyyy") + birthday.substring(4,6) + "01";
                        // 生日月第一天
                        Date date = DateUtils.parseDate(birthDayNowYear, "yyyyMMdd");
                        // 生日月最后一天
                        Calendar ca = Calendar.getInstance();
                        ca.setTime(date);
                        ca.set(Calendar.DAY_OF_MONTH, ca.getActualMaximum(Calendar.DAY_OF_MONTH));
                        Date monthLastDayDate = ca.getTime();

                        Date startDraw = DateUtils.addDays( date,-Integer.parseInt(startDrawTimeConfigNum));
                        Date endDraw = DateUtils.addDays( monthLastDayDate,Integer.parseInt(endDrawTimeConfigNum));

                        String startTime = DateUtils.formatDate(startDraw, "yyyyMMdd");
                        String endTime = DateUtils.formatDate(endDraw, "yyyyMMdd");
                        String nowTime = DateUtils.formatDate(new Date(), "yyyyMMdd");
                        finalRight.setStartTime(Integer.parseInt(startTime));
                        finalRight.setEndTime(Integer.parseInt(endTime));
                    }
                }
                resp.setCompareRightsResps(finalRights);
            }
        }

        // 使用共享卡等级
        GetRightsReq getRightsReq = new GetRightsReq();
        getRightsReq.setRightsType(RightsTypeEnum.BIRTH_COUPON.getCode());
        getRightsReq.setBrandId(data.getBrandId());
        getRightsReq.setCardLevel(data.getVipShareTypeId().intValue());
        List<RightsResp> rights = rightsRepository.getRightsByRedisOrDataBase(getRightsReq);
        if(CollectionUtils.isEmpty(rights)){
            resp.setMsg("当前品牌和卡等级未配置生日优惠券!");
            resp.setStatus(false);
            resp.setCode("50004");
            return resp;
        }


        //4. 校验权益配置的门店规则
        List<RightsResp> finalRights = filterSuitRightsResp(rights,data.getCustomerId(),data.getStoreId(), data.getUnionId(), data.getBrandId());
        if(CollectionUtils.isEmpty(finalRights)){
            resp.setMsg("无匹配的权益!");
            resp.setStatus(false);
            return resp;
        }


        Date now = new Date();

        log.info("finalRights = {}",JSONObject.toJSONString(finalRights));
        if(CollectionUtils.isEmpty(finalRights)){
            resp.setMsg("当前品牌和卡等级未配置生日优惠券!");
            resp.setStatus(false);
            resp.setCode("50004");
            return resp;
        }

        boolean fianlStatus = false;

        if(StringUtils.isBlank(birthday) || "-1".equals(birthday)){
            resp.setMsg("当前品牌用户未设置生日!");
            resp.setCode("50000");
            finalRights.stream().forEach(e->{e.setCode("50000");e.setStatus(false);});
            resp.setRightsResps(finalRights);
            resp.setStatus(false);
            return resp;
        }



        // 1. 校验用户是否已经领取过生日礼  查询用户今年是否领取过生日礼
        Integer findBirthSendLogStartYearMonth = null;
        Integer findBirthSendLogEndYearMonth = null;
        String month = birthday.substring(4, 6);
        String nowYearNowMonth = DateUtil.parseDate(new Date(), "yyyyMM");
        String nowMonth = nowYearNowMonth.substring(4);
        //今年用户生日
        String userBirth = nowYearNowMonth.substring(0, 4) + month;
        String userBirthStart = nowYearNowMonth.substring(0, 4) + "01";
        String userBirthEnd = nowYearNowMonth.substring(0, 4) + "12";

        if(Integer.parseInt(nowMonth) <= Integer.parseInt(month)){
            // 当前月份已经超过或者等于用户日期   当前时间  //  开始时间是去年8月  结束时间是 今年 8月
            findBirthSendLogStartYearMonth = (Integer.parseInt(userBirthStart)) ;
            findBirthSendLogEndYearMonth =  Integer.parseInt(userBirthEnd);
        }else{
            // 当前月份大于用户日期   明年                     开始时间是今年8月   结束时间是 用户明年生日
            findBirthSendLogStartYearMonth = (Integer.parseInt(userBirthStart) + 100);
            findBirthSendLogEndYearMonth = (Integer.parseInt(userBirthEnd) + 100) ;
        }

        if(intimacySwitchFlag.equals("0")){
            log.info("生日礼开关处于关闭状态，所有人都不可以领取生日礼!");
            for (RightsResp finalRight : finalRights) {
                finalRight.setStatus(false);
                finalRight.setMsg("暂时无法领取生日礼，请稍后再试");
                finalRight.setReceiveStatus(0);
                finalRight.setCode("50006");
                Map map = JSONObject.parseObject(finalRight.getUseRule(), Map.class);

                // 生日月前x天  生日月后x天
                String startDrawTimeConfigType = map.get("startDrawTimeConfigType").toString();
                String endDrawTimeConfigType = map.get("endDrawTimeConfigType").toString();
                String yearConsume = map.get("yearConsume") == null ? "-999999":map.get("yearConsume").toString();
                String startDrawTimeConfigNum = "0";
                String endDrawTimeConfigNum = "0";
                if(!startDrawTimeConfigType.equals("0")){
                    startDrawTimeConfigNum = map.get("startDrawTimeConfigNum").toString();
                }
                if(!endDrawTimeConfigType.equals("0")){
                    endDrawTimeConfigNum = map.get("endDrawTimeConfigNum").toString();
                }
                //获取用户今年生日
                String birthDayNowYear = DateUtils.formatDate(now, "yyyy") + birthday.substring(4,6) + "01";
                // 生日月第一天
                Date date = DateUtils.parseDate(birthDayNowYear, "yyyyMMdd");
                // 生日月最后一天
                Calendar ca = Calendar.getInstance();
                ca.setTime(date);
                ca.set(Calendar.DAY_OF_MONTH, ca.getActualMaximum(Calendar.DAY_OF_MONTH));
                Date monthLastDayDate = ca.getTime();

                Date startDraw = DateUtils.addDays( date,-Integer.parseInt(startDrawTimeConfigNum));
                Date endDraw = DateUtils.addDays( monthLastDayDate,Integer.parseInt(endDrawTimeConfigNum));

                String startTime = DateUtils.formatDate(startDraw, "yyyyMMdd");
                String endTime = DateUtils.formatDate(endDraw, "yyyyMMdd");
                String nowTime = DateUtils.formatDate(now, "yyyyMMdd");
                finalRight.setStartTime(Integer.parseInt(startTime));
                finalRight.setEndTime(Integer.parseInt(endTime));
            }
            resp.setMsg("暂时无法领取生日礼，请稍后再试!");
            resp.setStatus(false);
            resp.setCode("50006");
            resp.setReceiveStatus(0);
            resp.setRightsResps(finalRights);
            return resp;
        }


        // 0. 校验以前是否已经领取过生日礼 处理老的领取过的
        List<JicSendCouponLog> jicSendCouponLogs = jicSendCouponLogMapper.selectSendCouponUser(data.getOpenId(),findBirthSendLogStartYearMonth,findBirthSendLogEndYearMonth);

        List<BirthCouponSendLog> birthCouponSendLogs  = birthCouponSendLogMapper.selectByCardNoSendMonthByStartAndEnd(findBirthSendLogStartYearMonth,findBirthSendLogEndYearMonth,
                data.getCardNo(),BirthCouponSendLogStatusConstatnt.SEND);


        //2. 验证用户是否近一年消费满xxx金额 yearConsume
        // 2. 2024-08-01  验证用户的今年生日 - 去年截止 这12个月
        //获取用户今年生日
        String endYyyyMmDd = DateUtils.formatDate(now, "yyyy") + birthday.substring(4);
        // 取当前这个的当月结尾时间
        Date endYyyyMmDdDate = DateUtil.parseDate(endYyyyMmDd, "yyyyMMdd");
        // 获取当月最后一天
        Calendar calendarDate = Calendar.getInstance();
        calendarDate.setTime(endYyyyMmDdDate);
        calendarDate.set(Calendar.DAY_OF_MONTH, calendarDate.getActualMaximum(Calendar.DAY_OF_MONTH));
        Date endYyyyMmDdDateLast = calendarDate.getTime();
        endYyyyMmDd = DateUtils.formatDate(endYyyyMmDdDateLast, "yyyyMMdd");

//        String endYyyyMmDd = DateUtils.formatDate(now, "yyyyMMdd");
        // 用户今年生日往前推11个月
        Date startDate = DateUtils.addMonths(endYyyyMmDdDate, -11);
        String startYyyyMmDd = DateUtils.formatDate(startDate, "yyyyMMdd");
        startYyyyMmDd = startYyyyMmDd.substring(0, 6) + "01";
        // 查询金额
        BigDecimal decimal = mRetailMapper.sumConsume(String.valueOf(data.getId()), Integer.parseInt(startYyyyMmDd), Integer.parseInt(endYyyyMmDd));


        for (RightsResp finalRight : finalRights) {
            //1. 校验权益与用户当前的卡的生日是否匹配
            Map map = JSONObject.parseObject(finalRight.getUseRule(), Map.class);

            // 生日月前x天  生日月后x天
            String startDrawTimeConfigType = map.get("startDrawTimeConfigType").toString();
            String endDrawTimeConfigType = map.get("endDrawTimeConfigType").toString();
            String yearConsume = map.get("yearConsume") == null ? "-999999":map.get("yearConsume").toString();
            String startDrawTimeConfigNum = "0";
            String endDrawTimeConfigNum = "0";
            if(!startDrawTimeConfigType.equals("0")){
                startDrawTimeConfigNum = map.get("startDrawTimeConfigNum").toString();
            }
            if(!endDrawTimeConfigType.equals("0")){
                endDrawTimeConfigNum = map.get("endDrawTimeConfigNum").toString();
            }

            //获取用户今年生日
            String birthDayNowYear = DateUtils.formatDate(now, "yyyy") + birthday.substring(4,6) + "01";
            // 生日月第一天
            Date date = DateUtils.parseDate(birthDayNowYear, "yyyyMMdd");
            // 生日月最后一天
            Calendar ca = Calendar.getInstance();
            ca.setTime(date);
            ca.set(Calendar.DAY_OF_MONTH, ca.getActualMaximum(Calendar.DAY_OF_MONTH));
            Date monthLastDayDate = ca.getTime();

            Date startDraw = DateUtils.addDays( date,-Integer.parseInt(startDrawTimeConfigNum));
            Date endDraw = DateUtils.addDays( monthLastDayDate,Integer.parseInt(endDrawTimeConfigNum));

            String startTime = DateUtils.formatDate(startDraw, "yyyyMMdd");
            String endTime = DateUtils.formatDate(endDraw, "yyyyMMdd");
            String nowTime = DateUtils.formatDate(now, "yyyyMMdd");
            finalRight.setStartTime(Integer.parseInt(startTime));
            finalRight.setEndTime(Integer.parseInt(endTime));

            resp.setCardLevel(data.getVipShareTypeId().intValue());

            if(CollectionUtils.isNotEmpty(jicSendCouponLogs)){
                // 需要直接判断用户 当前的领取的生日券的 创建时间和当前时间是否在同一个月  如果不是 那么直接返回失败
//                Date createTime = jicSendCouponLogs.get(0).getCreateTime();
//                if(!DateUtils.formatDate(date,"yyyy-MM").equals(DateUtils.formatDate(createTime,"yyyy-MM"))){
//                    resp.setMsg("用户修改生日，不可领取,领取月份为 : "+DateUtils.formatDate(createTime,"yyyy-MM")+"!");
//                    resp.setStatus(false);
//                    resp.setCode("50003");
//                    finalRights.stream().forEach(e->{e.setStatus(false);e.setMsg("用户修改生日，不可领取,领取月份为 : "+DateUtils.formatDate(createTime,"yyyy-MM")+"!");e.setCode("50003");});
//                    resp.setRightsResps(finalRights);
//                    return resp;
//                }
//
//                Object rule = map.get("rule");
//                List<Map> mapList = JSONObject.parseArray(JSONObject.toJSONString(rule), Map.class);
//                List<String> couponIds = mapList.stream().map(r -> r.get("couponId").toString()).collect(Collectors.toList());
//                if(CollectionUtils.isNotEmpty(jicSendCouponLogs)){
//                    for (JicSendCouponLog jicSendCouponLog : jicSendCouponLogs) {
//                        for (String couponId : couponIds) {
//                            if(couponId.equals(jicSendCouponLog.getCouponId())){
//                                //包含  当前的这个券id已经发过了  则不可领
//                                resp.setMsg("用户今年当前卡号已经领取过生日优惠券!");
//                                resp.setStatus(false);
//                                resp.setCode("50006");
//                                resp.setReceiveStatus(1);
//                                if(StringUtils.isNotBlank(jicSendCouponLog.getCouponNo())){
//                                    resp.setVoucherNos(Arrays.asList(jicSendCouponLog.getCouponNo().split(",")));
//                                }
//                                return resp;
//                            }
//                        }
//                    }
//                    if(!finalRight.getStatus()){
//                        continue;
//                    }
//                }
                resp.setMsg("用户今年当前卡号已经领取过生日优惠券!");
                resp.setStatus(false);
                resp.setCode("50006");
                resp.setReceiveStatus(1);
                finalRights.stream().forEach(e->{e.setStatus(false);e.setMsg("用户今年当前卡号已经领取过生日优惠券");e.setCode("50006");e.setReceiveStatus(1);});
                resp.setRightsResps(finalRights);
//                    if(StringUtils.isNotBlank(jicSendCouponLog.getCouponNo())){
//                        resp.setVoucherNos(Arrays.asList(jicSendCouponLog.getCouponNo().split(",")));
//                    }
                return resp;
            }



            if(CollectionUtils.isNotEmpty(birthCouponSendLogs)){
                // 需要直接判断用户 当前的领取的生日券的 创建时间和当前时间是否在同一个月  如果不是 那么直接返回失败
                Date createTime = birthCouponSendLogs.get(0).getCreateTime();
                if(!DateUtils.formatDate(date,"yyyy-MM").equals(DateUtils.formatDate(createTime,"yyyy-MM"))){
                    resp.setMsg("用户修改生日，不可领取,领取月份为 : "+DateUtils.formatDate(createTime,"yyyy-MM")+"!");
                    resp.setStatus(false);
                    resp.setCode("50003");
                    finalRights.stream().forEach(e->{
                        for (BirthCouponSendLog birthCouponSendLog : birthCouponSendLogs) {
                            if(birthCouponSendLog.getRightsId().equals(e.getRightsId())){
                                e.setReceiveStatus(1);
                            }
                        }
                        e.setStartTime(Integer.parseInt(startTime));
                        e.setEndTime(Integer.parseInt(endTime));
                        e.setStatus(false);
                        e.setMsg("用户修改生日，不可领取,领取月份为 : "+DateUtils.formatDate(createTime,"yyyy-MM")+"!");
                        e.setCode("50003");
                    });
                    resp.setRightsResps(finalRights);
                    return resp;
                }
                String rightsId = finalRight.getRightsId();

                for (BirthCouponSendLog birthCouponSendLog : birthCouponSendLogs) {
                    if(birthCouponSendLog.getRightsId().equals(rightsId)){
                        //包含  当前的这个券id已经发过了  则不可领
                        finalRight.setMsg("用户今年当前卡号已经领取过生日优惠券!");
                        finalRight.setStatus(false);
                        finalRight.setReceiveStatus(1);
                        finalRight.setCode("50006");
                        if(StringUtils.isNotBlank(birthCouponSendLog.getVoucherNos())){
                            finalRight.setVoucherNos(Arrays.asList(birthCouponSendLog.getVoucherNos().split(",")));
                        }
                        break;
                    }else{
                        // 判断用户已经领取了折扣券  根据权益类型 和 awardId进行判断 分别拿出 awardId
                        BRights bRights = bRightsMapper.selectByPrimaryKey(birthCouponSendLog.getRightsId());
                        BRights shouldSendRights = bRightsMapper.selectByPrimaryKey(rightsId);
                        String useRule = bRights.getUseRule();
                        String useRule1 = shouldSendRights.getUseRule();

                        Object rule = JSONObject.parseObject(useRule, Map.class).get("rule");
                        List<Map> mapList = JSONObject.parseArray(JSONObject.toJSONString(rule), Map.class);

                        Object rule2 = JSONObject.parseObject(useRule1, Map.class).get("rule");
                        List<Map> mapLis2 = JSONObject.parseArray(JSONObject.toJSONString(rule2), Map.class);

                        for (Map map1 : mapLis2) {
                            Object couponId = map1.get("couponId");
                            for (Map map2 : mapList) {
                                if(couponId.toString().equals(map2.get("couponId").toString())){
                                    // 则此权益不能领取
                                    //包含  当前的这个券id已经发过了  则不可领
                                    finalRight.setMsg("当前券用户已经领取过，请领取其他的券!");
                                    finalRight.setStatus(false);
                                    finalRight.setReceiveStatus(1);
                                    finalRight.setCode("50010");
                                    if(StringUtils.isNotBlank(birthCouponSendLog.getVoucherNos())){
                                        finalRight.setVoucherNos(Arrays.asList(birthCouponSendLog.getVoucherNos().split(",")));
                                    }
                                }
                            }
                        }
                    }
                }
                if(!finalRight.getStatus()){
                    continue;
                }
            }


            //3. 校验权益配置的卡信息是否符合开卡xx天
            String createDate = data.getCreateDate();
            Integer effectiveTimeNum = finalRight.getEffectiveTimeNum();
            if(effectiveTimeNum > 0){
                // 差值
                Date createTime = DateUtils.parseDate(createDate, DateUtils.DATETIME_PATTERN);
                Long day = ChronoUnit.DAYS.between(createTime.toInstant(), now.toInstant());
                if(day < effectiveTimeNum){
                    finalRight.setMsg("开卡未满"+effectiveTimeNum+"天!");
                    finalRight.setStatus(false);
                    finalRight.setCode("50002");
                    continue;
                }
            }


            if(decimal == null){
                decimal = BigDecimal.ZERO;
            }
            finalRight.setTotAmount(decimal);

            if(Integer.parseInt(nowTime) < Integer.parseInt(startTime) || Integer.parseInt(nowTime) > Integer.parseInt(endTime)){
                finalRight.setMsg("当前品牌用户还未到生日月!");
                finalRight.setCode("50001");
                finalRight.setStatus(false);
                continue;
            }

            if(decimal.compareTo(new BigDecimal(yearConsume)) < 0 ){
                finalRight.setConsumeAmount(new BigDecimal(yearConsume).subtract(decimal));
                finalRight.setMsg("当前品牌用户近一年消费不足"+yearConsume+"元，无法领取!");
                finalRight.setStatus(false);
                finalRight.setCode("50005");
            }

            if(finalRight.getStatus()){
                fianlStatus = true;
            }
        }

        // 获取最终状态
        resp.setStatus(fianlStatus);
        resp.setRightsResps(finalRights);
        return resp;
    }


    /**
     *
     * @param memberDatas
     * @param requestData  可以是new 一个新对象
     * @return
     */
    private List<CheckCanReceiveDto> checkCanReceive(List<MemberCardEntity> memberDatas, ReceiveBirthCouponReq requestData) {
        if(CollectionUtils.isEmpty(memberDatas)){
            return new ArrayList<>();
        }
        List<CheckCanReceiveDto> checkCanReceiveDtoList = new ArrayList<>();
        // 映射
        List<Map> birthList = JSONObject.parseArray(birthWeid, Map.class);

        // 查询所有权益 根据品牌查  没有根据卡id查询 后面需要过滤卡id
        GetRightsReq getRightsReq = new GetRightsReq();
        getRightsReq.setRightsType(RightsTypeEnum.BIRTH_COUPON.getCode());
        getRightsReq.setBrandIds(memberDatas.stream().map(r->r.getBrandId()).collect(Collectors.toList()));
        List<RightsResp> allRights = bRightsMapper.getRightsByBrandIds(getRightsReq);

        // 1. 校验用户是否已经领取过生日礼  查询用户今年是否领取过生日礼
        Integer findBirthSendLogStartYearMonth = null;
        Integer findBirthSendLogEndYearMonth = null;
        String month = "12";  // 默认 12月
        String nowYearNowMonth = DateUtil.parseDate(new Date(), "yyyyMM");
        String nowMonth = nowYearNowMonth.substring(4);
        //今年用户生日
        String userBirthStart = nowYearNowMonth.substring(0, 4) + "01";
        String userBirthEnd = nowYearNowMonth.substring(0, 4) + "12";

        if(Integer.parseInt(nowMonth) <= Integer.parseInt(month)){
            // 当前月份已经超过或者等于用户日期   当前时间  //  开始时间是去年8月  结束时间是 今年 8月
            findBirthSendLogStartYearMonth = (Integer.parseInt(userBirthStart)) ;
            findBirthSendLogEndYearMonth =  Integer.parseInt(userBirthEnd);
        }else{
            // 当前月份大于用户日期   明年                     开始时间是今年8月   结束时间是 用户明年生日
            findBirthSendLogStartYearMonth = (Integer.parseInt(userBirthStart) + 100);
            findBirthSendLogEndYearMonth = (Integer.parseInt(userBirthEnd) + 100) ;
        }

        List<String> openIds = memberDatas.stream().map(r -> r.getOpenId()).collect(Collectors.toList());
        List<String> cardNos = memberDatas.stream().map(r -> r.getCardNo()).collect(Collectors.toList());

        // 0. 校验以前是否已经领取过生日礼 处理老的领取过的  查询所有用户去年的券
        List<JicSendCouponLog> jicSendCouponLogsAll =
                jicSendCouponLogMapper.selectSendCouponUserBatch(openIds,findBirthSendLogStartYearMonth,findBirthSendLogEndYearMonth);

        List<BirthCouponSendLog> birthCouponSendLogsAll  =
                birthCouponSendLogMapper.selectByCardNoSendMonthByStartAndEndBatch(findBirthSendLogStartYearMonth,findBirthSendLogEndYearMonth,
                        cardNos,BirthCouponSendLogStatusConstatnt.SEND);

        Map<String,List<BRights>> groupbyBRightsId = new HashMap<>();

        if(CollectionUtils.isNotEmpty(birthCouponSendLogsAll)){
            Set<String> rightsIds = birthCouponSendLogsAll.stream().map(r -> r.getRightsId()).collect(Collectors.toSet());
            List<BRights> bRightsall = bRightsMapper.selectByPrimaryKeys(new ArrayList<>(rightsIds));
            groupbyBRightsId = bRightsall.stream().collect(Collectors.groupingBy(r->r.getId()));
        }


        Map<String, List<BirthCouponSendLog>> groupByCardNo = birthCouponSendLogsAll.stream().collect(Collectors.groupingBy(r -> r.getCardNo()));
        Map<String, List<JicSendCouponLog>> groupByOpenId = jicSendCouponLogsAll.stream().collect(Collectors.groupingBy(r -> r.getOpenId()));


        for (Map birthMap : birthList) {
            // 开始处理
            CheckCanReceiveDto resp = new CheckCanReceiveDto();
            String weid = birthMap.get("weid").toString();
            resp.setWeid(weid);
            checkCanReceiveDtoList.add(resp);
        }

        for (CheckCanReceiveDto resp : checkCanReceiveDtoList) {
            String weid = resp.getWeid();
            for (MemberCardEntity memberData : memberDatas) {
                String brandId = memberData.getBrandId();
                String cardNo = memberData.getCardNo();
                if(weid.equals(brandId)){
                    resp.setOpenId(memberData.getWxOpenId());
                    resp.setCardNo(cardNo);
                    resp.setWid(memberData.getWid());
                    resp.setLevelId(memberData.getVipShareTypeLevelId()+"");
                    // 筛选过滤 可用权益
                    List<RightsResp> useRights = allRights.stream().filter(r -> r.getCardLevel().equals(memberData.getVipShareTypeId().toString())).collect(Collectors.toList());
                    if(CollectionUtils.isEmpty(useRights)){
                        resp.setMsg("当前品牌和卡等级未配置生日优惠券!");
                        resp.setStatus(false);
                        resp.setCode("50004");
                        continue;
                    }

                    //1. 校验用户是否已经领取过今年的生日礼
                    String birthday = memberData.getBirthday();

                    // 小童  蓬马 按照babyBirth取值
                    if("4".equals(memberData.getBrandId()) || "6924108367".equals(memberData.getBrandId())){
                        if(memberData.getBabyBirthday()!= null){
                            birthday = memberData.getBabyBirthday().toString();
                        }else{
                            birthday = null;
                        }
                    }

                    if(StringUtils.isNotBlank(birthday) && birthday.equals("-1")){
                        birthday = null;
                    }
                    if(StringUtils.isNotBlank(birthday)){
                        resp.setBirth(birthday);
                    }

                    int yearStart = Integer.parseInt(DateUtils.formatDate(new Date(), "yyyy") + "01");

                    if(requestData.getCardTypeId() != null){
                        GetRightsReq getRightsReq2 = new GetRightsReq();
                        getRightsReq2.setRightsType(RightsTypeEnum.BIRTH_COUPON.getCode());
                        getRightsReq2.setBrandId(memberData.getBrandId());
                        getRightsReq2.setCardLevel(requestData.getCardTypeId());
                        List<RightsResp> rights2 = bRightsMapper.getRights(getRightsReq2);
                        if(CollectionUtils.isNotEmpty(rights2)){
                            List<RightsResp> finalRights = filterSuitRightsResp(rights2, memberData.getCustomerId(), memberData.getStoreId(),memberData.getUnionId(),
                                    memberData.getBrandId());
                            for (RightsResp finalRight : finalRights) {

                                //1. 校验权益与用户当前的卡的生日是否匹配
                                Map map = JSONObject.parseObject(finalRight.getUseRule(), Map.class);

                                // 生日月前x天  生日月后x天
                                String startDrawTimeConfigType = map.get("startDrawTimeConfigType").toString();
                                String endDrawTimeConfigType = map.get("endDrawTimeConfigType").toString();
                                String yearConsume = map.get("yearConsume") == null ? "0":map.get("yearConsume").toString();
                                String startDrawTimeConfigNum = "0";
                                String endDrawTimeConfigNum = "0";
                                if(!startDrawTimeConfigType.equals("0")){
                                    startDrawTimeConfigNum = map.get("startDrawTimeConfigNum").toString();
                                }
                                if(!endDrawTimeConfigType.equals("0")){
                                    endDrawTimeConfigNum = map.get("endDrawTimeConfigNum").toString();
                                }
                                if(StringUtils.isNotBlank(birthday)){
                                    //获取用户今年生日
                                    String birthDayNowYear = DateUtils.formatDate(new Date(), "yyyy") + birthday.substring(4,6) + "01";
                                    // 生日月第一天
                                    Date date = DateUtils.parseDate(birthDayNowYear, "yyyyMMdd");
                                    // 生日月最后一天
                                    Calendar ca = Calendar.getInstance();
                                    ca.setTime(date);
                                    ca.set(Calendar.DAY_OF_MONTH, ca.getActualMaximum(Calendar.DAY_OF_MONTH));
                                    Date monthLastDayDate = ca.getTime();

                                    Date startDraw = DateUtils.addDays( date,-Integer.parseInt(startDrawTimeConfigNum));
                                    Date endDraw = DateUtils.addDays( monthLastDayDate,Integer.parseInt(endDrawTimeConfigNum));

                                    String startTime = DateUtils.formatDate(startDraw, "yyyyMMdd");
                                    String endTime = DateUtils.formatDate(endDraw, "yyyyMMdd");
                                    String nowTime = DateUtils.formatDate(new Date(), "yyyyMMdd");
                                    finalRight.setStartTime(Integer.parseInt(startTime));
                                    finalRight.setEndTime(Integer.parseInt(endTime));
                                }
                            }
                            resp.setCompareRightsResps(finalRights);
                        }
                    }

                    //4. 校验权益配置的门店规则
                    List<RightsResp> finalRights = filterSuitRightsResp(useRights,memberData.getCustomerId(),memberData.getStoreId(),
                            memberData.getUnionId(), memberData.getBrandId());
                    if(CollectionUtils.isEmpty(finalRights)){
                        resp.setMsg("无匹配的权益!");
                        resp.setStatus(false);
                        continue;
                    }

                    Date now = new Date();

                    log.info("finalRights = {}",JSONObject.toJSONString(finalRights));
                    if(CollectionUtils.isEmpty(finalRights)){
                        resp.setMsg("当前品牌和卡等级未配置生日优惠券!");
                        resp.setStatus(false);
                        resp.setCode("50004");
                        continue;
                    }


                    boolean fianlStatus = false;

                    if(StringUtils.isBlank(birthday) || "-1".equals(birthday)){
                        resp.setMsg("当前品牌用户未设置生日!");
                        resp.setCode("50000");
                        finalRights.stream().forEach(e->{e.setCode("50000");e.setStatus(false);});
                        resp.setRightsResps(finalRights);
                        resp.setStatus(false);
                        continue;
                    }


                    // 当前生日
                    month = birthday.substring(4, 6);

                    List<BirthCouponSendLog> birthCouponSendLogs;
                    List<JicSendCouponLog> jicSendCouponLogs = new ArrayList<>();
                    if(Integer.parseInt(nowMonth) <= Integer.parseInt(month)){
                        // 当前月份已经超过或者等于用户日期   当前时间  //  开始时间是去年8月  结束时间是 今年 8月  把这些用户取出来  根据cardNo 或者 openId
                        birthCouponSendLogs  = groupByCardNo.get(memberData.getCardNo());
                        jicSendCouponLogs = groupByOpenId.get(memberData.getOpenId());
                    } else {
                        birthCouponSendLogs = new ArrayList<>();
                    }

                    //2. 验证用户是否近一年消费满xxx金额 yearConsume
                    // 2. 2024-08-01  验证用户的今年生日 - 去年截止 这12个月
                    //获取用户今年生日
                    String endYyyyMmDd = DateUtils.formatDate(now, "yyyy") + birthday.substring(4);
                    // 取当前这个的当月结尾时间
                    Date endYyyyMmDdDate = DateUtil.parseDate(endYyyyMmDd, "yyyyMMdd");
                    // 获取当月最后一天
                    Calendar calendarDate = Calendar.getInstance();
                    calendarDate.setTime(endYyyyMmDdDate);
                    calendarDate.set(Calendar.DAY_OF_MONTH, calendarDate.getActualMaximum(Calendar.DAY_OF_MONTH));
                    Date endYyyyMmDdDateLast = calendarDate.getTime();
                    endYyyyMmDd = DateUtils.formatDate(endYyyyMmDdDateLast, "yyyyMMdd");
                    // 用户今年生日往前推11个月
                    Date startDate = DateUtils.addMonths(endYyyyMmDdDate, -11);
                    String startYyyyMmDd = DateUtils.formatDate(startDate, "yyyyMMdd");
                    startYyyyMmDd = startYyyyMmDd.substring(0, 6) + "01";
                    // 查询金额
                    BigDecimal decimal = mRetailMapper.sumConsume(String.valueOf(memberData.getId()), Integer.parseInt(startYyyyMmDd), Integer.parseInt(endYyyyMmDd));

                    inside:for (RightsResp finalRight : finalRights) {
                        //1. 校验权益与用户当前的卡的生日是否匹配
                        Map map = JSONObject.parseObject(finalRight.getUseRule(), Map.class);

                        // 生日月前x天  生日月后x天
                        String startDrawTimeConfigType = map.get("startDrawTimeConfigType").toString();
                        String endDrawTimeConfigType = map.get("endDrawTimeConfigType").toString();
                        String yearConsume = map.get("yearConsume") == null ? "-999999":map.get("yearConsume").toString();
                        String startDrawTimeConfigNum = "0";
                        String endDrawTimeConfigNum = "0";
                        if(!startDrawTimeConfigType.equals("0")){
                            startDrawTimeConfigNum = map.get("startDrawTimeConfigNum").toString();
                        }
                        if(!endDrawTimeConfigType.equals("0")){
                            endDrawTimeConfigNum = map.get("endDrawTimeConfigNum").toString();
                        }

                        //获取用户今年生日
                        String birthDayNowYear = DateUtils.formatDate(now, "yyyy") + birthday.substring(4,6) + "01";
                        // 生日月第一天
                        Date date = DateUtils.parseDate(birthDayNowYear, "yyyyMMdd");
                        // 生日月最后一天
                        Calendar ca = Calendar.getInstance();
                        ca.setTime(date);
                        ca.set(Calendar.DAY_OF_MONTH, ca.getActualMaximum(Calendar.DAY_OF_MONTH));
                        Date monthLastDayDate = ca.getTime();

                        Date startDraw = DateUtils.addDays( date,-Integer.parseInt(startDrawTimeConfigNum));
                        Date endDraw = DateUtils.addDays( monthLastDayDate,Integer.parseInt(endDrawTimeConfigNum));

                        String startTime = DateUtils.formatDate(startDraw, "yyyyMMdd");
                        String endTime = DateUtils.formatDate(endDraw, "yyyyMMdd");
                        String nowTime = DateUtils.formatDate(now, "yyyyMMdd");
                        finalRight.setStartTime(Integer.parseInt(startTime));
                        finalRight.setEndTime(Integer.parseInt(endTime));

                        resp.setCardLevel(memberData.getVipShareTypeId().intValue());

                        if(CollectionUtils.isNotEmpty(jicSendCouponLogs)){
                            // 需要直接判断用户 当前的领取的生日券的 创建时间和当前时间是否在同一个月  如果不是 那么直接返回失败
                            resp.setMsg("用户今年当前卡号已经领取过生日优惠券!");
                            resp.setStatus(false);
                            resp.setCode("50006");
                            resp.setReceiveStatus(1);
                            finalRights.stream().forEach(e->{e.setStatus(false);e.setMsg("用户今年当前卡号已经领取过生日优惠券");e.setCode("50006");e.setReceiveStatus(1);});
                            resp.setRightsResps(finalRights);
                            break;
                        }



                        if(CollectionUtils.isNotEmpty(birthCouponSendLogs)){
                            // 需要直接判断用户 当前的领取的生日券的 创建时间和当前时间是否在同一个月  如果不是 那么直接返回失败
                            Date createTime = birthCouponSendLogs.get(0).getCreateTime();
                            if(!DateUtils.formatDate(date,"yyyy-MM").equals(DateUtils.formatDate(createTime,"yyyy-MM"))){
                                resp.setMsg("用户修改生日，不可领取,领取月份为 : "+DateUtils.formatDate(createTime,"yyyy-MM")+"!");
                                resp.setStatus(false);
                                resp.setCode("50003");
                                finalRights.stream().forEach(e->{
                                    for (BirthCouponSendLog birthCouponSendLog : birthCouponSendLogs) {
                                        if(birthCouponSendLog.getRightsId().equals(e.getRightsId())){
                                            e.setReceiveStatus(1);
                                            if(StringUtils.isNotBlank(birthCouponSendLog.getVoucherNos())){
                                                finalRight.setVoucherNos(Arrays.asList(birthCouponSendLog.getVoucherNos().split(",")));
                                            }
                                        }
                                    }
                                    e.setStartTime(Integer.parseInt(startTime));
                                    e.setEndTime(Integer.parseInt(endTime));
                                    e.setStatus(false);
                                    e.setMsg("用户修改生日，不可领取,领取月份为 : "+DateUtils.formatDate(createTime,"yyyy-MM")+"!");
                                    e.setCode("50003");
                                });

                                resp.setRightsResps(finalRights);
                                break;
                            }

                            String rightsId = finalRight.getRightsId();
                            for (BirthCouponSendLog birthCouponSendLog : birthCouponSendLogs) {
                                if(birthCouponSendLog.getRightsId().equals(rightsId)){
                                    //包含  当前的这个券id已经发过了  则不可领
                                    finalRight.setMsg("用户今年当前卡号已经领取过生日优惠券!");
                                    finalRight.setStatus(false);
                                    finalRight.setReceiveStatus(1);
                                    finalRight.setCode("50006");
                                    if(StringUtils.isNotBlank(birthCouponSendLog.getVoucherNos())){
                                        finalRight.setVoucherNos(Arrays.asList(birthCouponSendLog.getVoucherNos().split(",")));
                                    }
                                    break;
                                }else{
                                    // 判断用户已经领取了折扣券  根据权益类型 和 awardId进行判断 分别拿出 awardId
                                    List<BRights> bRights = groupbyBRightsId.get(birthCouponSendLog.getRightsId());
                                    String useRule = bRights.get(0).getUseRule();
                                    String useRule1 = finalRight.getUseRule();

                                    Object rule = JSONObject.parseObject(useRule, Map.class).get("rule");
                                    List<Map> mapList = JSONObject.parseArray(JSONObject.toJSONString(rule), Map.class);

                                    Object rule2 = JSONObject.parseObject(useRule1, Map.class).get("rule");
                                    List<Map> mapLis2 = JSONObject.parseArray(JSONObject.toJSONString(rule2), Map.class);

                                    for (Map map1 : mapLis2) {
                                        Object couponId = map1.get("couponId");
                                        for (Map map2 : mapList) {
                                            if(couponId.toString().equals(map2.get("couponId").toString())){
                                                // 则此权益不能领取
                                                //包含  当前的这个券id已经发过了  则不可领
                                                finalRight.setMsg("当前券用户已经领取过，请领取其他的券!");
                                                finalRight.setStatus(false);
                                                finalRight.setReceiveStatus(1);
                                                finalRight.setCode("50010");
                                                if(StringUtils.isNotBlank(birthCouponSendLog.getVoucherNos())){
                                                    finalRight.setVoucherNos(Arrays.asList(birthCouponSendLog.getVoucherNos().split(",")));
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            if(!finalRight.getStatus()){
                                continue;
                            }
                        }


                        //3. 校验权益配置的卡信息是否符合开卡xx天
                        String createDate = memberData.getCreateDate();
                        Integer effectiveTimeNum = finalRight.getEffectiveTimeNum();
                        if(effectiveTimeNum > 0){
                            // 差值
                            Date createTime = DateUtils.parseDate(createDate, DateUtils.DATETIME_PATTERN);
                            Long day = ChronoUnit.DAYS.between(createTime.toInstant(), now.toInstant());
                            if(day < effectiveTimeNum){
                                finalRight.setMsg("开卡未满"+effectiveTimeNum+"天!");
                                finalRight.setStatus(false);
                                finalRight.setCode("50002");
                                continue;
                            }
                        }

                        if(decimal == null){
                            decimal = BigDecimal.ZERO;
                        }
                        finalRight.setTotAmount(decimal);

                        if(Integer.parseInt(nowTime) < Integer.parseInt(startTime) || Integer.parseInt(nowTime) > Integer.parseInt(endTime)){
                            finalRight.setMsg("当前品牌用户还未到生日月!");
                            finalRight.setCode("50001");
                            finalRight.setStatus(false);
                            continue;
                        }

                        if(decimal.compareTo(new BigDecimal(yearConsume)) < 0 ){
                            finalRight.setConsumeAmount(new BigDecimal(yearConsume).subtract(decimal));
                            finalRight.setMsg("当前品牌用户近一年消费不足"+yearConsume+"元，无法领取!");
                            finalRight.setStatus(false);
                            finalRight.setCode("50005");
                        }

                        if(finalRight.getStatus()){
                            fianlStatus = true;
                        }
                    }

                    // 获取最终状态
                    resp.setStatus(fianlStatus);
                    resp.setRightsResps(finalRights);
                }
            }
        }
        return checkCanReceiveDtoList;
    }

    public List<RightsResp> filterSuitRightsResp(List<RightsResp> rights, Integer customerId,
                                                 Integer storeId, String unionid, String brandId) {
        List<RightsResp> finalRights = new ArrayList<>();

        if(storeId == null || storeId == -1){
            // 无门店的  直接去获取 无门店权益 其他则 有店门 正常跑
            finalRights = rights.stream().filter(rightsResp -> rightsResp.getSuitableStoreType() == 6).collect(Collectors.toList());
        }else{
            for (RightsResp right : rights) {
                if(right.getSuitableStoreType() == 3){
                    finalRights.add(right);
                }else if(right.getSuitableStoreType() == 4){
                    if(right.getSuitCusType() == null){
                        right.setSuitCusType(0);
                    }
                    if(right.getSuitCusType().equals(0)){
                        //验证门店编码是否在之内
                        List<String> list = bRightsCustomizeStoreMapper.selectByRightIdAndStoreId(right.getRightsId(),storeId+"");
                        if(CollectionUtils.isNotEmpty(list)){
                            finalRights.add(right);
                        }
                    }else{
                        // 新门店包
//                        List<String> packetIds = bRightsCustomizeStoreMapper.selectByRightId(right.getRightsId());
//                        List<Integer> cstoreIds = new ArrayList<>();
//                        cstoreIds.add(storeId);
//                        for (String packetId : packetIds) {
//                            List<Integer> resultList = targetStoreIds(packetId, cstoreIds);
//                            if(CollectionUtils.isNotEmpty(resultList)){
//                                log.info("打印出来  原来单个门店包匹配的  权益id  = {}",right.getRightsId());
//                                finalRights.add(right);
//                                break;
//                            }
//                        }
                    }
                }else if(right.getSuitableStoreType() == 2){
                    //经销
                    if(!"176".equals(String.valueOf(customerId))){
                        finalRights.add(right);
                    }
                }else if(right.getSuitableStoreType() == 1){
                    //直营
                    if("176".equals(String.valueOf(customerId))){
                        finalRights.add(right);
                    }
                }
            }

            // 需要批量门店包接口
            List<RightsResp> shouldBatchStoreIdRights = rights.stream().filter(r -> r.getSuitableStoreType().equals(4)
                    && r.getSuitCusType().equals(1)).collect(Collectors.toList());

            if(CollectionUtils.isNotEmpty(shouldBatchStoreIdRights)){
                // 批量门店包后
                Set<String> filterRightsIds = new HashSet<>();
                // rightsId
                Set<String> rihgtsIdsSet = shouldBatchStoreIdRights.stream().map(r -> r.getRightsId()).collect(Collectors.toSet());

                List<BRightsCustomizeStore> bRightsCustomizeStores = new ArrayList<>();
                String join = RedisKeysEnum.REDIS_RIGHTS_STOREIDS.join(JSONObject.toJSONString(rihgtsIdsSet));
                if(rightsRedisCacheFlag.equals("1")){
                    Object o = redisService.get(join);
                    if(o != null){
                        bRightsCustomizeStores = JSONObject.parseArray(o.toString(),BRightsCustomizeStore.class);
                    }
                }
                if(CollectionUtils.isEmpty(bRightsCustomizeStores)){
                    bRightsCustomizeStores = bRightsCustomizeStoreMapper
                            .selectByRightIds(shouldBatchStoreIdRights.stream().map(r->r.getRightsId()).collect(Collectors.toList()));
                    // 设置缓存
                    if(CollectionUtils.isNotEmpty(bRightsCustomizeStores)){
                        redisService.set(join,JSONObject.toJSONString(bRightsCustomizeStores),3600*4);
                    }
                }

                // 根据门店包ID分组
                Map<String, List<BRightsCustomizeStore>> groupByStorePacketId = bRightsCustomizeStores.stream().collect(Collectors.groupingBy(r -> r.getStoreId()));
                List<Long> packetIds = bRightsCustomizeStores.stream().map(r -> Long.parseLong(r.getStoreId())).collect(Collectors.toList());
                // 根据权益id分组
                Map<String, List<RightsResp>> groupById = shouldBatchStoreIdRights.stream().collect(Collectors.groupingBy(r -> r.getRightsId()));

                List<StorePacketResp> storePacketResps = targetStoreIdByStorePacketIds(storeId.toString(), packetIds);
                // 判断
                for (StorePacketResp storePacketResp : storePacketResps) {
                    // 命中 并且 storeId 包含
                    if(storePacketResp.getStorePackageId() != null && storePacketResp.isHit()){
                        // 获取权益id
                        List<BRightsCustomizeStore> bRightsCustomizeStores1 = groupByStorePacketId.get(storePacketResp.getStorePackageId().toString());
                        for (BRightsCustomizeStore bRightsCustomizeStore : bRightsCustomizeStores1) {
                            List<RightsResp> rightsResps = groupById.get(bRightsCustomizeStore.getbRightsId());
                            // 拿出来id
                            if(CollectionUtils.isNotEmpty(rightsResps)){
                                List<String> collect = rightsResps.stream().map(r -> r.getRightsId()).collect(Collectors.toList());
                                // 已经是去重的id
                                filterRightsIds.addAll(collect);
                            }
                        }
                    }
                }
                // 权益iD
                log.info("打印出来  新的批量门店包匹配的  权益id  = {}",JSONObject.toJSONString(filterRightsIds));
                // 筛选出来
                List<RightsResp> collect = shouldBatchStoreIdRights.stream().filter(r -> filterRightsIds.contains(r.getRightsId())).collect(Collectors.toList());
                // 符合门店包的权益
                finalRights.addAll(collect);
            }


            // ----------------------------------------------------------- 2025-02-28 ----------------------------------- 增加  人群包判断
            // 不开缓存走人群包  开着缓存 不走人群包
            if(!rightsRedisCacheFlag.equals("1")){
                if(StringUtils.isNotBlank(unionid) && StringUtils.isNotBlank(brandId)){
                    // 查询到用户的卡号
                    MemberQueryContext memberQueryContext = new MemberQueryContext();
                    memberQueryContext.setUnionId(unionid);
                    memberQueryContext.setBrandId(brandId);
                    memberQueryContext.setStatus("Y");
                    CustomerBaseResponse<List<MemberCardEntity>> memberCardList = iUserVipService.getMemberCardList(memberQueryContext);
                    if(memberCardList != null && CollectionUtils.isNotEmpty(memberCardList.getData())){
                        List<MemberCardEntity> data = memberCardList.getData();
                        MemberCardEntity memberCardEntity = data.get(0);
                        String cardNo = memberCardEntity.getCardNo();

                        Set<String> filterPeopleCrowdRightsIds = new HashSet<>();
                        // 筛选权益符合条件的权益
                        // 筛选需要人群包的权益
                        List<RightsResp> shouldMatchPeople = rights.stream().filter(r -> StringUtils.isNotBlank(r.getPeopleCrowdIds())).collect(Collectors.toList());
                        if(CollectionUtils.isNotEmpty(shouldMatchPeople)){
                            // 根据 人群包id分组权益  一个人群包可能对应多个权益id  如果这个人群包id返回true  那么里面的所有权益都为true
                            Map<String,List<RightsResp>>  groupByPeopleCrowdIds = new HashMap<>();
                            for (RightsResp shouldMatchPerson : shouldMatchPeople) {
                                String peopleCrowdIds = shouldMatchPerson.getPeopleCrowdIds();
                                String[] split = peopleCrowdIds.split(",");
                                for (String peopleCrowdId : split) {
                                    List<RightsResp> rightsResps = groupByPeopleCrowdIds.get(peopleCrowdId);
                                    if(CollectionUtils.isEmpty(rightsResps)){
                                        rightsResps = new ArrayList<>();
                                        rightsResps.add(shouldMatchPerson);
                                    }else{
                                        rightsResps.add(shouldMatchPerson);
                                    }
                                    groupByPeopleCrowdIds.put(peopleCrowdId,rightsResps);
                                }
                            }

                            //封装参数
                            PeoplePacketReq peoplePacketReq = new PeoplePacketReq();
//                                peoplePacketReq.setUnionId(unionid);
                            peoplePacketReq.setCrowdIds(new ArrayList<>(groupByPeopleCrowdIds.keySet()));
                            peoplePacketReq.setCardNo(cardNo);

                            //  groupByPeopleCrowdIds  key 就是全部的需要判断的 人群包id
                            List<PeoplePacketResp> peoplePacketResps = iPackageStoresService.peopleBatchHit(peoplePacketReq);
                            if(CollectionUtils.isNotEmpty(peoplePacketResps)){
                                // 处理人群包返回数据
                                for (PeoplePacketResp peoplePacketResp : peoplePacketResps) {
                                    String crowdId = peoplePacketResp.getCrowdId();
                                    if(peoplePacketResp.isHit()){
                                        List<RightsResp> rightsResps = groupByPeopleCrowdIds.get(crowdId);
                                        // 添加id到数据中
                                        if(CollectionUtils.isNotEmpty(rightsResps)){
                                            for (RightsResp rightsResp : rightsResps) {
                                                // 去重塞入
                                                filterPeopleCrowdRightsIds.add(rightsResp.getRightsId());
                                            }
                                        }
                                    }
                                }
                            }
                            log.info("符合人群包权益数据id = {}",JSONObject.toJSONString(filterPeopleCrowdRightsIds));
                            // 筛选出来符合人群包的权益信息
                            List<RightsResp> filterMatchPeoples = shouldMatchPeople.stream().filter(r ->
                                    filterPeopleCrowdRightsIds.contains(r.getRightsId())).collect(Collectors.toList());


                            List<RightsResp> matchFinalResult = new ArrayList<>();

                            // 处理数据  双方都有的数据 才是最终数据
                            // 权益A走了门店包 符合条件  但是权益A不走人群包， 也是需要展示的
                            // 权益A未走门店包， 但是走了人群包  人群包只有匹配才展示  不匹配则不展示
                            // 权益A走了门店包   权益A也走人群包  人群报只有符合条件才展示
                            // 权益A未走门店包   并且也未走人群包  直接展示
                            for (RightsResp finalRight : finalRights) {
                                // 如果 finalRights 的 人群包信息为空的  那么就没有走人群包  直接添加
                                if(StringUtils.isBlank(finalRight.getPeopleCrowdIds())){
                                    matchFinalResult.add(finalRight);
                                }else{
                                    for (RightsResp filterMatchPerson : filterMatchPeoples) {
                                        if(finalRight.getRightsId().equals(filterMatchPerson.getRightsId())){
                                            matchFinalResult.add(finalRight);
                                        }
                                    }
                                }
                            }
                            // 符合门店包的权益  和 符合人群包 的权益 才能展示  任何一个不符合则不展示
                            // 最终数据赋值
                            finalRights = matchFinalResult;
                            log.info("匹配完人群包的最终权益数据id = {}",JSONObject.toJSONString(finalRights.stream().map(r->r.getRightsId())));
                        }

                    }
                }
            }
        }

        return finalRights;
    }


    /**
     * 获取外部符合的
     * @param storeIds
     * @return
     */
    @Override
    public List<Integer> targetStoreIds(String storePackageId, List<Integer> storeIds){
        StoreListContext storeListContext = new StoreListContext();
        storeListContext.setStorePackageId(Integer.valueOf(storePackageId));
        storeListContext.setStoreIdList(storeIds);
        List<Integer> result = iPackageStoresService.storeListById(storeListContext);
        return result;
    }

    public List<StorePacketResp> targetStoreIdByStorePacketIds(String storeId, List<Long> storePackageIds){
        if(CollectionUtils.isEmpty(storePackageIds)){
            return new ArrayList<>();
        }
        String storePackageIdSorted= storePackageIds.stream()
                .sorted(Comparator.comparing(Long::intValue))
                .map(e-> String.valueOf(e)).collect(Collectors.joining(""));

        String keys = RedisKeysEnum.REDIS_STORE_PACKAGE_KEY.join(storeId,storePackageIdSorted);
        if(rightsRedisCacheFlag.equals("1")){
            if(redisService.hasKey(keys)){
                return JSONObject.parseArray(redisService.get(keys).toString(),StorePacketResp.class);
            }
        }
        StorePacketReq storePacketReq = new StorePacketReq();
        storePacketReq.setStorePackageIds(storePackageIds);
        storePacketReq.setStoreId(Long.parseLong(storeId));
        List<StorePacketResp> result = iPackageStoresService.memberBatchHit(storePacketReq);
        if(CollectionUtils.isNotEmpty(result)){
            redisService.set(keys,JSONObject.toJSONString(result),3600*24);
        }
        return result;
    }


    @Override
    public List<ReceiveBirthCouponResp> checkCanReceiveBirthCouponByBrandId(ReceiveBirthCouponReq requestData) {
        List<ReceiveBirthCouponResp> respList = new ArrayList<>();

        MemberQueryContext req = new MemberQueryContext();
        req.setUnionId(requestData.getUnionid());
        req.setStatus("Y");
        CustomerBaseResponse<List<MemberCardEntity>> memberCards = iUserVipService.getMemberCardList(req);
        if(CollectionUtils.isNotEmpty(memberCards.getData())) {
            List<MemberCardEntity> memberCardEntities = new ArrayList<>();
            memberCardEntities.addAll(memberCards.getData());
            List<CheckCanReceiveDto> checkCanReceives = checkCanReceive(memberCardEntities,requestData);
            String jsonString = JSONObject.toJSONString(checkCanReceives);
            return JSONObject.parseArray(jsonString,ReceiveBirthCouponResp.class);
        }
        ReceiveBirthCouponResp couponResp = new ReceiveBirthCouponResp();
        couponResp.setMsg("未查询到用户卡信息");
        respList.add(couponResp);
        return respList;
    }

    @Override
    public void addScanCodeLog(ScanCodeLog requestData) {
        // 不开启缓存则进行插入
        if(!rightsRedisCacheFlag.equals("1")){
            // 插入数据
            requestData.setId(IdLeaf.getId(IdConstant.SCAN_CODE_LOG));
            requestData.setCreateTime(new Date());
            scanCodeLogMapper.insert(requestData);
        }
    }

    private List<MaterialBySourceCodesContext> getEbInfo(List<String> sourceCodes) {

        try {
            GetEbInfoReq getEbInfoReq = new GetEbInfoReq();
            getEbInfoReq.setSourceCodes(sourceCodes);

            log.info("getEbInfo req = {}",JSONObject.toJSONString(sourceCodes));
            Response<CustomerBaseResponse<List<MaterialBySourceCodesContext>>> execute = iJicInfoHttpApi.findMaterialBySourceCodes(getEbInfoReq).execute();
            log.info("getEbInfo resp = {}",JSONObject.toJSONString(execute));
            log.info("getEbInfo resp = {}",JSONObject.toJSONString(execute.body()));
            if(execute.isSuccessful()){
                CustomerBaseResponse<List<MaterialBySourceCodesContext>> body = execute.body();
                if(body.getCode() == 200){
                    return body.getData();
                }
            }
        }catch (Exception e){
            log.info("getEbInfo 发生错误，参数 ： {} ",JSONObject.toJSONString(sourceCodes),e);
        }
        return null;
    }


    public void insertBAddUserRightLog( String unionid,String outNo) {
        List<BNewAddUserRightLog> result = new LinkedList<>();
        //直接转就行了，前太直接传递对象
        BNewAddUserRightLog bAddUserRightLog = new BNewAddUserRightLog();
        bAddUserRightLog.setUpdateTime(new Date());
        bAddUserRightLog.setCreateTime(new Date());
        bAddUserRightLog.setUnionid(unionid);
        bAddUserRightLog.setId(IdLeaf.getId(IdConstant.B_NEW_USER_RIGHTS));
        bAddUserRightLog.setOutNo(outNo);
        result.add(bAddUserRightLog);
        // 批量插入
        bNewAddUserRightLogMapper.batchInsert(result);
    }
}
