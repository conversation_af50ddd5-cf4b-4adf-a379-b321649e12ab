package org.springcenter.marketing.modules.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR> TASK_PART_DETAILS
 * @Date:2023/2/13 20:04
 */
@Data
@TableName(value = "task_part_details")
public class TaskPartDetails {
    /**
     * 主键
     */
    @TableId
    private String id;

    /**
     *
     */
    @TableField(value = "UNIONID")
    private String unionid;

    /**
     * 任务模板id
     */
    @TableField(value = "TASK_TEMP_ID")
    private String taskTempId;

    /**
     * 任务类型0:签到 1:订单评价  2:社区发帖  3:自定义  4:生日 5：公众号 6：导购
     */
    @TableField(value = "TASK_TEMP_TYPE")
    private Integer taskTempType;

    /**
     * 小程序品牌
     */
    @TableField(value = "WEID")
    private String weid;

    /**
     * 昵称
     */
    @TableField(value = "NICKNAME")
    private String nickname;

    /**
     * 手机号
     */
    @TableField(value = "PHONE")
    private String phone;

    /**
     * 会员等级
     */
    @TableField(value = "VIP_LEVEL")
    private Integer vipLevel;

    /**
     * 完成状态 0未完成 1已完成
     */
    @TableField(value = "FINISH_STATUS")
    private Integer finishStatus;

    /**
     * 领取时间
     */
    @TableField(value = "RECIEVE_TIME")
    private Date recieveTime;

    /**
     * 完成次数
     */
    @TableField(value = "COMPLETION_NUM")
    private Integer completionNum;

    /**
     * 审核中的数量
     */
    @TableField(value = "IN_REVIEW_NUM")
    private Integer inReviewNum;

    /**
     * 创建时间
     */
    @TableField(value = "CREATE_TIME")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME")
    private Date updateTime;

    /**
     * 0正常 1已删除
     */
    @TableField(value = "IS_DELETED")
    private Long isDeleted;

    /**
     * 隶属周期开始时间
     */
    @TableField(value = "IN_CYCLE_START_TIME")
    private Date inCycleStartTime;

    /**
     * 隶属周期结束时间
     */
    @TableField(value = "IN_CYCLE_END_TIME")
    private Date inCycleEndTime;

    /**
     * 任务明细编码
     */
    @TableField(value = "TASK_ITEM_NO")
    private String taskItemNo;

    /**
     * 任务模板版本
     */
    @TableField(value = "VERSION")
    private String version;

    /**
     * 用户操作 完成处理时间
     */
    @TableField(value = "DEAL_TIME")
    private Date dealTime;

    /**
     * 任务状态：0任务有效 1任务无效
     */
    @TableField(value = "STATUS")
    private Integer status;

    @TableField(value = "TASK_INFO_ID")
    private String taskInfoId;

    /**
     * 用户任务的状态 0未领取任务 1已领取任务 2统计中[此状态只有消费才有，是已完成但是不能领取] 3待领取奖励 4已领取奖励||已完成 5用户任务状态结束
     */
    @TableField(value = "TASK_STATUS")
    private Integer taskStatus;

    /**
     * 任务状态开始时间
     */
    @TableField(value = "TASK_STATUS_START_TIME")
    private Date taskStatusStartTime;

    /**
     * 任务状态结束时间
     */
    @TableField(value = "TASK_STATUS_END_TIME")
    private Date taskStatusEndTime;
}
