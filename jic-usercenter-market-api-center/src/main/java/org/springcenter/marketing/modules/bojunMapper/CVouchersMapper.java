package org.springcenter.marketing.modules.bojunMapper;


import org.apache.ibatis.annotations.Param;
import org.springcenter.marketing.modules.model.CVouchers;

import java.util.Date;
import java.util.List;

/**
* @Entity com.jnby.infrastructure.box.model.CVouchers
*/
public interface CVouchersMapper {

    int updateValidDateById(@Param("voucherNo") String voucherNo,@Param("validDate") Integer validDate);

    Long selectByVouvherNo(@Param("voucherNo") String voucherNo);
}
