package org.springcenter.marketing.modules.bojunMapper;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springcenter.marketing.api.dto.miniapp.EmployeeBaseDto;
import org.springcenter.marketing.modules.model.HrEmployee;

import java.util.List;

public interface HrEmployeeMapper {

    @Select("select id , handset as phone  from HR_EMPLOYEE where c_store_id = #{storeId} and handset is not null and ISACTIVE = 'Y'")
    List<EmployeeBaseDto> selectByStoreId(@Param("storeId") String storeId);


    int deleteByPrimaryKey(Long id);

    int insert(HrEmployee record);

    int insertSelective(HrEmployee record);

    HrEmployee selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(HrEmployee record);

    int updateByPrimaryKey(HrEmployee record);


    List<HrEmployee> selectListByIds(@Param("ids") List<Long> ids);


    /**
     * 根据手机号查询
     * @param phone
     * @return
     */
    List<HrEmployee> selectListByPhone(@Param("phone") String phone);



    /**
     * 获取有效的ids
     *
     * @param ids
     * @return
     */
    List<Long> activeIds(@Param("ids") List<Long> ids);


    /**
     * 根据cstoreids 查询hrids
     * @return
     */
    List<HrEmployee> selectListByStoreIds(@Param("ids") List<Long> ids, @Param("phone") String phone);
}