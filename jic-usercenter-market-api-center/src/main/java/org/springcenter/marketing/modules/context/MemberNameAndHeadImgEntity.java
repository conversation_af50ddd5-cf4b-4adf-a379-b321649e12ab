package org.springcenter.marketing.modules.context;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@ApiModel("会员头像信息")
@Data
public class MemberNameAndHeadImgEntity {

    @ApiModelProperty("用户昵称")
    private String userName;
    @ApiModelProperty("用户头像")
    private String headImgUrl;
    @ApiModelProperty("卡号")
    private String cardNo;

}
