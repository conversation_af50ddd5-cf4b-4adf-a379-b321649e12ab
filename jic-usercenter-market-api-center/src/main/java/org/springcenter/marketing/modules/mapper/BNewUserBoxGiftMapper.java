package org.springcenter.marketing.modules.mapper;

import org.apache.ibatis.annotations.Param;
import org.springcenter.marketing.api.dto.admin.GetBoxGiftListReq;
import org.springcenter.marketing.modules.model.BNewUserBoxGift;

import java.util.Date;
import java.util.List;

public interface BNewUserBoxGiftMapper {
    int deleteByPrimaryKey(String id);

    int insert(BNewUserBoxGift record);

    int insertSelective(BNewUserBoxGift record);

    BNewUserBoxGift selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(BNewUserBoxGift record);

    int updateByPrimaryKey(BNewUserBoxGift record);

    List<BNewUserBoxGift> selectAvialableUserRightsBoxGiftByUnionId(@Param("unionid") String unionid,
                                                                   @Param("rightType") Integer rightType,
                                                                   @Param("bMemberCardId") String bMemberCardId,
                                                                   @Param("now") Date date,
                                                                   @Param("outNo") String outNo);


    List<BNewUserBoxGift> selectUserRightsBoxGiftByUnionId(@Param("unionid") String unionid,
                                                                    @Param("rightType") Integer rightType,
                                                                    @Param("bMemberCardId") String bMemberCardId,
                                                                    @Param("now") Date date,
                                                                    @Param("outNo") String outNo);


    List<BNewUserBoxGift> selectByConsumeOutNos(@Param("consumeOutNos") List<String> consumeOutNos);

    List<BNewUserBoxGift> selectUnSuccessSendOrSendNotUpdate();

    List<BNewUserBoxGift> syncLogisticsStatus(@Param("id") String id);

    List<BNewUserBoxGift> getBoxGiftList(GetBoxGiftListReq requestData);

    List<BNewUserBoxGift> selectByBoxSns(@Param("boxSns") List<String> boxSns);

    List<BNewUserBoxGift> selectByOutNo(@Param("outNo") String outNo);

    List<BNewUserBoxGift> selectByConsumeOutNosAndStatus(@Param("consumeOutNos") List<String> consumeOutNos);

    void updateEbNumIsNull(BNewUserBoxGift update);

    void updateBySku(@Param("originSku") String originSku, @Param("newSku") String newSku,
                     @Param("sendNum") String sendNum,@Param("newName") String newName ,@Param("arcBrandId") String arcBrandId);
}