package org.springcenter.marketing.modules.model;

import com.alibaba.fastjson.JSON;
//import com.jnby.base.service.rights.ApplyForBoxRights;
//import com.jnby.base.service.rights.Options;
//import com.jnby.common.util.ObjUtils;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class BRights {
    private String id;

    private String name;

    private String outName;

    private String label;

    private String icon;

    private String content;

    private String actionUrl;

    /**
     * 弃用字段
     */
    @Deprecated
    private String bMemberCardId;

    private Long rightsType;

    private Date createTime;

    private Date updateTime;

    private Long status;

    private Long isDel;

    @ApiModelProperty(value = "行动点名称,appid,或者视频号id")
    private String actionName;

    @ApiModelProperty(value = "按钮名称")
    private String buttonName;

    private String useRule;

    private Long sorted;
    private String detailImgs;



    @ApiModelProperty(value = "生效时间   0 开卡即生效  大于 0   发放N天后生效  (正整数是几则发放几天后生效)")
    private Integer effectiveTimeNum;


    @ApiModelProperty(value = "适用商家类型   1    直营可用        2  经销可用      3  均可用      4 自定义商家")
    private Integer suitableStoreType;

    @ApiModelProperty(value = "0  原来自定义  走库    1 新自定义 走门店包")
    private Integer suitCusType;


    @ApiModelProperty(value = "人群包 ids")
    private String peopleCrowdIds;


    @ApiModelProperty(value = "非此表字段")
    @TableField(exist = false)
    private Integer levelId;


    @ApiModelProperty(value = "人群包 ids")
    @TableField(exist = false)
    private Integer usePeopleCrowd;


    public Integer getUsePeopleCrowd() {
        return usePeopleCrowd;
    }

    public void setUsePeopleCrowd(Integer usePeopleCrowd) {
        this.usePeopleCrowd = usePeopleCrowd;
    }

    public String getPeopleCrowdIds() {
        return peopleCrowdIds;
    }

    public void setPeopleCrowdIds(String peopleCrowdIds) {
        this.peopleCrowdIds = peopleCrowdIds;
    }

    public Integer getSuitCusType() {
        return suitCusType;
    }

    public void setSuitCusType(Integer suitCusType) {
        this.suitCusType = suitCusType;
    }

    public String getButtonName() {
        return buttonName;
    }

    public void setButtonName(String buttonName) {
        this.buttonName = buttonName;
    }

    public Integer getCardLevel() {
        return cardLevel;
    }

    public void setCardLevel(Integer cardLevel) {
        this.cardLevel = cardLevel;
    }

    @ApiModelProperty(value = "非此表字段 卡等级")
    private Integer cardLevel;

    private String birthGif;


    private Integer isOnceForAll;


    @ApiModelProperty(value = "升级文案")
    private String upgradeContent;

    @ApiModelProperty(value = "周期   0 无周期  1 每天  2 每周  3 每月  4 每年")
    private Integer cycleType;




    @ApiModelProperty(value = "权益价值")
    private BigDecimal rightsValue;


    @ApiModelProperty(value = "box+页权益图")
    private String rightsImg;


    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @ApiModelProperty(value = "额外扩展信息")
    private String extendJson;


    public String getExtendJson() {
        return extendJson;
    }

    public void setExtendJson(String extendJson) {
        this.extendJson = extendJson;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public BigDecimal getRightsValue() {
        return rightsValue;
    }

    public void setRightsValue(BigDecimal rightsValue) {
        this.rightsValue = rightsValue;
    }


    public String getRightsImg() {
        return rightsImg;
    }

    public void setRightsImg(String rightsImg) {
        this.rightsImg = rightsImg;
    }


    @ApiModelProperty(value = " 跳转链接类型   0  H5   1 小程序  2 视频号 ")
    private Integer actionType;

    public Integer getActionType() {
        return actionType;
    }

    public void setActionType(Integer actionType) {
        this.actionType = actionType;
    }

    public Integer getIsOnceForAll() {
        return isOnceForAll;
    }

    public void setIsOnceForAll(Integer isOnceForAll) {
        this.isOnceForAll = isOnceForAll;
    }

    public String getUpgradeContent() {
        return upgradeContent;
    }

    public void setUpgradeContent(String upgradeContent) {
        this.upgradeContent = upgradeContent;
    }

    public Integer getCycleType() {
        return cycleType;
    }

    public void setCycleType(Integer cycleType) {
        this.cycleType = cycleType;
    }

    public String getBirthGif() {
        return birthGif;
    }

    public void setBirthGif(String birthGif) {
        this.birthGif = birthGif;
    }

    public Integer getLevelId() {
        return levelId;
    }

    public void setLevelId(Integer levelId) {
        this.levelId = levelId;
    }

    public Integer getEffectiveTimeNum() {
        return effectiveTimeNum;
    }

    public void setEffectiveTimeNum(Integer effectiveTimeNum) {
        this.effectiveTimeNum = effectiveTimeNum;
    }

    public Integer getSuitableStoreType() {
        return suitableStoreType;
    }

    public void setSuitableStoreType(Integer suitableStoreType) {
        this.suitableStoreType = suitableStoreType;
    }


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public String getOutName() {
        return outName;
    }

    public void setOutName(String outName) {
        this.outName = outName;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label == null ? null : label.trim();
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon == null ? null : icon.trim();
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content == null ? null : content.trim();
    }

    public String getActionUrl() {
        return actionUrl;
    }

    public void setActionUrl(String actionUrl) {
        this.actionUrl = actionUrl == null ? null : actionUrl.trim();
    }

    public String getbMemberCardId() {
        return bMemberCardId;
    }

    public void setbMemberCardId(String bMemberCardId) {
        this.bMemberCardId = bMemberCardId == null ? null : bMemberCardId.trim();
    }

    public Long getRightsType() {
        return rightsType;
    }

    public void setRightsType(Long rightsType) {
        this.rightsType = rightsType;
    }

    public Long getStatus() {
        return status;
    }

    public void setStatus(Long status) {
        this.status = status;
    }

    public Long getIsDel() {
        return isDel;
    }

    public void setIsDel(Long isDel) {
        this.isDel = isDel;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }



    public String getActionName() {
        return actionName;
    }

    public void setActionName(String actionName) {
        this.actionName = actionName == null ? null : actionName.trim();
    }

    public String getUseRule() {
        return useRule;
    }

    public void setUseRule(String useRule) {
        this.useRule = useRule == null ? null : useRule.trim();
    }

    public Long getSorted() {
        return sorted;
    }

    public void setSorted(Long sorted) {
        this.sorted = sorted;
    }

    public String getDetailImgs() {
        return detailImgs;
    }

    public void setDetailImgs(String detailImgs) {
        this.detailImgs = detailImgs;
    }

//    public ApplyForBoxRights getApplyForBoxRights(){
//        if(this.useRule == null || "".equals(this.useRule)){
//            return new ApplyForBoxRights();
//        }
//        Map<String, Object> objAttr = new HashMap<>();
//        List<Options> optionsList = JSON.parseArray(this.useRule, Options.class);
//        optionsList.forEach(option -> {
//            objAttr.put(option.getKey(), option.getValue());
//        });
//        ApplyForBoxRights applyForBoxRights = ObjUtils.map2Object(objAttr, ApplyForBoxRights.class);
//        return applyForBoxRights;
//    }
}
