<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.marketing.modules.bojunMapper.CallBojunBaseMapper">
  <resultMap id="BaseResultMap" type="org.springcenter.marketing.api.dto.CallTaskDetailResp">
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="COMMON_INFO" jdbcType="VARCHAR" property="commonInfo" />
    <result column="CALL_PHONE_NAME" jdbcType="VARCHAR" property="callPhoneName" />
    <result column="PHONE" jdbcType="VARCHAR" property="phone" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="IS_DEL" jdbcType="DECIMAL" property="isDel" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="STATUS" jdbcType="VARCHAR" property="status" />
    <result column="CALL_DETAIL_ID" jdbcType="VARCHAR" property="callDetailId" />
    <result column="CALL_PARAMS" jdbcType="VARCHAR" property="callParams" />
  </resultMap>

  <select id="selectByResultSql" resultMap="BaseResultMap">
    ${sql}
  </select>

</mapper>