package org.springcenter.marketing.modules.integration.cache;

import org.springcenter.marketing.modules.entity.CommentEntity;

/**
 * <AUTHOR>
 *
 * 社区评论 缓存层
 */
public interface IPeopleCommunityCommentCacheService {

    /**
     * 获取社区评论
     * @param unionId
     * @param brandId
     * @return
     */
    CommentEntity getCommentList(String unionId, String brandId);


    /**
     * 清除社区评论的缓存
     * @param unionId
     * @param brandId
     * @return
     */
    void delCommentList(String unionId, String brandId);

    /**
     * 社区点击
     * @param unionId
     * @param brandId
     * @return
     */
    Boolean clickComment(String unionId, String brandId);
}
