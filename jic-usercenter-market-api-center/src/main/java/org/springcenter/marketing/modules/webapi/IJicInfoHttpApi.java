package org.springcenter.marketing.modules.webapi;

import org.springcenter.marketing.api.checkRightsDto.CheckBoxGiftRightsDto;
import org.springcenter.marketing.api.context.GetEbInfoReq;
import org.springcenter.marketing.api.context.SendMaterialBoxGiftContext;
import org.springcenter.marketing.api.dto.CustomerBaseResponse;
import org.springcenter.marketing.api.dto.GetWeimoMemberInfo;
import org.springcenter.marketing.api.dto.GetWeimoMemberIsOpenCardReq;
import org.springcenter.marketing.modules.context.AddIntegralContext;
import org.springcenter.marketing.modules.context.AddIntegralEntity;
import org.springcenter.marketing.modules.context.MaterialBySourceCodesContext;
import org.springcenter.marketing.modules.context.PeoplePagEntity;
import org.springcenter.marketing.modules.entity.ChangeEntryEntitu;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.POST;

import java.util.List;
import java.util.concurrent.Executor;

/**
 * <AUTHOR>
 * @Date:2023/2/17 10:07
 */
public interface IJicInfoHttpApi {

    @POST("/sdk/integral-center/integral/add")
    Call<CustomerBaseResponse<AddIntegralEntity>> addIntegral(@Body AddIntegralContext addIntegralContext);



    @POST("/wxmall/wxmall-center/weimo/member/detail")
    Call<CustomerBaseResponse<GetWeimoMemberInfo>> findWeimoMemberInfo(@Body GetWeimoMemberIsOpenCardReq getWeimoMemberIsOpenCardReq);


    /**
     * 发送实物礼快递
     * @param
     * @return
     */
    @POST("/sdk/order-center/order/create")
    Call<CustomerBaseResponse<SendMaterialBoxGiftContext>> sendMaterialBoxGift(@Body CheckBoxGiftRightsDto.SendMaterialDto sendMaterialDto);

    /**
     * 根据sourceCodes 批量查询
     * @param sourceCodes
     * @return
     */
    @POST("/sdk/order-center/ebso/order/list/info")
    Call<CustomerBaseResponse<List<MaterialBySourceCodesContext>>> findMaterialBySourceCodes(@Body GetEbInfoReq getEbInfoReq);




    @POST("/mq/member-mq/member/changeEntry")
    Call<CustomerBaseResponse<Boolean>> changeEntry(@Body ChangeEntryEntitu changeEntryEntitu);



}
