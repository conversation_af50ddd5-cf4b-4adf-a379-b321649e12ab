<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.marketing.modules.bojunMapper.MRetailMapper">


    <select id="sumConsume" resultType="java.math.BigDecimal">
        select sum(t.tot_amt_actual) from m_retail t where t.isactive = 'Y' and t.is_virtual = 'N'
                                                       and t.c_vip_id=#{vipId} and t.billdate between #{startDate} and #{endDate}
                                                       and nvl(storeid,'线下门店') in ('微商城','线下门店','BOX','内淘宝','移动')
                                                       and t.status=2
    </select>
    <select id="selectMretailByVipIdAndOrderType"
            resultType="org.springcenter.marketing.api.dto.miniapp.FindRecordMsgByTemplateIdResp">
        select docno as bizId, orderType as recordMsg from     m_retail
        where C_VIP_ID = #{vipId} and isactive = 'Y' and ordertype in ('THLS','HHLS')  and status=2
          and statustime  >= #{createDate} order by statustime  desc
    </select>
    <select id="selectMretailByVipIdAndDdlys"
            resultType="org.springcenter.marketing.api.dto.miniapp.FindRecordMsgByTemplateIdResp">
        select docno as bizId, orderType as recordMsg from  m_retail
        where C_VIP_ID = #{vipId} and isactive = 'Y' and status=2 and ddly in
        <foreach collection="ddlys" item="item" separator="," close=")" open="(">
                #{item}
        </foreach>
         and statustime >= #{createDate}
        order by statustime desc
    </select>



    <select id="selectByVipIdAndTypeAndChannel"
            resultType="org.springcenter.marketing.modules.model.MRetailInfoEntity">
        select ID, tot_amt_actual as totAmountActual, storeid as storeId, EB_SO_ID as ebSoId, CREATIONDATE as creationDate,
                docno as docNo
        from m_retail t where t.isactive = 'Y' and t.is_virtual = 'N'
                          and t.c_vip_id=#{vipId}
                          and nvl(t.ddly, '0') not in ('JFHG','JFDH','SNWX')
                          and t.status=2
                          <if test="list != null and list.size > 0">
                              and nvl(storeid,'线下门店') in
                              <foreach collection="list" item="item" separator="," close=")" open="(">
                                  #{item}
                              </foreach>
                          </if>
    </select>
    <select id="selectOrderIdsForScan" resultType="java.lang.Long">
        SELECT id FROM M_RETAIL a
        WHERE a.billdate &gt;= #{billDate}
          AND CREATIONDATE BETWEEN to_date(#{creationStart}, 'yyyy-mm-dd hh24:mi:ss') AND to_date(#{creationEnd}, 'yyyy-mm-dd hh24:mi:ss')
          AND a.status = 2
          AND a.isactive = 'Y'
          AND a.is_virtual = 'N'
          -- 退款单的总金额小于0
--           AND a.tot_amt_actual &gt; 0
          AND nvl(a.description, '-1') not like '%冲单%'
          AND nvl(a.description, '-1') not like '%拆单%'
          AND (a.STOREID IN ('BOX','微商城','移动','内淘宝') OR a.STOREID IS NULL OR a.STOREID = '')
          AND a.ORDERTYPE IN ('ZCLS','THLS','HHLS')
        ORDER BY CREATIONDATE ASC
    </select>

    <parameterMap type="java.util.Map" id="orderRefundMap">
        <parameter property="IN_JSON" mode="IN" jdbcType="CLOB"/>
        <parameter property="ORDERID" mode="OUT" jdbcType="VARCHAR"/>
        <parameter property="RET" mode="OUT" jdbcType="INTEGER"/>
        <parameter property="ERRMSG" mode="OUT" jdbcType="VARCHAR"/>
    </parameterMap>


    <select id="callCreateRetailRefundOrder" statementType="CALLABLE" parameterMap="orderRefundMap">
        <![CDATA[
        CALL PORTAL_RETAIL_REFUND_2022(?,?,?,?)
        ]]>
    </select>
    <select id="selectIdByDocNo" resultType="java.lang.Long">
        select id from  M_RETAIL where docno = #{docNo}
    </select>
</mapper>