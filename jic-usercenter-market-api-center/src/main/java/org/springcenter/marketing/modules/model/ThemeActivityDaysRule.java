package org.springcenter.marketing.modules.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 活动天数规则
 * @TableName theme_activity_days_rule
 */
@Data
@TableName(value ="theme_activity_days_rule")
public class ThemeActivityDaysRule implements Serializable {
    /**
     * 主键
     */
    @TableId
    private Integer id;

    /**
     * 活动主键id
     */
    @TableField(value = "theme_activity_id")
    private Integer themeActivityId;

    /**
     * 签到天数
     */
    @TableField(value = "sign_in_days")
    private Integer signInDays;

    /**
     * 排序  从小到大
     */
    @TableField(value = "sort_num")
    private Integer sortNum;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 0 正常  1 删除
     */
    @TableField(value = "is_del")
    private Integer isDel;


    /**
     * 奖品id信息
     */
    @TableField(value = "award_config_ids")
    private String awardConfigIds;


    @TableField(value = "award_title")
    private String awardTitle;

    @TableField(value = "award_subtitle")
    private String awardSubtitle;

}