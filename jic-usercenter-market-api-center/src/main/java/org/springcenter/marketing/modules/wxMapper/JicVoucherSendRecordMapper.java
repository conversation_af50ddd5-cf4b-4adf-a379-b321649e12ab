package org.springcenter.marketing.modules.wxMapper;


import org.apache.ibatis.annotations.Param;
import org.springcenter.marketing.modules.model.JicVoucherSendRecord;

import java.util.List;

public interface JicVoucherSendRecordMapper {
    int deleteByPrimaryKey(Long id);

    int insert(JicVoucherSendRecord record);

    int insertSelective(JicVoucherSendRecord record);

    JicVoucherSendRecord selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(JicVoucherSendRecord record);

    int updateByPrimaryKey(JicVoucherSendRecord record);

    List<JicVoucherSendRecord> selectByReferOrder(@Param("referOrder") String referOrder);
}