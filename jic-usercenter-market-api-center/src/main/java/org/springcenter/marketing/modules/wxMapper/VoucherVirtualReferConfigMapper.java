package org.springcenter.marketing.modules.wxMapper;


import org.apache.ibatis.annotations.Param;
import org.springcenter.marketing.api.dto.miniapp.EmployeeBaseDto;
import org.springcenter.marketing.modules.model.VoucherVirtualReferConfig;
import org.springcenter.marketing.modules.model.WxFans;

import java.math.BigDecimal;
import java.util.List;

public interface VoucherVirtualReferConfigMapper {

    int insert(VoucherVirtualReferConfig record);

    int insertSelective(VoucherVirtualReferConfig record);


    VoucherVirtualReferConfig selectById(@Param("awardid") BigDecimal awardid);

    void updateById(VoucherVirtualReferConfig voucherVirtualReferConfig);


    List<WxFans> selectJicWxfansByUniondIdAndWeId(@Param("unionid") String unionid, @Param("weid") String weid);


}