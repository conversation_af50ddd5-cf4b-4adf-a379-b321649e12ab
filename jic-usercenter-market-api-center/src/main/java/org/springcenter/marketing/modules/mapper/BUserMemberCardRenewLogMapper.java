package org.springcenter.marketing.modules.mapper;

import org.apache.ibatis.annotations.Param;
import org.springcenter.marketing.modules.model.BUserMemberCardRenewLog;

public interface BUserMemberCardRenewLogMapper {
    int deleteByPrimaryKey(String id);

    int insert(BUserMemberCardRenewLog record);

    int insertSelective(BUserMemberCardRenewLog record);

    BUserMemberCardRenewLog selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(BUserMemberCardRenewLog record);

    int updateByPrimaryKey(BUserMemberCardRenewLog record);

    BUserMemberCardRenewLog selectByOrderId(@Param("orderId") String orderNo);
}