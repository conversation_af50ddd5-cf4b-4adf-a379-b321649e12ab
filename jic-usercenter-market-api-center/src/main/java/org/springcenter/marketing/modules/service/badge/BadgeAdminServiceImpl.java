package org.springcenter.marketing.modules.service.badge;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Preconditions;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springcenter.marketing.api.dto.admin.*;
import org.springcenter.marketing.api.enums.BadgeActivityStatusEnum;
import org.springcenter.marketing.api.enums.BadgeOptTypeEnum;
import org.springcenter.marketing.config.IdConfig;
import org.springcenter.marketing.modules.convert.BadgeConvert;
import org.springcenter.marketing.modules.domain.badge.repository.BadgeRepository;
import org.springcenter.marketing.modules.mapper.BadgeDetailMapper;
import org.springcenter.marketing.modules.mapper.BadgeGainRecordLogMapper;
import org.springcenter.marketing.modules.mapper.BadgeThemeMapper;
import org.springcenter.marketing.modules.model.BadgeDetail;
import org.springcenter.marketing.modules.model.BadgeGainRecordLog;
import org.springcenter.marketing.modules.model.BadgeTheme;
import org.springcenter.marketing.modules.model.Cardmain;
import org.springcenter.marketing.modules.service.badge.bo.LogBO;
import org.springcenter.marketing.modules.util.DateUtils;
import org.springcenter.marketing.modules.wxMapper.CardmainMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 主题勋章
 */
@Slf4j
@Service
public class BadgeAdminServiceImpl implements IBadgeAdminService {

    @Autowired
    @Qualifier("marketingTransactionTemplate")
    private TransactionTemplate template;

    @Resource
    private BadgeThemeMapper badgeThemeMapper;

    @Resource
    private BadgeDetailMapper badgeDetailMapper;

    @Resource
    private BadgeGainRecordLogMapper badgeGainRecordLogMapper;

    @Resource
    private CardmainMapper cardmainMapper;

    @Resource
    private IBadgeOptLogService logService;

    @Resource
    private BadgeRepository badgeRepository;
    @Override
    public void saveOrUpdateTheme(BadgeThemeUpdateReq req) {
        BadgeTheme theme = BadgeConvert.INSTANCE.themeReq2domain(req);
        // 刷缓存
        badgeRepository.flushAllBadgesCache();
        if (req.getId() == null) {
            theme.setBizId(IdConfig.getBadgeThemeId());
            theme.setCreateTime(new Date());
            theme.setUpdateTime(new Date());
            theme.setIsDelete(false);
            template.execute(action->{
                badgeThemeMapper.insert(theme);
                logService.saveBadgeLog(theme.getBizId(), BadgeOptTypeEnum.THEME_CREATE, req.getOptPerson(), null, new LogBO(theme));
                return true;
            });
        } else {
            Preconditions.checkArgument(theme.getId() != null, "id不能为空");
            BadgeTheme dbTheme = badgeThemeMapper.selectById(theme.getId());
            Assert.notNull(dbTheme, "主题不存在");
            Assert.isTrue(!dbTheme.getIsDelete(), "主题已删除");
            theme.setUpdateTime(new Date());
            template.execute(action->{
                badgeThemeMapper.updateById(theme);
                logService.saveBadgeLog(dbTheme.getBizId(), BadgeOptTypeEnum.THEME_UPDATE, req.getOptPerson(), new LogBO(dbTheme), new LogBO(theme));
                return true;
            });
        }
    }

    @Override
    public void deleteTheme(BadgeDeleteReq req) {
        Preconditions.checkArgument(req.getId() != null, "id不能为空");

        BadgeTheme dbTheme = badgeThemeMapper.selectById(req.getId());
            Assert.notNull(dbTheme, "主题不存在");
            Assert.isTrue(!dbTheme.getIsDelete(), "主题已删除");

        // 检查是否存在未删除的勋章
        LambdaQueryWrapper<BadgeDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BadgeDetail::getBadgeThemeId, req.getId())
                .eq(BadgeDetail::getIsDelete, false);
        long count = badgeDetailMapper.selectCount(wrapper);
        Assert.isTrue(count == 0, "该主题下存在未删除的勋章，不能删除");
        // 刷缓存
        badgeRepository.flushAllBadgesCache();
        // 执行删除
        BadgeTheme theme = new BadgeTheme();
        theme.setId(req.getId());
        theme.setIsDelete(true);
        theme.setUpdateTime(new Date());
        template.execute(action->{
            badgeThemeMapper.updateById(theme);
            logService.saveBadgeLog(dbTheme.getBizId(), BadgeOptTypeEnum.THEME_DELETE, req.getOptPerson(), new LogBO(dbTheme), new LogBO(theme));
            return true;
        });
    }
    
    @Override
    public BadgeThemeResp queryThemeById(Integer id) {
        BadgeTheme theme = badgeThemeMapper.selectById(id);
        Assert.notNull(theme, "主题不存在");
        Assert.isTrue(!theme.getIsDelete(), "主题已删除");
        return BadgeConvert.INSTANCE.themeDomain2Resp(theme);
    }

    @Override
    public void saveOrUpdateDetail(BadgeDetailUpdateReq req) {
        // 刷缓存
        badgeRepository.flushAllBadgesCache();
        if (req.getId() == null) {
            // 校验主题是否存在
            BadgeTheme theme = badgeThemeMapper.selectById(req.getBadgeThemeId());
            Assert.notNull(theme, "主题不存在");
            Assert.isTrue(!theme.getIsDelete(), "主题已删除");
            BadgeDetail detail = BadgeConvert.INSTANCE.detailReq2Domain(req);
            detail.setBizId(IdConfig.getBadgeDetailId());
            detail.setThemeBizId(theme.getBizId());
            detail.setCreateTime(new Date());
            detail.setUpdateTime(new Date());
            detail.setIsDelete(false);
            log.info("保存勋章: {}", JSON.toJSONString(detail));
            template.execute(action->{
                badgeDetailMapper.insert(detail);
                logService.saveBadgeLog(detail.getBizId(), BadgeOptTypeEnum.BADGE_CREATE, req.getCreatePerson(), null, new LogBO(detail));
                return true;
            });
        } else {
            Preconditions.checkArgument(req.getId() != null, "id不能为空");
            BadgeDetail dbDetail = badgeDetailMapper.selectById(req.getId());
            Assert.notNull(dbDetail, "勋章不存在");
            Assert.isTrue(!dbDetail.getIsDelete(), "勋章已删除,无法处理");
            BadgeDetail detail = BadgeConvert.INSTANCE.detailReq2Domain(req);
            detail.setCreatePerson(null);
            detail.setUpdateTime(new Date());
            log.info("编辑勋章: {}", JSON.toJSONString(detail));
            template.execute(action->{
                badgeDetailMapper.updateById(detail);
                logService.saveBadgeLog(dbDetail.getBizId(), BadgeOptTypeEnum.BADGE_UPDATE, req.getCreatePerson(), new LogBO(dbDetail), new LogBO(detail));
                return true;
            });
        }
    }

    @Override
    public void deleteDetail(BadgeDeleteReq req) {
        Preconditions.checkArgument(req.getId() != null, "id不能为空");
        // 先查询勋章详情，判断勋章是否未开始，如果已开始则不可以删除。
        BadgeDetail badgeDetail = badgeDetailMapper.selectById(req.getId());
        Assert.notNull(badgeDetail, "勋章不存在");
        Assert.isTrue(!badgeDetail.getIsDelete(), "勋章已删除");
        Assert.isTrue(new Date().before(badgeDetail.getStartTime()), "勋章已开始，不能删除");
        // 刷缓存
        badgeRepository.flushAllBadgesCache();
        BadgeDetail detail = new BadgeDetail();
        detail.setId(req.getId());
        detail.setIsDelete(true);
        detail.setUpdateTime(new Date());
        log.info("删除勋章: {}", JSON.toJSONString(detail));
        template.execute(action->{
            badgeDetailMapper.updateById(detail);
            logService.saveBadgeLog(badgeDetail.getBizId(), BadgeOptTypeEnum.BADGE_DELETE, req.getOptPerson(), new LogBO(badgeDetail), new LogBO(detail));
            return true;
        });
    }

    @Override
    public BadgeDetailResp queryDetailById(Integer id) {
        BadgeDetail badgeDetail = badgeDetailMapper.selectById(id);
        Assert.notNull(badgeDetail, "勋章不存在");
        Assert.isTrue(!badgeDetail.getIsDelete(), "勋章已删除");
        BadgeDetailResp detail = BadgeConvert.INSTANCE.detailDomain2DetailResp(badgeDetail);
        if (detail != null) {
            // 设置勋章状态
            Date now = new Date();
            if (now.before(DateUtils.parseByDateTimePattern(detail.getStartTime()))) {
                detail.setActivityStatus(BadgeActivityStatusEnum.NOT_START.getCode());
                detail.setActivityStatusName(BadgeActivityStatusEnum.NOT_START.getMsg());
            } else if (now.after(DateUtils.parseByDateTimePattern(detail.getEndTime()))) {
                detail.setActivityStatus(BadgeActivityStatusEnum.ENDING.getCode());
                detail.setActivityStatusName(BadgeActivityStatusEnum.ENDING.getMsg());
            } else {
                detail.setActivityStatus(BadgeActivityStatusEnum.RUNNING.getCode());
                detail.setActivityStatusName(BadgeActivityStatusEnum.RUNNING.getMsg());
            }
        }
        return detail;
    }

    @Override
    public List<BadgeListQueryResp> queryBadgeList(BadgeListQueryReq req) {
        List<BadgeListQueryResp> respList = Lists.newArrayList();
        // 1. 查询主题
        LambdaQueryWrapper<BadgeTheme> themeWrapper = new LambdaQueryWrapper<>();
        themeWrapper.eq(BadgeTheme::getIsDelete, false);
        themeWrapper.eq(req.getThemeId() != null, BadgeTheme::getId, req.getThemeId());
        themeWrapper.like(StringUtils.isNotBlank(req.getThemeName()), BadgeTheme::getName, req.getThemeName());
        List<BadgeTheme> themeList = badgeThemeMapper.selectList(themeWrapper);
        log.info("当前条件获取到的主题 {}", JSON.toJSONString(themeList));
        if (themeList.isEmpty()) {
            return respList;
        }
        // 2. 查询勋章
        LambdaQueryWrapper<BadgeDetail> detailWrapper = new LambdaQueryWrapper<>();
        detailWrapper.eq(BadgeDetail::getIsDelete, false)
                .in(BadgeDetail::getBadgeThemeId, themeList.stream().map(BadgeTheme::getId).collect(Collectors.toList()))
                .eq(req.getDetailId() != null, BadgeDetail::getBizId, req.getDetailId())
                .like(StringUtils.isNotBlank(req.getDetailName()), BadgeDetail::getName, req.getDetailName())
                .eq(StringUtils.isNotBlank(req.getCreatePerson()), BadgeDetail::getCreatePerson, req.getCreatePerson());
        BadgeActivityStatusEnum statusEnum = BadgeActivityStatusEnum.getByCode(req.getDetailActivityStatus());
        switch (statusEnum) {
            case NOT_START:
                detailWrapper.gt(BadgeDetail::getStartTime, new Date());
                break;
            case RUNNING:
                detailWrapper.le(BadgeDetail::getStartTime, new Date())
                        .ge(BadgeDetail::getEndTime, new Date());
                break;
            case ENDING:
                detailWrapper.lt(BadgeDetail::getEndTime, new Date());
                break;
            case ALL:
                break;
        }
        List<BadgeDetail> detailList = badgeDetailMapper.selectList(detailWrapper);
        log.info("当前条件获取到的勋章 {}", JSON.toJSONString(detailList));
        // 根据主题id分组
        Map<Integer, List<BadgeDetail>> detailMap = detailList.stream().collect(Collectors.groupingBy(BadgeDetail::getBadgeThemeId));

        // 3. 组装返回结果
        themeList.forEach(theme -> {
            BadgeListQueryResp rsp = BadgeConvert.INSTANCE.listTheme2Resp(theme);
            List<BadgeDetailResp> detailRespList = Optional.ofNullable(detailMap.get(theme.getId())).orElse(Lists.newArrayList()).stream()
                    .map(BadgeConvert.INSTANCE::detailDomain2DetailResp).collect(Collectors.toList());
            if (!detailRespList.isEmpty()) {
                detailRespList.forEach(detail -> {
                    // 设置勋章状态
                    Date now = new Date();
                    if (now.before(DateUtils.parseByDateTimePattern(detail.getStartTime()))) {
                        detail.setActivityStatus(BadgeActivityStatusEnum.NOT_START.getCode());
                        detail.setActivityStatusName(BadgeActivityStatusEnum.NOT_START.getMsg());
                    } else if (now.after(DateUtils.parseByDateTimePattern(detail.getEndTime()))) {
                        detail.setActivityStatus(BadgeActivityStatusEnum.ENDING.getCode());
                        detail.setActivityStatusName(BadgeActivityStatusEnum.ENDING.getMsg());
                    } else {
                        detail.setActivityStatus(BadgeActivityStatusEnum.RUNNING.getCode());
                        detail.setActivityStatusName(BadgeActivityStatusEnum.RUNNING.getMsg());
                    }
                });
            }
            rsp.setBadgeDetailRespList(detailRespList);
            respList.add(rsp);
        });

        return respList;
    }

    @Override
    public List<BadgeGainRecordQueryResp> queryBadgeGainRecords(BadgeGainRecordQueryReq req, com.jnby.common.Page page) {
        LambdaQueryWrapper<BadgeGainRecordLog> queryWrapper = new LambdaQueryWrapper<>();
        // 必传条件
        queryWrapper.eq(BadgeGainRecordLog::getIsDelete, false);
        queryWrapper.in(BadgeGainRecordLog::getBadgeBizId, req.getBizIdList());
        queryWrapper.ge(StringUtils.isNotBlank(req.getCreateTimeStart()), BadgeGainRecordLog::getCreateTime, req.getCreateTimeStart());
        queryWrapper.le(StringUtils.isNotBlank(req.getCreateTimeEnd()), BadgeGainRecordLog::getCreateTime, req.getCreateTimeEnd());
        // 选填条件
        queryWrapper.eq(StringUtils.isNotBlank(req.getDocNo()), BadgeGainRecordLog::getDocNo, req.getDocNo());
        queryWrapper.like(StringUtils.isNotBlank(req.getBadgeName()), BadgeGainRecordLog::getBadgeName, req.getBadgeName());
        queryWrapper.eq(req.getStatus() != null, BadgeGainRecordLog::getStatus, req.getStatus());
        queryWrapper.orderByDesc(BadgeGainRecordLog::getId);

        // 如果是用户信息查询，先走cardmain表查询，获取到unionId，再走badge_gain_record_log表查询
        if (StringUtils.isNotBlank(req.getGroupCardNo()) || StringUtils.isNotBlank(req.getBrandCardNo()) || StringUtils.isNotBlank(req.getPhoneNumber())) {
            log.info("当前通过顾客信息查询 集团卡号[{}], 品牌卡号[{}], 手机号[{}]", req.getGroupCardNo(), req.getBrandCardNo(), req.getPhoneNumber());
            Cardmain cardmain = cardmainMapper.selectByConditionLimit1(req.getBrandCardNo(), req.getGroupCardNo(), req.getPhoneNumber());
            if (cardmain == null || StringUtils.isBlank(cardmain.getUnionid())) {
                log.info("当前通过顾客信息查询 未获取到用户unionId信息，无法查询");
                return Lists.newArrayList();
            }
            queryWrapper.eq(BadgeGainRecordLog::getUnionId, cardmain.getUnionid());
        }

        // 执行分页查询
        com.github.pagehelper.Page<BadgeGainRecordLog> hPage = PageHelper.startPage(page.getPageNo(),
                page.getPageSize());
        badgeGainRecordLogMapper.selectList(queryWrapper);
        PageInfo<BadgeGainRecordLog> pageInfo = new PageInfo(hPage);
        page.setCount(pageInfo.getTotal());
        page.setPages(pageInfo.getPages());
        page.setPageSize(pageInfo.getPageSize());
        List<BadgeGainRecordLog> logs = pageInfo.getList();
        return Optional.ofNullable(logs).orElse(Lists.newArrayList()).stream().map(BadgeConvert.INSTANCE::log2BadgeGainRecordQueryResp).collect(Collectors.toList());
    }
}
