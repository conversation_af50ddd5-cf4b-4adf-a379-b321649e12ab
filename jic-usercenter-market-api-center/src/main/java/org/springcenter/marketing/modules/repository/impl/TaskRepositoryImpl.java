package org.springcenter.marketing.modules.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.Query;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jnby.common.util.IdLeaf;
import org.apache.commons.collections.MapUtils;
import org.springcenter.marketing.api.dto.*;
import org.springcenter.marketing.api.enums.TaskTemplateStatusEnum;
import org.springcenter.marketing.api.enums.TaskTemplateTypeEnum;
import org.springcenter.marketing.modules.context.PeoplePagEntity;
import org.springcenter.marketing.modules.mapper.*;
import org.springcenter.marketing.modules.model.*;
import org.springcenter.marketing.modules.repository.ITaskRepository;
import org.springcenter.marketing.modules.service.*;
import org.springcenter.marketing.modules.wxMapper.StoreReservationOrderMapper;
import org.springcenter.product.api.enums.IsDeleteEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.StopWatch;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date:2023/2/9 10:15
 */
@Slf4j
@Repository
public class TaskRepositoryImpl implements ITaskRepository {
    private static final String TASK_TEMP_ID = "TASK_TEMP_ID";

    @Autowired
    private ITaskPartDetailsService iTaskPartDetailsService;

    @Autowired
    private TaskTemplateOperatorMapper taskTemplateOperatorMapper;

    @Autowired
    private TaskTemplateMapper taskTemplateMapper;

    @Autowired
    private TaskTemplateLogMapper taskTemplateLogMapper;

    @Autowired
    private TaskTemplateLimitationMapper taskTemplateLimitationMapper;

    @Autowired
    private TaskTemplateOutInfoMapper taskTemplateOutInfoMapper;

    @Autowired
    private TaskTemplateRewardInfoMapper taskTemplateRewardInfoMapper;

    @Autowired
    private TaskTemplateExecutionMapper taskTemplateExecutionMapper;

    private static final String prefix = "T";

    private static final String prefixOfPart = "P";

    private static final Integer threshold = 0;

    @Autowired
    private TaskPartDetailsMapper taskPartDetailsMapper;

    @Autowired
    private TaskPartDetailsItemMapper taskPartDetailsItemMapper;

    @Autowired
    private TaskTemplatePopInfoMapper taskTemplatePopInfoMapper;

    @Autowired
    private ITaskTemplateExecutionService iTaskTemplateExecutionService;

    @Autowired
    private ITaskTemplateLimitationService iTaskTemplateLimitationService;

    @Autowired
    private ITaskTemplateLogService iTaskTemplateLogService;

    @Autowired
    private ITaskTemplateService itaskTemplateService;

    @Autowired
    private ITaskTemplateOperatorService iTaskTemplateOperatorService;

    @Autowired
    private ITaskTemplateOutInfoService iTaskTemplatePopInfoService;

    @Autowired
    private ITaskTemplateRewardInfoService iTaskTemplateRewardInfoService;

    @Autowired
    private StoreReservationOrderMapper storeReservationOrderMapper;

    @Override
    public List<TaskTemplateOperator> queryTaskOperators(TaskOperatorsReq requestData) {
        QueryWrapper<TaskTemplateOperator> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("TASK_TEMP_ID", requestData.getTaskId());
        queryWrapper.orderByDesc("CREATE_TIME");
        return taskTemplateOperatorMapper.selectList(queryWrapper);
    }

    @Override
    public List<TaskTemplateInfo> querySameTypeUnFinishTask(Integer taskType, String weId) {
        return taskTemplateMapper.querySameTypeUnFinishTask(taskType, weId);
    }

    @Override
    public String getTaskTemplateNo() {
        String seq = IdLeaf.getId(TASK_TEMP_ID);
        String taskNo = prefix + String.format("%07d", Long.valueOf(seq));
        return taskNo;
    }

    public static void main(String[] args) {
        String seq = IdLeaf.getId(TASK_TEMP_ID);
        String taskNo = prefix + String.format("%07d", Long.valueOf(seq));
        System.out.println(taskNo);
    }

    @Override
    public String getTaskPartDetailNo() {
        String seq = IdLeaf.getId(TASK_TEMP_ID);
        String taskNo = prefixOfPart + String.format("%07d", Long.valueOf(seq));
        return taskNo;
    }

    @Override
    public void batchInsertDataAndLogs(List<TaskTemplate> insertTaskTemplates, List<TaskTemplateLog> insertLogs,
                                       List<TaskTemplateLimitation> taskTemplateLimitations, List<TaskTemplateOutInfo> outInfos,
                                       List<TaskTemplateExecution> executions, List<TaskTemplateRewardInfo> rewardInfos) {
        itaskTemplateService.saveBatch(insertTaskTemplates);
        iTaskTemplateLogService.saveBatch(insertLogs);
        if (CollectionUtils.isNotEmpty(taskTemplateLimitations)) {
            iTaskTemplateLimitationService.saveBatch(taskTemplateLimitations);
        }
        if (CollectionUtils.isNotEmpty(outInfos)) {
            iTaskTemplatePopInfoService.saveBatch(outInfos);
        }
        if (CollectionUtils.isNotEmpty(executions)) {
            iTaskTemplateExecutionService.saveBatch(executions);
        }
        if (CollectionUtils.isNotEmpty(rewardInfos)) {
            iTaskTemplateRewardInfoService.saveBatch(rewardInfos);
        }
    }

    @Override
    public TaskTemplate querySignInTask(Integer code, String weId) {
        QueryWrapper<TaskTemplate> query = new QueryWrapper<>();
        query.eq("TASK_TYPE", code);
        query.in("IS_DELETED", IsDeleteEnum.NORMAL.getCode());
        query.in("STATUS", TaskTemplateStatusEnum.PUT_ON_SHELVES.getCode());
        query.in("WEID", weId);
        return taskTemplateMapper.selectOne(query);
    }

    @Override
    public List<TaskTemplate> queryTaskLists(TaskTemplateListReq req) {
        return taskTemplateMapper.selectListByParams(req.getTaskTypes(),
                req.getTaskNo(), req.getTaskAliasName(), req.getStatus(), req.getEndTime(), req.getStartTime(),
                req.getWeIds(), new Date());
    }

    @Override
    public TaskTemplateDto selectTaskTemplateById(String id) {
        //根据id查询任务模板
        QueryWrapper<TaskTemplate> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("ID", id);
        queryWrapper.eq("IS_DELETED", IsDeleteEnum.NORMAL.getCode());
        TaskTemplate taskTemplate = taskTemplateMapper.selectOne(queryWrapper);
        //根据任务id查询限制人群
       /* QueryWrapper<TaskTemplateLimitation> query = new QueryWrapper<>();
        query.eq("TASK_TEMP_ID", id);
        query.eq("IS_DELETED", IsDeleteEnum.NORMAL.getCode());
        List<TaskTemplateLimitation> taskTemplateLimitations = taskTemplateLimitationMapper.selectList(query);*/
        //根据任务id查询限制人群
        QueryWrapper<TaskTemplateRewardInfo> rewardQuery = new QueryWrapper<>();
        rewardQuery.eq("TASK_TEMP_ID", id);
        rewardQuery.eq("IS_DELETED", IsDeleteEnum.NORMAL.getCode());
        List<TaskTemplateRewardInfo> taskTemplateRewardInfos = taskTemplateRewardInfoMapper.selectList(rewardQuery);
        //根据任务id查询限制人群
        QueryWrapper<TaskTemplateExecution> executeQuery = new QueryWrapper<>();
        executeQuery.eq("TASK_TEMP_ID", id);
        executeQuery.eq("IS_DELETED", IsDeleteEnum.NORMAL.getCode());
        List<TaskTemplateExecution> taskTemplateExecutions = taskTemplateExecutionMapper.selectList(executeQuery);
        //根据任务id查询限制人群
        QueryWrapper<TaskTemplateOutInfo> infoQuery = new QueryWrapper<>();
        infoQuery.eq("TASK_TEMP_ID", id);
        infoQuery.eq("IS_DELETED", IsDeleteEnum.NORMAL.getCode());
        List<TaskTemplateOutInfo> taskTemplateOutInfos = taskTemplateOutInfoMapper.selectList(infoQuery);

        // 根据任务id查询任务弹窗信息
        QueryWrapper<TaskTemplatePopInfo> popQuery = new QueryWrapper<>();
        popQuery.eq("TASK_TEMPLATE_ID", id);
        popQuery.eq("IS_DELETED", IsDeleteEnum.NORMAL.getCode());
        TaskTemplatePopInfo popInfo = taskTemplatePopInfoMapper.selectOne(popQuery);
        return TaskTemplateDto.build(taskTemplate, new ArrayList<>(), taskTemplateOutInfos, taskTemplateExecutions,
                taskTemplateRewardInfos, popInfo);
    }

    @Override
    public void updateTaskTemplateAndOther(TaskTemplate taskTemplate, TaskTemplateLog taskTemplateLog,
                                   List<TaskTemplateLimitation> insertPeopleInfo, TaskTemplateOperator taskTemplateOperator,
                                   List<TaskTemplateLimitation> oldPeopleInfo, String oldVersion,
                                   TaskTemplateOutInfo outInfo,  TaskTemplateExecution execution,
                                   List<TaskTemplateRewardInfo> rewardInfos) {
        // 任务模板更新
        taskTemplateMapper.updateTaskTemplate(taskTemplate, oldVersion);
        // 插入任务模板日志
        taskTemplateLogMapper.insert(taskTemplateLog);
        // 插入任务人群限制信息
        if (CollectionUtils.isNotEmpty(insertPeopleInfo)) {
            iTaskTemplateLimitationService.saveBatch(insertPeopleInfo);
        }
        // 插入任务操作人信息
        taskTemplateOperatorMapper.insert(taskTemplateOperator);
        // 将原有的人群限制置为无效
        if (CollectionUtils.isNotEmpty(oldPeopleInfo)) {
            taskTemplateLimitationMapper.batchUpdate(oldPeopleInfo);
        }
        // 更新外露信息
        if (outInfo != null) {
            taskTemplateOutInfoMapper.updateById(outInfo);
        }

        // 更新执行信息
        if (execution != null) {
            taskTemplateExecutionMapper.updateById(execution);
        }

        // 更新奖励信息
        if (CollectionUtils.isNotEmpty(rewardInfos)) {
            taskTemplateRewardInfoMapper.batchUpdate(rewardInfos);
        }
    }

    @Override
    public TaskTemplateInfo selectTaskByStatusAndType(Integer taskType, Integer status, String weId) {
        return taskTemplateMapper.selectRunningTaskByType(taskType, status, weId);
    }

    @Override
    public void operateTaskTemplate(TaskTemplate taskTemplate, TaskTemplateLog taskTemplateLog, TaskTemplateOperator taskTemplateOperator) {
        // 任务模板更新
        taskTemplateMapper.updateById(taskTemplate);
        // 插入任务模板日志
        taskTemplateLogMapper.insert(taskTemplateLog);
        // 插入任务操作人信息
        taskTemplateOperatorMapper.insert(taskTemplateOperator);

    }

    @Override
    public List<TaskTemplateDetailsResp> queryTaskTemplateDetails(TaskTemplateDetailsReq requestData) {
        return taskPartDetailsMapper
                .queryTaskTemplateDetailsByParam(requestData.getId(), requestData.getItemId(), requestData.getPhone(),
                                                    requestData.getStartTime(), requestData.getEndTime(), requestData.getNickName());
    }

    @Override
    public List<TaskTemplate> selectInProcessTask() {
        QueryWrapper<TaskTemplate> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("IS_DELETED", IsDeleteEnum.NORMAL.getCode());
        queryWrapper.in("STATUS", TaskTemplateStatusEnum.IN_PROCESS.getCode());
        return taskTemplateMapper.selectList(queryWrapper);
    }

    @Override
    public List<TaskTemplate> selectUnStartTask() {
        QueryWrapper<TaskTemplate> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("IS_DELETED", IsDeleteEnum.NORMAL.getCode());
        queryWrapper.in("STATUS", TaskTemplateStatusEnum.NOT_STARTED.getCode());
        return taskTemplateMapper.selectList(queryWrapper);
    }

    @Override
    public void updateTaskTemplate(TaskTemplate v) {
        taskTemplateMapper.updateById(v);
    }

    @Override
    public void insertTaskTemplateLog(TaskTemplateLog taskTemplateLog) {
        taskTemplateLogMapper.insert(taskTemplateLog);
    }

    @Override
    public void updateTaskTemplatePartDetailByTaskTempId(String id) {
        taskPartDetailsMapper.updateTaskTemplatePartDetailByCycle(id);
    }

    @Override
    public List<TaskTemplateInfo> selectInProcessTaskAndInCycle(UserTaskListReq req) {
        List<TaskTemplateInfo> rets = new ArrayList<>();

        Date date = new Date();
        // 筛选进行中的任务
        List<String> taskIds = taskTemplateMapper.selectInProcessTaskAndInCycle(req.getWeId(), date);
        // 签到类型的返回数据
        List<String> signInIds = taskTemplateMapper.selectInProcessSignInTaskAndInCycle(req.getWeId(), date);
        if (CollectionUtils.isNotEmpty(signInIds)) {
            taskIds.addAll(signInIds);
        }

        // 获取新手任务
        List<String> newerIds = taskTemplateMapper.selectNewerTask(req.getWeId());
        if (CollectionUtils.isNotEmpty(newerIds)) {
            taskIds.addAll(newerIds);
        }

        // 组装所有信息
        if (CollectionUtils.isEmpty(taskIds)) {
            return rets;
        }
        // 根据任务id查询基本信息、执行信息、外露信息、奖励信息
        QueryWrapper<TaskTemplate> taskWrapper = new QueryWrapper<>();
        QueryWrapper<TaskTemplateExecution> executionQueryWrapper = new QueryWrapper<>();
        QueryWrapper<TaskTemplateOutInfo> outInfoQueryWrapper = new QueryWrapper<>();
        QueryWrapper<TaskTemplateRewardInfo> rewardInfoQueryWrapper = new QueryWrapper<>();
        taskWrapper.in("ID", taskIds);
        executionQueryWrapper.in("TASK_TEMP_ID", taskIds);
        executionQueryWrapper.eq("IS_DELETED", IsDeleteEnum.NORMAL.getCode());
        outInfoQueryWrapper.in("TASK_TEMP_ID", taskIds);
        outInfoQueryWrapper.eq("IS_DELETED", IsDeleteEnum.NORMAL.getCode());
        rewardInfoQueryWrapper.in("TASK_TEMP_ID", taskIds);
        rewardInfoQueryWrapper.eq("IS_DELETED", IsDeleteEnum.NORMAL.getCode());
        List<TaskTemplate> taskTemplates = taskTemplateMapper.selectList(taskWrapper);
        List<TaskTemplateExecution> taskTemplateExecutions = taskTemplateExecutionMapper.selectList(executionQueryWrapper);
        List<TaskTemplateOutInfo> outInfos = taskTemplateOutInfoMapper.selectList(outInfoQueryWrapper);
        List<TaskTemplateRewardInfo> rewardInfos = taskTemplateRewardInfoMapper.selectList(rewardInfoQueryWrapper);
        // 根据id组装信息
        if (CollectionUtils.isEmpty(taskTemplates)) {
            return rets;
        }
        HashMap<String, TaskTemplateExecution> executionHashMap = taskTemplateExecutions.stream()
                .collect(HashMap::new, (k, v) -> k.put(v.getTaskTempId(), v), HashMap::putAll);
        HashMap<String, TaskTemplateOutInfo> outInfoHashMap = outInfos.stream()
                .collect(HashMap::new, (k, v) -> k.put(v.getTaskTempId(), v), HashMap::putAll);
        // 门槛
        HashMap<String, TaskTemplateRewardInfo> rewardInfoHashMap = rewardInfos.stream()
                .filter(v -> Objects.equals(v.getType(), 0))
                .collect(HashMap::new, (k, v) -> k.put(v.getTaskTempId(), v), HashMap::putAll);

        // 积分
        HashMap<String, TaskTemplateRewardInfo> pointInfoHashMap = rewardInfos.stream()
                .filter(v -> Objects.equals(v.getType(), 1))
                .collect(HashMap::new, (k, v) -> k.put(v.getTaskTempId(), v), HashMap::putAll);

        // 券
        Map<String, List<TaskTemplateRewardInfo>> coupMap = rewardInfos.stream()
                .filter(v -> Objects.equals(v.getType(), 2))
                .collect(Collectors.groupingBy(TaskTemplateRewardInfo::getTaskTempId));

        // 库存
        Map<String, TaskTemplateRewardInfo> stockMap = rewardInfos.stream()
                .filter(v -> Objects.equals(v.getType(), 3))
                .collect(HashMap::new, (k, v) -> k.put(v.getTaskTempId(), v), HashMap::putAll);

        // 奖励发放时间
        Map<String, TaskTemplateRewardInfo> issueMap = rewardInfos.stream()
                .filter(v -> Objects.equals(v.getType(), 4))
                .collect(HashMap::new, (k, v) -> k.put(v.getTaskTempId(), v), HashMap::putAll);

        // 领奖时效
        Map<String, TaskTemplateRewardInfo> claimMap = rewardInfos.stream()
                .filter(v -> Objects.equals(v.getType(), 5))
                .collect(HashMap::new, (k, v) -> k.put(v.getTaskTempId(), v), HashMap::putAll);

        // 奖励
        Map<String, List<TaskTemplateRewardInfo>> rewardMap = rewardInfos.stream()
                .collect(Collectors.groupingBy(TaskTemplateRewardInfo::getTaskTempId));

        taskTemplates.forEach(v -> {
            TaskTemplateInfo info = new TaskTemplateInfo();
            BeanUtils.copyProperties(v, info);
            TaskTemplateExecution taskTemplateExecution = executionHashMap.get(v.getId());
            if (v.getExecutionMode() != null && v.getExecutionMode() == 0 && taskTemplateExecution != null) {
                BeanUtils.copyProperties(taskTemplateExecution, info);
            }
            TaskTemplateOutInfo templateOutInfo = outInfoHashMap.get(v.getId());
            if (templateOutInfo != null) {
                BeanUtils.copyProperties(templateOutInfo, info);
            }
            TaskTemplateRewardInfo taskTemplateRewardInfo = rewardInfoHashMap.get(v.getId());
            if (taskTemplateRewardInfo != null) {
                info.setThreshold(Integer.valueOf(taskTemplateRewardInfo.getValue()));
            }

            TaskTemplateRewardInfo taskTemplateRewardInfo1 = pointInfoHashMap.get(v.getId());
            if (taskTemplateRewardInfo1 != null) {
                info.setGainPoints(Integer.valueOf(taskTemplateRewardInfo1.getValue()));
            }


            if (MapUtils.isNotEmpty(coupMap) && CollectionUtils.isNotEmpty(coupMap.get(v.getId()))) {
                info.setAwardId(1);
            }

            TaskTemplateRewardInfo stockInfo = stockMap.get(v.getId());
            if (stockInfo != null) {
                info.setRewardStock(Integer.valueOf(stockInfo.getValue()));
            }

            TaskTemplateRewardInfo issueInfo = issueMap.get(v.getId());
            if (issueInfo != null) {
                info.setRewardIssueTime(Integer.valueOf(issueInfo.getValue()));
            }

            TaskTemplateRewardInfo claimInfo = claimMap.get(v.getId());
            if (claimInfo != null) {
                info.setRewardClaimableTime(Integer.valueOf(claimInfo.getValue()));
            }

            // 设置领取奖励类型 0：手动领取 1：自动发放
            info.setDrawType((MapUtils.isEmpty(rewardMap) || CollectionUtils.isEmpty(rewardMap.get(v.getId())))
                    ? 1 : rewardMap.get(v.getId()).get(0).getDrawType());
            info.setId(v.getId());
            rets.add(info);
        });
        return rets;
    }

    @Override
    public TaskPartDetails selectUserPartByTaskInfo(TaskInfo v, UserTaskListReq requestData) {
        QueryWrapper<TaskPartDetails> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("TASK_TEMP_ID", v.getTaskTempId());
        queryWrapper.eq("TASK_INFO_ID", v.getId());
        queryWrapper.eq("WEID", v.getWeid());
        queryWrapper.eq("UNIONID", requestData.getUnionId());
        queryWrapper.eq("STATUS", 0);
        queryWrapper.eq("IS_DELETED", 0);
        queryWrapper.eq("TASK_TEMP_TYPE", v.getTaskType());
        queryWrapper.ne("TASK_STATUS", 5);
        queryWrapper.orderByDesc("CREATE_TIME");
        if (v.getInCycleStartTime() != null) {
            queryWrapper.eq("IN_CYCLE_START_TIME", v.getInCycleStartTime());
        }
        if (v.getInCycleEndTime() != null) {
            queryWrapper.eq("IN_CYCLE_END_TIME", v.getInCycleEndTime());
        }
        return taskPartDetailsMapper.selectOne(queryWrapper);
    }

    @Override
    public void batchInsertTaskPartDetails(List<TaskPartDetails> inserts) {
        iTaskPartDetailsService.saveBatch(inserts);
    }

    @Override
    public void batchUpdateTaskPartDetails(List<TaskPartDetails> updates) {
        taskPartDetailsMapper.batchUpdate(updates);
    }

    @Override
    public TaskPartDetails selectTaskPartDetailsByUnionIdAndType(Integer type, String unionId, String weId) {
        QueryWrapper<TaskPartDetails> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("TASK_TEMP_TYPE", type);
        queryWrapper.eq("UNIONID", unionId);
        queryWrapper.eq("STATUS", 0);
        queryWrapper.eq("WEID", weId);
        queryWrapper.orderByDesc("CREATE_TIME");
        queryWrapper.orderByDesc("TASK_ITEM_NO");
        List<TaskPartDetails> taskPartDetails = taskPartDetailsMapper.selectList(queryWrapper);
        return CollectionUtils.isNotEmpty(taskPartDetails) ? taskPartDetails.get(0) : null;
    }

    @Override
    public TaskTemplateInfo getTaskTemplateById(String taskTempId) {
        return taskTemplateMapper.selectInfoByTaskTampId(taskTempId);
    }

    @Override
    public void updateTaskPartDetails(TaskPartDetails taskPartDetails) {
        taskPartDetailsMapper.updateById(taskPartDetails);
    }

    @Override
    public void insertTaskPartDetailsItem(TaskPartDetailsItem taskPartDetailsItem) {
        LambdaQueryWrapper<TaskPartDetailsItem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TaskPartDetailsItem::getUnionId, taskPartDetailsItem.getUnionId());
        queryWrapper.eq(TaskPartDetailsItem::getTaskTempId, taskPartDetailsItem.getTaskTempId());
        queryWrapper.eq(TaskPartDetailsItem::getTaskTempType, taskPartDetailsItem.getTaskTempType());
        queryWrapper.eq(TaskPartDetailsItem::getWeid, taskPartDetailsItem.getWeid());
        queryWrapper.eq(TaskPartDetailsItem::getThirdPartId, taskPartDetailsItem.getThirdPartId());
        queryWrapper.eq(TaskPartDetailsItem::getStatus, taskPartDetailsItem.getStatus());
        TaskPartDetailsItem item = taskPartDetailsItemMapper.selectOne(queryWrapper);
        if (item != null) {
            TaskPartDetailsItem update = new TaskPartDetailsItem();
            update.setId(item.getId());
            update.setUpdateTime(new Date());
            taskPartDetailsItemMapper.updateById(update);
        }else{
            taskPartDetailsItemMapper.insert(taskPartDetailsItem);
        }
    }

    @Override
    public void updateTaskTemplateLimitationStatus(String id) {
        taskTemplateLimitationMapper.updateDeletedByTaskTemplateId(id);
    }

    @Override
    public TaskPartDetails queryTaskPartDetailsById(String id) {
        QueryWrapper<TaskPartDetails> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", id);
        queryWrapper.eq("STATUS", 0);
        return taskPartDetailsMapper.selectOne(queryWrapper);
    }

    @Override
    public Pair queryNumRunningAndWaiting(TaskTemplateListReq req) {
        Integer running = taskTemplateMapper.selectRunningNum(new Date(), req.getTaskTypes(),
                req.getTaskNo(), req.getTaskAliasName(), req.getStatus(), req.getEndTime(), req.getStartTime(),
                req.getWeIds());
        Integer waiting = taskTemplateMapper.selectWaitingNum(new Date(), req.getTaskTypes(),
                req.getTaskNo(), req.getTaskAliasName(), req.getStatus(), req.getEndTime(), req.getStartTime(),
                req.getWeIds());
        return Pair.of(waiting, running);
    }

    @Override
    public void takeOffTheShelf() {
        taskTemplateMapper.takeOffTheShelfTask();
    }

    @Override
    public void expireUserTask() {
        taskPartDetailsMapper.expireUserTask();
    }

    @Override
    public TaskPartDetailsItem selectItemByThirdPartId(ListenerEvent requestData) {
        QueryWrapper<TaskPartDetailsItem> query = new QueryWrapper<>();
        query.eq("STATUS", requestData.getStatus());
        query.eq("WEID", requestData.getWeId());
        query.eq("THIRD_PART_ID", requestData.getUniqueIdent());
        query.eq("TASK_TEMP_TYPE", requestData.getType());
        query.eq("UNIONID", requestData.getUnionId());
        return taskPartDetailsItemMapper.selectOne(query);
    }

    @Override
    public List<TaskTemplateRewardInfo> queryTaskTempRewardInfo(String taskTempId) {
        QueryWrapper<TaskTemplateRewardInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("TASK_TEMP_ID", taskTempId);
        // queryWrapper.eq("TYPE", 1);
        queryWrapper.eq("IS_DELETED", IsDeleteEnum.NORMAL.getCode());
        return taskTemplateRewardInfoMapper.selectList(queryWrapper);
    }

    @Override
    public List<String> selectUserStoreInfo(String storeCode) {
        return storeReservationOrderMapper.selectByStoreId(storeCode);
    }

    @Override
    public List<TaskPartDetails> querySignInTaskByUnionId(String unionId, Integer code) {
        QueryWrapper<TaskPartDetails> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("UNIONID", unionId);
        queryWrapper.eq("TASK_TEMP_TYPE", code);
        queryWrapper.eq("IS_DELETED", IsDeleteEnum.NORMAL.getCode());
        queryWrapper.eq("STATUS", 0);
        queryWrapper.eq("FINISH_STATUS", 1);
        return taskPartDetailsMapper.selectList(queryWrapper);
    }

    @Override
    public List<TaskTemplateInfo> selectTaskByWeId(String weId) {
        return taskTemplateMapper.selectRunningTask(new Date(), weId);
    }

    @Override
    public List<TaskPartDetailsItem> querySignLogByUnionId(String unionId, Integer code) {
        return taskPartDetailsItemMapper.selectSignTodayLogByUnionId(unionId, code);
    }

    @Override
    public void insertDataByTaskPartDetailsItem(TaskPartDetailsItem backLog) {
        taskPartDetailsItemMapper.insert(backLog);
    }

    @Override
    public List<TaskPartDetails> queryBeforeSixTaskList(String unionId, String weId, Date start, Date end) {
        return taskPartDetailsMapper.selectDuringDateTaskList(unionId, weId, start, end);
    }

    @Override
    public List<TaskPartDetails> queryTodayTaskList(String weId, String unionId) throws ParseException {
        Date currentDate = new Date();
        Calendar current = Calendar.getInstance();
        current.setTime(currentDate);

        // 获取前一天的日期
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String date = sdf.format(current.getTime());
        Date start = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse( date + " 00:00:00");
        Date end = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(date + " 23:59:59");
        return taskPartDetailsMapper.selectDuringDateTaskList(unionId, weId, start, end);
    }

    @Override
    public List<TaskPartDetails> queryAllNewerTaskList(String weId, String unionId) {
        return taskPartDetailsMapper.selectAllNewerTaskList(unionId, weId);
    }

    @Override
    public Map<String, Integer> selectCostTaskCount() {
        return taskPartDetailsMapper.selectCostTaskCount();
    }

    @Override
    public void batchInsertOperator(List<TaskTemplateOperator> operators) {
        iTaskTemplateOperatorService.saveBatch(operators);
    }


}
