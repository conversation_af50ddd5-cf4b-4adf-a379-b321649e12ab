<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.marketing.modules.mapper.AwardConfigMapper">

    <resultMap id="BaseResultMap" type="org.springcenter.marketing.modules.model.AwardConfig">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="awardType" column="award_type" jdbcType="INTEGER"/>
            <result property="awardName" column="award_name" jdbcType="VARCHAR"/>
            <result property="awardCode" column="award_code" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="isDel" column="is_del" jdbcType="INTEGER"/>
            <result property="awardNum" column="award_num" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,theme_activity_days_rule_id,award_type,
        award_name,award_code,create_time,
        update_time,is_del,award_num
    </sql>
</mapper>
