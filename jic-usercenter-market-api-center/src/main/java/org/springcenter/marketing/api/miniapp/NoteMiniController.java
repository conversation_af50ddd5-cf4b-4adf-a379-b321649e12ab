package org.springcenter.marketing.api.miniapp;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Preconditions;
import com.jnby.common.CommonRequest;
import com.jnby.common.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springcenter.marketing.api.dto.miniapp.*;
import org.springcenter.marketing.modules.service.note.INoteAppService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/miniapp/note/api")
@Api(tags = "互动留言-小程序接口")
public class NoteMiniController {

    @Autowired
    private INoteAppService noteAppService;

    @ApiOperation(value = "留言列表")
    @PostMapping("/listNotes")
    public ResponseResult<List<NoteListItemResp>> listNotes(@Validated @RequestBody CommonRequest<NoteListQueryReq> req) {
        req.getRequestData().check();
        log.info("分页查询留言 入参: {}", JSON.toJSONString(req));
        List<NoteListItemResp> list = noteAppService.listNotes(req.getRequestData(), req.getPage());
        log.info("分页查询留言 总数={}, 数据={}", req.getPage().getCount(), JSON.toJSONString(list));
        return ResponseResult.success(list, req.getPage());
    }

    @ApiOperation(value = "打卡成功天数")
    @PostMapping("/getJoinDays")
    public ResponseResult<Integer> getJoinDays(@Valid @RequestBody CommonRequest<NoteActivityBaseReq> req) {
        Preconditions.checkArgument(StringUtils.isNotBlank(req.getRequestData().getCardNo()), "cardNo不能为空");
//        Integer days = noteMiniService.getJoinDays(req.getRequestData());
        return ResponseResult.success(1);
    }


    @ApiOperation(value = "草稿箱留言总数")
    @PostMapping("/getMyDraftCount")
    public ResponseResult<Integer> getMyDraftCount(@Valid @RequestBody CommonRequest<NoteActivityBaseReq> req) {
//        Integer count = noteMiniService.getMyDraftCount(req.getRequestData());
        return ResponseResult.success(1);
    }

    @ApiOperation(value = "行为上报")
    @PostMapping("/behaviorUpload")
    public ResponseResult<Boolean> behavior(@Valid @RequestBody CommonRequest<NoteBehaviorReq> req) {
        Boolean ok = noteAppService.likeOrCancel(req.getRequestData());
        return ResponseResult.success(ok);
    }

    @ApiOperation(value = "新增/编辑草稿箱")
    @PostMapping("/saveDraft")
    public ResponseResult<Long> saveDraft(@Valid @RequestBody CommonRequest<NoteDraftSaveReq> req) {
        log.info("新增/编辑留言草稿箱 入参: {}", JSON.toJSONString(req));
//        Long draftId = noteAppService.saveDraft(req.getRequestData());
        Long draftId = 1L;
        log.info("新增/编辑留言草稿箱 结果: draftId={}", draftId);
        return ResponseResult.success(draftId);
    }

    @ApiOperation(value = "删除草稿箱")
    @PostMapping("/deleteDraft")
    public ResponseResult<Boolean> deleteDraft(@Valid @RequestBody CommonRequest<NoteDraftDetailReq> req) {
        log.info("删除留言草稿箱 入参: {}", JSON.toJSONString(req));
//        Boolean result = noteAppService.deleteDraft(req.getRequestData());
        Boolean result = true;
        log.info("删除留言草稿箱 结果: {}", result);
        return ResponseResult.success(result);
    }

    @ApiOperation(value = "草稿箱列表")
    @PostMapping("/listDrafts")
    public ResponseResult<List<NoteDraftListItemResp>> listDrafts(@Valid @RequestBody CommonRequest<NoteActivityBaseReq> req) {
        log.info("查询草稿箱列表 入参: {}", JSON.toJSONString(req));
//        List<NoteDraftListItemResp> list = noteAppService.listDrafts(req.getRequestData(), req.getPage());
        List<NoteDraftListItemResp> list = null;
        log.info("查询草稿箱列表 总数={}, 数据={}", req.getPage().getCount(), JSON.toJSONString(list));
        return ResponseResult.success(list, req.getPage());
    }

    @ApiOperation(value = "草稿箱详情")
    @PostMapping("/getDraftDetail")
    public ResponseResult<NoteDraftDetailResp> getDraftDetail(@Valid @RequestBody CommonRequest<NoteDraftDetailReq> req) {
        log.info("获取草稿详情 入参: {}", JSON.toJSONString(req));
//        NoteDraftDetailResp detail = noteAppService.getDraftDetail(req.getRequestData());
        NoteDraftDetailResp detail = null;
        log.info("获取草稿详情 结果: {}", JSON.toJSONString(detail));
        return ResponseResult.success(detail);
    }

}
