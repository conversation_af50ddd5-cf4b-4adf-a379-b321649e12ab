package org.springcenter.marketing.api.miniapp;

import com.jnby.common.CommonRequest;
import com.jnby.common.ResponseResult;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springcenter.marketing.api.dto.*;
import org.springcenter.marketing.api.dto.miniapp.QueryBirthReq;
import org.springcenter.marketing.modules.entity.BrandSubTrick;
import org.springcenter.marketing.modules.entity.CostTaskPopResp;
import org.springcenter.marketing.modules.entity.TaskUpdateUserInfoReq;
import org.springcenter.marketing.modules.service.ITaskService;
import org.springcenter.marketing.modules.util.RedissonUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.text.ParseException;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/3/14 17:16
 */
@Slf4j
@RestController
@RequestMapping("/miniapp/task/api")
public class MiniTaskApiController {

    @Autowired
    private ITaskService taskService;

    @Autowired
    private RedissonUtil redissonUtil;

    @ResponseBody
    @PostMapping("/query/user/task/list")
    @ApiOperation(value = "查询用户任务列表")
    public ResponseResult<List<UserTaskListResp>> queryUserTaskList(@RequestBody @Validated CommonRequest<UserTaskListReq> request){
        String key = "tryAfterUserTask:" + request.getRequestData().getUnionId() + request.getRequestData().getWeId()
                + request.getRequestData().getPhone();
        if (!redissonUtil.tryLock(key)) {
            log.info("查询用户任务列表 操作频繁key：{}", key);
            return ResponseResult.success(Collections.emptyList());
        }

        try {
            return ResponseResult.success(taskService.queryUserTaskList(request.getRequestData()));
        } catch (ParseException e) {
            throw new RuntimeException(e);
        } finally {
            redissonUtil.unlock(key);
        }
    }

    @ResponseBody
    @PostMapping("/click/task")
    @ApiOperation(value = "点击领取")
    public ResponseResult<Boolean> clickTaskByUser(@RequestBody CommonRequest<UserClickTaskReq> request){
        String key = "tryAfterUserTaskClick:" + request.getRequestData().getUnionId() +
                request.getRequestData().getId() + request.getRequestData().getType()
                + request.getRequestData().getOperate();
        if (!redissonUtil.tryLock(key)) {
            return ResponseResult.success(true);
        }

        try {
            return ResponseResult.success(taskService.clickTaskByUser(request.getRequestData()));
        } finally {
            redissonUtil.unlock(key);
        }

    }

    @ResponseBody
    @PostMapping("/listener")
    @ApiOperation(value = "监听状态接口")
    public ResponseResult<Boolean> listener(@RequestBody @Validated CommonRequest<ListenerEvent> request){
        return ResponseResult.success(taskService.listener(request.getRequestData()));
    }

    @ResponseBody
    @PostMapping("/judgeIsShow")
    @ApiOperation(value = "判断是否展示限时任务")
    public ResponseResult<Boolean> judgeIsShow(@RequestBody @Validated CommonRequest<JudgeIsShowReq> request){
        return ResponseResult.success(taskService.judgeIsShow(request.getRequestData()));
    }

    @ResponseBody
    @PostMapping("/set/reminder")
    @ApiOperation(value = "设置提醒")
    public ResponseResult<Boolean> setReminder(@RequestBody CommonRequest<UserClickReminderReq> request){
        String key = "tryAfterUserReminderClick:" + request.getRequestData().getUnionId() +
                request.getRequestData().getWeId() + request.getRequestData().getIsReminder();
        if (!redissonUtil.tryLock(key)) {
            return ResponseResult.success(true);
        }

        try {
            return ResponseResult.success(taskService.setReminder(request.getRequestData()));
        } finally {
            redissonUtil.unlock(key);
        }

    }


    @ResponseBody
    @PostMapping("/query/user/reminder")
    @ApiOperation(value = "查询用户是否有提醒")
    public ResponseResult<UserReminderInfoResp> queryUserReminder(@RequestBody CommonRequest<UserReminderInfoReq> request){
       return ResponseResult.success(taskService.queryUserReminder(request.getRequestData()));
    }


    @ResponseBody
    @PostMapping("/query/user/is/subscribe")
    @ApiOperation(value = "查询当前用户是否已经订阅公众号 false:没有订阅 true:已经订阅")
    public ResponseResult<Boolean> queryUserIsSubscribe(@RequestBody CommonRequest<UserReminderInfoReq> request){
        return ResponseResult.success(taskService.queryUserIsSubscribe(request.getRequestData()));
    }


    @ResponseBody
    @PostMapping("/query/subscribe/trick")
    @ApiOperation(value = "根据品牌id获取话术")
    public ResponseResult<BrandSubTrick> querySubscribeTrick(@RequestBody CommonRequest<String> request){
        return ResponseResult.success(taskService.querySubscribeTrick(request.getRequestData()));
    }



    @ResponseBody
    @PostMapping("/queryBirth")
    @ApiOperation(value = "查询生日  data里面直接就是生日")
    public ResponseResult<String> queryBirth(@RequestBody @Validated CommonRequest<QueryBirthReq> request){
        return ResponseResult.success(taskService.queryBirth(request.getRequestData()));
    }

    @ResponseBody
    @PostMapping("/drawCostPopTask")
    @ApiOperation(value = "消费任务领取成功弹窗 入参为UserTaskListResp的id")
    public ResponseResult<CostTaskPopResp> drawCostPopTask(@RequestBody @Validated CommonRequest<String> request){
        return ResponseResult.success(taskService.drawCostPopTask(request.getRequestData()));
    }


    @ResponseBody
    @PostMapping("/query/user/child/task")
    @ApiOperation(value = "查询用户是否有童孩任务")
    public ResponseResult<ChildIsDisplayResp> queryUserChildTask(@RequestBody @Validated CommonRequest<UserTaskListReq> request){
        String key = "queryUserChildTask:" + request.getRequestData().getUnionId() + request.getRequestData().getWeId()
                + request.getRequestData().getPhone();
        if (!redissonUtil.tryLock(key)) {
            log.info("查询用户童孩任务 操作频繁key：{}", key);
            return ResponseResult.success(null);
        }

        try {
            return ResponseResult.success(taskService.queryUserChildTask(request.getRequestData()));
        } finally {
            redissonUtil.unlock(key);
        }
    }


    @ResponseBody
    @PostMapping("/update/user/info")
    @ApiOperation(value = "更新用户基本信息")
    public ResponseResult<Boolean> updateUserInfo(@RequestBody @Validated CommonRequest<TaskUpdateUserInfoReq> request){
        return ResponseResult.success(taskService.updateUserInfo(request.getRequestData()));
    }
}
