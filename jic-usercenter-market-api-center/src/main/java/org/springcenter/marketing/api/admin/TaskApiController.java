package org.springcenter.marketing.api.admin;

import com.jnby.common.CommonRequest;
import com.jnby.common.ResponseResult;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springcenter.marketing.api.dto.*;
import org.springcenter.marketing.common.constant.ImageConstant;
import org.springcenter.marketing.modules.service.ITaskService;
import org.springcenter.marketing.modules.service.ITaskTemplateHandle;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Date:2023/2/9 9:50
 */
@Slf4j
@RestController
@RequestMapping("/admin/task/api")
public class TaskApiController {

    @Autowired
    private ITaskService taskService;

    @Autowired
    private List<ITaskTemplateHandle> taskTemplateHandles;

    @ResponseBody
    @PostMapping("/query/task/operator")
    @ApiOperation(value = "查询操作人列表")
    public ResponseResult<List<TaskOperatorsResp>> queryTaskOperators(@RequestBody @Validated CommonRequest<TaskOperatorsReq> request){
        return ResponseResult.success(taskService.queryTaskOperators(request.getRequestData(), request.getPage()), request.getPage());
    }

    @ResponseBody
    @PostMapping("/add/task")
    @ApiOperation(value = "添加任务模板")
    public ResponseResult<List<TaskTemplateResp>> addTaskTemplate(@RequestBody @Validated CommonRequest<TaskTemplateReq> request){
        ITaskTemplateHandle taskTemplateHandle = taskTemplateHandles.stream()
                                                 .filter(item -> item.getType().contains(request.getRequestData().getTaskType()))
                                                 .findFirst().orElse(null);
        if (!StringUtils.contains(request.getRequestData().getIcon(), ImageConstant.PRESS_IMG_SUFFIX)) {
            request.getRequestData().setIcon(request.getRequestData().getIcon() + ImageConstant.PRESS_IMG_SUFFIX);
        }
        List<TaskTemplateResp> ret = taskTemplateHandle.addTaskTemplate(request.getRequestData());
        return ResponseResult.success(ret);
    }

    @ResponseBody
    @PostMapping("/query/task/list")
    @ApiOperation(value = "查询任务模板列表")
    public ResponseResult<TaskTemplateListResp> queryTaskList(@RequestBody CommonRequest<TaskTemplateListReq> request){
        return ResponseResult.success(taskService.queryTaskList(request.getRequestData(), request.getPage()), request.getPage());
    }

    @ResponseBody
    @PostMapping("/query/task/detail")
    @ApiOperation(value = "查询任务模板详情")
    public ResponseResult<TaskTemplateDetailResp> queryTaskDetail(@RequestBody @Validated CommonRequest<TaskTemplateDetailReq> request){
        return ResponseResult.success(taskService.queryTaskDetail(request.getRequestData()));
    }

    @ResponseBody
    @PostMapping("/update/task/detail")
    @ApiOperation(value = "编辑任务详情")
    public ResponseResult<List<TaskTemplateResp>> updateTaskDetail(@RequestBody @Validated CommonRequest<UpdateTaskTemplateReq> request){
        ITaskTemplateHandle taskTemplateHandle = taskTemplateHandles.stream()
                .filter(item -> item.getType().contains(request.getRequestData().getTaskType()))
                .findFirst().orElse(null);
        if (!StringUtils.contains(request.getRequestData().getIcon(), ImageConstant.PRESS_IMG_SUFFIX)) {
            request.getRequestData().setIcon(request.getRequestData().getIcon() + ImageConstant.PRESS_IMG_SUFFIX);
        }
        return ResponseResult.success(taskTemplateHandle.updateTaskDetail(request.getRequestData()));
    }

    @ResponseBody
    @PostMapping("/operate/task")
    @ApiOperation(value = "操作任务状态")
    public ResponseResult<List<TaskTemplateResp>> operateTask(@RequestBody @Validated CommonRequest<OperateTaskTemplateReq> request){
        return ResponseResult.success(taskService.operateTask(request.getRequestData()));
    }

    @ResponseBody
    @PostMapping("/query/task/template/details")
    @ApiOperation(value = "查询任务明细")
    public ResponseResult<List<TaskTemplateDetailsResp>> queryTaskTemplateDetails(@RequestBody @Validated CommonRequest<TaskTemplateDetailsReq> request){
        return ResponseResult.success(taskService.queryTaskTemplateDetails(request.getRequestData(), request.getPage()), request.getPage());
    }

    @ResponseBody
    @PostMapping("/batch/update/task")
    @ApiOperation(value = "批量修改日期")
    public ResponseResult<Boolean> batchUpdateTask(@RequestBody @Validated CommonRequest<BatchUpdateTaskTemplateReq> request){
        return ResponseResult.success(taskService.batchUpdateTaskDate(request.getRequestData()));
    }


    @ResponseBody
    @PostMapping("/takeOffTheShelfJob")
    @ApiOperation(value = "下架job")
    public void takeOffTheShelfJob(){
        taskService.takeOffTheShelfJob();
    }

    @ResponseBody
    @PostMapping("/expireUserJob")
    @ApiOperation(value = "将用户活动置为过期")
    public void expireUserJob(){
        taskService.expireUserJob();
    }



    @ResponseBody
    @PostMapping("/generate/task/info")
    @ApiOperation(value = "生成任务")
    public void generateTaskInfo(){
        taskService.generateTaskInfo();
    }


    @ResponseBody
    @PostMapping("/expire/task/info")
    @ApiOperation(value = "将任务制成过期")
    public void expireTaskInfo(){
        taskService.expireTaskInfo();
    }


    @ResponseBody
    @PostMapping("/send/subscribe/info")
    @ApiOperation(value = "公众号提醒")
    public void sendSubInfo(@RequestBody CommonRequest<Long> request){
        taskService.sendTaskReminderInfo(request.getRequestData());
    }

}
