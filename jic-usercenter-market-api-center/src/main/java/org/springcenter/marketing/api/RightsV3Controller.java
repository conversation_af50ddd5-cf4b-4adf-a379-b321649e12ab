package org.springcenter.marketing.api;


import com.alibaba.fastjson.JSONObject;
import com.jnby.common.CommonRequest;
import com.jnby.common.ResponseResult;
import org.springcenter.marketing.api.checkRightsDto.CheckRightCouponV3DtoForPre;
import org.springcenter.marketing.api.constant.RedisKeyConstant;
import org.springcenter.marketing.api.context.UseRightsContext;
import org.springcenter.marketing.api.dto.*;
import org.springcenter.marketing.api.dto.admin.ReceiveBirthCouponReq;
import org.springcenter.marketing.api.dto.admin.ReceiveBirthCouponResp;
import org.springcenter.marketing.api.entity.CheckRightsEntity;
import org.springcenter.marketing.modules.model.BNewUserRightsCoupon;
import org.springcenter.marketing.modules.service.RightsV3Service;
import org.springcenter.marketing.modules.util.RedisService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springcenter.marketing.modules.util.RedissonUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/rightsV3/api")
public class RightsV3Controller {

    @Autowired
    private RightsV3Service rightsV3Service;

    @Autowired
    private RedisService redisService;

    @Value("${app.reflect.jumpurl}")
    private String appReflectJumpUrl;

    @Autowired
    private RedissonUtil redissonUtil;



    @ResponseBody
    @PostMapping("/openCard")
    @ApiOperation(value = "开通权益")
    public ResponseResult openCard(@RequestBody  CommonRequest<OpenCardAndRightsReq> request){
        rightsV3Service.openCardAndRights(request.getRequestData());
        return ResponseResult.success();
    }


    @ResponseBody
    @PostMapping("/loseCard")
    @ApiOperation(value = "失效卡")
    public ResponseResult loseCard(@RequestBody  CommonRequest<OpenCardAndRightsReq> request){
        rightsV3Service.loseEffectCardByPhone(request.getRequestData());
        return ResponseResult.success();
    }


    @ResponseBody
    @PostMapping("/loseCardByOutNo")
    @ApiOperation(value = "失效卡 根据outNo")
    public ResponseResult loseCardByOutNo(@RequestBody  CommonRequest<LoseCardByOutNoReq> request){
        rightsV3Service.loseCardByOutNo(request.getRequestData());
        return ResponseResult.success();
    }


    @ResponseBody
    @PostMapping("/checkOrUse")
    @ApiOperation(value = "验证权益，消耗权益")
    public ResponseResult<Boolean> check(@RequestBody CommonRequest<CheckRightsEntity> request){
        CheckRightsEntity requestData = request.getRequestData();
        if(requestData.getIsDeductionRights()){
            String key = RedisKeyConstant.CHECK_OR_USE_RIGHTS.join(requestData.getUnionid(),requestData.getApplicationParty(),requestData.getRightsTypeEnum().getCode());
            try {
                boolean flag = redissonUtil.tryLock("MARKET:"+key);
                if(!flag){
                    log.info("消耗权益未获取锁 消耗失败");
                    return ResponseResult.success(false);
                }
                Boolean aBoolean = rightsV3Service.checkRightsIsAvailableAndDeductionRights(request.getRequestData());
                return ResponseResult.success(aBoolean);
            }catch (Exception e){
                log.error("验证权益 出现错误 :  request = {} ,e = ", JSONObject.toJSONString(request),e);
                return ResponseResult.success(true);
            }finally {
                redissonUtil.unlock("MARKET:"+key);
            }
        }else{
            Boolean aBoolean = rightsV3Service.checkRightsIsAvailableAndDeductionRights(request.getRequestData());
            return ResponseResult.success(aBoolean);
        }
    }




    @ResponseBody
    @PostMapping("/batchUseUserRights")
    @ApiOperation(value = "批量消耗用户次数类权益")
    public ResponseResult<Boolean> batchUseUserRights(@RequestBody CommonRequest<List<BatchUseUserRightsReq>> request){
        rightsV3Service.batchUseUserRights(request.getRequestData());
        return ResponseResult.success(true);
    }

    @ResponseBody
    @PostMapping("/preCalcExpansionCoupon")
    @ApiOperation(value = "预计算膨胀券")
    public ResponseResult<List<PreCalcResp>> preCalcExpansionCoupon(@RequestBody CommonRequest<List<CheckRightCouponV3DtoForPre>> request){
        return ResponseResult.success( rightsV3Service.preCalcExpansionCoupon(request.getRequestData()));
    }



    @ResponseBody
    @PostMapping("/getUserAvailableCardApplicationParty")
    @ApiOperation(value = "获取当前适用方类型用户所有可用卡")
    public ResponseResult<List<BNewUserMemberCardResp>> getUserAvailableCardApplicationParty(@RequestBody CommonRequest<UserAvailableCardApplicationPartyReq> request){
        return ResponseResult.success( rightsV3Service.getUserAvailableCardApplicationParty(request.getRequestData()));
    }

    @ResponseBody
    @PostMapping("/getUserAllCardApplicationParty")
    @ApiOperation(value = "获取当前适用方类型用户所有卡")
    public ResponseResult<List<BNewUserMemberCardResp>> getUserAllCardApplicationParty(@RequestBody CommonRequest<UserAvailableCardApplicationPartyReq> request){
        return ResponseResult.success( rightsV3Service.getUserAllCardApplicationParty(request.getRequestData()));
    }


    // 新加
    @ResponseBody
    @PostMapping("/getUserCardBySubId")
    @ApiOperation(value = "根据subid查询用户卡信息")
    public ResponseResult<BNewUserMemberCardResp> getUserCardBySubId(@RequestBody CommonRequest<GetUserCardBySubId> request){
        return ResponseResult.success( rightsV3Service.getUserCardBySubId(request.getRequestData()));
    }


    @ResponseBody
    @PostMapping("/getUserRightsBySubIds")
    @ApiOperation(value = "根据subIds查询用户次数类权益信息")
    public ResponseResult<List<UserRightsBySubIdsDto>> getUserRightsBySubIds(@RequestBody CommonRequest<GetUserCardBySubId> request){
        return ResponseResult.success( rightsV3Service.getUserRightsBySubIds(request.getRequestData()));
    }


    @ResponseBody
    @PostMapping("/getUserSubV3CouponBySubId")
    @ApiOperation(value = "根据subid查询用户膨胀券规则 只针对用户")
    public ResponseResult<List<UserSubV3CouponBySubIdResp>> getUserSubV3CouponBySubId(@RequestBody CommonRequest<GetUserCardBySubId> request){
        return ResponseResult.success( rightsV3Service.getUserSubV3CouponBySubId(request.getRequestData()));
    }



    @ResponseBody
    @PostMapping("/getUserRightsByOutNo")
    @ApiOperation(value = "根据outNo获取到用户的权益信息")
    public ResponseResult<List<BNewUserRightsResp>> getUserRightsByOutNo(@RequestBody CommonRequest<GetUserRightsByOutNoReq> request){
        return ResponseResult.success( rightsV3Service.getUserRightsByOutNo(request.getRequestData()));
    }



    @ResponseBody
    @PostMapping("/getBoxGiftByBoxSns")
    @ApiOperation(value = "根据boxSn获取收盒礼")
    public ResponseResult<List<BNewUserBoxGiftResp>> getBoxGiftByBoxSns(@RequestBody  @Validated  CommonRequest<GetBoxGiftByBoxSn> request){
        return ResponseResult.success( rightsV3Service.getBoxGiftByBoxSns(request.getRequestData()));
    }


    @ResponseBody
    @PostMapping("/getSubMemberCardRights")
    @ApiOperation(value = "获取卡配置，以及卡配置的权益   根据卡类型查询")
    public ResponseResult<List<SubMemberCardRightsResp>> getSubMemberCardRights(@RequestBody CommonRequest<SubMemberCardRightsReq> request) {
        return ResponseResult.success(rightsV3Service.getSubMemberCardRights(request.getRequestData()));
    }


    @ResponseBody
    @PostMapping("/getUserBoxGiftRights")
    @ApiOperation(value = "获取随盒礼  发没发都返回")
    public ResponseResult<List<BNewUserBoxGiftResp>> getUserBoxGiftRights(@RequestBody CommonRequest<List<UserBoxGiftRightsReq>> request) {
        return ResponseResult.success(rightsV3Service.getUserBoxGiftRights(request.getRequestData()));
    }


    // 根据consumeOutNo 查询随盒礼  和  膨胀券

    @ResponseBody
    @PostMapping("/getUserBoxGiftRightsByConsumeOutNo")
    @ApiOperation(value = "根据consumeOutNo 去查询 随盒礼权益")
    public ResponseResult<List<BNewUserBoxGiftResp>> getUserBoxGiftRightsByConsumeOutNo(@RequestBody CommonRequest<UserRightsByConsumeOutNoReq> request) {
        return ResponseResult.success(rightsV3Service.getUserBoxGiftRightsByConsumeOutNo(request.getRequestData()));
    }



    @ResponseBody
    @PostMapping("/getUserSubCouponRightsByConsumeOutNo")
    @ApiOperation(value = "根据 consumeOutNo 去查询 权益  膨胀券权益")
    public ResponseResult<List<BNewUserRightsCouponResp>> getUserSubCouponRightsByConsumeOutNo(@RequestBody CommonRequest<UserRightsByConsumeOutNoReq> request) {
        return ResponseResult.success(rightsV3Service.getUserSubCouponRightsByConsumeOutNo(request.getRequestData()));
    }


    @ResponseBody
    @PostMapping("/recover")
    @ApiOperation(value = "恢复权益")
    public ResponseResult<Boolean> recover(@RequestBody CommonRequest<UseRightsContext> request){
        UseRightsContext requestData = request.getRequestData();
        Integer reflectAppidToApplicationParty  = null;
        if(requestData.getApplicationParty() == null){
            reflectAppidToApplicationParty= rightsV3Service.reflectAppidToApplicationParty(request.getRequestData().getAppid());
        }else{
            reflectAppidToApplicationParty = requestData.getApplicationParty();
        }
        return ResponseResult.success(rightsV3Service.recoverRights(reflectAppidToApplicationParty,requestData));
    }


    @ResponseBody
    @PostMapping("/reflectAppIdToApplicationPart")
    @ApiOperation(value = "映射 appid 到 适用方")
    public ResponseResult<Integer> reflectAppIdToApplicationPart(@RequestBody CommonRequest<String> request){
        return ResponseResult.success(rightsV3Service.reflectAppidToApplicationParty(request.getRequestData()));
    }

    @PostMapping(value = "/dealTryAfterBuyIsHaveUnFinshBox")
    @ResponseBody
    public ResponseResult dealTryAfterBuyIsHaveUnFinshBox(@RequestBody  CommonRequest<DealTryAfterBuyIsHaveUnFinishBoxReq> request){
        return ResponseResult.success(rightsV3Service.dealTryAfterBuyIsHaveUnFinshBox(request.getRequestData()));
    }



    /**
     * 查询当前用户的权益信息
     */
    @ResponseBody
    @PostMapping(value = "/getRightsByUnionIdAndAppid")
    @ApiOperation(value = "根据用户unionid 和 appid 获取用户的信息 获取用户权益信息")
    public ResponseResult<GetRightsByUnionIdAndAppIdResp> getRightsByUnionIdAndAppid(@Validated @RequestBody CommonRequest<GetRightsByUnionIdAndAppId> request){
        GetRightsByUnionIdAndAppIdResp getRightsByUnionIdAndAppIdResp = new GetRightsByUnionIdAndAppIdResp();
        //查询用户信息

//        Boolean isBlack = customerDetailsService.checkCustomerIsBlack(request.getRequestData().getUnionId());
//        if(isBlack){
//            getRightsByUnionIdAndAppIdResp.setUnUsedNum("-1");
//            getRightsByUnionIdAndAppIdResp.setLimitNumber("0");
//            return ResponseResult.success(getRightsByUnionIdAndAppIdResp);
//        }
        Integer applitionParty= rightsV3Service.reflectAppidToApplicationParty(request.getRequestData().getAppId());
        Object unUsedNumber = redisService.get(RedisKeyConstant.B_USER_RIGHTS_KEYS_TOTAL_NUM.join(applitionParty, request.getRequestData().getUnionId()));
        Object limitNumber = redisService.get(RedisKeyConstant.B_USER_RIGHTS_KEYS_LIMIT_NUM.join(applitionParty, request.getRequestData().getUnionId()));
        Object isHaveTryAfterBuy = redisService.get(RedisKeyConstant.B_USER_RIGHTS_IS_HAVE_TRY_AFTER_BUY.join(applitionParty, request.getRequestData().getUnionId()));
        if(unUsedNumber == null){
            getRightsByUnionIdAndAppIdResp.setUnUsedNum("-1");
            getRightsByUnionIdAndAppIdResp.setLimitNumber("0");
            getRightsByUnionIdAndAppIdResp.setIsHaveTryAfterBuy("0");
        }else{
            getRightsByUnionIdAndAppIdResp.setUnUsedNum(unUsedNumber.toString());
            getRightsByUnionIdAndAppIdResp.setLimitNumber(limitNumber.toString());
            getRightsByUnionIdAndAppIdResp.setIsHaveTryAfterBuy(isHaveTryAfterBuy == null ? "0":isHaveTryAfterBuy.toString());
        }
        return ResponseResult.success(getRightsByUnionIdAndAppIdResp);
    }




    /**
     * 查询当前用户是否有先试后买未完结的单子
     */
    @ResponseBody
    @PostMapping(value = "/isHaveUnFinishBox")
    @ApiOperation(value = "查询当前用户是否有先试后买未完结的单子")
    public ResponseResult<HaveUnFinishBoxResp> isHaveUnFinishBox(@Validated @RequestBody CommonRequest<HaveUnFinishBoxReq> request){
        HaveUnFinishBoxResp haveUnFinishBoxResp = new HaveUnFinishBoxResp();
        Integer num = rightsV3Service.isHaveUnFinishBox(request.getRequestData());
        if(num > 0){
            haveUnFinishBoxResp.setIsHaveUnFinishBox(1);
        }
        return ResponseResult.success(haveUnFinishBoxResp);
    }

    /**
     * 判断当前用户是否有权益且无进行中的单子
     */
    @ResponseBody
    @PostMapping(value = "/isHaveAndNoOrderUser")
    @ApiOperation(value = "判断当前用户是否有权益且无进行中的单子")
    public ResponseResult<Boolean> isHaveAndNoOrderUser(@Validated @RequestBody CommonRequest<HaveUnFinishBoxReq> request) {
        // 查询是否有权益
        Integer applitionParty= rightsV3Service.reflectAppidToApplicationParty(request.getRequestData().getAppId());
        Object unUsedNumber = redisService.get(RedisKeyConstant.B_USER_RIGHTS_KEYS_TOTAL_NUM.join(applitionParty, request.getRequestData().getUnionId()));
        // 查询是否有进行中的单子
        Integer num = rightsV3Service.isHaveUnFinishBox(request.getRequestData());
        if (unUsedNumber == null) {
            return ResponseResult.success(Boolean.FALSE);
        }
        if (Integer.valueOf(unUsedNumber.toString()) > 0 && num == 0) {
            return ResponseResult.success(Boolean.TRUE);
        }
        return ResponseResult.success(Boolean.FALSE);
    }


    /**
     * 给前端配置的东西
     * @param
     * @return
     */
    @GetMapping(value = "/getAppIdReflectJumpUrl")
    @ResponseBody
    public ResponseResult getAppIdReflectJumpUrl(){
        return ResponseResult.success(appReflectJumpUrl);
    }


    /**
     * 否可领生日券
     */
    @PostMapping(value = "/receiveBirthCoupon")
    @ResponseBody
    @ApiOperation(value = "领取生日券")
    public ResponseResult<ReceiveBirthCouponResp> receiveBirthCoupon(@RequestBody CommonRequest<ReceiveBirthCouponReq> commonRequest){
        try {
            boolean flag = redissonUtil.tryLock("receiveBirthCoupon:"+commonRequest.getRequestData().getCardNo(),0,5);
            if(!flag){
                log.info("消耗权益未获取锁 消耗失败");
                return ResponseResult.error(-1,"请求频繁，请稍后重试!");
            }
            commonRequest.getRequestData().setIsReceive(true);
            ReceiveBirthCouponResp receiveBirthCouponResp = rightsV3Service.receiveBirthCoupon(commonRequest.getRequestData());
            return ResponseResult.success(receiveBirthCouponResp);
        }catch (Exception e){
            log.info("领取生日券出现错误 = ",e);
        }
        return ResponseResult.error(-1,"失败!");
    }



    @PostMapping(value = "/checkCanReceiveBirthCoupon")
    @ResponseBody
    @ApiOperation(value = "验证当前用户是否可领生日优惠券，如果用户已经领取过 那么返回生日优惠券编码 voucherNo")
    public ResponseResult<ReceiveBirthCouponResp> checkCanReceiveBirthCoupon(@RequestBody CommonRequest<ReceiveBirthCouponReq> commonRequest){
        Object key = redisService.get(RedisKeyConstant.RECEIVEBIRTHCOUPON.join(commonRequest.getRequestData().getCardNo()));
        if(key != null){
            return ResponseResult.error(-1,"正在领取中，请稍后重试!");
        }
        commonRequest.getRequestData().setIsReceive(false);
        ReceiveBirthCouponResp receiveBirthCouponResp = rightsV3Service.receiveBirthCoupon(commonRequest.getRequestData());
        return ResponseResult.success(receiveBirthCouponResp);
    }



    @PostMapping(value = "/checkCanReceiveBirthCouponByBrandId")
    @ResponseBody
    @ApiOperation(value = "验证当前用户是否可领生日优惠券，如果用户已经领取过 那么返回生日优惠券编码 voucherNo")
    public ResponseResult<List<ReceiveBirthCouponResp>> checkCanReceiveBirthCouponByBrandId(@RequestBody CommonRequest<ReceiveBirthCouponReq> commonRequest){
        Object key = redisService.get(RedisKeyConstant.RECEIVEBIRTHCOUPONBATCH.join(commonRequest.getRequestData().getUnionid()));
        if(key != null){
            return ResponseResult.error(-1,"正在领取中，请稍后重试!");
        }
        commonRequest.getRequestData().setIsReceive(false);
        List<ReceiveBirthCouponResp> receiveBirthCouponResp = rightsV3Service.checkCanReceiveBirthCouponByBrandId(commonRequest.getRequestData());
        return ResponseResult.success(receiveBirthCouponResp);
    }

}
