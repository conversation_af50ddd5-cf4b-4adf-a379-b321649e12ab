package org.springcenter.marketing.api;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jnby.common.CommonRequest;
import com.jnby.common.ResponseResult;
import com.jnby.common.util.IdLeaf;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.Message;
import org.springcenter.marketing.api.constant.IdConstant;
import org.springcenter.marketing.api.dto.ConsumeByVipIdResp;
import org.springcenter.marketing.api.dto.IntimacyByVipIdResp;
import org.springcenter.marketing.api.dto.*;
import org.springcenter.marketing.api.lookQuickReward.req.SyncSingleLookTag2RedisReq;
import org.springcenter.marketing.modules.bojunMapper.CclientVipMapper;
import org.springcenter.marketing.modules.listener.IMessageListener;
import org.springcenter.marketing.modules.listener.db.ColumnMeta;
import org.springcenter.marketing.modules.listener.db.ColumnValue;
import org.springcenter.marketing.modules.listener.db.Table;
import org.springcenter.marketing.modules.mapper.RUserExpandMapper;
import org.springcenter.marketing.modules.model.CclientVip;
import org.springcenter.marketing.modules.model.RUserExpand;
import org.springcenter.marketing.modules.service.IBLookUserTagPkgService;
import org.springcenter.marketing.modules.service.IClientVipService;
import org.springcenter.marketing.modules.service.IWashVoucherService;
import org.springcenter.marketing.modules.service.RightsV3Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/test/api")
public class TestController {


    @Autowired
    private RightsV3Service rightsV3Service;

    @Autowired
    private IWashVoucherService washVoucherService;


    @Autowired
    private IClientVipService iClientVipService;

    @Autowired
    private RUserExpandMapper rUserExpandMapper;

    @Resource(name = "intimacyAndConsumeListener")
    IMessageListener iMessageListener;

    @Autowired
    private CclientVipMapper cclientVipMapper;

    @Resource
    private IBLookUserTagPkgService ibLookUserTagPkgService;



    @ResponseBody
    @GetMapping("/flushJnbyCard")
    @ApiOperation(value = "刷江南布衣+金卡")
    public ResponseResult flushJnbyCard() throws UnsupportedEncodingException {
        log.info("刷江南布衣+金卡有问题用户开始");
        // 查询金卡的
        com.github.pagehelper.Page<Object> hPageTotal = PageHelper.startPage(1, 100);
        cclientVipMapper.selectByCardTypeIdAndIsActive();
        PageInfo<CclientVip> pageInfoTotal = new PageInfo(hPageTotal);
        long total = pageInfoTotal.getTotal();
        int pageSize = 100;
        long pageTotal = 0;
        if(total % pageSize == 0){
            pageTotal = total / pageSize;
        }else{
            pageTotal = total / pageSize + 1;
        }

        for(int i = 1 ; i <= pageTotal; i++){
            com.github.pagehelper.Page<Object> hPageTotal2 = PageHelper.startPage(i, pageSize);
            cclientVipMapper.selectByCardTypeIdAndIsActive();
            PageInfo<CclientVip> pageInfoTotal2 = new PageInfo(hPageTotal2);
            List<CclientVip> list = pageInfoTotal2.getList();
            for (CclientVip cclientVip : list) {
                Table table = new Table();
                List<ColumnValue> columns = new ArrayList<>();
                ColumnValue columnValue = new ColumnValue();
                columnValue.setColumn(new ColumnMeta("C_VIP_ID",1));
                columnValue.setValue(cclientVip.getId());
                columns.add(columnValue);
                table.setTableName("C_CLIENT_VIP");
                table.setColumns(columns);

                Message message = new Message();
                message.setTags("FA_VIPINTEGRAL_FTP");
                message.setBody(JSONObject.toJSONString(table).getBytes("UTF-8"));
                message.setKeys("testKey");
                iMessageListener.consume(message);
            }
            log.info("刷江南布衣+金卡有问题用户第 "+i+" 页");
        }
        log.info("刷江南布衣+金卡有问题用户结束");
        return ResponseResult.success();
    }


    @ResponseBody
    @GetMapping("/flushRedisCache")
    @ApiOperation(value = "刷新兜底缓存")
    public ResponseResult flushRedisCache(){
        rightsV3Service.flushCache();
        return ResponseResult.success();
    }



    @ResponseBody
    @GetMapping("/sendCouponJob")
    @ApiOperation(value = "凭证券定时任务发送jobtest")
    public ResponseResult sendCouponJob(){
        washVoucherService.sendCouponJob();
        return ResponseResult.success();
    }



    @ResponseBody
    @GetMapping("/notActiveSendMessageJob")
    @ApiOperation(value = "发送月初消息")
    public ResponseResult notActiveSendMessageJob(){
        washVoucherService.notActiveSendMessageJob();
        return ResponseResult.success();
    }


    @ResponseBody
    @GetMapping("/notActiveSendMessageEndJob")
    @ApiOperation(value = "发送月末消息 ")
    public ResponseResult notActiveSendMessageEndJob(){
        washVoucherService.notActiveSendMessageEndJob();
        return ResponseResult.success();
    }


    @ResponseBody
    @GetMapping("/expireUserMemberCard")
    @ApiOperation(value = "失效用户卡 ")
    public ResponseResult expireUserMemberCard(){
        rightsV3Service.expireUserMemberCard();
        return ResponseResult.success();
    }


    @ResponseBody
    @GetMapping("/resetUserRightsJob")
    @ApiOperation(value = "重置权益 ")
    public ResponseResult resetUserRightsJob(){
        rightsV3Service.resetUserRightsJob(3);
        return ResponseResult.success();
    }



    @ResponseBody
    @GetMapping("/resetUserCouponV3")
    @ApiOperation(value = "重置膨胀券的信息 ")
    public ResponseResult resetUserCouponV3(@RequestParam(value = "consumeOutNo",required =true) String consumeOutNo){
        rightsV3Service.resetUserCouponV3(consumeOutNo);
        return ResponseResult.success();
    }


    @ResponseBody
    @GetMapping("/expireWashVoucherByMonth")
    @ApiOperation(value = "失效因上个月领的券 没有使用的 导致提交不了预约 ")
    public ResponseResult expireWashVoucherByMonth(){
        washVoucherService.expireWashVoucherByMonth();
        return ResponseResult.success();
    }


    @ResponseBody
    @GetMapping("/testRUserExpandChangeByCard")
    @ApiOperation(value = "根据cardNo 更改 ")
    public ResponseResult testRUserExpandChangeByCard(@RequestParam(value = "cardNo") String cardno){
        IntimacyByVipIdResp intimacyByVipId = iClientVipService.getIntimacyByVipId(cardno);
        if(intimacyByVipId != null){
            log.info("consume = IntimacyListener = {}", JSONObject.toJSONString(intimacyByVipId));
            IntimacyByVipIdResp data = intimacyByVipId;
            String unionid = data.getUnionid();
            String weid = data.getWeid();
            RUserExpand rUserExpand = rUserExpandMapper.selectByUnionIdAndWeId(unionid,weid);
            if(rUserExpand != null){
                RUserExpand update = new RUserExpand();
                update.setId(rUserExpand.getId());
                if(data.getIntimacy().compareTo(BigDecimal.ZERO) > 0){
                    update.setIntimacy(data.getIntimacy());
                }else{
                    update.setIntimacy(BigDecimal.ZERO);
                }
                update.setUpdateTime(new Date());
                rUserExpandMapper.updateByPrimaryKeySelective(update);
            }else{
                RUserExpand insert = new RUserExpand();
                insert.setId(IdLeaf.getId(IdConstant.RUSEREXPAND));
                insert.setCClientVipId(Long.parseLong(data.getCVipId()));
                insert.setUnionid(unionid);
                insert.setWeid(weid);
                insert.setCreateTime(new Date());
                insert.setUpdateTime(new Date());
                if(data.getIntimacy().compareTo(BigDecimal.ZERO) > 0){
                    insert.setIntimacy(data.getIntimacy());
                }else{
                    insert.setIntimacy(BigDecimal.ZERO);
                }
                rUserExpandMapper.insertSelective(insert);
            }
        }
        return ResponseResult.success();
    }




    @ResponseBody
    @GetMapping("/testRUserExpandChangeByCVipId")
    @ApiOperation(value = "根据cvipid更改 ")
    public ResponseResult testRUserExpandChangeByCVipId(@RequestParam(value = "cVipId") String cVipId){
        ConsumeByVipIdResp consumeByVipId = iClientVipService.getConsumeByVipId(cVipId);
        if(consumeByVipId != null) {
            log.info("consume = consumeListener = {}", JSONObject.toJSONString(consumeByVipId));
            ConsumeByVipIdResp data = consumeByVipId;
            String unionid = data.getUnionid();
            String weid = data.getWeid();
            BigDecimal consume = data.getConsume();
            RUserExpand rUserExpand = rUserExpandMapper.selectByUnionIdAndWeId(unionid,weid);
            if(rUserExpand != null){
                RUserExpand update = new RUserExpand();
                update.setId(rUserExpand.getId());
                update.setUpdateTime(new Date());
                if(consume.compareTo(BigDecimal.ZERO) > 0){
                    update.setConsumePointMonth(consume);
                }else{
                    update.setConsumePointMonth(BigDecimal.ZERO);
                }
                rUserExpandMapper.updateByPrimaryKeySelective(update);
            }else{
                RUserExpand update = new RUserExpand();
                update.setId(IdLeaf.getId(IdConstant.RUSEREXPAND));
                update.setCClientVipId(Long.parseLong(cVipId));
                update.setUnionid(unionid);
                update.setWeid(weid);
                update.setCreateTime(new Date());
                update.setUpdateTime(new Date());
                if(consume.compareTo(BigDecimal.ZERO) > 0){
                    update.setConsumePointMonth(consume);
                }else{
                    update.setConsumePointMonth(BigDecimal.ZERO);
                }
                rUserExpandMapper.insertSelective(update);
            }
        }
        return ResponseResult.success();
    }



    @ResponseBody
    @GetMapping("/syncLogisticsStatus")
    @ApiOperation(value = "同步发收盒礼物流 ")
    public ResponseResult syncLogisticsStatus(@RequestParam(value = "id",required = false) String id ){
        rightsV3Service.syncLogisticsStatus(id);
        return ResponseResult.success();
    }

    @ResponseBody
    @PostMapping("/syncAllLookTag2Redis")
    @ApiOperation(value = "同步全量用户LOOK快赏操作记录到redis")
    public ResponseResult syncAllLookTag2Redis(){

        ibLookUserTagPkgService.syncAllDb2Redis();
        return ResponseResult.success();
    }

    @ResponseBody
    @PostMapping("/syncSingleLookTag2Redis")
    @ApiOperation(value = "同步单个用户LOOK快赏操作记录到redis ")
    public ResponseResult syncAllLookTag2Redis(@RequestBody CommonRequest<SyncSingleLookTag2RedisReq> request){
        SyncSingleLookTag2RedisReq requestData = request.getRequestData();
        ibLookUserTagPkgService.syncDb2Redis(requestData);
        return ResponseResult.success();
    }

}
