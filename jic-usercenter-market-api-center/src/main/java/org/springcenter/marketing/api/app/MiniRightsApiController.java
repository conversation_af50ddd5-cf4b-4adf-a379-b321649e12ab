package org.springcenter.marketing.api.app;

import com.alibaba.fastjson.JSONObject;
import com.jnby.common.CommonRequest;
import com.jnby.common.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springcenter.marketing.api.dto.*;
import org.springcenter.marketing.api.dto.miniapp.BrandCardMsgReq;
import org.springcenter.marketing.api.dto.miniapp.BrandCardMsgResp;
import org.springcenter.marketing.api.dto.miniapp.RightsV3ConfigResp;
import org.springcenter.marketing.api.enums.RedisKeysEnum;
import org.springcenter.marketing.modules.context.*;
import org.springcenter.marketing.modules.model.LessBookConfig;
import org.springcenter.marketing.modules.model.ScanCodeLog;
import org.springcenter.marketing.modules.service.BookService;
import org.springcenter.marketing.modules.service.IRightsService;
import org.springcenter.marketing.modules.service.IUserVipService;
import org.springcenter.marketing.modules.service.RightsV3Service;
import org.springcenter.marketing.modules.util.RedisService;
import org.springcenter.marketing.modules.webapi.IVoucherApi;
import org.springcenter.marketing.modules.wxMapper.ScanCodeLogMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import retrofit2.Response;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**macy
 *  权益小程序端
 */
@Slf4j
@RestController
@RequestMapping("/miniRights/api")
@RefreshScope
@Api(value = "MiniRightsApiController", tags = "权益小程序端")
public class MiniRightsApiController {

    @Autowired
    private IRightsService rightsService;

    @Autowired
    private RightsV3Service rightsV3Service;

    @Autowired
    IUserVipService iUserVipService;

    @Value("${wx.brand.commonConfigs}")
    private String commonConfigs;

    @Autowired
    private RedisService redisService;

    @Autowired
    private IVoucherApi voucherApi;

    @Autowired
    private BookService bookService;





    /**
     * 刷卡上的权益信息到redis中
     * 1. 集团卡权益
     * 2. 品牌卡的权益
     * 3. 整合
     */
    @ResponseBody
    @PostMapping("/flushCardRightsToRedis")
    @ApiOperation(value = "刷卡上的权益信息到redis中")
    public ResponseResult flushCardRightsToRedis(){
        rightsService.flushCardRightsToRedis();
        return ResponseResult.success();
    }

    @ResponseBody
    @PostMapping("/getRightsLight")
    @ApiOperation(value = "获取用户的卡的权益")
    public ResponseResult<List<RightsLightDto>> getRightsLight(@RequestBody CommonRequest<GetRightsLightDto> request){
        List<RightsLightDto> list = rightsService.getRightsLight(request.getRequestData());
        return ResponseResult.success(list);
    }

    /**
     * 用户进入权益详情页  存储数据
     */
    @ResponseBody
    @PostMapping("/clickUserRights")
    @ApiOperation(value = "用户进入权益详情页   (点击之后  进行图标不显示 new)")
    public ResponseResult clickUserRights(@Validated @RequestBody CommonRequest<ClickUserRightsDto> request){
        rightsService.clickUserRights(request.getRequestData());
        return ResponseResult.success();
    }


    @ResponseBody
    @GetMapping("/cardSort")
    @ApiOperation(value = "更多品牌卡类顺序")
    public ResponseResult<List<BrandConfigDto>> cardSort(){
        List<BrandConfigDto> brandConfigs = JSONObject.parseArray(commonConfigs, BrandConfigDto.class);
        return ResponseResult.success(brandConfigs);
    }



    /**
     * 获取集团卡和品牌卡
     */
    @ResponseBody
    @PostMapping("/getUserClientVip")
    @ApiOperation(value = "获取集团卡和品牌卡  有数据情况下， 第一张为集团卡， 其他为品牌卡")
    public ResponseResult<List<MemberCardEntity>> getUserClientVip(@Validated @RequestBody CommonRequest<GetUserClientVipReq> request){
        List<MemberCardEntity> result = new ArrayList<>();

        GetUserClientVipReq requestData = request.getRequestData();
//        MemberCardQueryContext req = new MemberCardQueryContext();
//        req.setUnionId(requestData.getUnionid());
//        req.setStatus("Y");
//        CustomerBaseResponse<MemberCardEntity> memberCard = iUserVipService.getMemberCard(req);
//        if(memberCard != null && memberCard.getData() != null){
//            result.add(memberCard.getData());
//        }
        MemberQueryContext memberQueryContext = new MemberQueryContext();
        memberQueryContext.setUnionId(requestData.getUnionid());
        memberQueryContext.setStatus("Y");
        CustomerBaseResponse<List<MemberCardEntity>> memberCardList = iUserVipService.getMemberCardList(memberQueryContext);
        if(memberCardList != null && CollectionUtils.isNotEmpty(memberCardList.getData())){
            result.addAll(memberCardList.getData());
        }

        return ResponseResult.success(result);
    }

    @ResponseBody
    @PostMapping("/getUserBaseInfo")
    @ApiOperation(value = "获取用户基本信息  卡  less亲密值 积分 等")
    public ResponseResult<GetUserBaseInfoResp> getUserBaseInfo(@Validated @RequestBody CommonRequest<GetUserBaseInfoReq> request){
        return ResponseResult.success(rightsService.getUserBaseInfo(request.getRequestData()));
    }




    @ResponseBody
    @PostMapping("/saveUserNameAndHeadImg")
    @ApiOperation(value = "保存用户头像和昵称到redis中")
    public ResponseResult saveUserNameAndHeadImg(@Validated @RequestBody CommonRequest<UserNameAndHeadImgReq> request){
        rightsService.saveUserNameAndHeadImg(request.getRequestData());
        return ResponseResult.success();
    }


    @ResponseBody
    @PostMapping("/getUserNameAndHeadImg")
    @ApiOperation(value = "保存用户头像和昵称到redis中")
    public ResponseResult<UserNameAndHeadImgReq> getUserNameAndHeadImg(@Validated @RequestBody CommonRequest<UserNameAndHeadImgReq> request){
        return ResponseResult.success(rightsService.getUserNameAndHeadImg(request.getRequestData()));
    }



    @ResponseBody
    @PostMapping("/getWeimoMemberIsOpenCard")
    @ApiOperation(value = "查询微盟会员是否开通了当前品牌的卡")
    public ResponseResult<Boolean> getWeimoMemberIsOpenCard(@Validated @RequestBody CommonRequest<GetWeimoMemberIsOpenCardReq> request){
//        String key = RedisKeysEnum.WEIMOCARDOPEN.join(request.getRequestData().getWid(),request.getRequestData().getBrandId());
//        Object flag = redisService.get(key);
//        if(flag != null){
//            return ResponseResult.success(true);
//        }
        Boolean weimoMemberIsOpenCard = rightsService.getWeimoMemberIsOpenCard(request.getRequestData());
//        if(weimoMemberIsOpenCard){
//            redisService.set(key,weimoMemberIsOpenCard,7200);
//        }
        return ResponseResult.success(weimoMemberIsOpenCard);
    }




    @ResponseBody
    @PostMapping("/getRightsV3Config")
    @ApiOperation(value = "获取配置的收盒礼数量  优惠券数量 优惠券金额")
    public ResponseResult<RightsV3ConfigResp> getRightsV3Config(@Validated @RequestBody CommonRequest<RightsV3ConfigReq> request){
        RightsV3ConfigResp resp = rightsV3Service.getRightsV3Config(request.getRequestData());
        return ResponseResult.success(resp);
    }



    @ResponseBody
    @PostMapping("/getIsSendBoxGift")
    @ApiOperation(value = " 前台是否弹出话    true  弹出   false  不弹出这句话")
    public ResponseResult<Boolean> getIsSendBoxGift(@Validated @RequestBody CommonRequest<SendBoxGiftFlagReq> request){
        Boolean flag = rightsV3Service.getIsSendBoxGift(request.getRequestData());
        return ResponseResult.success(flag);
    }


    @ResponseBody
    @PostMapping("/getWeimoMemberCardNo")
    @ApiOperation(value = "查询微盟会员卡号")
    public ResponseResult<GetWeimoCardInfoResp> getWeimoMemberCardNo(@Validated @RequestBody CommonRequest<GetWeimoMemberIsOpenCardReq> request){
        return ResponseResult.success(rightsService.getWeimoMemberCardNo(request.getRequestData()));
    }




    @ResponseBody
    @PostMapping("/brandCardMsg")
    @ApiOperation(value = "会员卡信息 是否该品牌点亮 列表")
    public ResponseResult<List<BrandCardMsgResp>> brandCardMsg(@Validated @RequestBody CommonRequest<BrandCardMsgReq> request){
        return ResponseResult.success(rightsService.brandCardMsg(request.getRequestData()));
    }




    @ResponseBody
    @PostMapping("/getVoucherDetailsByVouchers")
    @ApiOperation(value = "根据多个券号查询券详情")
    public ResponseResult<List<VoucherRuleDetailsEntity>> getVoucherDetailsByVouchers(@Validated @RequestBody CommonRequest<VoucherDetailsListContext> request) throws IOException {
        Response<JICCommonVouResponse<List<VoucherRuleDetailsEntity>>> execute = voucherApi.voucherDetailsList(request.getRequestData()).execute();
        boolean successful = execute.isSuccessful();
        if(successful){
            List<VoucherRuleDetailsEntity> data = execute.body().getData();
            return ResponseResult.success(data);
        }
        return ResponseResult.error(-1,"查询优惠券详情失败!");
    }


    @ResponseBody
    @PostMapping("/list")
    @ApiOperation(value = "列表信息")
    public ResponseResult<LessBookConfig> list(){
        LessBookConfig lessBookConfig = bookService.list();
        return ResponseResult.success(lessBookConfig);
    }


    @ResponseBody
    @PostMapping("/addScanCodeLog")
    @ApiOperation(value = "添加日志")
    public ResponseResult addScanCodeLog(@RequestBody CommonRequest<ScanCodeLog> request){
        rightsV3Service.addScanCodeLog(request.getRequestData());
        return ResponseResult.success();
    }

}
