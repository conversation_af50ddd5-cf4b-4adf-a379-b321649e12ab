package org.springcenter.marketing.api.miniapp;

import com.jnby.common.CommonRequest;
import com.jnby.common.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springcenter.marketing.api.dto.admin.SendMaterialById;
import org.springcenter.marketing.api.dto.miniapp.ActivityAwardListResp;
import org.springcenter.marketing.modules.service.ThemeActivityService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/mini/themeActivity")
@Api(value = "miniThemeActivityController", tags = "互动留言板c端接口")
public class MiniThemeActivityController {

    @Autowired
    private ThemeActivityService themeActivityService;

    @ResponseBody
    @PostMapping("/updateMaterialAddress")
    @ApiOperation(value = "修改实物奖励地址  c端")
    public ResponseResult updateMaterialAddress(@RequestBody @Validated CommonRequest<SendMaterialById> request) {
        try {
            themeActivityService.updateMaterialAddress(request.getRequestData());
        }catch (Exception e){
            log.info("修改实物奖励地址 出现错误",e);
        }
        return ResponseResult.success();
    }


    // 活动结束后的奖励列表  当前活动的中奖信息
    @ResponseBody
    @PostMapping("/getActivityAwardList")
    @ApiOperation(value = "查询活动结束后的奖励信息   requestData里面直接传递 活动id ")
    public ResponseResult<List<ActivityAwardListResp>> getActivityAwardList(@RequestBody @Validated CommonRequest<String> request) {
        try {
            List<ActivityAwardListResp> activityAwardListResps = themeActivityService.getActivityAwardList(request.getRequestData());
            return ResponseResult.success(activityAwardListResps);
        }catch (Exception e){
            log.info("查询活动结束后的奖励信息 出现错误",e);
        }
        return ResponseResult.success(null);
    }


}
