package org.springcenter.marketing.api;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.jnby.common.CommonRequest;
import com.jnby.common.Page;
import com.jnby.common.ResponseResult;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springcenter.marketing.api.dto.*;
import org.springcenter.marketing.api.dto.admin.ClickShowReq;
import org.springcenter.marketing.api.dto.admin.ClickShowResp;
import org.springcenter.marketing.api.dto.admin.DeleteRightsByIdReq;
import org.springcenter.marketing.api.dto.admin.ExtendJsonForRightsResp;
import org.springcenter.marketing.api.enums.RightsTypeEnum;
import org.springcenter.marketing.api.enums.SuitableStoreTypeEnum;
import org.springcenter.marketing.modules.model.*;
import org.springcenter.marketing.modules.repository.ICStoreRepository;
import org.springcenter.marketing.modules.repository.IRightsRepository;
import org.springcenter.marketing.modules.service.IRightsService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springcenter.marketing.modules.service.RightsV3Service;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/marketing/api")
@RefreshScope
public class MarketingApiController {

    @Autowired
    private IRightsRepository iRightsRepository;

    @Autowired
    private IRightsService rightsService;

    @Autowired
    private RightsV3Service rightsV3Service;

    @Autowired
    private ICStoreRepository icStoreRepository;

    @Value("${rights.extend.json}")
    private String extendJson;



    @ResponseBody
    @PostMapping("/create")
    @ApiOperation(value = "特权创建&修改")
    public ResponseResult create(@RequestBody CommonRequest<RightsCreateReq> request){

        RightsCreateReq requestData = Optional.ofNullable(request.getRequestData()).orElseThrow(() -> new IllegalArgumentException("请求参数不可为空"));
        // 设置参数
        if(StringUtils.isBlank(requestData.getUpdateBy())){
            requestData.setUpdateBy(request.getUser_id());
        }
        if(SuitableStoreTypeEnum.CUSTOMIZE.getCode().equals(requestData.getSuitableStoreType())
                && CollectionUtils.isNotEmpty(requestData.getStoreIds())){
            // 如果是自定义商家
            List<String> storeIds = requestData.getStoreIds();
            List<CStore> cStores  = new ArrayList<>();
            // 批量查询
            if(requestData.getIsStoreId() == 1){
                List<Long> storeIdsLong = new ArrayList<>();
                for (String storeId : storeIds) {
                    storeIdsLong.add(Long.valueOf(storeId));
                }

                List<List<Long>> partition = Lists.partition(storeIdsLong, 90);
                for (List<Long> longs : partition) {
                    cStores.addAll(icStoreRepository.selectCStoreByIds(longs));
                }
            }else if(requestData.getIsStoreId() == 0){
                List<List<String>> partition = Lists.partition(storeIds, 90);
                for (List<String> stringList : partition) {
                    cStores.addAll(icStoreRepository.selectListByCodes(stringList));
                }
            }else if(requestData.getIsStoreId() == 2){
                for (String storeId : storeIds) {
                    CStore cStore = new CStore();
                    cStore.setId(Long.valueOf(storeId));
                    cStores.add(cStore);
                }
            }
            List<CStoreDto> cStoreDtos = JSONObject.parseArray(JSONObject.toJSONString(cStores), CStoreDto.class);
            request.getRequestData().setList(cStoreDtos);
        }

        iRightsRepository.createOrUpdateRights(request.getRequestData());
        try {
            rightsService.flushCardRightsToRedis();
        }catch (Exception e){
            log.error("e",e);
        }
        return ResponseResult.success();
    }



    @ResponseBody
    @PostMapping("/copyRightsById")
    @ApiOperation(value = "根据权益id进行copy权益  requestData 里面直接传递权益id")
    public ResponseResult copyRightsById(@RequestBody CommonRequest<String> request){
        iRightsRepository.copyRightsById(request.getRequestData(),request.getUser_id());
        return ResponseResult.success();
    }


    @ResponseBody
    @RequestMapping(value = "/detail",method = RequestMethod.POST)
    @ApiOperation(value = "特权详情")
    public ResponseResult<RightsDetailResp> detail(@RequestBody CommonRequest<QueryRightDetailReq> request){
        QueryRightDetailReq req = request.getRequestData();
        BRights rights = iRightsRepository.findById(req.getId());
        RightsDetailResp resp = new RightsDetailResp();
        if(SuitableStoreTypeEnum.CUSTOMIZE.getCode().equals(rights.getSuitableStoreType())){
            // 查询数据
            List<String> storeIds = iRightsRepository.findCustomerStroeIdsByRightId(rights.getId());
            resp.setStoreIds(storeIds);
        }

        BeanUtils.copyProperties(rights, resp);
        // 获取权益包id
        List<String> rightsIds = new ArrayList<>();
        rightsIds.add(rights.getId());
        List<RightsMemberCardRelation> relationByRightsIds = rightsService.findRelationByRightsIds(rightsIds);
        resp.setMemberCardIds(relationByRightsIds.stream().map(r->r.getMemberCardId()).collect(Collectors.toList()));

//        resp.setMemberCardId(rights.getbMemberCardId());
        //处理
        if(RightsTypeEnum.BIRTH_COUPON.getCode().equals(resp.getRightsType().intValue())){
            BirthCouponReq birthCouponReq  = JSONObject.parseObject(resp.getUseRule(), BirthCouponReq.class);
            BeanUtils.copyProperties(birthCouponReq,resp);
            resp.setUseRule(JSONObject.toJSONString(birthCouponReq.getRule()));
        }

        if(RightsTypeEnum.COUPON.getCode().equals(resp.getRightsType().intValue())){
            CouponReq couponReq  = JSONObject.parseObject(resp.getUseRule(), CouponReq.class);
            BeanUtils.copyProperties(couponReq,resp);
            resp.setUseRule(JSONObject.toJSONString(couponReq.getRule()));
        }

        return ResponseResult.success(resp);
    }


    @ResponseBody
    @PostMapping("/list")
    @ApiOperation(value = "特权列表")
    public ResponseResult<List<RightsDetailResp>> list(@RequestBody CommonRequest<RightsCreateReq> request){
        RightsCreateReq requestData = request.getRequestData();
        Page page = request.getPage();
        BRights rights = new BRights();
        if(StringUtils.isNotBlank(requestData.getName())){
            requestData.setName(requestData.getName().toUpperCase());
        }
        BeanUtils.copyProperties(requestData, rights);
        rights.setbMemberCardId(requestData.getMemberCardId());

        com.github.pagehelper.Page<BRights> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        iRightsRepository.findRights(rights);
        PageInfo<BRights> pageInfo = new PageInfo<>(hPage);
        page.setCount(hPage.getTotal());
        page.setPages(pageInfo.getPages());

        List<RightsDetailResp> list = new ArrayList<>();

        List<BRights> list1 = pageInfo.getList();
        if(CollectionUtils.isNotEmpty(list1)){
            List<String> ids = list1.stream().map(r -> r.getId()).collect(Collectors.toList());
            List<BRights> result = iRightsRepository.findByIds(ids);
            List<RightsMemberCardRelation> rightsMemberCardRelations = rightsService.findRelationByRightsIds(ids);
            Map<String, List<RightsMemberCardRelation>> collect = rightsMemberCardRelations.stream().collect(Collectors.groupingBy(r -> r.getRightsId()));

            list= result.stream().map(item -> {
                RightsDetailResp resp = new RightsDetailResp();
                BeanUtils.copyProperties(item, resp);
                if(CollectionUtils.isNotEmpty(collect.get(item.getId()))){
                    resp.setMemberCardIds(collect.get(item.getId()).stream().map(r->r.getMemberCardId()).collect(Collectors.toList()));
                    resp.setBelongNumber((long)collect.get(item.getId()).size());
                }else{
                    resp.setMemberCardIds(new ArrayList<>());
                    resp.setBelongNumber(0L);
                }
                return resp;
            }).collect(Collectors.toList());
        }
        return ResponseResult.success(list, page);
    }


    @ResponseBody
    @PostMapping("/clickShow")
    @ApiOperation(value = "点击查询名称信息")
    public ResponseResult<List<ClickShowResp>> clickShow(@RequestBody CommonRequest<ClickShowReq> request){
        List<ClickShowResp> list = rightsService.clickShow(request.getRequestData());
        return ResponseResult.success(list);
    }

    //删除权益  需要把关联关系也要删除
    @ResponseBody
    @PostMapping("/deleteById")
    @ApiOperation(value = "根据id删除权益信息 需要把关联关系也要删除")
    public ResponseResult deleteById(@RequestBody CommonRequest<DeleteRightsByIdReq> request){
        rightsService.deleteById(request.getRequestData());
        return ResponseResult.success();
    }


    @ApiOperation(value = " 根据参数获取权益配置信息")
    @RequestMapping(value = "/rights/getRights",method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult<List<RightsResp>> getRights(@RequestBody  CommonRequest<GetRightsReq> commonRequest){
        GetRightsReq requestData = commonRequest.getRequestData();
        if(requestData == null){
            return ResponseResult.success(new ArrayList<>());
        }
        List<RightsResp> list = iRightsRepository.getRights(requestData);
        return ResponseResult.success(list);
    }


    @ApiOperation(value = " 根据参数查询所有权益")
    @RequestMapping(value = "/findAll",method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult<List<BRightsDto>> findAll(@RequestBody  CommonRequest<BRightsDto> request){
        BRightsDto requestData = request.getRequestData();
        if(requestData == null){
            return ResponseResult.success(new ArrayList<>());
        }
        BRights params = new BRights();
        BeanUtils.copyProperties(requestData,params);
        List<BRights> list = iRightsRepository.findAll(params);
        List<BRightsDto> result = new ArrayList<>();
        for (BRights bRights : list) {
            BRightsDto bRightsDto = new BRightsDto();
            BeanUtils.copyProperties(bRights,bRightsDto);
            result.add(bRightsDto);
        }
        return ResponseResult.success(result);
    }



    @ResponseBody
    @PostMapping("/listForCardLog")
    @ApiOperation(value = "开卡记录列表")
    public ResponseResult<List<BUserMemberCardLogDto>> listForCardLog(@RequestBody CommonRequest<ListForBUserMemberCardLogReq> request){
        ListForBUserMemberCardLogReq requestData = request.getRequestData();
        Page page = request.getPage();
        List<BUserMemberCardLog> list = rightsV3Service.listForBUserMemberCardLog(requestData,page);
        List<BUserMemberCardLogDto> result = new ArrayList<>();
        for (BUserMemberCardLog bUserMemberCardLog : list) {
            BUserMemberCardLogDto bUserMemberCardLogDto = new BUserMemberCardLogDto();
            BeanUtils.copyProperties(bUserMemberCardLog,bUserMemberCardLogDto);
            result.add(bUserMemberCardLogDto);
        }
        return ResponseResult.success(result,page);
    }


    @ResponseBody
    @PostMapping("/cardTypeAllCondition")
    @ApiOperation(value = "获取该品牌的  品牌卡等级")
    public ResponseResult<List<CVipType>> cardTypeAllCondition(){
        return ResponseResult.success(rightsV3Service.cardTypeAllCondition());
    }



    @ResponseBody
    @PostMapping("/getExtendJsonRights")
    @ApiOperation(value = "额外信息配置  新会员体系升级")
    public ResponseResult<List<ExtendJsonForRightsResp>> getExtendJsonRights(){
        return ResponseResult.success(JSONObject.parseArray(extendJson,ExtendJsonForRightsResp.class));
    }

}
