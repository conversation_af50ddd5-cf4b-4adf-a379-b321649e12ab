package org.springcenter.marketing.api.admin;

import com.jnby.common.CommonRequest;
import com.jnby.common.Page;
import com.jnby.common.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springcenter.marketing.api.dto.admin.*;
import org.springcenter.marketing.modules.service.IBoxGiftService;
import org.springcenter.marketing.modules.service.IRightsService;
import org.springcenter.marketing.modules.service.RightsV3Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/adminRights/api")
@RefreshScope
@Api(value = "adminRightsController", tags = "权益后台接口")
public class AdminRightsController {

    @Autowired
    private IBoxGiftService boxGiftService;

    @Autowired
    private RightsV3Service rightsV3Service;


    @ResponseBody
    @PostMapping("/getBoxGiftList")
    @ApiOperation(value = "获取 待发货和已发货")
    public ResponseResult<List<GetBoxGiftListResp>> getBoxGiftList(@Validated @RequestBody CommonRequest<GetBoxGiftListReq> request){
        Page page = request.getPage();
        List<GetBoxGiftListResp> result = boxGiftService.getBoxGiftList(request.getRequestData(),page);
        return ResponseResult.success(result,page);
    }



    @ResponseBody
    @PostMapping("/reSendMaterial")
    @ApiOperation(value = "重推仓库制单")
    public ResponseResult reSendMaterial(@Validated @RequestBody CommonRequest<ReSendMaterialReq> request){
        rightsV3Service.reSendMaterial(request.getRequestData());
        return ResponseResult.success();
    }


    // 更新收盒礼数据  更改 地址信息  和 ebnum  ebnum清空  更新为null
    @ResponseBody
    @PostMapping("/updateBoxGiftParams")
    @ApiOperation(value = "更新收盒礼数据  更改 地址信息  和 ebnum")
    public ResponseResult updateBoxGiftParams(@Validated @RequestBody CommonRequest<UpdateBoxGiftParamsReq> request){
        rightsV3Service.updateBoxGiftParams(request.getRequestData());
        return ResponseResult.success();
    }



}
