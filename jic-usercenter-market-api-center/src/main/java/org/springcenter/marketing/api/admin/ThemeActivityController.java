package org.springcenter.marketing.api.admin;

import com.jnby.common.CommonRequest;
import com.jnby.common.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springcenter.marketing.api.dto.admin.*;
import org.springcenter.marketing.modules.service.ThemeActivityService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/admin/themeActivity")
@Api(value = "themeActivityController", tags = "less互动留言板后台接口")
public class ThemeActivityController {

    @Autowired
    private ThemeActivityService themeActivityService;


    @ResponseBody
    @PostMapping("/saveOrUpdateListAward")
    @ApiOperation(value = " 添加 或者 编辑  或者删除一批奖励信息")
    public ResponseResult<List<Integer>> saveOrUpdateListAward(@RequestBody @Validated CommonRequest<SaveOrUpdateListAwardReq> request){
        return ResponseResult.success(themeActivityService.saveOrUpdateListAward(request.getRequestData()));
    }


    @ResponseBody
    @PostMapping("/findAwardByIds")
    @ApiOperation(value = "根据ids查询奖励信息")
    public ResponseResult<List<SaveOrUpdateListAwardReq.ThemeActivityDaysRuleAwardReq>> findAwardByIds(@RequestBody @Validated CommonRequest<List<Integer>> request) {
        return ResponseResult.success(themeActivityService.findAwardByIds(request.getRequestData()));
    }




    @ResponseBody
    @PostMapping("/saveOrUpdateActivity")
    @ApiOperation(value = "新增或者编辑活动信息")
    public ResponseResult saveOrUpdateActivity(@RequestBody @Validated CommonRequest<ThemeActivityReq> request) {
        themeActivityService.saveOrUpdateActivity(request.getRequestData());
        return ResponseResult.success();
    }



    @ResponseBody
    @PostMapping("/activityList")
    @ApiOperation(value = "活动列表")
    public ResponseResult<List<ThemeActivityReq>> activityList(@RequestBody @Validated CommonRequest<ActivityListReq> request) {
        List<ThemeActivityReq> respList = themeActivityService.activityList(request.getRequestData(),request.getPage());
        return ResponseResult.success(respList, request.getPage());
    }


    @ResponseBody
    @PostMapping("/deleteActivity")
    @ApiOperation(value = "删除活动requestData里面直接传递id")
    public ResponseResult deleteActivity(@RequestBody @Validated CommonRequest<Integer> request) {
        themeActivityService.deleteActivity(request.getRequestData());
        return ResponseResult.success();
    }


    @ResponseBody
    @PostMapping("/updateStatusById")
    @ApiOperation(value = "根据id变更暂停开始状态")
    public ResponseResult updateStatusById(@RequestBody @Validated CommonRequest<UpdateStatusById> request) {
        themeActivityService.updateStatusById(request.getRequestData());
        return ResponseResult.success();
    }



    @ResponseBody
    @PostMapping("/userAwardList")
    @ApiOperation(value = "用户奖励列表  仅在活动结束后 才会自动产生 ")
    public ResponseResult<List<UserAwardListResp>> userAwardList(@RequestBody @Validated CommonRequest<UserAwardListReq> request) {
        List<UserAwardListResp> list = themeActivityService.userAwardList(request.getRequestData(),request.getPage());
        return ResponseResult.success(list,request.getPage());
    }



    @ResponseBody
    @PostMapping("/sendCouponOrPoint")
    @ApiOperation(value = "一键发奖， 积分 券  (当前接口也可以支持 单独一个发放）")
    public ResponseResult sendCouponOrPoint(@RequestBody @Validated CommonRequest<SendCouponOrPointDto> request) {
        try {
            themeActivityService.sendCouponOrPoint(request.getRequestData());
        }catch (Exception e){
            log.info("一键发奖 出现错误",e);
            return ResponseResult.error(-1,e.getMessage());
        }
        return ResponseResult.success();
    }


    @ResponseBody
    @PostMapping("/showSendPointButtonAndSendCouponButton")
    @ApiOperation(value = "判断展示 是否展示 一键发放积分按钮和一键发放券按钮")
    public ResponseResult<ShowSendPointButtonAndSendCouponButton> showSendPointButtonAndSendCouponButton(@RequestBody @Validated CommonRequest<SendCouponOrPointDto> request) {
        try {
            ShowSendPointButtonAndSendCouponButton showSendPointButtonAndSendCouponButton =
                    themeActivityService.showSendPointButtonAndSendCouponButton(request.getRequestData());
            return ResponseResult.success(showSendPointButtonAndSendCouponButton);
        }catch (Exception e){
            log.info("判断展示 是否展示 一键发放积分按钮和一键发放券按钮 出现错误",e);
        }
        return ResponseResult.success(new ShowSendPointButtonAndSendCouponButton());
    }




    @ResponseBody
    @PostMapping("/sendMaterialById")
    @ApiOperation(value = "根据id发送实物奖励")
    public ResponseResult sendMaterialById(@RequestBody @Validated CommonRequest<SendMaterialById> request) {
        try {
            themeActivityService.sendMaterialById(request.getRequestData());
        }catch (Exception e){
            log.info("根据id发送实物奖励 出现错误",e);
            return ResponseResult.error(-1,e.getMessage());
        }
        return ResponseResult.success();
    }


    @ResponseBody
    @PostMapping("/updateMaterialAddress")
    @ApiOperation(value = "修改实物奖励地址")
    public ResponseResult updateMaterialAddress(@RequestBody @Validated CommonRequest<SendMaterialById> request) {
        try {
            themeActivityService.updateMaterialAddress(request.getRequestData());
        }catch (Exception e){
            log.info("修改实物奖励地址 出现错误",e);
        }
        return ResponseResult.success();
    }




    @ResponseBody
    @PostMapping("/exportUserAwardList")
    @ApiOperation(value = "根据参数    进行导出excel  导出用户获奖名单")
    public ResponseResult<String> exportUserAwardList(@RequestBody CommonRequest<UserAwardListReq> request){
        String url = themeActivityService.exportUserAwardList(request.getRequestData());
        return ResponseResult.success(url);
    }



    @ResponseBody
    @PostMapping("/importSendMaterialUserAward")
    @ApiOperation(value = "根据参数进行发放用户奖励  仅发放实物   (仅发放没有发放奖励的人，有奖励的不进行发放)")
    public ResponseResult<String> importSendMaterialUserAward(@RequestBody CommonRequest<String> request){
        String url = themeActivityService.importSendMaterialUserAward(request.getRequestData());
        return ResponseResult.success(url);
    }



    @ResponseBody
    @PostMapping("/genUserAwardJob")
    @ApiOperation(value = "查询已经结束的活动 进行生成")
    public ResponseResult genUserAwardJob(){
        themeActivityService.genUserAwardJob();
        return ResponseResult.success();
    }

}
