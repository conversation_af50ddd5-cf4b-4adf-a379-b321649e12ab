package org.springcenter.marketing.config;

import brave.http.HttpTracing;
import brave.okhttp3.TracingInterceptor;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import okhttp3.ConnectionPool;
import okhttp3.OkHttpClient;
import okhttp3.logging.HttpLoggingInterceptor;
import org.springcenter.marketing.config.typeAdapter.DoubleDefaultAdapter;
import org.springcenter.marketing.config.typeAdapter.IntegerDefaultAdapter;
import org.springcenter.marketing.config.typeAdapter.LongDefaultAdapter;
import org.springcenter.marketing.modules.webapi.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;

import javax.annotation.Resource;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Configuration
@RefreshScope
public class RetrofitConfig {


    @Value("${jic.express.url}")
    private String jicExpressUrl;


    @Value("${third.url}")
    private String thirdUrl;



    @Value("${wy.aicall.service}")
    private String wyAicallUrl;


    @Value("${qiyu.call.service}")
    private String qiyuCallService;

    @Value("${new.people.url}")
    private String newPropelUrl;



    @Value("${bigdata.url}")
    private String bigDataUrl;


    /**
     * 注入托管 tracing
     */
    @Resource
    private HttpTracing httpTracing;

    /**
     * 增加请求返回""和"null"的处理
     * 1.Integer=>null
     * 2.Double=>null
     * 3.Long=>null
     */
    private static Gson gson = new GsonBuilder()
            .registerTypeAdapter(Integer.class, new IntegerDefaultAdapter())
            .registerTypeAdapter(Double.class, new DoubleDefaultAdapter())
            .registerTypeAdapter(Long.class, new LongDefaultAdapter()).create();


    /**
     * 定义Retrofit Map集合
     */
    private static Map<String, Retrofit> retrofitMap = new ConcurrentHashMap<>();


    /**
     * 获取Bean
     *
     * @param url
     * @return
     */
    public Retrofit getRetrofitBean(String url) {
        if (retrofitMap.containsKey(url)) {
            return retrofitMap.get(url);
        }

        //声明日志类
        HttpLoggingInterceptor httpLoggingInterceptor = new HttpLoggingInterceptor();
        //设定日志级别
        httpLoggingInterceptor.setLevel(HttpLoggingInterceptor.Level.BODY);

        int ioWorkerCount = 200;


        Retrofit retrofit = new Retrofit.Builder()
                .baseUrl(url)
                .client(
                        new OkHttpClient.Builder()
                                // 设置完整调用的默认超时。
                                .connectTimeout(5, TimeUnit.SECONDS)
                                .readTimeout(5, TimeUnit.SECONDS)
                                .writeTimeout(5, TimeUnit.SECONDS)
                                // 不重试
                                .retryOnConnectionFailure(false)
                                .connectionPool(new ConnectionPool(ioWorkerCount, 4, TimeUnit.MINUTES))
                                .addInterceptor(TracingInterceptor.create(httpTracing))
                                .addInterceptor(httpLoggingInterceptor)
                                .build())
                .addConverterFactory(GsonConverterFactory.create(gson))
                .build();
        retrofitMap.put(url, retrofit);
        return retrofit;
    }




    /**
     * 获取Bean(无cache)
     * @param url
     * @param callTimeOut 设置完整调用的默认超时
     * @return
     */
    public Retrofit getNoCacheRetrofitBean(String url, Long callTimeOut) {
        //声明日志类
        HttpLoggingInterceptor httpLoggingInterceptor = new HttpLoggingInterceptor();
        //设定日志级别
        httpLoggingInterceptor.setLevel(HttpLoggingInterceptor.Level.BODY);

        Retrofit retrofit = new Retrofit.Builder()
                .baseUrl(url)
                .client(
                        new OkHttpClient.Builder()
                                // 设置完整调用的默认超时。
                                .callTimeout(callTimeOut, TimeUnit.SECONDS)
                                // 不重试
                                .retryOnConnectionFailure(false)
                                .addInterceptor(httpLoggingInterceptor)
                                .build())
                .addConverterFactory(GsonConverterFactory.create(gson))
                .build();
        return retrofit;
    }



    /**
     * 获取Bean(无cache)
     * @param url
     * @param connectTimeout 连接时长
     * @param readTimeout 读取时长
     * @param writeTimeout 写入时长
     * @return
     */
    public Retrofit getNoCacheRetrofitBean(String url, Long connectTimeout, Long readTimeout, Long writeTimeout) {
        //声明日志类
        HttpLoggingInterceptor httpLoggingInterceptor = new HttpLoggingInterceptor();
        //设定日志级别
        httpLoggingInterceptor.setLevel(HttpLoggingInterceptor.Level.BODY);

        Retrofit retrofit = new Retrofit.Builder()
                .baseUrl(url)
                .client(
                        new OkHttpClient.Builder()
                                .connectTimeout(connectTimeout, TimeUnit.SECONDS)
                                .readTimeout(readTimeout, TimeUnit.SECONDS)
                                .writeTimeout(writeTimeout, TimeUnit.SECONDS)
                                // 不重试
                                .retryOnConnectionFailure(false)
                                .addInterceptor(httpLoggingInterceptor)
                                .build())
                .addConverterFactory(GsonConverterFactory.create(gson))
                .build();
        return retrofit;
    }


    /**
     * 获取Bean(无cache)
     *
     * @param url
     * @param connectCount 连接池 空闲数
     * @param callTimeOut  设置完整调用的默认超时
     * @return
     */
    public Retrofit getNoCacheRetrofitBean(String url, Integer connectCount, Integer callTimeOut) {
        //声明日志类
        HttpLoggingInterceptor httpLoggingInterceptor = new HttpLoggingInterceptor();
        //设定日志级别
        httpLoggingInterceptor.setLevel(HttpLoggingInterceptor.Level.BODY);

        Retrofit retrofit = new Retrofit.Builder()
                .baseUrl(url)
                .client(
                        new OkHttpClient.Builder()
                                // 设置完整调用的默认超时。
                                .callTimeout(callTimeOut, TimeUnit.SECONDS)
                                // 不重试
                                .retryOnConnectionFailure(false)
                                // 连接池 最大空闲个数和保活时间
                                .connectionPool(new ConnectionPool(connectCount, 3, TimeUnit.MINUTES))
                                .addInterceptor(TracingInterceptor.create(httpTracing))
                                .addInterceptor(httpLoggingInterceptor)
                                .build())
                .addConverterFactory(GsonConverterFactory.create(gson))
                .build();
        return retrofit;
    }


    /**
     * 获取API
     *
     * @param url     地址
     * @param service
     * @param <T>
     * @return
     */
    public <T> T getApi(String url, Class<T> service) {
        return getRetrofitBean(url).create(service);
    }


    @Bean
    public ICustomerVipHttpApi createICustomerVipHttpApi() {
        return getApi(jicExpressUrl, ICustomerVipHttpApi.class);
    }


    @Bean
    public IPeopleInfoHttpApi createIPeopleInfoHttpApi() {
        return getApi(thirdUrl, IPeopleInfoHttpApi.class);
    }

    @Bean
    public IJicInfoHttpApi createIJicInfoHttpApi() {
        return getApi(jicExpressUrl, IJicInfoHttpApi.class);
    }

    @Bean
    public IPackageStoresHttpApi createPackageStoresHttpApi() {
        return getApi(jicExpressUrl, IPackageStoresHttpApi.class);
    }

    @Bean
    public IGiftCardHttpApi createIGiftCardHttpApi() {
        return getApi(jicExpressUrl, IGiftCardHttpApi.class);
    }


    @Bean
    public IVoucherHttpApi createIVoucherHttpApi() {
        return getApi(jicExpressUrl, IVoucherHttpApi.class);
    }


    @Bean
    public IUserIntegralHttpApi createIUserIntegralHttpApi() {
        return getApi(jicExpressUrl, IUserIntegralHttpApi.class);
    }

    @Bean
    public IVoucherApi createIVoucherApi() {
        return getApi(jicExpressUrl, IVoucherApi.class);
    }


    @Bean
    public IMessageHttpApi createIMessageHttpApi() {
        return getApi(jicExpressUrl, IMessageHttpApi.class);
    }


    @Bean
    public MessageOpenSendWxHttpApi createMessageOpenSendWxHttpApi() {
        return getApi(thirdUrl, MessageOpenSendWxHttpApi.class);
    }


    @Bean
    public IWyAiCallingHttpApi createWyAiCallingHttpApi() {
        return getApi(wyAicallUrl, IWyAiCallingHttpApi.class);
    }


    @Bean
    public IQiyuPeopleCallingHttpApi createQiyuPeopleCallingHttpApi() {
        return getApi(qiyuCallService, IQiyuPeopleCallingHttpApi.class);
    }


    @Bean
    public IWeimobTokenHttpApi createWeimobTokenHttpService() {
        return getApi("https://jic.jnby.com/", IWeimobTokenHttpApi.class);
    }


    @Bean
    public IWeimobShopFulfilHttpApi createWeimobShopFulfilHttpService() {
        return getApi("https://dopen.weimob.com/", IWeimobShopFulfilHttpApi.class);
    }


    @Bean
    public INewPeopleInfoHttpApi createINewPeopleInfoHttpApi() {
        INewPeopleInfoHttpApi newPeopleInfoHttpApi = getNoCacheRetrofitBean(newPropelUrl, 50, 3).create(INewPeopleInfoHttpApi.class);
        return newPeopleInfoHttpApi;
    }

    @Bean
    public ILookLuckyDrawHttpApi createILookLuckyDrawHttpApi() {
        return getApi(jicExpressUrl, ILookLuckyDrawHttpApi.class);
    }

    @Bean
    public IBigDataLookHttpApi createIBigDataLookHttpApi() {
        return getApi(bigDataUrl, IBigDataLookHttpApi.class);
    }


}
