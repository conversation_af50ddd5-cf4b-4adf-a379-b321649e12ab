package org.springcenter.marketing.config.traceExtend;


import brave.Tracer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Configuration
public class TraceExtendConfig {

    @Resource
    private Tracer tracer;

    @Bean
    XxlJobTraceAspect createXxlJobTraceAspect() {
        return new XxlJobTraceAspect(tracer);
    }


    @Bean
    EventBusTraceAspect createEventBusTraceAspect() {
        return new EventBusTraceAspect(tracer);
    }
}
