package org.springcenter.markerting.task;

import com.alibaba.fastjson.JSONObject;
import com.jnby.common.Page;
import com.jnby.common.util.IdLeaf;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.killbill.bus.api.PersistentBus;
import org.springcenter.marketing.JnbyMarketingCenterApplication;
import org.springcenter.marketing.api.dto.*;
import org.springcenter.marketing.config.PropertiesValueConfig;
import org.springcenter.marketing.modules.context.AddIntegralContext;
import org.springcenter.marketing.modules.context.MemberBaseInfoQueryV2Context;
import org.springcenter.marketing.modules.context.PeoplePagContext;
import org.springcenter.marketing.modules.event.AddIntegralEvent;
import org.springcenter.marketing.modules.event.bus.AddIntegralEventBus;
import org.springcenter.marketing.modules.mapper.TaskInfoMapper;
import org.springcenter.marketing.modules.service.ITaskService;
import org.springcenter.marketing.modules.service.ITaskTemplateHandle;
import org.springcenter.marketing.modules.service.IWebService;
import org.springcenter.marketing.modules.webapi.ICustomerVipHttpApi;
import org.springcenter.marketing.modules.webapi.IPeopleInfoHttpApi;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CountDownLatch;

/**
 * <AUTHOR>
 * @Date:2023/4/25 10:14
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes={JnbyMarketingCenterApplication.class})
public class TaskTest {
     @Autowired
     private AddIntegralEventBus addIntegralEventBus;

     @Autowired
     private PropertiesValueConfig propertiesValueConfig;

     @Test
     public void testKillBill() throws PersistentBus.EventBusException {
          AddIntegralContext integralContext = new AddIntegralContext();
          AddIntegralEvent event = new AddIntegralEvent(integralContext,
                  Long.valueOf(IdLeaf.getDateId(propertiesValueConfig.getTaskTemplateTag())),
                  Long.valueOf(IdLeaf.getDateId(propertiesValueConfig.getTaskTemplateTag())),
                  UUID.randomUUID()
          );
          addIntegralEventBus.post(event);
     }

     @Autowired
     private ITaskService taskService;

     @Autowired
     private ITaskTemplateHandle taskTemplateHandle;

     @Autowired
     private List<ITaskTemplateHandle> taskTemplateHandles;

     @Test
     public void testAddTaskTemplate() throws ParseException {
          //--签到
        /*TaskTemplateReq taskTemplateReq = new TaskTemplateReq();
        taskTemplateReq.setButtonName("签到测试");
        //taskTemplateReq.setEndTime(new Date());
        taskTemplateReq.setTaskDescription("每天签到一次得三积分");
        taskTemplateReq.setButtonName("任务别名按钮");
        taskTemplateReq.setTaskLink("www.baidu.com");
        taskTemplateReq.setTaskType(0);
        taskTemplateReq.setIcon("icon954");
        taskTemplateReq.setTaskName("签到任务名称");
        taskTemplateReq.setSort(1);*/

          //===================================================================
          //--评价
        /*TaskTemplateReq taskTemplateReq = new TaskTemplateReq();
        taskTemplateReq.setButtonName("评价测试");
        taskTemplateReq.setEndTime(new Date());
        taskTemplateReq.setTaskDescription("一周三次二积分");
        taskTemplateReq.setTaskAliasName("任务别名--评价");
        taskTemplateReq.setTaskLink("www.baidu.com");
        taskTemplateReq.setTaskType(1);
        taskTemplateReq.setIcon("icon954");
        taskTemplateReq.setTaskName("任务名称评价");
        taskTemplateReq.setSort(2);
        List<TaskTemplateReq.PopulationObject> cardLevels = new ArrayList<>();
        TaskTemplateReq.PopulationObject param1 = new TaskTemplateReq.PopulationObject();
        param1.setId("1");
        param1.setName("银卡");
        cardLevels.add(param1);
        TaskTemplateReq.PopulationObject param2 = new TaskTemplateReq.PopulationObject();
        param2.setId("2");
        param2.setName("金卡");
        cardLevels.add(param2);
        taskTemplateReq.setCardLevels(cardLevels);

        List<TaskTemplateReq.PopulationObject> peoplePags = new ArrayList<>();
        TaskTemplateReq.PopulationObject peoplePags1 = new TaskTemplateReq.PopulationObject();
        peoplePags1.setId("11111");
        peoplePags1.setName("人群包1");
        peoplePags.add(peoplePags1);
        TaskTemplateReq.PopulationObject peoplePags2 = new TaskTemplateReq.PopulationObject();
        peoplePags2.setId("22222");
        peoplePags2.setName("人群包2");
        peoplePags.add(peoplePags2);
        taskTemplateReq.setPeoplePag(peoplePags);

        TaskTemplateReq.PopulationObject storePags = new TaskTemplateReq.PopulationObject();
        storePags.setId("11111");
        storePags.setName("门店1");
        taskTemplateReq.setStorePag(storePags);

        List<String> weIds = new ArrayList<>();
        weIds.add("11");
        weIds.add("4");
        taskTemplateReq.setWeIds(weIds);

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date startTime = dateFormat.parse("2023-04-01 10:00:00");
        Date endTime = dateFormat.parse("2023-09-01 10:00:00");
        taskTemplateReq.setStartTime(startTime);
        taskTemplateReq.setEndTime(endTime);
        taskTemplateReq.setExecutionMode(1);
        taskTemplateReq.setGainPoints(5);
        taskTemplateReq.setRequiredTimes(3);
        taskTemplateReq.setIsDesignatedPopulation(1);*/

          //=============================================================
          //--发帖
          TaskTemplateReq taskTemplateReq = new TaskTemplateReq();
          taskTemplateReq.setButtonName("发帖测试223");
          taskTemplateReq.setEndTime(new Date());
          taskTemplateReq.setTaskDescription("一周三次七积分");
          taskTemplateReq.setTaskAliasName("任务别名--发帖");
          taskTemplateReq.setTaskLink("www.baidu.com");
          taskTemplateReq.setTaskType(2);
          taskTemplateReq.setIcon("icon954");
          taskTemplateReq.setTaskName("任务名称发帖");
          taskTemplateReq.setSort(2);
          /*List<TaskTemplateReq.PopulationObject> cardLevels = new ArrayList<>();
          TaskTemplateReq.PopulationObject param1 = new TaskTemplateReq.PopulationObject();
          param1.setId("1");
          param1.setName("银卡");
          cardLevels.add(param1);
          TaskTemplateReq.PopulationObject param2 = new TaskTemplateReq.PopulationObject();
          param2.setId("2");
          param2.setName("金卡");
          cardLevels.add(param2);
          taskTemplateReq.setCardLevels(cardLevels);

          List<TaskTemplateReq.PopulationObject> peoplePags = new ArrayList<>();
          TaskTemplateReq.PopulationObject peoplePags1 = new TaskTemplateReq.PopulationObject();
          peoplePags1.setId("11111");
          peoplePags1.setName("人群包1");
          peoplePags.add(peoplePags1);
          TaskTemplateReq.PopulationObject peoplePags2 = new TaskTemplateReq.PopulationObject();
          peoplePags2.setId("22222");
          peoplePags2.setName("人群包2");
          peoplePags.add(peoplePags2);
          taskTemplateReq.setPeoplePag(peoplePags);

          TaskTemplateReq.PopulationObject storePags = new TaskTemplateReq.PopulationObject();
          storePags.setId("11111");
          storePags.setName("人群包1");
          taskTemplateReq.setStorePag(storePags);*/

          List<String> weIds = new ArrayList<>();
          weIds.add("4");
          taskTemplateReq.setWeIds(weIds);

          SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
          Date startTime = dateFormat.parse("2024-01-01 10:00:00");
          Date endTime = dateFormat.parse("2024-12-11 10:00:00");
          taskTemplateReq.setStartTime(startTime);
          taskTemplateReq.setEndTime(endTime);
          taskTemplateReq.setExecutionMode(1);
          taskTemplateReq.setGainPoints(5);
          taskTemplateReq.setRequiredTimes(3);
          taskTemplateReq.setIsDesignatedPopulation(1);
          taskTemplateReq.setExecutionCycle(1);
          taskTemplateReq.setExecutionCycleAttr("1,2,3");
          taskTemplateReq.setExecutionStartTime("9:00:00");
          taskTemplateReq.setExecutionEndTime("21:00:00");


          //==============================================================
          //--自定义
        /*TaskTemplateReq taskTemplateReq = new TaskTemplateReq();
        taskTemplateReq.setButtonName("自定义测试");
        taskTemplateReq.setEndTime(new Date());
        taskTemplateReq.setTaskDescription("自定义");
        taskTemplateReq.setTaskAliasName("任务别名--自定义");
        taskTemplateReq.setTaskLink("www.baidu.com");
        taskTemplateReq.setTaskType(3);
        taskTemplateReq.setIcon("icon954");
        taskTemplateReq.setTaskName("任务名称自定义");
        taskTemplateReq.setSort(2);
        List<TaskTemplateReq.PopulationObject> cardLevels = new ArrayList<>();
        TaskTemplateReq.PopulationObject param1 = new TaskTemplateReq.PopulationObject();
        param1.setId("1");
        param1.setName("银卡");
        cardLevels.add(param1);
        TaskTemplateReq.PopulationObject param2 = new TaskTemplateReq.PopulationObject();
        param2.setId("2");
        param2.setName("金卡");
        cardLevels.add(param2);
        taskTemplateReq.setCardLevels(cardLevels);

        List<TaskTemplateReq.PopulationObject> peoplePags = new ArrayList<>();
        TaskTemplateReq.PopulationObject peoplePags1 = new TaskTemplateReq.PopulationObject();
        peoplePags1.setId("11111");
        peoplePags1.setName("人群包1");
        peoplePags.add(peoplePags1);
        TaskTemplateReq.PopulationObject peoplePags2 = new TaskTemplateReq.PopulationObject();
        peoplePags2.setId("22222");
        peoplePags2.setName("人群包2");
        peoplePags.add(peoplePags2);
        taskTemplateReq.setPeoplePag(peoplePags);

        TaskTemplateReq.PopulationObject storePags = new TaskTemplateReq.PopulationObject();
        storePags.setId("11111");
        storePags.setName("人群包1");
        taskTemplateReq.setStorePag(storePags);

        List<String> weIds = new ArrayList<>();
        weIds.add("11");
        weIds.add("4");
        taskTemplateReq.setWeIds(weIds);

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date startTime = dateFormat.parse("2024-04-01 10:00:00");
        Date endTime = dateFormat.parse("2024-09-01 10:00:00");
        taskTemplateReq.setStartTime(startTime);
        taskTemplateReq.setEndTime(endTime);
        taskTemplateReq.setExecutionMode(1);
        taskTemplateReq.setGainPoints(5);
        taskTemplateReq.setRequiredTimes(3);
        taskTemplateReq.setIsDesignatedPopulation(1);
        taskTemplateReq.setExecutionCycle(2);
        taskTemplateReq.setExecutionCycleAttr("1,2,3");
        taskTemplateReq.setExecutionStartTime("9:00:00");
        taskTemplateReq.setExecutionEndTime("21:00:00");*/

          ITaskTemplateHandle taskTemplateHandle = taskTemplateHandles.stream()
                  .filter(item -> item.getType().contains(taskTemplateReq.getTaskType()))
                  .findFirst().orElse(null);
          List<TaskTemplateResp> ret = taskTemplateHandle.addTaskTemplate(taskTemplateReq);
          System.out.println("=========" + JSONObject.toJSONString(ret));
     }

     @Test
     public void queryTaskDetail() {
          TaskTemplateDetailReq req = new TaskTemplateDetailReq();
          //req.setId("20230222134519268");
          //req.setId("20230222140530286");
          //req.setId("202302221431302349");
          req.setId("202302221437352364");
          System.out.println("================" + JSONObject.toJSONString(taskService.queryTaskDetail(req)));
     }

     @Test
     public void testUpdateTaskDetail() throws ParseException {

          //--签到
        /*UpdateTaskTemplateReq updateTaskTemplateReq = new UpdateTaskTemplateReq();
        updateTaskTemplateReq.setId("20230222134519268");
        updateTaskTemplateReq.setButtonName("签到测试修改");
        updateTaskTemplateReq.setTaskDescription("描述");
        updateTaskTemplateReq.setTaskAliasName("签到任务别名修改");
        updateTaskTemplateReq.setTaskLink("www.baidu.com");
        updateTaskTemplateReq.setTaskType(0);
        updateTaskTemplateReq.setIcon("icon954");
        updateTaskTemplateReq.setTaskName("签到任务别名修改");
        updateTaskTemplateReq.setSort(10);
        updateTaskTemplateReq.setVersion("V1");
        updateTaskTemplateReq.setWeId("2504948039");
        updateTaskTemplateReq.setIsDesignatedPopulation(0);*/

          //===================================================================
          //--评价
        /*UpdateTaskTemplateReq updateTaskTemplateReq = new UpdateTaskTemplateReq();
        updateTaskTemplateReq.setId("20230222142025331");
        updateTaskTemplateReq.setVersion("V1");
        updateTaskTemplateReq.setButtonName("评价测试");
        updateTaskTemplateReq.setEndTime(new Date());
        updateTaskTemplateReq.setTaskDescription("评价描述");
        updateTaskTemplateReq.setTaskAliasName("任务别名--评价修改");
        updateTaskTemplateReq.setTaskLink("www.baidu.com");
        updateTaskTemplateReq.setTaskType(1);
        updateTaskTemplateReq.setIcon("icon954");
        updateTaskTemplateReq.setTaskName("任务名称评价修改");
        updateTaskTemplateReq.setSort(2);
        List<TaskTemplateReq.PopulationObject> cardLevels = new ArrayList<>();
        TaskTemplateReq.PopulationObject param1 = new TaskTemplateReq.PopulationObject();
        param1.setId("1");
        param1.setName("银卡");
        cardLevels.add(param1);
        TaskTemplateReq.PopulationObject param2 = new TaskTemplateReq.PopulationObject();
        param2.setId("7");
        param2.setName("铂金卡");
        cardLevels.add(param2);
        updateTaskTemplateReq.setCardLevels(cardLevels);

        List<TaskTemplateReq.PopulationObject> peoplePags = new ArrayList<>();
        TaskTemplateReq.PopulationObject peoplePags1 = new TaskTemplateReq.PopulationObject();
        peoplePags1.setId("11111");
        peoplePags1.setName("人群包1");
        peoplePags.add(peoplePags1);
        TaskTemplateReq.PopulationObject peoplePags2 = new TaskTemplateReq.PopulationObject();
        peoplePags2.setId("33333");
        peoplePags2.setName("人群包3");
        peoplePags.add(peoplePags2);
        updateTaskTemplateReq.setPeoplePag(peoplePags);

        TaskTemplateReq.PopulationObject storePags = new TaskTemplateReq.PopulationObject();
        storePags.setId("11111");
        storePags.setName("门店包1");
        updateTaskTemplateReq.setStorePag(storePags);
        updateTaskTemplateReq.setWeId("4");

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date startTime = dateFormat.parse("2024-03-02 10:00:00");
        Date endTime = dateFormat.parse("2024-03-22 10:00:00");
        updateTaskTemplateReq.setStartTime(startTime);
        updateTaskTemplateReq.setEndTime(endTime);
        updateTaskTemplateReq.setExecutionMode(1);
        updateTaskTemplateReq.setGainPoints(2);
        updateTaskTemplateReq.setRequiredTimes(1);
        updateTaskTemplateReq.setIsDesignatedPopulation(1);*/

          //=============================================================
          //--发帖
        /*UpdateTaskTemplateReq updateTaskTemplateReq = new UpdateTaskTemplateReq();
        updateTaskTemplateReq.setId("202302221431302349");
        updateTaskTemplateReq.setVersion("V1");
        updateTaskTemplateReq.setButtonName("发帖测试修改");
        updateTaskTemplateReq.setEndTime(new Date());
        updateTaskTemplateReq.setTaskDescription("修改");
        updateTaskTemplateReq.setTaskAliasName("任务别名--发帖修改");
        updateTaskTemplateReq.setTaskLink("www.baidu.com修改");
        updateTaskTemplateReq.setTaskType(2);
        updateTaskTemplateReq.setIcon("icon954");
        updateTaskTemplateReq.setTaskName("任务名称发帖修改");
        updateTaskTemplateReq.setSort(2);
        List<TaskTemplateReq.PopulationObject> cardLevels = new ArrayList<>();
        TaskTemplateReq.PopulationObject param1 = new TaskTemplateReq.PopulationObject();
        param1.setId("1");
        param1.setName("银卡");
        cardLevels.add(param1);
        TaskTemplateReq.PopulationObject param2 = new TaskTemplateReq.PopulationObject();
        param2.setId("3");
        param2.setName("泊金卡");
        cardLevels.add(param2);
        updateTaskTemplateReq.setCardLevels(cardLevels);

        List<TaskTemplateReq.PopulationObject> peoplePags = new ArrayList<>();
        TaskTemplateReq.PopulationObject peoplePags1 = new TaskTemplateReq.PopulationObject();
        peoplePags1.setId("11111");
        peoplePags1.setName("人群包1");
        peoplePags.add(peoplePags1);
        TaskTemplateReq.PopulationObject peoplePags2 = new TaskTemplateReq.PopulationObject();
        peoplePags2.setId("4444");
        peoplePags2.setName("人群包2");
        peoplePags.add(peoplePags2);
        updateTaskTemplateReq.setPeoplePag(peoplePags);

        TaskTemplateReq.PopulationObject storePags = new TaskTemplateReq.PopulationObject();
        storePags.setId("000001");
        storePags.setName("门店包");
        updateTaskTemplateReq.setStorePag(storePags);

        updateTaskTemplateReq.setWeId("4");

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date startTime = dateFormat.parse("2024-04-01 10:00:00");
        Date endTime = dateFormat.parse("2024-09-01 10:00:00");
        updateTaskTemplateReq.setStartTime(startTime);
        updateTaskTemplateReq.setEndTime(endTime);
        updateTaskTemplateReq.setExecutionMode(1);
        updateTaskTemplateReq.setGainPoints(1);
        updateTaskTemplateReq.setRequiredTimes(1);
        updateTaskTemplateReq.setIsDesignatedPopulation(1);
        updateTaskTemplateReq.setExecutionCycle(1);
        updateTaskTemplateReq.setExecutionCycleAttr("1,2,3,4,5");
        updateTaskTemplateReq.setExecutionStartTime("9:01:00");
        updateTaskTemplateReq.setExecutionEndTime("21:01:00");*/


          //==============================================================
          //--自定义
          UpdateTaskTemplateReq updateTaskTemplateReq = new UpdateTaskTemplateReq();
          updateTaskTemplateReq.setId("202302221437352364");
          updateTaskTemplateReq.setVersion("V2");
          updateTaskTemplateReq.setButtonName("自定义测试修改");
          updateTaskTemplateReq.setEndTime(new Date());
          updateTaskTemplateReq.setTaskDescription("自定义");
          updateTaskTemplateReq.setTaskAliasName("任务别名--自定义修改");
          updateTaskTemplateReq.setTaskLink("www.baidu.com");
          updateTaskTemplateReq.setTaskType(3);
          updateTaskTemplateReq.setIcon("icon954");
          updateTaskTemplateReq.setTaskName("任务名称自定义修改");
          updateTaskTemplateReq.setSort(2);
          /*List<TaskTemplateReq.PopulationObject> cardLevels = new ArrayList<>();
          TaskTemplateReq.PopulationObject param1 = new TaskTemplateReq.PopulationObject();
          param1.setId("1");
          param1.setName("银卡");
          cardLevels.add(param1);
          TaskTemplateReq.PopulationObject param2 = new TaskTemplateReq.PopulationObject();
          param2.setId("2");
          param2.setName("金卡");
          cardLevels.add(param2);
          updateTaskTemplateReq.setCardLevels(cardLevels);

          List<TaskTemplateReq.PopulationObject> peoplePags = new ArrayList<>();
          TaskTemplateReq.PopulationObject peoplePags1 = new TaskTemplateReq.PopulationObject();
          peoplePags1.setId("11111");
          peoplePags1.setName("人群包1");
          peoplePags.add(peoplePags1);
          TaskTemplateReq.PopulationObject peoplePags2 = new TaskTemplateReq.PopulationObject();
          peoplePags2.setId("000001");
          peoplePags2.setName("人群包001");
          peoplePags.add(peoplePags2);
          updateTaskTemplateReq.setPeoplePag(peoplePags);

          TaskTemplateReq.PopulationObject storePags = new TaskTemplateReq.PopulationObject();
          storePags.setId("11111");
          storePags.setName("人群包1");
          updateTaskTemplateReq.setStorePag(storePags);*/

          updateTaskTemplateReq.setWeId("4");

          SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
          Date startTime = dateFormat.parse("2024-04-01 10:00:00");
          Date endTime = dateFormat.parse("2024-09-01 10:00:00");
          updateTaskTemplateReq.setStartTime(startTime);
          updateTaskTemplateReq.setEndTime(endTime);
          updateTaskTemplateReq.setExecutionMode(0);
          updateTaskTemplateReq.setGainPoints(10);
          updateTaskTemplateReq.setRequiredTimes(10);
          updateTaskTemplateReq.setIsDesignatedPopulation(1);
          updateTaskTemplateReq.setExecutionCycle(2);
          updateTaskTemplateReq.setExecutionCycleAttr("1,2,3,22");
          updateTaskTemplateReq.setExecutionStartTime("9:01:00");
          updateTaskTemplateReq.setExecutionEndTime("21:01:00");

          ITaskTemplateHandle taskTemplateHandle = taskTemplateHandles.stream()
                  .filter(item -> item.getType().contains(updateTaskTemplateReq.getTaskType()))
                  .findFirst().orElse(null);
          System.out.println("===============" + JSONObject.toJSONString(taskTemplateHandle.updateTaskDetail(updateTaskTemplateReq)));
     }

     @Test
     public void operateList() {
          OperateTaskTemplateReq req = new OperateTaskTemplateReq();
          req.setId("202305221646599712");
          req.setStatus(1);
          System.out.println("===========" + taskService.operateTask(req));;
     }

     @Test
     public void searchTaskList() throws ParseException {
          TaskTemplateListReq req = new TaskTemplateListReq();
          //req.setTaskType();
          //req.setTaskNo("********");
       /* req.setTaskAliasName("任务别名--自定义");
        req.setWeId("4");*/
        /*SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");//注意月份是MM
        Date date = simpleDateFormat.parse("2023-2-20 10:00:00");
        Date date1 = simpleDateFormat.parse("2023-2-21 10:00:00");
        req.setStartTime(date);
        req.setEndTime(date1);*/
          //req.setStatus(2);
          req.setStatus(0);
          Page page = new Page();
          page.setPageNo(1);
          page.setPageSize(20);
          System.out.println("==============" + JSONObject.toJSONString(taskService.queryTaskList(req, page)));;
     }

     @Test
     public void searchOperator() {
          TaskOperatorsReq taskOperatorsReq = new TaskOperatorsReq();
          Page page = new Page();
          taskOperatorsReq.setTaskId("2023022010455330");
          page.setPageNo(1);
          page.setPageSize(2);
          System.out.println("============================" + taskService.queryTaskOperators(taskOperatorsReq, page));;
     }

     @Test
     public void searchDetailList() {
          TaskTemplateDetailsReq req = new TaskTemplateDetailsReq();
          Page page = new Page();
          req.setId("202305091802489425");
          req.setItemId("P0000118");
          page.setPageNo(1);
          page.setPageSize(20);
          System.out.println("============================" + JSONObject.toJSONString(taskService.queryTaskTemplateDetails(req, page)));

     }


     @Test
     public void testSearchUserJob() throws ParseException {
          UserTaskListReq req = new UserTaskListReq();
          req.setWeId("15");
          req.setUnionId("oZpUxswr4Tp6k9HZWyVr3_bmTZ4I");
          req.setOpenId("o_IlO1EHacVYSce_v7oZxgl1mJMs");
          req.setPhone("15546341352");
          req.setNickName("click");
          req.setVipLevel(1);
          /*StopWatch stopWatch = new StopWatch();
          stopWatch.start("任务总");*/
          System.out.println("=====" + JSONObject.toJSONString(taskService.queryUserTaskList(req)));
          //stopWatch.stop();
          //System.out.println("============任务总" + stopWatch.getTotalTimeSeconds());
     }

     @Test
     public void testClickTaskByUser() {
          UserClickTaskReq req = new UserClickTaskReq();
          req.setId("202312261032211968141");
          req.setUnionId("oZpUxsxK0mjvss7cZgSDUpnJ5yeE");
          req.setType(1);
          req.setOperate("1");
          System.out.println(JSONObject.toJSONString(taskService.clickTaskByUser(req)));
     }

     @Test
     public void listenerTest() {
          ListenerEvent req = new ListenerEvent();
          req.setWeId("15");
          req.setUnionId("oZpUxswr4Tp6k9HZWyVr3_bmTZ4I");
          req.setType(1);
          req.setUniqueIdent("PINGJIA2023122805");
          req.setStatus(1);
          taskService.listener(req);
     }

     @Test
     public void job() {
          // 下架
          taskService.takeOffTheShelfJob();

          // 过期
          taskService.expireUserJob();
     }

     @Test
     public void batchUpdate() throws ParseException {
          BatchUpdateTaskTemplateReq req = new BatchUpdateTaskTemplateReq();
          SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");//注意月份是MM
          Date date = simpleDateFormat.parse("2024-09-24 10:00:00");
          Date date1 = simpleDateFormat.parse("2024-09-25 10:00:00");
          BatchUpdateTaskTemplateReq.UpdateTaskTemplate template1 = new BatchUpdateTaskTemplateReq.UpdateTaskTemplate();
          template1.setId("202302221431302349");
          template1.setStartTime(date);
          template1.setEndTime(date1);
          BatchUpdateTaskTemplateReq.UpdateTaskTemplate template2 = new BatchUpdateTaskTemplateReq.UpdateTaskTemplate();
          Date date2 = simpleDateFormat.parse("2024-12-24 10:00:00");
          Date date3 = simpleDateFormat.parse("2024-12-25 10:00:00");
          template2.setId("202302230937512440");
          template2.setStartTime(date2);
          template2.setEndTime(date3);
          BatchUpdateTaskTemplateReq.UpdateTaskTemplate template3 = new BatchUpdateTaskTemplateReq.UpdateTaskTemplate();
          template3.setId("20230223094023450");
          Date date4 = simpleDateFormat.parse("2024-2-24 10:00:00");
          Date date5 = simpleDateFormat.parse("2024-2-25 10:00:00");
          template3.setStartTime(date4);
          template3.setEndTime(date5);
          List<BatchUpdateTaskTemplateReq.UpdateTaskTemplate> rets = new ArrayList<>();
          rets.add(template1);
          rets.add(template2);
          //rets.add(template3);
          req.setUpdates(rets);
          taskService.batchUpdateTaskDate(req);
     }

     @Autowired
     private IWebService webService;
     @Test
     public void testStore() {
          System.out.println("======================" + webService.getStoreInfo("oZpUxswr4Tp6k9HZWyVr3_bmTZ4I", "15"));
     }

     @Resource
     private IPeopleInfoHttpApi peopleInfoHttpApi;
     @Test
     public void testPeople() throws IOException {
          PeoplePagContext context = new PeoplePagContext();
          context.setOpenId("1");
          peopleInfoHttpApi.getPeopleInfoPag(context).execute();
     }

     @Resource
     private ICustomerVipHttpApi customerVipHttpApi;
     @Test
     public void testStoreInfo() throws IOException {
          MemberBaseInfoQueryV2Context context = new MemberBaseInfoQueryV2Context();
          context.setBrandId("15");
          context.setQueryType(1);
          context.setQueryValue("1");
          customerVipHttpApi.getUserBaseInfoV2(context).execute();
     }

     @Test
     public void judgeTask() {
          JudgeIsShowReq req = new JudgeIsShowReq();
          req.setWeId("15");
          StopWatch stopWatch = new StopWatch();
          stopWatch.start("开始");
          System.out.println("==========" + taskService.judgeIsShow(req));
          stopWatch.stop();
          System.out.println("============测试时间" + stopWatch.getTotalTimeSeconds());
     }

     @Autowired
     private TaskInfoMapper taskInfoMapper;



     @Test
     public void testbf() {
          CountDownLatch startLatch = new CountDownLatch(1);
          CountDownLatch endLatch = new CountDownLatch(10);
          for (int i = 0; i < 10; i++) {
               new Thread(() -> {
                    try {
                         startLatch.await();
                         //taskInfoMapper.updateInFactStockByInfo(
                    } catch (InterruptedException e) {
                         e.printStackTrace();
                    } finally {
                         endLatch.countDown();
                    }
               }).start();
          }

     }

}
